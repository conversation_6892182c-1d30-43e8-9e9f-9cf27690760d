{"name": "vibe-testing-suite", "version": "1.0.0", "description": "Dr<PERSON> <PERSON>'s Vibe Testing Suite - Emotional Response and Micro-UX Testing", "scripts": {"test:vibe": "playwright test --config=./setup/vibe-test-config.js", "test:vibe:headed": "playwright test --config=./setup/vibe-test-config.js --headed", "test:vibe:debug": "playwright test --config=./setup/vibe-test-config.js --debug", "test:vibe:ui": "playwright test --config=./setup/vibe-test-config.js --ui", "test:star-confidence": "playwright test core/star-interaction-confidence.spec.js --config=./setup/vibe-test-config.js", "test:bulk-anxiety": "playwright test core/bulk-operation-anxiety.spec.js --config=./setup/vibe-test-config.js", "test:suggestion-timing": "playwright test core/suggestion-timing-intelligence.spec.js --config=./setup/vibe-test-config.js", "report:vibe": "playwright show-report test-results/vibe-report", "report:emotional": "open test-results/emotional-journey-report.html"}, "dependencies": {"@playwright/test": "^1.40.0"}, "devDependencies": {"typescript": "^5.0.0"}, "keywords": ["vibe-testing", "emotional-response", "micro-ux", "user-experience", "playwright", "testing"], "author": "<PERSON>. <PERSON> - World-Renowned Test Expert", "license": "MIT"}