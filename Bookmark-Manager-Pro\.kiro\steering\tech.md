# Technology Stack & Build System

## Core Technologies

- **Frontend Framework**: React 19.1.0 with TypeScript 5.7+
- **Build Tool**: Vite 6.2+ with esbuild (NOT tsc)
- **Styling**: Tailwind CSS 4.1+ with CSS custom properties
- **State Management**: React Context API + Custom Hooks
- **Testing**: <PERSON><PERSON> (E2E), <PERSON><PERSON><PERSON> (Unit), <PERSON><PERSON> (Component)

## Key Libraries

- **AI Integration**: `@google/genai` for Gemini API
- **Performance**: `react-window` for virtual scrolling
- **NLP**: `compromise` and `natural` for text processing
- **Drag & Drop**: `@hello-pangea/dnd`
- **Data Fetching**: `@tanstack/react-query`
- **PDF Export**: `jspdf`
- **Data Visualization**: `d3`

## Build System Rules

### CRITICAL: NO TSC RULE
- **NEVER use `tsc` command directly**
- Use Vite + esbuild for all TypeScript compilation
- TypeScript checking handled by VS Code Language Server

### Development Commands
```bash
npm run dev              # Start development server
npm run build            # Production build
npm run preview          # Preview production build
npm run lint             # ESLint check
npm run test             # Playwright tests
npm run test:unit        # Vitest unit tests
```

### Emergency Commands (20-second timeout rule)
```bash
npm run lint             # Quick syntax check
npm run build:check      # Development build
npm run dev              # Real-time error detection
```

### Memory-Optimized Commands
```bash
npm run dev:memory       # Development with increased memory
npm run emergency:cleanup # Memory cleanup
npm run emergency:dev    # Cleanup + dev with memory optimization
```

## Performance Targets

- Development server start: < 5 seconds
- Hot reload: < 1 second
- Production build: < 20 seconds
- Test execution: < 30 seconds

## Environment Setup

### Required Environment Variables
```bash
GEMINI_API_KEY=your_api_key_here  # Required for AI features
```

### WSL Development (Linux/Windows)
```bash
# MANDATORY first step in every WSL session:
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"

# Add to ~/.bashrc for permanent fix:
echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
```

## Code Quality Tools

- **ESLint**: Comprehensive TypeScript + React rules
- **TypeScript**: Strict mode with path aliases (`@/*`)
- **Prettier**: Code formatting (via ESLint integration)
- **Husky**: Pre-commit hooks for quality gates

## Testing Strategy

- **Unit Tests**: Vitest with jsdom environment
- **Component Tests**: Cypress with React Testing Library
- **E2E Tests**: Playwright across multiple browsers
- **Performance Tests**: Core Web Vitals monitoring
- **Accessibility Tests**: axe-core integration

## Memory Optimization

- **Automatic Cleanup**: Emergency cleanup at 82%+ memory usage
- **Bundle Splitting**: Vendor, icons, and utility chunks
- **Virtual Scrolling**: For large bookmark collections
- **Lazy Loading**: Components loaded on demand
- **Cache Management**: TTL and LRU eviction strategies