/**
 * COMPREHENSIVE PLAYLIST FEATURE TESTS
 * Tests for PlaylistPanel component and SmartPlaylistService
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { PlaylistPanel } from '../components/PlaylistPanel'
import { smartPlaylistService } from '../services/smartPlaylistService'
import type { Bookmark, Playlist } from '../../types'

// Mock data
const mockBookmarks: Bookmark[] = [
  {
    id: '1',
    title: 'React Documentation',
    url: 'https://react.dev',
    description: 'Official React documentation',
    tags: ['react', 'documentation', 'frontend'],
    collection: 'Development',
    dateAdded: '2024-01-01T00:00:00Z',
    favicon: 'https://react.dev/favicon.ico',
    isPrivate: false
  },
  {
    id: '2',
    title: 'TypeScript Handbook',
    url: 'https://typescriptlang.org/docs',
    description: 'TypeScript documentation and guides',
    tags: ['typescript', 'documentation', 'programming'],
    collection: 'Development',
    dateAdded: '2024-01-02T00:00:00Z',
    favicon: 'https://typescriptlang.org/favicon.ico',
    isPrivate: false
  },
  {
    id: '3',
    title: 'Design Inspiration',
    url: 'https://dribbble.com',
    description: 'Creative design inspiration',
    tags: ['design', 'inspiration', 'ui'],
    collection: 'Design',
    dateAdded: '2024-01-03T00:00:00Z',
    favicon: 'https://dribbble.com/favicon.ico',
    isPrivate: false
  }
]

const mockPlaylists: Playlist[] = [
  {
    id: 'playlist-1',
    name: 'Frontend Development',
    description: 'Resources for frontend development',
    color: '#3b82f6',
    bookmarkIds: ['1', '2'],
    dateCreated: '2024-01-01T00:00:00Z',
    dateUpdated: '2024-01-01T00:00:00Z'
  },
  {
    id: 'playlist-2',
    name: 'Design Resources',
    description: 'Design inspiration and tools',
    color: '#ec4899',
    bookmarkIds: ['3'],
    dateCreated: '2024-01-02T00:00:00Z',
    dateUpdated: '2024-01-02T00:00:00Z'
  }
]

// Mock BookmarkContext
const mockBookmarkContext = {
  bookmarks: mockBookmarks,
  playlists: mockPlaylists,
  selectedBookmarks: [],
  filteredBookmarks: mockBookmarks,
  isSelectMode: false,
  selectedPlaylist: null,
  createPlaylist: vi.fn(),
  updatePlaylist: vi.fn(),
  deletePlaylist: vi.fn(),
  getPlaylistBookmarks: vi.fn((id: string) => {
    const playlist = mockPlaylists.find(p => p.id === id)
    return playlist ? mockBookmarks.filter(b => playlist.bookmarkIds.includes(b.id)) : []
  }),
  setSelectedPlaylist: vi.fn()
}

// Mock ModernThemeContext
const mockThemeContext = {
  themeMode: 'modern' as const
}

vi.mock('../contexts/BookmarkContext', () => ({
  useBookmarks: () => mockBookmarkContext
}))

vi.mock('../contexts/ModernThemeContext', () => ({
  useModernTheme: () => mockThemeContext
}))

describe('Playlist Feature Tests', () => {
  describe('SmartPlaylistService', () => {
    it('should generate smart playlist suggestions', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { maxSuggestions: 3, minConfidence: 0.5 }
      )

      expect(suggestions).toBeDefined()
      expect(Array.isArray(suggestions)).toBe(true)
      expect(suggestions.length).toBeGreaterThan(0)
      
      // Check suggestion structure
      suggestions.forEach(suggestion => {
        expect(suggestion).toHaveProperty('id')
        expect(suggestion).toHaveProperty('name')
        expect(suggestion).toHaveProperty('description')
        expect(suggestion).toHaveProperty('color')
        expect(suggestion).toHaveProperty('confidence')
        expect(suggestion).toHaveProperty('bookmarkIds')
        expect(suggestion).toHaveProperty('reasoning')
        expect(suggestion).toHaveProperty('category')
        
        expect(typeof suggestion.confidence).toBe('number')
        expect(suggestion.confidence).toBeGreaterThanOrEqual(0)
        expect(suggestion.confidence).toBeLessThanOrEqual(1)
        expect(Array.isArray(suggestion.bookmarkIds)).toBe(true)
      })
    })

    it('should generate playlist analytics', async () => {
      const playlist = mockPlaylists[0]
      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        mockBookmarks
      )

      expect(analytics).toBeDefined()
      expect(analytics.totalBookmarks).toBe(2)
      expect(analytics.contentTypes).toBeDefined()
      expect(analytics.domains).toBeDefined()
      expect(analytics.tags).toBeDefined()
      expect(analytics.temporalPatterns).toBeDefined()
      expect(analytics.topDomains).toBeDefined()
      expect(analytics.recommendations).toBeDefined()
      
      expect(typeof analytics.averageAge).toBe('number')
      expect(typeof analytics.engagementScore).toBe('number')
      expect(typeof analytics.duplicateCount).toBe('number')
      expect(typeof analytics.brokenLinksCount).toBe('number')
      expect(Array.isArray(analytics.temporalPatterns)).toBe(true)
      expect(Array.isArray(analytics.topDomains)).toBe(true)
      expect(Array.isArray(analytics.recommendations)).toBe(true)
    })

    it('should auto-create playlists above threshold', async () => {
      const autoPlaylists = await smartPlaylistService.autoCreatePlaylists(
        mockBookmarks,
        mockPlaylists,
        { autoCreateThreshold: 0.8 }
      )

      expect(Array.isArray(autoPlaylists)).toBe(true)
      
      // All auto-created playlists should have high confidence
      autoPlaylists.forEach(playlist => {
        expect(playlist.confidence).toBeGreaterThanOrEqual(0.8)
      })
    })

    it('should categorize suggestions correctly', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists
      )

      const validCategories = ['content-based', 'temporal', 'behavioral', 'semantic']
      
      suggestions.forEach(suggestion => {
        expect(validCategories).toContain(suggestion.category)
      })
    })

    it('should respect confidence thresholds', async () => {
      const highConfidenceSuggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { minConfidence: 0.9 }
      )

      const lowConfidenceSuggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { minConfidence: 0.1 }
      )

      expect(lowConfidenceSuggestions.length).toBeGreaterThanOrEqual(highConfidenceSuggestions.length)
      
      highConfidenceSuggestions.forEach(suggestion => {
        expect(suggestion.confidence).toBeGreaterThanOrEqual(0.9)
      })
    })
  })

  describe('PlaylistPanel Component', () => {
    beforeEach(() => {
      vi.clearAllMocks()
    })

    it('should render when open', () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      expect(screen.getByText('Smart Playlists')).toBeInTheDocument()
      expect(screen.getByText('Create New Playlist')).toBeInTheDocument()
    })

    it('should not render when closed', () => {
      const { container } = render(<PlaylistPanel isOpen={false} onClose={vi.fn()} />)
      
      expect(container.firstChild).toBeNull()
    })

    it('should display existing playlists', () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      expect(screen.getByText('Frontend Development')).toBeInTheDocument()
      expect(screen.getByText('Design Resources')).toBeInTheDocument()
    })

    it('should handle playlist creation', async () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      const nameInput = screen.getByPlaceholderText('My Awesome Playlist')
      const descriptionInput = screen.getByPlaceholderText('A collection of my favorite websites')
      const createButton = screen.getByText('Create Playlist')
      
      fireEvent.change(nameInput, { target: { value: 'Test Playlist' } })
      fireEvent.change(descriptionInput, { target: { value: 'Test description' } })
      fireEvent.click(createButton)
      
      expect(mockBookmarkContext.createPlaylist).toHaveBeenCalledWith(
        'Test Playlist',
        'Test description',
        '#4a9eff',
        []
      )
    })

    it('should display smart controls', () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      expect(screen.getByText('AI Suggest')).toBeInTheDocument()
      expect(screen.getByText('Auto-Create')).toBeInTheDocument()
    })

    it('should handle playlist editing', async () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      const editButtons = screen.getAllByTitle('Edit playlist')
      fireEvent.click(editButtons[0])
      
      // Should enter edit mode
      const saveButton = await screen.findByText('Save')
      expect(saveButton).toBeInTheDocument()
    })

    it('should handle playlist deletion', async () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      const deleteButtons = screen.getAllByTitle('Delete playlist')
      fireEvent.click(deleteButtons[0])
      
      expect(mockBookmarkContext.deletePlaylist).toHaveBeenCalledWith('playlist-1')
    })

    it('should show analytics button for playlists', () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      const analyticsButtons = screen.getAllByTitle('View Analytics')
      expect(analyticsButtons.length).toBeGreaterThan(0)
    })

    it('should handle color selection', () => {
      render(<PlaylistPanel isOpen={true} onClose={vi.fn()} />)
      
      const colorButtons = screen.getAllByRole('button')
      const colorButton = colorButtons.find(button => 
        button.style.backgroundColor && button.style.backgroundColor !== ''
      )
      
      if (colorButton) {
        fireEvent.click(colorButton)
        // Color should be selected (implementation depends on UI feedback)
      }
    })
  })
})
