import React, { useMemo } from 'react';

interface DomainExtractionTestProps {
  testUrls?: string[];
}

const DomainExtractionTest: React.FC<DomainExtractionTestProps> = ({ 
  testUrls = [
    'https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/',
    'https://github.com/user/repo',
    'https://very-long-domain-name.com/extremely/long/path/that/goes/on/and/on',
    'https://short.com/a',
    'invalid-url-test',
    'https://example.com/very-long-path-name-that-exceeds-fifteen-characters-easily',
    'https://subdomain.example.com/short',
  ]
}) => {
  
  // Original domain extraction logic (simple)
  const extractSimpleDomain = (url: string) => {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return url;
    }
  };

  // Improved domain extraction logic (matches BookmarkCard.tsx)
  const extractSmartDomain = (url: string) => {
    try {
      const urlObj = new URL(url);
      const hostname = urlObj.hostname.replace('www.', '');
      
      // For very long URLs like dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/
      // Show domain + truncated path to prevent layout issues
      if (urlObj.pathname && urlObj.pathname.length > 20) {
        const pathParts = urlObj.pathname.split('/').filter(Boolean);
        if (pathParts.length > 0) {
          const firstPath = pathParts[0];
          if (firstPath.length > 15) {
            return `${hostname}/${firstPath.substring(0, 15)}...`;
          }
          return `${hostname}/${firstPath}`;
        }
      }
      
      return hostname;
    } catch {
      // Fallback for invalid URLs - truncate if too long
      const truncated = url.length > 30 ? url.substring(0, 30) + '...' : url;
      return truncated;
    }
  };

  const testResults = useMemo(() => {
    return testUrls.map(url => ({
      original: url,
      simple: extractSimpleDomain(url),
      smart: extractSmartDomain(url),
      improvement: extractSimpleDomain(url) !== extractSmartDomain(url)
    }));
  }, [testUrls]);

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'monospace', 
      backgroundColor: '#1a1a1a', 
      color: '#fff',
      borderRadius: '8px',
      margin: '20px 0'
    }}>
      <h2 style={{ color: '#4ade80', marginBottom: '20px' }}>
        🧪 Domain Extraction Test Results
      </h2>
      
      <div style={{ marginBottom: '20px', fontSize: '14px', color: '#888' }}>
        Testing the improved domain extraction logic that provides better context for long URLs
        while preventing layout overflow issues.
      </div>

      <div style={{ display: 'grid', gap: '16px' }}>
        {testResults.map((result, index) => (
          <div 
            key={index}
            style={{ 
              padding: '12px',
              backgroundColor: '#2a2a2a',
              borderRadius: '6px',
              border: result.improvement ? '2px solid #22c55e' : '1px solid #444'
            }}
          >
            <div style={{ marginBottom: '8px', fontSize: '12px', color: '#888' }}>
              Original URL:
            </div>
            <div style={{ 
              marginBottom: '12px', 
              wordBreak: 'break-all',
              fontSize: '13px',
              color: '#e5e5e5'
            }}>
              {result.original}
            </div>
            
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '12px' }}>
              <div>
                <div style={{ fontSize: '12px', color: '#ef4444', marginBottom: '4px' }}>
                  ❌ Simple Logic:
                </div>
                <div style={{ 
                  padding: '6px 8px',
                  backgroundColor: '#333',
                  borderRadius: '4px',
                  fontSize: '13px',
                  border: '1px solid #555'
                }}>
                  {result.simple}
                </div>
              </div>
              
              <div>
                <div style={{ fontSize: '12px', color: '#22c55e', marginBottom: '4px' }}>
                  ✅ Smart Logic:
                </div>
                <div style={{ 
                  padding: '6px 8px',
                  backgroundColor: '#333',
                  borderRadius: '4px',
                  fontSize: '13px',
                  border: result.improvement ? '1px solid #22c55e' : '1px solid #555'
                }}>
                  {result.smart}
                </div>
              </div>
            </div>
            
            {result.improvement && (
              <div style={{ 
                marginTop: '8px', 
                fontSize: '11px', 
                color: '#22c55e',
                fontStyle: 'italic'
              }}>
                ✨ Improved: Provides better context while preventing layout issues
              </div>
            )}
          </div>
        ))}
      </div>

      <div style={{ 
        marginTop: '20px', 
        padding: '12px',
        backgroundColor: '#333',
        borderRadius: '6px',
        fontSize: '12px'
      }}>
        <strong style={{ color: '#4ade80' }}>Smart Domain Extraction Rules:</strong>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>If pathname length &gt; 20 chars: show domain + first path segment</li>
          <li>If first path segment &gt; 15 chars: truncate with ellipsis</li>
          <li>Otherwise: show hostname only</li>
          <li>Invalid URLs: truncate at 30 chars with ellipsis</li>
        </ul>
      </div>

      <div style={{ 
        marginTop: '16px', 
        padding: '12px',
        backgroundColor: '#1e40af',
        borderRadius: '6px',
        fontSize: '12px'
      }}>
        <strong>Layout Benefits:</strong>
        <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
          <li>Prevents horizontal overflow in bookmark cards</li>
          <li>Maintains useful context for users</li>
          <li>Consistent display across different URL lengths</li>
          <li>Better responsive behavior on mobile devices</li>
        </ul>
      </div>
    </div>
  );
};

export default DomainExtractionTest;
