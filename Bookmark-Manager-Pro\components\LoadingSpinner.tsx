import React, { FC } from 'react';
import SpinnerIcon from './icons/SpinnerIcon';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  className?: string;
  showMessage?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
}

const sizeClasses = {
  sm: 'w-4 h-4',
  md: 'w-6 h-6',
  lg: 'w-8 h-8',
  xl: 'w-12 h-12'
};

const colorClasses = {
  primary: 'text-sky-500',
  secondary: 'text-slate-400',
  success: 'text-green-500',
  warning: 'text-yellow-500',
  error: 'text-red-500'
};

const LoadingSpinner: FC<LoadingSpinnerProps> = ({
  size = 'md',
  message = 'Loading...',
  className = '',
  showMessage = true,
  color = 'primary'
}) => {
  const spinnerSizeClass = sizeClasses[size];
  const colorClass = colorClasses[color];

  return (
    <div className={`flex items-center justify-center ${className}`} role="status" aria-live="polite">
      <div className="flex flex-col items-center space-y-2">
        <SpinnerIcon className={`${spinnerSizeClass} ${colorClass} animate-spin`} />
        {showMessage && (
          <span className="text-sm text-slate-400 animate-pulse">
            {message}
          </span>
        )}
      </div>
      <span className="sr-only">{message}</span>
    </div>
  );
};

export default React.memo(LoadingSpinner);