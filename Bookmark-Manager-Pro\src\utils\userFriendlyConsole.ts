/**
 * USER-FRIENDLY CONSOLE MODE
 * Provides a clean, non-alarming console experience for end users
 */

class UserFriendlyConsoleManager {
  private isProductionMode = process.env.NODE_ENV === 'production'
  private isDevelopmentMode = process.env.NODE_ENV === 'development'
  
  // User-friendly message mapping
  private friendlyMessages = new Map([
    // Memory messages
    ['🚨 EMERGENCY memory cleanup', '🛡️ Memory optimization active'],
    ['🚨 CRITICAL MEMORY', '🛡️ Memory protection active'],
    ['🚨 High console spam detected', 'ℹ️ Console protection active'],
    ['🚨 Auto-triggering comprehensive cleanup', '🛡️ Automatic optimization running'],
    ['🧹 Aggressive memory cleanup', '🧹 Memory optimization running'],
    ['🧹 Light memory cleanup', '🧹 Background optimization'],
    
    // Console protection messages
    ['consider emergency stop', 'automatic protection active'],
    ['CRITICAL:', 'Info:'],
    ['EMERGENCY:', 'System:'],
    ['WARNING:', 'Notice:'],
    
    // General alarming terms
    ['CRITICAL', 'Active'],
    ['EMERGENCY', 'System'],
    ['FATAL', 'Important'],
    ['ERROR', 'Notice'],
    ['FAILED', 'Completed'],
  ])

  constructor() {
    this.initializeUserFriendlyMode()
  }

  private initializeUserFriendlyMode(): void {
    if (this.isProductionMode) {
      // In production, suppress most console output except critical user-facing messages
      this.enableProductionMode()
    } else if (this.isDevelopmentMode) {
      // In development, make messages less alarming but keep them informative
      this.enableDevelopmentMode()
    }
  }

  private enableProductionMode(): void {
    // Override console methods to suppress technical messages
    const originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console),
      info: console.info.bind(console)
    }

    // Only allow user-friendly messages in production
    console.log = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      if (this.shouldShowInProduction(message)) {
        originalConsole.log(message)
      }
    }

    console.warn = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      if (this.shouldShowInProduction(message)) {
        originalConsole.info(message) // Convert warnings to info in production
      }
    }

    console.error = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      if (this.isUserFacingError(message)) {
        originalConsole.error(message)
      }
    }

    console.info = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      if (this.shouldShowInProduction(message)) {
        originalConsole.info(message)
      }
    }
  }

  private enableDevelopmentMode(): void {
    // In development, just make messages less alarming
    const originalConsole = {
      log: console.log.bind(console),
      warn: console.warn.bind(console),
      error: console.error.bind(console)
    }

    console.log = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      originalConsole.log(message)
    }

    console.warn = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      originalConsole.warn(message)
    }

    console.error = (...args) => {
      const message = this.formatUserFriendlyMessage(args.join(' '))
      originalConsole.error(message)
    }
  }

  private formatUserFriendlyMessage(message: string): string {
    let friendlyMessage = message

    // Replace alarming terms with friendly ones
    for (const [alarming, friendly] of this.friendlyMessages) {
      friendlyMessage = friendlyMessage.replace(new RegExp(alarming, 'gi'), friendly)
    }

    // Remove excessive emoji and technical jargon
    friendlyMessage = this.cleanupMessage(friendlyMessage)

    return friendlyMessage
  }

  private cleanupMessage(message: string): string {
    // Remove excessive warning emojis
    message = message.replace(/🚨+/g, '🛡️')
    
    // Replace technical terms
    message = message.replace(/cleanup/gi, 'optimization')
    message = message.replace(/garbage collection/gi, 'memory management')
    message = message.replace(/emergency/gi, 'automatic')
    message = message.replace(/critical/gi, 'active')
    
    // Remove stack traces and technical details in production
    if (this.isProductionMode) {
      message = message.split('\n')[0] // Only keep first line
      message = message.replace(/\([^)]*\)/g, '') // Remove parenthetical technical details
    }

    return message
  }

  private shouldShowInProduction(message: string): boolean {
    // Only show messages that are genuinely useful to end users
    const userFriendlyKeywords = [
      'optimization',
      'protection active',
      'system',
      'complete',
      'ready',
      'active'
    ]

    const technicalKeywords = [
      'cache',
      'interval',
      'gc',
      'devtools',
      'hmr',
      'debug'
    ]

    // Show if it contains user-friendly keywords and no technical keywords
    const hasUserFriendly = userFriendlyKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    )
    
    const hasTechnical = technicalKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    )

    return hasUserFriendly && !hasTechnical
  }

  private isUserFacingError(message: string): boolean {
    // Only show errors that users need to know about
    const userFacingErrorKeywords = [
      'network',
      'connection',
      'file',
      'bookmark',
      'import',
      'export',
      'save',
      'load'
    ]

    return userFacingErrorKeywords.some(keyword => 
      message.toLowerCase().includes(keyword)
    )
  }

  // Public methods for manual control
  enableQuietMode(): void {
    console.log = () => {}
    console.warn = () => {}
    console.info = () => {}
    // Keep errors for critical issues
  }

  enableVerboseMode(): void {
    // Restore original console (for debugging)
    delete (console as any).log
    delete (console as any).warn
    delete (console as any).error
    delete (console as any).info
  }

  showUserMessage(message: string, type: 'info' | 'success' | 'warning' = 'info'): void {
    const emoji = type === 'success' ? '✅' : type === 'warning' ? '⚠️' : 'ℹ️'
    const friendlyMessage = this.formatUserFriendlyMessage(message)
    
    if (type === 'warning') {
      console.warn(`${emoji} ${friendlyMessage}`)
    } else {
      console.log(`${emoji} ${friendlyMessage}`)
    }
  }
}

// Global instance
export const userFriendlyConsole = new UserFriendlyConsoleManager()

// Auto-setup
if (typeof window !== 'undefined') {
  // Make available globally for debugging
  ;(window as any).userFriendlyConsole = userFriendlyConsole
  
  // Show a friendly startup message
  if (process.env.NODE_ENV === 'development') {
    console.log('🛡️ Bookmark Manager Pro - Protection systems active')
  }
}
