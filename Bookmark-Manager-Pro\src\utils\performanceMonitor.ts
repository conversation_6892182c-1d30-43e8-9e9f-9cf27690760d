/**
 * Performance monitoring utility for debugging Generate Summaries issues
 */

interface PerformanceMetrics {
  memoryUsage: number;
  domNodeCount: number;
  renderCount: number;
  timestamp: number;
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetrics[] = [];
  private renderCounts = new Map<string, number>();
  private isMonitoring = false;
  private monitorInterval: NodeJS.Timeout | null = null;

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    console.log('🔍 Performance monitoring started');
    
    this.monitorInterval = setInterval(() => {
      this.collectMetrics();
    }, 2000);
  }

  stopMonitoring(): void {
    if (!this.isMonitoring) return;
    
    this.isMonitoring = false;
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    
    console.log('🔍 Performance monitoring stopped');
    this.generateReport();
  }

  trackRender(componentName: string): void {
    const currentCount = this.renderCounts.get(componentName) || 0;
    this.renderCounts.set(componentName, currentCount + 1);
    
    // Warn about excessive re-renders
    if (currentCount > 50) {
      console.warn(`⚠️ Excessive re-renders detected for ${componentName}: ${currentCount + 1} renders`);
    }
  }

  private collectMetrics(): void {
    try {
      let memoryUsage = 0;
      
      // Get memory usage if available
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        memoryUsage = memInfo.usedJSHeapSize / 1024 / 1024; // MB
      }
      
      // Count DOM nodes
      const domNodeCount = document.querySelectorAll('*').length;
      
      // Total render count
      const totalRenderCount = Array.from(this.renderCounts.values()).reduce((sum, count) => sum + count, 0);
      
      const metrics: PerformanceMetrics = {
        memoryUsage,
        domNodeCount,
        renderCount: totalRenderCount,
        timestamp: Date.now()
      };
      
      this.metrics.push(metrics);
      
      // Keep only last 50 metrics to prevent memory buildup
      if (this.metrics.length > 50) {
        this.metrics = this.metrics.slice(-50);
      }
      
      // Check for performance issues
      this.checkPerformanceIssues(metrics);
      
    } catch (error) {
      console.error('Performance monitoring error:', error);
    }
  }

  private checkPerformanceIssues(metrics: PerformanceMetrics): void {
    // Memory usage warnings
    if (metrics.memoryUsage > 500) {
      console.warn(`⚠️ High memory usage: ${metrics.memoryUsage.toFixed(1)}MB`);
    }
    
    // DOM node count warnings
    if (metrics.domNodeCount > 8000) {
      console.warn(`⚠️ High DOM node count: ${metrics.domNodeCount} nodes`);
    }
    
    // Render count warnings
    if (this.metrics.length >= 2) {
      const previousMetrics = this.metrics[this.metrics.length - 2];
      const renderDiff = metrics.renderCount - previousMetrics.renderCount;
      
      if (renderDiff > 20) {
        console.warn(`⚠️ High render rate: ${renderDiff} renders in 2 seconds`);
      }
    }
  }

  private generateReport(): void {
    if (this.metrics.length === 0) return;
    
    const lastMetrics = this.metrics[this.metrics.length - 1];
    const firstMetrics = this.metrics[0];
    
    console.group('📊 Performance Report');
    console.log(`Duration: ${((lastMetrics.timestamp - firstMetrics.timestamp) / 1000).toFixed(1)}s`);
    console.log(`Final Memory Usage: ${lastMetrics.memoryUsage.toFixed(1)}MB`);
    console.log(`Final DOM Node Count: ${lastMetrics.domNodeCount}`);
    console.log(`Total Renders: ${lastMetrics.renderCount}`);
    
    // Component render breakdown
    console.log('\nComponent Render Counts:');
    Array.from(this.renderCounts.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .forEach(([component, count]) => {
        console.log(`  ${component}: ${count} renders`);
      });
    
    console.groupEnd();
  }

  reset(): void {
    this.metrics = [];
    this.renderCounts.clear();
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }
}

export const performanceMonitor = PerformanceMonitor.getInstance();

// Hook for tracking component renders
export const useRenderTracking = (componentName: string) => {
  React.useEffect(() => {
    performanceMonitor.trackRender(componentName);
  });
};

// React import for the hook
import React from 'react';

// Expose performance monitor globally for easier access
if (typeof window !== 'undefined') {
  (window as any).performanceMonitor = performanceMonitor;
}
