/**
 * CONSOLE PROTECTION SYSTEM
 * Prevents console spam and manages logging intelligently
 */

interface LogEntry {
  message: string
  timestamp: number
  count: number
}

class ConsoleProtectionManager {
  private messageHistory = new Map<string, LogEntry>()
  private messageCount = 0
  private startTime = Date.now()
  
  // Configuration
  private readonly MAX_MESSAGES_PER_PERIOD = 50
  private readonly PERIOD_DURATION = 10000 // 10 seconds
  private readonly DUPLICATE_THRESHOLD = 3 // Max same message repeats
  private readonly CLEANUP_INTERVAL = 30000 // 30 seconds
  
  // Original console methods
  private originalConsole = {
    log: console.log.bind(console),
    warn: console.warn.bind(console),
    error: console.error.bind(console),
    info: console.info.bind(console),
    debug: console.debug.bind(console)
  }
  
  private isProtectionActive = false
  private cleanupInterval: number | null = null

  constructor() {
    this.startProtection()
  }

  startProtection(): void {
    if (this.isProtectionActive) return
    
    this.isProtectionActive = true
    
    // Override console methods
    console.log = this.createProtectedLogger('log')
    console.warn = this.createProtectedLogger('warn')
    console.info = this.createProtectedLogger('info')
    console.debug = this.createProtectedLogger('debug')
    // Keep error logging always available
    console.error = this.createProtectedLogger('error', true)
    
    // Start cleanup interval
    this.cleanupInterval = window.setInterval(() => {
      this.cleanup()
    }, this.CLEANUP_INTERVAL)
    
    this.originalConsole.log('🛡️ Console protection activated')
  }

  stopProtection(): void {
    if (!this.isProtectionActive) return
    
    // Restore original console methods
    console.log = this.originalConsole.log
    console.warn = this.originalConsole.warn
    console.error = this.originalConsole.error
    console.info = this.originalConsole.info
    console.debug = this.originalConsole.debug
    
    // Clear cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
    
    this.isProtectionActive = false
    this.originalConsole.log('🛡️ Console protection deactivated')
  }

  private createProtectedLogger(level: string, alwaysAllow = false) {
    return (...args: any[]) => {
      const message = args.map(arg => 
        typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
      ).join(' ')
      
      // Always allow errors and critical messages
      if (alwaysAllow || this.isCriticalMessage(message)) {
        this.originalConsole[level as keyof typeof this.originalConsole](...args)
        return
      }
      
      // Check if we should allow this message
      if (this.shouldAllowMessage(message)) {
        this.originalConsole[level as keyof typeof this.originalConsole](...args)
        this.recordMessage(message)
      } else {
        // Silently drop the message
        this.recordDroppedMessage(message)
      }
    }
  }

  private shouldAllowMessage(message: string): boolean {
    const now = Date.now()
    
    // Reset counter if period has passed
    if (now - this.startTime > this.PERIOD_DURATION) {
      this.messageCount = 0
      this.startTime = now
    }
    
    // Check message count limit
    if (this.messageCount >= this.MAX_MESSAGES_PER_PERIOD) {
      return false
    }
    
    // Check for duplicate messages
    const messageKey = this.getMessageKey(message)
    const existing = this.messageHistory.get(messageKey)
    
    if (existing && existing.count >= this.DUPLICATE_THRESHOLD) {
      return false
    }
    
    return true
  }

  private recordMessage(message: string): void {
    this.messageCount++
    
    const messageKey = this.getMessageKey(message)
    const existing = this.messageHistory.get(messageKey)
    
    if (existing) {
      existing.count++
      existing.timestamp = Date.now()
    } else {
      this.messageHistory.set(messageKey, {
        message,
        timestamp: Date.now(),
        count: 1
      })
    }
  }

  private recordDroppedMessage(message: string): void {
    // Keep track of dropped messages for debugging
    const messageKey = this.getMessageKey(message)
    const existing = this.messageHistory.get(messageKey)
    
    if (existing) {
      existing.count++
    }
  }

  private getMessageKey(message: string): string {
    // Create a key for message deduplication
    return message.substring(0, 100) // First 100 chars
  }

  private isCriticalMessage(message: string): boolean {
    const criticalKeywords = [
      'error', 'critical', 'emergency', 'crash', 'failed',
      'exception', 'fatal', 'urgent', '🚨', '❌'
    ]
    
    const lowerMessage = message.toLowerCase()
    return criticalKeywords.some(keyword => lowerMessage.includes(keyword))
  }

  private cleanup(): void {
    const now = Date.now()
    const expireTime = 60000 // 1 minute
    
    // Remove old message history
    for (const [key, entry] of this.messageHistory.entries()) {
      if (now - entry.timestamp > expireTime) {
        this.messageHistory.delete(key)
      }
    }
    
    // Reset counters if needed
    if (now - this.startTime > this.PERIOD_DURATION) {
      this.messageCount = 0
      this.startTime = now
    }
  }

  // Public methods for monitoring
  getStats(): {
    messagesThisPeriod: number
    maxMessages: number
    duplicateMessages: number
    droppedMessages: number
    isProtectionActive: boolean
  } {
    const duplicateMessages = Array.from(this.messageHistory.values())
      .filter(entry => entry.count > 1).length
    
    const droppedMessages = Array.from(this.messageHistory.values())
      .reduce((sum, entry) => sum + Math.max(0, entry.count - this.DUPLICATE_THRESHOLD), 0)
    
    return {
      messagesThisPeriod: this.messageCount,
      maxMessages: this.MAX_MESSAGES_PER_PERIOD,
      duplicateMessages,
      droppedMessages,
      isProtectionActive: this.isProtectionActive
    }
  }

  // Emergency methods
  emergencyStop(): void {
    // Stop all console output except errors
    console.log = () => {}
    console.warn = () => {}
    console.info = () => {}
    console.debug = () => {}
    
    this.originalConsole.error('🚨 EMERGENCY: Console output stopped due to spam')
  }

  emergencyRestore(): void {
    this.stopProtection()
    this.startProtection()
    this.originalConsole.log('🔄 Console protection restored after emergency')
  }
}

// Global instance
export const consoleProtection = new ConsoleProtectionManager()

// Auto-start protection in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Make available globally for debugging
  ;(window as any).consoleProtection = consoleProtection
  
  // Monitor for console spam and auto-activate emergency mode (silent monitoring)
  setInterval(() => {
    const stats = consoleProtection.getStats()

    // Only log warnings in development mode and when really necessary
    if (process.env.NODE_ENV === 'development') {
      if (stats.messagesThisPeriod > stats.maxMessages * 0.95) {
        // Only show this once per period to avoid spam
        const now = Date.now()
        const lastWarning = (window as any)._lastConsoleWarning || 0
        if (now - lastWarning > 30000) { // 30 seconds cooldown
          console.log('ℹ️ Console protection active - message limiting in effect')
          ;(window as any)._lastConsoleWarning = now
        }
      }

      // Only show critical spam warning if it's really excessive
      if (stats.droppedMessages > 500) { // Increased threshold
        const now = Date.now()
        const lastCritical = (window as any)._lastConsoleCritical || 0
        if (now - lastCritical > 60000) { // 1 minute cooldown
          console.warn('⚠️ High console activity detected - automatic protection active')
          ;(window as any)._lastConsoleCritical = now
        }
      }
    }
    // In production, run completely silently
  }, 10000) // Check every 10 seconds instead of 5
}
