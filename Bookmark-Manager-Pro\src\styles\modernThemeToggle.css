/* Modern Theme Toggle Styling */

/* Compact Theme Toggle */
.modern-theme-toggle-compact {
  display: flex;
  gap: 8px;
  align-items: center;
}

.modern-theme-toggle-btn.compact,
.color-scheme-toggle-btn.compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  background: var(--primary-bg, #ffffff);
  color: var(--text-primary, #1e293b);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modern-theme-toggle-btn.compact:hover,
.color-scheme-toggle-btn.compact:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.theme-label {
  font-size: 12px;
  font-weight: 500;
}

/* Full Theme Toggle Container */
.modern-theme-toggle-container {
  position: relative;
  z-index: 1000;
}

.modern-theme-toggle-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 10px;
  background: var(--primary-bg, #ffffff);
  color: var(--text-primary, #1e293b);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.modern-theme-toggle-trigger:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
}

/* Theme Panel */
.modern-theme-panel {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 320px;
  background: var(--primary-bg, #ffffff);
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  padding: 20px;
  z-index: 1001;
  animation: slideDown 0.2s ease-out;
}

.modern-theme-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--border-color, #e2e8f0);
}

.modern-theme-panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary, #64748b);
  cursor: pointer;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* Theme Sections */
.modern-theme-section {
  margin-bottom: 20px;
}

.modern-theme-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Theme Options */
.modern-theme-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 12px;
  background: var(--secondary-bg, #f8fafc);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.modern-theme-option:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

.modern-theme-option.active {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.theme-preview {
  flex-shrink: 0;
}

.preview-card {
  width: 40px;
  height: 30px;
  border-radius: 6px;
  border: 1px solid var(--border-color, #e2e8f0);
  overflow: hidden;
  position: relative;
}

.preview-card.theme-classic {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.preview-card.theme-modern {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.preview-header {
  height: 8px;
  background: rgba(0, 0, 0, 0.1);
}

.preview-content {
  padding: 4px;
}

.preview-line {
  height: 2px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 1px;
  margin-bottom: 2px;
}

.preview-line.short {
  width: 60%;
}

.theme-info {
  flex: 1;
  text-align: left;
}

.theme-name {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #1e293b);
  margin-bottom: 2px;
}

.theme-description {
  display: block;
  font-size: 12px;
  color: var(--text-secondary, #64748b);
}

.active-indicator {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: var(--accent-color, #3b82f6);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* Color Scheme Options */
.color-scheme-options {
  display: flex;
  gap: 8px;
}

.color-scheme-option {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 10px;
  background: var(--secondary-bg, #f8fafc);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.color-scheme-option:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-2px);
}

.color-scheme-option.active {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.1);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.scheme-icon {
  font-size: 18px;
}

.scheme-name {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary, #1e293b);
}

/* Theme Status */
.modern-theme-status {
  background: var(--tertiary-bg, #f1f5f9);
  border-radius: 10px;
  padding: 12px;
  margin-bottom: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  margin-bottom: 4px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-item span:first-child {
  color: var(--text-secondary, #64748b);
}

.status-value {
  font-weight: 600;
  color: var(--text-primary, #1e293b);
}

/* Theme Actions */
.modern-theme-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border: 2px solid transparent;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn.primary {
  background: var(--accent-color, #3b82f6);
  color: white;
}

.action-btn.primary:hover {
  background: var(--accent-hover, #2563eb);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.action-btn.secondary {
  background: var(--secondary-bg, #f8fafc);
  color: var(--text-primary, #1e293b);
  border-color: var(--border-color, #e2e8f0);
}

.action-btn.secondary:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

/* Backdrop */
.modern-theme-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(2px);
  z-index: 1000;
}

/* Quick Theme Switch */
.quick-modern-theme-switch {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 2px solid var(--border-color, #e2e8f0);
  border-radius: 8px;
  background: var(--primary-bg, #ffffff);
  color: var(--text-primary, #1e293b);
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-modern-theme-switch:hover {
  border-color: var(--accent-color, #3b82f6);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-1px);
}

.switch-label {
  font-size: 11px;
}

/* Theme Indicator */
.modern-theme-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: var(--text-secondary, #64748b);
}

.theme-mode-indicator,
.color-scheme-indicator {
  padding: 2px 6px;
  background: var(--tertiary-bg, #f1f5f9);
  border-radius: 4px;
  font-weight: 500;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .modern-theme-panel {
    width: 280px;
    right: -20px;
  }
  
  .color-scheme-options {
    flex-direction: column;
  }
  
  .modern-theme-actions {
    flex-direction: column;
  }
}
