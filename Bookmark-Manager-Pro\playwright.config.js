import { defineConfig, devices } from '@playwright/test';

/**
 * Playwright Configuration for Bookmark Manager Pro
 * Comprehensive testing setup for cross-browser compatibility
 */
export default defineConfig({
  testDir: './tests',
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  /* Opt out of parallel tests on CI. */
  workers: process.env.CI ? 1 : undefined,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/results.xml' }],
    ['./tests/vibe/reporters/vibe-metrics-reporter.js'],
    ['./tests/vibe/reporters/emotional-journey-reporter.js']
  ],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    baseURL: 'http://localhost:5173',

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: 'on-first-retry',

    /* Take screenshot on failure */
    screenshot: 'only-on-failure',

    /* Record video on failure */
    video: 'retain-on-failure',

    /* Global timeout for each action */
    actionTimeout: 10000,

    /* Global timeout for navigation */
    navigationTimeout: 30000,
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },

    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },

    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },

    /* Test against mobile viewports. */
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },

    /* Test against branded browsers. */
    {
      name: 'Microsoft Edge',
      use: { ...devices['Desktop Edge'], channel: 'msedge' },
    },
    {
      name: 'Google Chrome',
      use: { ...devices['Desktop Chrome'], channel: 'chrome' },
    },

    /* Dr. Elena's Vibe Testing Projects */
    {
      name: 'vibe-desktop',
      testDir: './tests/vibe',
      use: {
        ...devices['Desktop Chrome'],
        colorScheme: 'light',
        reducedMotion: 'no-preference',
        actionTimeout: 5000, // Realistic timing for vibe tests
        testIdAttribute: 'data-vibe-testid'
      }
    },
    {
      name: 'vibe-mobile',
      testDir: './tests/vibe',
      use: {
        ...devices['iPhone 13'],
        hasTouch: true
      }
    },
    {
      name: 'vibe-accessibility',
      testDir: './tests/vibe',
      use: {
        ...devices['Desktop Chrome'],
        reducedMotion: 'reduce',
        colorScheme: 'dark'
      }
    }
  ],

  /* Run your local dev server before starting the tests */
  webServer: {
    command: 'npm run dev',
    url: 'http://localhost:5173',
    reuseExistingServer: !process.env.CI,
    timeout: 120000,
  },

  /* Global setup and teardown */
  globalSetup: './tests/global-setup.js',
  globalTeardown: './tests/global-teardown.js',

  /* Test timeout */
  timeout: 30000,

  /* Expect timeout */
  expect: {
    timeout: 5000,
    toHaveScreenshot: {
      threshold: 0.2,
      mode: 'strict'
    },
    toMatchSnapshot: {
      threshold: 0.2
    }
  },

  /* Output directory for test artifacts */
  outputDir: 'test-results/',

  /* Test match patterns */
  testMatch: [
    '**/*.spec.js',
    '**/*.test.js',
    '**/*.e2e.js'
  ],

  /* Global test configuration */
  globalTimeout: 600000, // 10 minutes for entire test suite
});