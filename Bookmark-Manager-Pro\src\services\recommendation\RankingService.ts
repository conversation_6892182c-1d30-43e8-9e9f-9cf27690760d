/**
 * RANKING SERVICE
 * Ranks recommendation candidates using machine learning and heuristics
 */

import type {
  RankingService,
  Candidate,
  RankedRecommendation,
  FeatureVector,
  Explanation,
  FeedbackData,
  ExplanationFactor
} from './interfaces'

export class ModernRankingService implements RankingService {
  private modelWeights = {
    content: 0.4,
    collaborative: 0.3,
    temporal: 0.2,
    contextual: 0.1
  }

  private feedbackHistory: FeedbackData[] = []

  async rank(candidates: Candidate[], features: FeatureVector): Promise<RankedRecommendation[]> {
    // Calculate scores for each candidate
    const scoredCandidates = candidates.map((candidate, index) => {
      const score = this.calculateScore(candidate, features)
      const diversity = this.calculateDiversity(candidate, candidates)
      const novelty = this.calculateNovelty(candidate)
      
      return {
        ...candidate,
        rank: 0, // Will be set after sorting
        score,
        explanation: { primary: '', secondary: [], confidence: 0, factors: [] }, // Will be generated
        diversity,
        novelty
      }
    })

    // Sort by score (descending)
    const rankedCandidates = scoredCandidates.sort((a, b) => b.score - a.score)

    // Assign ranks and generate explanations
    return rankedCandidates.map((candidate, index) => ({
      ...candidate,
      rank: index + 1,
      explanation: this.generateBasicExplanation(candidate)
    }))
  }

  async explainRanking(recommendation: RankedRecommendation): Promise<Explanation> {
    const factors: ExplanationFactor[] = []

    // Analyze content features
    if (recommendation.features.contentFeatures.length > 0) {
      const contentScore = recommendation.features.contentFeatures.reduce((sum, val) => sum + val, 0) / recommendation.features.contentFeatures.length
      factors.push({
        type: 'content_similarity',
        weight: this.modelWeights.content,
        description: `Content similarity score: ${(contentScore * 100).toFixed(1)}%`,
        evidence: ['Similar topics', 'Related keywords', 'Content type match']
      })
    }

    // Analyze collaborative features
    if (recommendation.features.collaborativeFeatures.length > 0) {
      const collabScore = recommendation.features.collaborativeFeatures.reduce((sum, val) => sum + val, 0) / recommendation.features.collaborativeFeatures.length
      factors.push({
        type: 'user_behavior',
        weight: this.modelWeights.collaborative,
        description: `User behavior similarity: ${(collabScore * 100).toFixed(1)}%`,
        evidence: ['Similar user patterns', 'Community preferences', 'Interaction history']
      })
    }

    // Analyze temporal features
    if (recommendation.features.temporalFeatures.length > 0) {
      const temporalScore = recommendation.features.temporalFeatures.reduce((sum, val) => sum + val, 0) / recommendation.features.temporalFeatures.length
      factors.push({
        type: 'temporal_pattern',
        weight: this.modelWeights.temporal,
        description: `Temporal relevance: ${(temporalScore * 100).toFixed(1)}%`,
        evidence: ['Recent trends', 'Time-based patterns', 'Seasonal preferences']
      })
    }

    // Generate primary explanation
    const primaryFactor = factors.reduce((max, factor) => 
      factor.weight > max.weight ? factor : max, factors[0])

    return {
      primary: primaryFactor?.description || 'Recommended based on multiple factors',
      secondary: factors.slice(1).map(f => f.description),
      confidence: recommendation.confidence,
      factors
    }
  }

  async updateRankingModel(feedback: FeedbackData[]): Promise<void> {
    // Store feedback for model improvement
    this.feedbackHistory.push(...feedback)

    // Update model weights based on feedback
    await this.adjustModelWeights(feedback)

    // Limit feedback history size
    if (this.feedbackHistory.length > 1000) {
      this.feedbackHistory = this.feedbackHistory.slice(-1000)
    }
  }

  private calculateScore(candidate: Candidate, globalFeatures: FeatureVector): number {
    const candidateFeatures = candidate.features
    
    // Calculate weighted score
    const contentScore = this.calculateFeatureScore(candidateFeatures.contentFeatures)
    const collaborativeScore = this.calculateFeatureScore(candidateFeatures.collaborativeFeatures)
    const temporalScore = this.calculateFeatureScore(candidateFeatures.temporalFeatures)
    const contextualScore = this.calculateFeatureScore(candidateFeatures.contextualFeatures)

    const weightedScore = 
      contentScore * this.modelWeights.content +
      collaborativeScore * this.modelWeights.collaborative +
      temporalScore * this.modelWeights.temporal +
      contextualScore * this.modelWeights.contextual

    // Apply confidence boost
    return weightedScore * candidate.confidence
  }

  private calculateFeatureScore(features: number[]): number {
    if (features.length === 0) return 0
    
    // Normalize and average features
    const normalizedFeatures = features.map(f => Math.max(0, Math.min(1, f)))
    return normalizedFeatures.reduce((sum, val) => sum + val, 0) / normalizedFeatures.length
  }

  private calculateDiversity(candidate: Candidate, allCandidates: Candidate[]): number {
    // Calculate how different this candidate is from others
    let diversitySum = 0
    let comparisons = 0

    allCandidates.forEach(other => {
      if (other.id !== candidate.id) {
        const similarity = this.calculateCandidateSimilarity(candidate, other)
        diversitySum += (1 - similarity)
        comparisons++
      }
    })

    return comparisons > 0 ? diversitySum / comparisons : 0.5
  }

  private calculateNovelty(candidate: Candidate): number {
    // Calculate how novel this candidate is based on source and type
    const noveltyScores: Record<string, number> = {
      'content': 0.3,
      'collaborative': 0.6,
      'temporal': 0.8,
      'hybrid': 0.7
    }

    const baseNovelty = noveltyScores[candidate.source] || 0.5
    
    // Boost novelty for less common types
    const typeBoost = candidate.type === 'tag-group' ? 0.2 : 0
    
    return Math.min(1, baseNovelty + typeBoost)
  }

  private calculateCandidateSimilarity(candidate1: Candidate, candidate2: Candidate): number {
    // Calculate similarity based on bookmark overlap
    const set1 = new Set(candidate1.bookmarkIds)
    const set2 = new Set(candidate2.bookmarkIds)
    
    const intersection = new Set(Array.from(set1).filter(x => set2.has(x)))
    const union = new Set([...Array.from(set1), ...Array.from(set2)])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }

  private generateBasicExplanation(candidate: RankedRecommendation): Explanation {
    const factors: ExplanationFactor[] = []

    // Add source-based explanation
    switch (candidate.source) {
      case 'content':
        factors.push({
          type: 'content_similarity',
          weight: 0.8,
          description: 'Based on content similarity to your bookmarks',
          evidence: ['Similar topics', 'Related keywords']
        })
        break
      case 'collaborative':
        factors.push({
          type: 'user_behavior',
          weight: 0.8,
          description: 'Based on similar users\' preferences',
          evidence: ['Community patterns', 'User similarities']
        })
        break
      case 'temporal':
        factors.push({
          type: 'temporal_pattern',
          weight: 0.8,
          description: 'Based on trending patterns',
          evidence: ['Recent trends', 'Time patterns']
        })
        break
      default:
        factors.push({
          type: 'content_similarity',
          weight: 0.6,
          description: 'Based on multiple recommendation signals',
          evidence: ['Mixed factors']
        })
    }

    return {
      primary: factors[0]?.description || 'Recommended for you',
      secondary: [],
      confidence: candidate.confidence,
      factors
    }
  }

  private async adjustModelWeights(feedback: FeedbackData[]): Promise<void> {
    // Simple weight adjustment based on feedback
    const positiveFeedback = feedback.filter(f => f.feedback === 'like' || f.feedback === 'created_playlist')
    const negativeFeedback = feedback.filter(f => f.feedback === 'dislike' || f.feedback === 'not_interested')

    if (feedback.length === 0) return

    // Adjust weights based on feedback ratio
    const positiveRatio = positiveFeedback.length / feedback.length
    
    if (positiveRatio > 0.7) {
      // Good performance, slight boost to current strategy
      const boost = 0.05
      this.modelWeights.content = Math.min(1, this.modelWeights.content + boost)
    } else if (positiveRatio < 0.3) {
      // Poor performance, rebalance weights
      this.modelWeights = {
        content: 0.3,
        collaborative: 0.4,
        temporal: 0.2,
        contextual: 0.1
      }
    }

    // Normalize weights to sum to 1
    const totalWeight = Object.values(this.modelWeights).reduce((sum, w) => sum + w, 0)
    Object.keys(this.modelWeights).forEach(key => {
      this.modelWeights[key as keyof typeof this.modelWeights] /= totalWeight
    })
  }
}