# Contributing to Bookmark Manager Pro

Thank you for your interest in contributing to Bookmark Manager Pro! This document provides guidelines and information for contributors.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Setup](#development-setup)
- [Contributing Guidelines](#contributing-guidelines)
- [Pull Request Process](#pull-request-process)
- [Coding Standards](#coding-standards)
- [Testing Guidelines](#testing-guidelines)
- [Documentation](#documentation)
- [Issue Reporting](#issue-reporting)
- [Feature Requests](#feature-requests)

## Code of Conduct

### Our Pledge

We are committed to making participation in this project a harassment-free experience for everyone, regardless of age, body size, disability, ethnicity, gender identity and expression, level of experience, nationality, personal appearance, race, religion, or sexual identity and orientation.

### Our Standards

**Positive behavior includes:**
- Using welcoming and inclusive language
- Being respectful of differing viewpoints and experiences
- Gracefully accepting constructive criticism
- Focusing on what is best for the community
- Showing empathy towards other community members

**Unacceptable behavior includes:**
- The use of sexualized language or imagery
- Trolling, insulting/derogatory comments, and personal or political attacks
- Public or private harassment
- Publishing others' private information without explicit permission
- Other conduct which could reasonably be considered inappropriate

## Getting Started

### Prerequisites

#### Required Software
- Node.js 18 or higher
- npm or yarn package manager
- Git
- A code editor (VS Code recommended)
- Basic knowledge of React, TypeScript, and modern web development

⚠️ **IMPORTANT**: This project is undergoing a major architecture refactoring. Please review the [IMPLEMENTATION_PLAN.md](./IMPLEMENTATION_PLAN.md) Priority 0 section before contributing.

#### New Architecture Knowledge Requirements
- **State Management**: Context API + Custom Hooks pattern
- **Component Architecture**: Base components with composition
- **Design System**: Consistent styling with CSS modules
- **Testing**: Component testing with React Testing Library
- **TypeScript**: Strict typing with proper interfaces

#### Node.js Installation (If Not Already Installed)

**For Linux/Unix Systems (Ubuntu/Debian)**

If you encounter PATH issues or don't have Node.js installed:

1. **Fix PATH Issues**
   ```bash
   export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
   ```

2. **Verify sudo works**
   ```bash
   sudo --version
   ```

3. **Install Node.js via snap (recommended)**
   ```bash
   sudo snap install node --channel=20/stable --classic
   ```

4. **Verify installation**
   ```bash
   node --version
   npm --version
   ```

5. **Make PATH permanent**
   ```bash
   # Add to bashrc for permanent fix
   echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```

**For Windows/macOS**
- Download from [nodejs.org](https://nodejs.org/) (LTS version recommended)
- Follow the installer instructions

### First-time Setup

1. **Fork the repository**
   ```bash
   # Click the "Fork" button on GitHub
   ```

2. **Clone your fork**
   ```bash
   git clone https://github.com/YOUR_USERNAME/Bookmark-Manager-Pro.git
   cd Bookmark-Manager-Pro
   ```

3. **Add upstream remote**
   ```bash
   git remote add upstream https://github.com/ORIGINAL_OWNER/Bookmark-Manager-Pro.git
   ```

4. **Install dependencies**
   ```bash
   npm install
   ```

5. **Set up environment**
   ```bash
   cp .env.example .env.local
   # Add your Gemini API key to .env.local
   ```

6. **Start development server**
   ```bash
   npm run dev
   ```

## Development Setup

### Prerequisites
- Node.js 18+ and npm
- Git
- Code editor (VS Code recommended)
- Gemini API key for AI features

### Local Development
1. Fork and clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables:
   ```bash
   # Create .env file
   VITE_GEMINI_API_KEY=your_gemini_api_key_here
   ```
4. Start development server: `npm run dev`
5. Open http://localhost:5173

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint
- `npm run type-check` - Run TypeScript compiler check

### Project Structure

```
Bookmark-Manager-Pro/
├── components/          # React components
│   ├── ActionToolbar.tsx
│   ├── AdvancedSearchModal.tsx
│   ├── BookmarkItem.tsx
│   ├── BookmarkList.tsx
│   ├── BookmarkQA.tsx
│   ├── BulkEditModal.tsx
│   ├── BulkOperations.tsx
│   ├── ErrorBoundary.tsx
│   ├── InstructionSlideViewer.tsx
│   ├── ToastNotification.tsx
│   ├── VirtualizedBookmarkList.tsx
│   ├── YouTubeProcessor.tsx
│   └── icons/          # SVG icon components
├── services/           # Business logic and API services
│   ├── advancedSearchService.ts
│   ├── bookmarkParser.ts
│   ├── bulkOperationsService.ts
│   ├── clientSideProcessor.ts
│   ├── errorService.ts
│   ├── geminiService.ts
│   ├── hybridProcessor.ts
│   ├── qaService.ts
│   ├── userTierService.ts
│   └── youtubeService.ts
├── types.ts            # TypeScript type definitions
├── constants.ts        # Application constants
├── App.tsx            # Main application component
└── main.tsx           # Application entry point
```

## Coding Standards

### TypeScript
- Use strict TypeScript configuration
- Define proper interfaces for all data structures
- Avoid `any` type - use proper typing
- Use meaningful variable and function names

### React (Updated for New Architecture)
- **Components**: Use functional components with hooks only
- **State Management**: Use Context API + Custom Hooks (no direct useState in components)
- **Base Components**: Extend from BaseBookmarkComponent for bookmark-related UI
- **Error Boundaries**: Implement proper error boundaries
- **Performance**: Use React.memo for performance optimization
- **Composition**: Prefer composition over inheritance

### State Management Guidelines
- **Global State**: Use Context Providers (BookmarkContext, UIContext)
- **Local State**: Use custom hooks (useBookmarkData, useModalManager)
- **No Direct State**: Avoid useState in components, use custom hooks instead
- **Immutable Updates**: Always use immutable state updates
- **Type Safety**: Properly type all context values and hook returns

### Component Development
- **Base Components**: Extend BaseBookmarkComponent for consistency
- **Props Interface**: Define clear TypeScript interfaces for all props
- **CSS Modules**: Use CSS modules for component styling
- **Accessibility**: Include proper ARIA attributes and keyboard navigation
- **Testing**: Write tests for all component behaviors

### Code Style
- **Formatting**: Use ESLint and Prettier for consistent formatting
- **Naming**: Follow established naming conventions
  - Components: PascalCase (e.g., `BookmarkCard`)
  - Hooks: camelCase with 'use' prefix (e.g., `useBookmarkData`)
  - Context: PascalCase with 'Context' suffix (e.g., `BookmarkContext`)
  - Files: kebab-case for utilities, PascalCase for components
- **Documentation**: Write self-documenting code with clear variable names
- **Comments**: Add comments for complex business logic and architecture decisions

## Testing Guidelines (Updated for New Architecture)

### Unit Tests
- **Utility Functions**: Write tests for all utility functions
- **Custom Hooks**: Test all custom hooks with @testing-library/react-hooks
- **Context Providers**: Test context providers with mock consumers
- **Component Behavior**: Test component behavior, not implementation details
- **React Testing Library**: Use React Testing Library for all component tests
- **Coverage Goals**: Aim for 90%+ coverage on critical paths

### Component Testing
- **Base Components**: Test BaseBookmarkComponent thoroughly as foundation
- **Props Testing**: Test all prop combinations and edge cases
- **State Integration**: Test component integration with context providers
- **User Interactions**: Test all user interactions (clicks, keyboard, etc.)
- **Accessibility**: Test ARIA attributes and keyboard navigation

### State Management Testing
- **Context Providers**: Test all context provider state changes
- **Custom Hooks**: Test hook state updates and side effects
- **State Persistence**: Test localStorage and sessionStorage integration
- **Error States**: Test error handling in state management

### Integration Tests
- **Service Integration**: Test service integrations
- **API Interactions**: Verify API interactions with MSW
- **End-to-End Flows**: Test complete user workflows
- **Error Scenarios**: Test comprehensive error handling

### Testing Requirements for PRs
- [ ] All new components have tests
- [ ] All new hooks have tests
- [ ] All modified components have updated tests
- [ ] Integration tests for new features
- [ ] Tests pass locally and in CI

## Pull Request Process

1. **Create Feature Branch**: `git checkout -b feature/your-feature-name`
2. **Make Changes**: Implement your feature with tests
3. **Run Tests**: Ensure all tests pass
4. **Update Documentation**: Update relevant docs if needed
5. **Submit PR**: Create pull request with clear description
6. **Code Review**: Address feedback from maintainers
7. **Merge**: PR will be merged after approval

### PR Requirements
- [ ] All tests pass
- [ ] Code follows style guidelines
- [ ] Documentation updated if needed
- [ ] No breaking changes (or clearly documented)
- [ ] Feature is complete and tested

## Issue Reporting

### Bug Reports
Include:
- Steps to reproduce
- Expected vs actual behavior
- Browser/environment details
- Screenshots if applicable

### Feature Requests
Include:
- Clear description of the feature
- Use case and benefits
- Proposed implementation approach
- Any relevant mockups or examples

## Getting Help

- Check existing issues and documentation first
- Join our community discussions
- Tag maintainers for urgent issues
- Be respectful and patient with responses

---

*Thank you for contributing to Bookmark Manager Pro! Your efforts help make this tool better for everyone.*

### Development Workflow

1. **Create a feature branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make your changes**
   - Follow coding standards
   - Add tests for new functionality
   - Update documentation as needed

3. **Test your changes**
   ```bash
   npm run test
   npm run lint
   npm run type-check
   ```

4. **Commit your changes**
   ```bash
   git add .
   git commit -m "feat: add new feature description"
   ```

5. **Push to your fork**
   ```bash
   git push origin feature/your-feature-name
   ```

6. **Create a Pull Request**

## Contributing Guidelines

### Types of Contributions

We welcome various types of contributions:

- **Bug fixes**: Fix existing issues
- **Feature enhancements**: Improve existing features
- **New features**: Add new functionality
- **Documentation**: Improve or add documentation
- **Performance improvements**: Optimize code performance
- **UI/UX improvements**: Enhance user experience
- **Testing**: Add or improve tests

### Before You Start

1. **Check existing issues**: Look for existing issues or feature requests
2. **Create an issue**: If none exists, create one to discuss your proposal
3. **Get feedback**: Wait for maintainer feedback before starting work
4. **Assign yourself**: Comment on the issue to indicate you're working on it

## Pull Request Process

### PR Requirements

- [ ] **Clear description**: Explain what changes you made and why
- [ ] **Issue reference**: Link to the related issue(s)
- [ ] **Tests**: Include tests for new functionality
- [ ] **Documentation**: Update relevant documentation
- [ ] **No breaking changes**: Unless discussed and approved
- [ ] **Clean commit history**: Squash commits if necessary

### PR Template

```markdown
## Description
Brief description of changes

## Related Issue
Fixes #(issue number)

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Tests pass locally
- [ ] Added tests for new functionality
- [ ] Manual testing completed

## Screenshots (if applicable)
[Add screenshots here]

## Checklist
- [ ] Code follows project style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
- [ ] No console errors
```

### Review Process

1. **Automated checks**: CI/CD pipeline runs tests and linting
2. **Code review**: Maintainers review your code
3. **Feedback**: Address any requested changes
4. **Approval**: Once approved, your PR will be merged

## Coding Standards

### TypeScript Guidelines

- **Use TypeScript**: All new code should be in TypeScript
- **Type safety**: Avoid `any` types, use proper type definitions
- **Interfaces**: Define interfaces for complex objects
- **Enums**: Use enums for constants with multiple values

```typescript
// Good
interface BookmarkProps {
  id: string;
  title: string;
  url: string;
  tags?: string[];
}

// Avoid
const bookmark: any = { /* ... */ };
```

### React Guidelines

- **Functional components**: Use functional components with hooks
- **Props interface**: Define props interface for each component
- **Destructuring**: Destructure props in function parameters
- **Hooks**: Use custom hooks for reusable logic

```typescript
// Good
interface ButtonProps {
  onClick: () => void;
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
}

const Button: React.FC<ButtonProps> = ({ onClick, children, variant = 'primary' }) => {
  return (
    <button className={`btn btn-${variant}`} onClick={onClick}>
      {children}
    </button>
  );
};
```

### Styling Guidelines

- **Tailwind CSS**: Use Tailwind utility classes
- **Consistent spacing**: Use Tailwind spacing scale
- **Responsive design**: Consider mobile-first approach
- **Dark mode**: Support dark mode where applicable

### File Naming

- **Components**: PascalCase (e.g., `BookmarkList.tsx`)
- **Services**: camelCase (e.g., `geminiService.ts`)
- **Utils**: camelCase (e.g., `exportUtils.ts`)
- **Types**: camelCase (e.g., `types.ts`)

### Code Organization

- **Single responsibility**: Each file should have a single purpose
- **Logical grouping**: Group related functionality together
- **Clear imports**: Use absolute imports where possible
- **Export patterns**: Use named exports for utilities, default for components

## Testing Guidelines

### Testing Strategy

- **Unit tests**: Test individual functions and components
- **Integration tests**: Test component interactions
- **E2E tests**: Test complete user workflows

### Testing Tools

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing
- **Cypress**: End-to-end testing

### Writing Tests

```typescript
// Component test example
import { render, screen, fireEvent } from '@testing-library/react';
import { Button } from './Button';

describe('Button', () => {
  it('renders with correct text', () => {
    render(<Button onClick={() => {}}>Click me</Button>);
    expect(screen.getByText('Click me')).toBeInTheDocument();
  });

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn();
    render(<Button onClick={handleClick}>Click me</Button>);
    fireEvent.click(screen.getByText('Click me'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Test Coverage

- Aim for 80%+ test coverage
- Focus on critical business logic
- Test edge cases and error conditions

## Documentation

### Documentation Types

- **Code comments**: Explain complex logic
- **JSDoc**: Document functions and classes
- **README**: Project overview and setup
- **API docs**: Service and utility documentation
- **User guides**: Feature documentation

### Documentation Standards

```typescript
/**
 * Processes bookmark file and extracts bookmark data
 * @param file - The HTML file containing bookmarks
 * @param options - Processing options
 * @returns Promise resolving to array of bookmarks
 * @throws Error if file format is invalid
 */
export async function processBookmarkFile(
  file: File,
  options: ProcessingOptions = {}
): Promise<Bookmark[]> {
  // Implementation
}
```

## Issue Reporting

### Bug Reports

When reporting bugs, please include:

- **Clear title**: Descriptive summary of the issue
- **Steps to reproduce**: Detailed steps to recreate the bug
- **Expected behavior**: What should happen
- **Actual behavior**: What actually happens
- **Environment**: Browser, OS, Node.js version
- **Screenshots**: If applicable
- **Console errors**: Any error messages

### Bug Report Template

```markdown
**Bug Description**
A clear description of the bug

**Steps to Reproduce**
1. Go to '...'
2. Click on '...'
3. Scroll down to '...'
4. See error

**Expected Behavior**
What you expected to happen

**Actual Behavior**
What actually happened

**Environment**
- OS: [e.g. Windows 10]
- Browser: [e.g. Chrome 91]
- Node.js: [e.g. 18.0.0]

**Additional Context**
Any other context about the problem
```

## Feature Requests

### Proposing Features

1. **Check existing requests**: Search for similar feature requests
2. **Create detailed proposal**: Explain the feature and its benefits
3. **Provide use cases**: Show how the feature would be used
4. **Consider implementation**: Think about how it might be implemented

### Feature Request Template

```markdown
**Feature Description**
A clear description of the feature

**Problem Statement**
What problem does this solve?

**Proposed Solution**
How should this feature work?

**Use Cases**
Specific examples of how this would be used

**Additional Context**
Any other context or screenshots
```

## Development Tips

### Debugging

- Use React Developer Tools
- Enable TypeScript strict mode
- Use console.log strategically
- Leverage browser debugging tools

### Performance

- Use React.memo for expensive components
- Implement proper key props for lists
- Avoid unnecessary re-renders
- Optimize bundle size

### Accessibility

- Use semantic HTML elements
- Provide proper ARIA labels
- Ensure keyboard navigation
- Test with screen readers

## Getting Help

### Resources

- **Documentation**: Check existing docs first
- **Issues**: Search existing issues
- **Discussions**: Use GitHub Discussions for questions
- **Code review**: Ask for feedback on your approach

### Communication

- **Be respectful**: Maintain professional communication
- **Be specific**: Provide detailed information
- **Be patient**: Maintainers are volunteers
- **Be helpful**: Help others when you can

## Recognition

Contributors are recognized in:

- **Contributors list**: Added to README
- **Release notes**: Mentioned in changelog
- **Special recognition**: For significant contributions

## License

By contributing to Bookmark Manager Pro, you agree that your contributions will be licensed under the same license as the project.

---

**Thank you for contributing to Bookmark Manager Pro!**

Your contributions help make this project better for everyone. Whether you're fixing bugs, adding features, or improving documentation, every contribution is valuable and appreciated.

**Questions?** Feel free to open an issue or start a discussion if you need help getting started.

---

**Last Updated**: December 2024
**Version**: 1.0.0