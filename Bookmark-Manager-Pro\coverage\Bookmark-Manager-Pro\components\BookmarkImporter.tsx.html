
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Bookmark-Manager-Pro/components/BookmarkImporter.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">Bookmark-Manager-Pro/components</a> BookmarkImporter.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/158</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/158</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a>
<a name='L112'></a><a href='#L112'>112</a>
<a name='L113'></a><a href='#L113'>113</a>
<a name='L114'></a><a href='#L114'>114</a>
<a name='L115'></a><a href='#L115'>115</a>
<a name='L116'></a><a href='#L116'>116</a>
<a name='L117'></a><a href='#L117'>117</a>
<a name='L118'></a><a href='#L118'>118</a>
<a name='L119'></a><a href='#L119'>119</a>
<a name='L120'></a><a href='#L120'>120</a>
<a name='L121'></a><a href='#L121'>121</a>
<a name='L122'></a><a href='#L122'>122</a>
<a name='L123'></a><a href='#L123'>123</a>
<a name='L124'></a><a href='#L124'>124</a>
<a name='L125'></a><a href='#L125'>125</a>
<a name='L126'></a><a href='#L126'>126</a>
<a name='L127'></a><a href='#L127'>127</a>
<a name='L128'></a><a href='#L128'>128</a>
<a name='L129'></a><a href='#L129'>129</a>
<a name='L130'></a><a href='#L130'>130</a>
<a name='L131'></a><a href='#L131'>131</a>
<a name='L132'></a><a href='#L132'>132</a>
<a name='L133'></a><a href='#L133'>133</a>
<a name='L134'></a><a href='#L134'>134</a>
<a name='L135'></a><a href='#L135'>135</a>
<a name='L136'></a><a href='#L136'>136</a>
<a name='L137'></a><a href='#L137'>137</a>
<a name='L138'></a><a href='#L138'>138</a>
<a name='L139'></a><a href='#L139'>139</a>
<a name='L140'></a><a href='#L140'>140</a>
<a name='L141'></a><a href='#L141'>141</a>
<a name='L142'></a><a href='#L142'>142</a>
<a name='L143'></a><a href='#L143'>143</a>
<a name='L144'></a><a href='#L144'>144</a>
<a name='L145'></a><a href='#L145'>145</a>
<a name='L146'></a><a href='#L146'>146</a>
<a name='L147'></a><a href='#L147'>147</a>
<a name='L148'></a><a href='#L148'>148</a>
<a name='L149'></a><a href='#L149'>149</a>
<a name='L150'></a><a href='#L150'>150</a>
<a name='L151'></a><a href='#L151'>151</a>
<a name='L152'></a><a href='#L152'>152</a>
<a name='L153'></a><a href='#L153'>153</a>
<a name='L154'></a><a href='#L154'>154</a>
<a name='L155'></a><a href='#L155'>155</a>
<a name='L156'></a><a href='#L156'>156</a>
<a name='L157'></a><a href='#L157'>157</a>
<a name='L158'></a><a href='#L158'>158</a>
<a name='L159'></a><a href='#L159'>159</a>
<a name='L160'></a><a href='#L160'>160</a>
<a name='L161'></a><a href='#L161'>161</a>
<a name='L162'></a><a href='#L162'>162</a>
<a name='L163'></a><a href='#L163'>163</a>
<a name='L164'></a><a href='#L164'>164</a>
<a name='L165'></a><a href='#L165'>165</a>
<a name='L166'></a><a href='#L166'>166</a>
<a name='L167'></a><a href='#L167'>167</a>
<a name='L168'></a><a href='#L168'>168</a>
<a name='L169'></a><a href='#L169'>169</a>
<a name='L170'></a><a href='#L170'>170</a>
<a name='L171'></a><a href='#L171'>171</a>
<a name='L172'></a><a href='#L172'>172</a>
<a name='L173'></a><a href='#L173'>173</a>
<a name='L174'></a><a href='#L174'>174</a>
<a name='L175'></a><a href='#L175'>175</a>
<a name='L176'></a><a href='#L176'>176</a>
<a name='L177'></a><a href='#L177'>177</a>
<a name='L178'></a><a href='#L178'>178</a>
<a name='L179'></a><a href='#L179'>179</a>
<a name='L180'></a><a href='#L180'>180</a>
<a name='L181'></a><a href='#L181'>181</a>
<a name='L182'></a><a href='#L182'>182</a>
<a name='L183'></a><a href='#L183'>183</a>
<a name='L184'></a><a href='#L184'>184</a>
<a name='L185'></a><a href='#L185'>185</a>
<a name='L186'></a><a href='#L186'>186</a>
<a name='L187'></a><a href='#L187'>187</a>
<a name='L188'></a><a href='#L188'>188</a>
<a name='L189'></a><a href='#L189'>189</a>
<a name='L190'></a><a href='#L190'>190</a>
<a name='L191'></a><a href='#L191'>191</a>
<a name='L192'></a><a href='#L192'>192</a>
<a name='L193'></a><a href='#L193'>193</a>
<a name='L194'></a><a href='#L194'>194</a>
<a name='L195'></a><a href='#L195'>195</a>
<a name='L196'></a><a href='#L196'>196</a>
<a name='L197'></a><a href='#L197'>197</a>
<a name='L198'></a><a href='#L198'>198</a>
<a name='L199'></a><a href='#L199'>199</a>
<a name='L200'></a><a href='#L200'>200</a>
<a name='L201'></a><a href='#L201'>201</a>
<a name='L202'></a><a href='#L202'>202</a>
<a name='L203'></a><a href='#L203'>203</a>
<a name='L204'></a><a href='#L204'>204</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
import React, { ChangeEvent } from 'react';
<span class="cstat-no" title="statement not covered" >import { ArrowPathIcon } from './icons/HeroIcons';</span>
<span class="cstat-no" title="statement not covered" >import SpinnerIcon from './icons/SpinnerIcon';</span>
<span class="cstat-no" title="statement not covered" >import FileUpload from '../src/components/FileUpload';</span>
&nbsp;
interface BookmarkImporterProps {
  onFileChange: (event: ChangeEvent&lt;HTMLInputElement&gt;) =&gt; void;
  isLoading: boolean;
  onClearImport: () =&gt; void;
  hasImportedFile: boolean;
  resetToken: number;
  onFileUpload?: (file: File) =&gt; Promise&lt;void&gt;;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const BookmarkImporter: React.FC&lt;BookmarkImporterProps&gt; = ({ </span>
<span class="cstat-no" title="statement not covered" >  onFileChange, </span>
<span class="cstat-no" title="statement not covered" >  isLoading, </span>
<span class="cstat-no" title="statement not covered" >  onClearImport, </span>
<span class="cstat-no" title="statement not covered" >  hasImportedFile,</span>
<span class="cstat-no" title="statement not covered" >  resetToken,</span>
<span class="cstat-no" title="statement not covered" >  onFileUpload</span>
<span class="cstat-no" title="statement not covered" >}) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const handleFileSelect = (file: File) =&gt; {</span>
    // Create a proper synthetic event
<span class="cstat-no" title="statement not covered" >    const fileList = {</span>
<span class="cstat-no" title="statement not covered" >      0: file,</span>
<span class="cstat-no" title="statement not covered" >      length: 1,</span>
<span class="cstat-no" title="statement not covered" >      item: (index: number) =&gt; index === 0 ? file : null,</span>
<span class="cstat-no" title="statement not covered" >      [Symbol.iterator]: function* () { yield file; }</span>
<span class="cstat-no" title="statement not covered" >    } as FileList;</span>
    
<span class="cstat-no" title="statement not covered" >    const syntheticEvent = {</span>
<span class="cstat-no" title="statement not covered" >      target: { files: fileList },</span>
<span class="cstat-no" title="statement not covered" >      currentTarget: { files: fileList },</span>
<span class="cstat-no" title="statement not covered" >      nativeEvent: new Event('change'),</span>
<span class="cstat-no" title="statement not covered" >      bubbles: false,</span>
<span class="cstat-no" title="statement not covered" >      cancelable: false,</span>
<span class="cstat-no" title="statement not covered" >      defaultPrevented: false,</span>
<span class="cstat-no" title="statement not covered" >      eventPhase: 0,</span>
<span class="cstat-no" title="statement not covered" >      isTrusted: false,</span>
<span class="cstat-no" title="statement not covered" >      preventDefault: () =&gt; {},</span>
<span class="cstat-no" title="statement not covered" >      isDefaultPrevented: () =&gt; false,</span>
<span class="cstat-no" title="statement not covered" >      stopPropagation: () =&gt; {},</span>
<span class="cstat-no" title="statement not covered" >      isPropagationStopped: () =&gt; false,</span>
<span class="cstat-no" title="statement not covered" >      persist: () =&gt; {},</span>
<span class="cstat-no" title="statement not covered" >      timeStamp: Date.now(),</span>
<span class="cstat-no" title="statement not covered" >      type: 'change'</span>
<span class="cstat-no" title="statement not covered" >    } as ChangeEvent&lt;HTMLInputElement&gt;;</span>
<span class="cstat-no" title="statement not covered" >    onFileChange(syntheticEvent);</span>
<span class="cstat-no" title="statement not covered" >  };</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;div className="bookmark-importer-modern bg-gradient-to-br from-surface-50 to-surface-100 dark:from-dark-surface-secondary dark:to-dark-surface-tertiary rounded-2xl shadow-lg border border-surface-200 dark:border-dark-border-primary overflow-hidden"&gt;</span>
      {/* Header Section with Enhanced Design */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="relative p-8 bg-gradient-to-r from-primary-500 to-primary-600 text-white"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="absolute inset-0 bg-black/10" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;div className="relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="flex items-center space-x-3 mb-3"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 7h6m0 10v-3M9 17v-3m3-2h.01" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;h2 className="text-2xl font-bold"&gt;</span>
                Import Your Bookmarks
<span class="cstat-no" title="statement not covered" >              &lt;/h2&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;p className="text-primary-100 text-sm"&gt;</span>
                Seamlessly migrate your bookmark collection
<span class="cstat-no" title="statement not covered" >              &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
          
          {/* Feature Highlights */}
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-sm text-primary-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;Multiple formats supported&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-sm text-primary-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;Intelligent parsing&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-sm text-primary-100"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;Secure processing&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
&nbsp;
      {/* Content Section */}
<span class="cstat-no" title="statement not covered" >      &lt;div className="p-8"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;FileUpload</span>
<span class="cstat-no" title="statement not covered" >          key={resetToken}</span>
<span class="cstat-no" title="statement not covered" >          onFileSelect={handleFileSelect}</span>
<span class="cstat-no" title="statement not covered" >          onFileUpload={onFileUpload}</span>
<span class="cstat-no" title="statement not covered" >          acceptedTypes={['.html', '.json', '.xml']}</span>
<span class="cstat-no" title="statement not covered" >          maxSizeInMB={10}</span>
<span class="cstat-no" title="statement not covered" >          multiple={false}</span>
<span class="cstat-no" title="statement not covered" >          className="mb-6"</span>
<span class="cstat-no" title="statement not covered" >          disabled={isLoading}</span>
<span class="cstat-no" title="statement not covered" >        /&gt;</span>
&nbsp;
        {/* Success State with Enhanced Design */}
<span class="cstat-no" title="statement not covered" >        {hasImportedFile &amp;&amp; !isLoading &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="mt-6 p-6 bg-gradient-to-r from-success-50 to-success-100 dark:from-success-900/20 dark:to-success-800/20 border border-success-200 dark:border-success-800 rounded-2xl"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center justify-between"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="p-2 bg-success-500 rounded-xl"&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24"&gt;</span>
<span class="cstat-no" title="statement not covered" >                    &lt;path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;h3 className="text-lg font-semibold text-success-900 dark:text-success-300"&gt;</span>
                    Import Successful! 🎉
<span class="cstat-no" title="statement not covered" >                  &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                  &lt;p className="text-sm text-success-700 dark:text-success-400"&gt;</span>
                    Your bookmarks have been successfully imported and are ready to use.
<span class="cstat-no" title="statement not covered" >                  &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;button</span>
<span class="cstat-no" title="statement not covered" >                onClick={onClearImport}</span>
<span class="cstat-no" title="statement not covered" >                className="</span>
                  px-4 py-2 bg-success-600 hover:bg-success-700 text-white
                  rounded-xl font-medium transition-all duration-200
                  hover:scale-105 hover:shadow-lg
                  focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2
                  flex items-center space-x-2
                "
              &gt;
<span class="cstat-no" title="statement not covered" >                &lt;ArrowPathIcon className="w-4 h-4" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;span&gt;Import Another&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/button&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
        )}
&nbsp;
        {/* Loading State with Enhanced Design */}
<span class="cstat-no" title="statement not covered" >        {isLoading &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="mt-6 p-6 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 border border-primary-200 dark:border-primary-800 rounded-2xl"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="relative"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;SpinnerIcon className="w-8 h-8 text-primary-600" /&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="absolute inset-0 rounded-full bg-primary-500/20 animate-ping" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;h3 className="text-lg font-semibold text-primary-900 dark:text-primary-300"&gt;</span>
                  Processing Your Bookmarks...
<span class="cstat-no" title="statement not covered" >                &lt;/h3&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;p className="text-sm text-primary-700 dark:text-primary-400"&gt;</span>
                  We're analyzing and importing your bookmark file. This may take a moment.
<span class="cstat-no" title="statement not covered" >                &lt;/p&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
            
            {/* Progress Indicator */}
<span class="cstat-no" title="statement not covered" >            &lt;div className="mt-4"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-full bg-primary-200 dark:bg-primary-800 rounded-full h-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >                &lt;div className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full animate-pulse w-3/5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
        )}
&nbsp;
        {/* Help Section */}
<span class="cstat-no" title="statement not covered" >        &lt;div className="mt-8 p-6 bg-surface-100 dark:bg-dark-surface-quaternary rounded-2xl border border-surface-200 dark:border-dark-border-secondary"&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;svg className="w-4 h-4 text-primary-500" fill="currentColor" viewBox="0 0 20 20"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/svg&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;span&gt;Supported File Formats&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/h4&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs"&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-2 h-2 bg-primary-500 rounded-full" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;&lt;strong&gt;HTML:&lt;/strong&gt; Browser exports&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-2 h-2 bg-primary-500 rounded-full" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;&lt;strong&gt;JSON:&lt;/strong&gt; Structured data&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300"&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;div className="w-2 h-2 bg-primary-500 rounded-full" /&gt;</span>
<span class="cstat-no" title="statement not covered" >              &lt;span&gt;&lt;strong&gt;XML:&lt;/strong&gt; OPML format&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/div&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default BookmarkImporter;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T06:55:52.394Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    