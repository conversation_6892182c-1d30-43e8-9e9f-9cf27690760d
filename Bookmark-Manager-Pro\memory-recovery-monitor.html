<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Recovery Monitor - Bookmark Manager Pro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1rem;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #3498db;
            transition: transform 0.3s ease;
        }

        .status-card:hover {
            transform: translateY(-5px);
        }

        .status-card.critical {
            border-left-color: #e74c3c;
        }

        .status-card.warning {
            border-left-color: #f39c12;
        }

        .status-card.safe {
            border-left-color: #27ae60;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .card-description {
            color: #7f8c8d;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #27ae60, #f39c12, #e74c3c);
            transition: width 0.5s ease;
            border-radius: 5px;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 30px 0;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        .log-container {
            background: #2c3e50;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            max-height: 300px;
            overflow-y: auto;
        }

        .log-entry {
            color: #ecf0f1;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.2);
        }

        .log-entry.warning {
            background: rgba(243, 156, 18, 0.2);
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.2);
        }

        .log-entry.success {
            background: rgba(39, 174, 96, 0.2);
        }

        .timestamp {
            color: #95a5a6;
            font-size: 0.8rem;
        }

        .emergency-banner {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: 600;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .hidden {
            display: none;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-indicator.safe {
            background: #27ae60;
        }

        .status-indicator.warning {
            background: #f39c12;
        }

        .status-indicator.critical {
            background: #e74c3c;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0.3; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ Memory Recovery Monitor</h1>
            <p class="subtitle">Real-time Memory Management & Recovery System</p>
        </div>

        <div id="emergencyBanner" class="emergency-banner hidden">
            🚨 CRITICAL MEMORY SITUATION DETECTED - EMERGENCY PROTOCOLS ACTIVE
        </div>

        <div class="status-grid">
            <div class="status-card" id="memoryCard">
                <div class="card-title">
                    <span class="status-indicator" id="memoryIndicator"></span>
                    Memory Usage
                </div>
                <div class="card-value" id="memoryUsage">---%</div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memoryProgress"></div>
                </div>
                <div class="card-description" id="memoryDescription">
                    Monitoring current memory consumption...
                </div>
            </div>

            <div class="status-card" id="cleanupCard">
                <div class="card-title">
                    <span class="status-indicator" id="cleanupIndicator"></span>
                    Cleanup Status
                </div>
                <div class="card-value" id="cleanupStatus">Standby</div>
                <div class="card-description" id="cleanupDescription">
                    Emergency cleanup system ready
                </div>
            </div>

            <div class="status-card" id="systemCard">
                <div class="card-title">
                    <span class="status-indicator" id="systemIndicator"></span>
                    System Health
                </div>
                <div class="card-value" id="systemHealth">Monitoring</div>
                <div class="card-description" id="systemDescription">
                    Overall system performance status
                </div>
            </div>

            <div class="status-card" id="preventiveCard">
                <div class="card-title">
                    <span class="status-indicator" id="preventiveIndicator"></span>
                    Preventive Monitor
                </div>
                <div class="card-value" id="preventiveStatus">Active</div>
                <div class="card-description" id="preventiveDescription">
                    Automatic monitoring and intervention system
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="checkMemoryRecovery()">
                📊 Check Memory Recovery
            </button>
            <button class="btn btn-warning" onclick="refreshBrowser()">
                🔄 Browser Refresh
            </button>
            <button class="btn btn-success" onclick="checkCleanupStatus()">
                ✅ Status Check
            </button>
            <button class="btn btn-danger" onclick="emergencyCleanup()">
                🚨 Emergency Cleanup
            </button>
        </div>

        <div class="log-container">
            <div id="logEntries">
                <div class="log-entry info">
                    <span class="timestamp">[${new Date().toLocaleTimeString()}]</span>
                    Memory Recovery Monitor initialized
                </div>
            </div>
        </div>
    </div>

    <script>
        class MemoryRecoveryMonitor {
            constructor() {
                this.isMonitoring = false;
                this.monitoringInterval = null;
                this.lastMemoryUsage = 0;
                this.cleanupHistory = [];
                this.init();
            }

            init() {
                this.log('Memory Recovery Monitor starting...', 'info');
                this.startMonitoring();
                this.checkInitialStatus();
            }

            startMonitoring() {
                if (this.isMonitoring) return;
                
                this.isMonitoring = true;
                this.monitoringInterval = setInterval(() => {
                    this.updateMemoryStats();
                    this.checkSystemHealth();
                }, 2000);
                
                this.log('Continuous monitoring started', 'success');
            }

            stopMonitoring() {
                if (!this.isMonitoring) return;
                
                this.isMonitoring = false;
                if (this.monitoringInterval) {
                    clearInterval(this.monitoringInterval);
                    this.monitoringInterval = null;
                }
                
                this.log('Monitoring stopped', 'warning');
            }

            updateMemoryStats() {
                try {
                    let memoryUsage = 0;
                    let memoryInfo = {};

                    // Try to get memory information
                    if (performance.memory) {
                        const used = performance.memory.usedJSHeapSize;
                        const total = performance.memory.totalJSHeapSize;
                        const limit = performance.memory.jsHeapSizeLimit;
                        
                        memoryUsage = ((used / limit) * 100).toFixed(1);
                        memoryInfo = { used, total, limit };
                    } else {
                        // Fallback estimation
                        memoryUsage = this.estimateMemoryUsage();
                    }

                    this.updateMemoryDisplay(memoryUsage, memoryInfo);
                    this.checkMemoryThresholds(memoryUsage);
                    
                } catch (error) {
                    this.log(`Error updating memory stats: ${error.message}`, 'error');
                }
            }

            estimateMemoryUsage() {
                // Estimate based on DOM elements and other factors
                const domElements = document.querySelectorAll('*').length;
                const estimatedUsage = Math.min(50 + (domElements / 100), 95);
                return estimatedUsage.toFixed(1);
            }

            updateMemoryDisplay(usage, info) {
                const usageElement = document.getElementById('memoryUsage');
                const progressElement = document.getElementById('memoryProgress');
                const indicatorElement = document.getElementById('memoryIndicator');
                const cardElement = document.getElementById('memoryCard');
                const descriptionElement = document.getElementById('memoryDescription');

                usageElement.textContent = `${usage}%`;
                progressElement.style.width = `${usage}%`;

                // Update status based on usage
                cardElement.className = 'status-card';
                indicatorElement.className = 'status-indicator';
                
                if (usage >= 85) {
                    cardElement.classList.add('critical');
                    indicatorElement.classList.add('critical');
                    descriptionElement.textContent = 'CRITICAL: Immediate action required';
                    this.showEmergencyBanner();
                } else if (usage >= 70) {
                    cardElement.classList.add('warning');
                    indicatorElement.classList.add('warning');
                    descriptionElement.textContent = 'WARNING: High memory usage detected';
                } else {
                    cardElement.classList.add('safe');
                    indicatorElement.classList.add('safe');
                    descriptionElement.textContent = 'Memory usage within safe limits';
                    this.hideEmergencyBanner();
                }

                // Log significant changes
                if (Math.abs(usage - this.lastMemoryUsage) > 5) {
                    this.log(`Memory usage: ${usage}% (${usage > this.lastMemoryUsage ? 'increased' : 'decreased'})`, 
                             usage >= 85 ? 'error' : usage >= 70 ? 'warning' : 'info');
                    this.lastMemoryUsage = usage;
                }
            }

            checkMemoryThresholds(usage) {
                if (usage >= 90) {
                    this.triggerEmergencyCleanup('Critical memory threshold exceeded');
                } else if (usage >= 80) {
                    this.triggerAggressiveCleanup('High memory threshold exceeded');
                }
            }

            showEmergencyBanner() {
                document.getElementById('emergencyBanner').classList.remove('hidden');
            }

            hideEmergencyBanner() {
                document.getElementById('emergencyBanner').classList.add('hidden');
            }

            checkSystemHealth() {
                const healthElement = document.getElementById('systemHealth');
                const indicatorElement = document.getElementById('systemIndicator');
                const cardElement = document.getElementById('systemCard');
                const descriptionElement = document.getElementById('systemDescription');

                // Check various system health indicators
                const isHealthy = this.assessSystemHealth();
                
                if (isHealthy) {
                    healthElement.textContent = 'Healthy';
                    indicatorElement.className = 'status-indicator safe';
                    cardElement.className = 'status-card safe';
                    descriptionElement.textContent = 'All systems operating normally';
                } else {
                    healthElement.textContent = 'Degraded';
                    indicatorElement.className = 'status-indicator warning';
                    cardElement.className = 'status-card warning';
                    descriptionElement.textContent = 'Performance issues detected';
                }
            }

            assessSystemHealth() {
                // Simple health assessment
                try {
                    const start = performance.now();
                    // Simulate a small operation
                    for (let i = 0; i < 1000; i++) {
                        Math.random();
                    }
                    const end = performance.now();
                    
                    // If operation takes too long, system might be under stress
                    return (end - start) < 10;
                } catch (error) {
                    return false;
                }
            }

            checkInitialStatus() {
                this.log('Checking initial system status...', 'info');
                
                // Check if silent emergency cleanup is available
                if (typeof window.silentEmergencyCleanup !== 'undefined') {
                    this.log('Silent emergency cleanup system detected', 'success');
                    this.updateCleanupStatus('Available');
                } else {
                    this.log('Silent emergency cleanup not available', 'warning');
                    this.updateCleanupStatus('Limited');
                }

                // Update preventive monitoring status
                this.updatePreventiveStatus('Active');
            }

            updateCleanupStatus(status) {
                const statusElement = document.getElementById('cleanupStatus');
                const indicatorElement = document.getElementById('cleanupIndicator');
                const cardElement = document.getElementById('cleanupCard');
                const descriptionElement = document.getElementById('cleanupDescription');

                statusElement.textContent = status;
                
                switch (status) {
                    case 'Available':
                        indicatorElement.className = 'status-indicator safe';
                        cardElement.className = 'status-card safe';
                        descriptionElement.textContent = 'Emergency cleanup ready and operational';
                        break;
                    case 'Active':
                        indicatorElement.className = 'status-indicator warning';
                        cardElement.className = 'status-card warning';
                        descriptionElement.textContent = 'Cleanup operation in progress';
                        break;
                    case 'Limited':
                        indicatorElement.className = 'status-indicator warning';
                        cardElement.className = 'status-card warning';
                        descriptionElement.textContent = 'Basic cleanup functions available';
                        break;
                    default:
                        indicatorElement.className = 'status-indicator';
                        cardElement.className = 'status-card';
                        descriptionElement.textContent = 'Cleanup system status unknown';
                }
            }

            updatePreventiveStatus(status) {
                const statusElement = document.getElementById('preventiveStatus');
                const indicatorElement = document.getElementById('preventiveIndicator');
                const cardElement = document.getElementById('preventiveCard');
                const descriptionElement = document.getElementById('preventiveDescription');

                statusElement.textContent = status;
                indicatorElement.className = 'status-indicator safe';
                cardElement.className = 'status-card safe';
                descriptionElement.textContent = 'Continuous monitoring and automatic intervention';
            }

            triggerEmergencyCleanup(reason) {
                this.log(`Emergency cleanup triggered: ${reason}`, 'error');
                this.updateCleanupStatus('Active');
                
                try {
                    if (typeof window.silentEmergencyCleanup !== 'undefined') {
                        window.silentEmergencyCleanup.execute();
                        this.log('Silent emergency cleanup executed', 'success');
                    } else {
                        this.performBasicCleanup();
                    }
                } catch (error) {
                    this.log(`Emergency cleanup failed: ${error.message}`, 'error');
                }
                
                setTimeout(() => {
                    this.updateCleanupStatus('Available');
                }, 5000);
            }

            triggerAggressiveCleanup(reason) {
                this.log(`Aggressive cleanup triggered: ${reason}`, 'warning');
                this.performBasicCleanup();
            }

            performBasicCleanup() {
                try {
                    // Basic cleanup operations
                    if (window.gc) {
                        window.gc();
                        this.log('Garbage collection forced', 'info');
                    }
                    
                    // Clear caches if available
                    if ('caches' in window) {
                        caches.keys().then(names => {
                            names.forEach(name => {
                                caches.delete(name);
                            });
                        });
                        this.log('Browser caches cleared', 'info');
                    }
                    
                    this.log('Basic cleanup completed', 'success');
                } catch (error) {
                    this.log(`Basic cleanup error: ${error.message}`, 'error');
                }
            }

            log(message, type = 'info') {
                const logContainer = document.getElementById('logEntries');
                const timestamp = new Date().toLocaleTimeString();
                
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
                
                logContainer.appendChild(logEntry);
                logContainer.scrollTop = logContainer.scrollHeight;
                
                // Keep only last 50 entries
                while (logContainer.children.length > 50) {
                    logContainer.removeChild(logContainer.firstChild);
                }
            }
        }

        // Global functions for button actions
        function checkMemoryRecovery() {
            monitor.log('Manual memory recovery check initiated', 'info');
            monitor.updateMemoryStats();
            
            // Additional recovery verification
            setTimeout(() => {
                const currentUsage = parseFloat(document.getElementById('memoryUsage').textContent);
                if (currentUsage < 70) {
                    monitor.log(`Memory recovery successful: ${currentUsage}%`, 'success');
                } else if (currentUsage < 85) {
                    monitor.log(`Memory partially recovered: ${currentUsage}%`, 'warning');
                } else {
                    monitor.log(`Memory still critical: ${currentUsage}%`, 'error');
                }
            }, 1000);
        }

        function refreshBrowser() {
            monitor.log('Browser refresh initiated', 'warning');
            
            if (confirm('This will refresh the page and reset memory state. Continue?')) {
                monitor.log('Browser refresh confirmed - reloading page', 'info');
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            } else {
                monitor.log('Browser refresh cancelled', 'info');
            }
        }

        function checkCleanupStatus() {
            monitor.log('Checking cleanup system status', 'info');
            
            try {
                if (typeof window.silentEmergencyCleanup !== 'undefined') {
                    const status = window.silentEmergencyCleanup.getStatus();
                    monitor.log(`Cleanup status: ${JSON.stringify(status)}`, 'success');
                    
                    // Update UI based on status
                    if (status.isActive) {
                        monitor.updateCleanupStatus('Active');
                    } else if (status.isAvailable) {
                        monitor.updateCleanupStatus('Available');
                    } else {
                        monitor.updateCleanupStatus('Limited');
                    }
                } else {
                    monitor.log('Silent emergency cleanup not available', 'warning');
                    monitor.log('Using basic cleanup functions only', 'info');
                    monitor.updateCleanupStatus('Limited');
                }
            } catch (error) {
                monitor.log(`Status check failed: ${error.message}`, 'error');
            }
        }

        function emergencyCleanup() {
            monitor.log('Manual emergency cleanup requested', 'warning');
            
            if (confirm('This will perform an emergency memory cleanup. Continue?')) {
                monitor.triggerEmergencyCleanup('Manual emergency cleanup requested');
            } else {
                monitor.log('Emergency cleanup cancelled', 'info');
            }
        }

        // Initialize the monitor
        let monitor;
        document.addEventListener('DOMContentLoaded', () => {
            monitor = new MemoryRecoveryMonitor();
        });

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                monitor.log('Page hidden - reducing monitoring frequency', 'info');
            } else {
                monitor.log('Page visible - resuming normal monitoring', 'info');
                monitor.updateMemoryStats();
            }
        });

        // Handle beforeunload
        window.addEventListener('beforeunload', () => {
            if (monitor) {
                monitor.stopMonitoring();
            }
        });
    </script>
</body>
</html>