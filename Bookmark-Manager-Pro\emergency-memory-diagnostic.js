/**
 * EMERGENCY MEMORY DIAGNOSTIC & CLEANUP
 * Addresses 63.2% memory growth issue
 */

class EmergencyMemoryDiagnostic {
  constructor() {
    this.startTime = Date.now()
    this.initialMemory = this.getMemoryStats()
  }

  getMemoryStats() {
    if (!('memory' in performance)) {
      return { error: 'Memory API not available' }
    }

    const memory = performance.memory
    return {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024), // MB
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024), // MB
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024), // MB
      usagePercentage: Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)
    }
  }

  async runDiagnostic() {
    console.log('🔍 EMERGENCY MEMORY DIAGNOSTIC STARTED')
    console.log('=' .repeat(50))
    
    const current = this.getMemoryStats()
    console.log('📊 Current Memory Status:')
    console.log(`   Used: ${current.used} MB`)
    console.log(`   Total: ${current.total} MB`)
    console.log(`   Limit: ${current.limit} MB`)
    console.log(`   Usage: ${current.usagePercentage}%`)
    
    if (current.usagePercentage >= 60) {
      console.log('⚠️  HIGH MEMORY USAGE DETECTED!')
      await this.performEmergencyCleanup()
    }
    
    // Check for potential memory leaks
    this.checkForLeaks()
    
    return current
  }

  async performEmergencyCleanup() {
    console.log('🧹 PERFORMING EMERGENCY CLEANUP...')
    
    try {
      // 1. Clear all timeouts and intervals
      console.log('   Clearing timeouts/intervals...')
      for (let i = 1; i < 99999; i++) {
        clearTimeout(i)
        clearInterval(i)
      }
      
      // 2. Force garbage collection if available
      if ('gc' in window && typeof window.gc === 'function') {
        console.log('   Running garbage collection...')
        for (let i = 0; i < 3; i++) {
          window.gc()
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      }
      
      // 3. Clear caches
      console.log('   Clearing caches...')
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(name => caches.delete(name)))
      }
      
      // 4. Clear localStorage items that might be large
      console.log('   Optimizing localStorage...')
      const keysToCheck = ['bookmarkData', 'searchHistory', 'tempData']
      keysToCheck.forEach(key => {
        const item = localStorage.getItem(key)
        if (item && item.length > 100000) { // > 100KB
          console.log(`   Clearing large localStorage item: ${key} (${Math.round(item.length/1024)}KB)`)
          localStorage.removeItem(key)
        }
      })
      
      // 5. Clear React DevTools data if present
      if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        console.log('   Clearing React DevTools data...')
        try {
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null
          window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null
        } catch (e) {
          // Ignore errors
        }
      }
      
      // 6. Force memory release patterns
      console.log('   Forcing memory release...')
      const temp = new Array(1000000).fill(null)
      temp.length = 0
      
      console.log('✅ Emergency cleanup completed!')
      
      // Check memory after cleanup
      await new Promise(resolve => setTimeout(resolve, 1000))
      const afterCleanup = this.getMemoryStats()
      console.log('📊 Memory After Cleanup:')
      console.log(`   Used: ${afterCleanup.used} MB (was ${this.initialMemory.used} MB)`)
      console.log(`   Usage: ${afterCleanup.usagePercentage}% (was ${this.initialMemory.usagePercentage}%)`)
      
      const reduction = this.initialMemory.usagePercentage - afterCleanup.usagePercentage
      if (reduction > 0) {
        console.log(`🎉 Memory usage reduced by ${reduction.toFixed(1)}%!`)
      }
      
    } catch (error) {
      console.error('❌ Error during emergency cleanup:', error)
    }
  }

  checkForLeaks() {
    console.log('🔍 Checking for potential memory leaks...')
    
    // Check for excessive DOM nodes
    const domNodes = document.querySelectorAll('*').length
    if (domNodes > 5000) {
      console.warn(`⚠️  High DOM node count: ${domNodes} (consider virtualization)`)
    }
    
    // Check for excessive event listeners
    const eventListenerCount = this.estimateEventListeners()
    if (eventListenerCount > 100) {
      console.warn(`⚠️  High event listener count: ~${eventListenerCount} (check for cleanup)`)
    }
    
    // Check for large objects in global scope
    this.checkGlobalObjects()
  }

  estimateEventListeners() {
    // Rough estimation based on common patterns
    const elements = document.querySelectorAll('[onclick], [onmouseover], [onmouseout]')
    return elements.length + (window.addEventListener ? 50 : 0) // Rough estimate
  }

  checkGlobalObjects() {
    const globalKeys = Object.keys(window)
    const suspiciousKeys = globalKeys.filter(key => {
      try {
        const value = window[key]
        return value && typeof value === 'object' && 
               JSON.stringify(value).length > 50000 // > 50KB
      } catch (e) {
        return false
      }
    })
    
    if (suspiciousKeys.length > 0) {
      console.warn('⚠️  Large global objects detected:', suspiciousKeys)
    }
  }

  // Quick access methods for console
  static async quickCleanup() {
    const diagnostic = new EmergencyMemoryDiagnostic()
    return await diagnostic.runDiagnostic()
  }

  static getQuickStats() {
    const diagnostic = new EmergencyMemoryDiagnostic()
    return diagnostic.getMemoryStats()
  }
}

// Auto-run if memory usage is high
if (typeof window !== 'undefined' && 'memory' in performance) {
  const currentUsage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100
  
  console.log(`🧠 Current memory usage: ${currentUsage.toFixed(1)}%`)
  
  if (currentUsage >= 60) {
    console.log('🚨 HIGH MEMORY USAGE DETECTED - Running emergency diagnostic...')
    EmergencyMemoryDiagnostic.quickCleanup()
  }
}

// Export for manual use
if (typeof window !== 'undefined') {
  window.EmergencyMemoryDiagnostic = EmergencyMemoryDiagnostic
  console.log('💡 Emergency Memory Diagnostic loaded!')
  console.log('   Usage: EmergencyMemoryDiagnostic.quickCleanup()')
  console.log('   Stats: EmergencyMemoryDiagnostic.getQuickStats()')
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = EmergencyMemoryDiagnostic
}