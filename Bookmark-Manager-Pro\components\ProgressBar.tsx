import React from 'react';

interface ProgressBarProps {
  progress: number; // 0-100
  message?: string;
  showPercentage?: boolean;
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  className?: string;
  animated?: boolean;
}

const sizeClasses = {
  sm: 'h-1',
  md: 'h-2',
  lg: 'h-3'
};

const colorClasses = {
  primary: 'bg-sky-500',
  secondary: 'bg-slate-400',
  success: 'bg-green-500',
  warning: 'bg-yellow-500',
  error: 'bg-red-500'
};

const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  message,
  showPercentage = true,
  size = 'md',
  color = 'primary',
  className = '',
  animated = true
}) => {
  // Ensure progress is between 0 and 100
  const normalizedProgress = Math.min(Math.max(progress, 0), 100);
  const sizeClass = sizeClasses[size];
  const colorClass = colorClasses[color];

  return (
    <div className={`w-full ${className}`} role="progressbar" aria-valuenow={normalizedProgress} aria-valuemin={0} aria-valuemax={100}>
      {(message || showPercentage) && (
        <div className="flex justify-between items-center mb-2">
          {message && (
            <span className="text-sm text-slate-300">
              {message}
            </span>
          )}
          {showPercentage && (
            <span className="text-sm text-slate-400 font-mono">
              {Math.round(normalizedProgress)}%
            </span>
          )}
        </div>
      )}
      
      <div className={`w-full bg-slate-700 rounded-full overflow-hidden ${sizeClass}`}>
        <div
          className={`${sizeClass} ${colorClass} rounded-full transition-all duration-300 ease-out ${
            animated ? 'animate-pulse' : ''
          }`}
          style={{ width: `${normalizedProgress}%` }}
        >
          {animated && (
            <div className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent animate-shimmer" />
          )}
        </div>
      </div>
      
      <span className="sr-only">
        {message ? `${message}: ` : ''}Progress: {Math.round(normalizedProgress)}%
      </span>
    </div>
  );
};

export default React.memo(ProgressBar);