{"version": "0.2.0", "configurations": [{"name": "Launch Chrome", "request": "launch", "type": "chrome", "url": "http://localhost:5173", "webRoot": "${workspaceFolder}", "sourceMaps": true, "sourceMapPathOverrides": {"webpack:///src/*": "${webRoot}/src/*", "webpack:///./*": "${webRoot}/*", "webpack:///./~/*": "${webRoot}/node_modules/*"}, "preLaunchTask": "Dev: Start Server"}, {"name": "Attach to Chrome", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}", "sourceMaps": true}, {"name": "Debug Tests", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vitest", "args": ["run", "--reporter=verbose"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug Current Test File", "type": "node", "request": "launch", "program": "${workspaceFolder}/node_modules/.bin/vitest", "args": ["run", "${relativeFile}"], "console": "integratedTerminal", "internalConsoleOptions": "neverOpen", "sourceMaps": true, "skipFiles": ["<node_internals>/**"]}]}