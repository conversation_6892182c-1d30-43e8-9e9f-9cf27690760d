/**
 * PLAYLIST FEATURE DEMO & TESTING UTILITY
 * Interactive demo for testing playlist functionality
 */

import { smartPlaylistService } from '../services/smartPlaylistService'
import type { Bookmark, Playlist } from '../../types'

// Demo data generator
export const generateDemoData = () => {
  const demoBookmarks: Bookmark[] = [
    {
      id: 'demo-1',
      title: 'React Documentation',
      url: 'https://react.dev',
      description: 'Official React documentation and guides',
      tags: ['react', 'documentation', 'frontend', 'javascript'],
      collection: 'Development',
      dateAdded: '2024-01-01T00:00:00Z',
      favicon: 'https://react.dev/favicon.ico',
      isPrivate: false,
      isFavorite: true,
      visits: 45
    },
    {
      id: 'demo-2',
      title: 'TypeScript Handbook',
      url: 'https://typescriptlang.org/docs',
      description: 'Complete TypeScript documentation',
      tags: ['typescript', 'documentation', 'programming', 'javascript'],
      collection: 'Development',
      dateAdded: '2024-01-02T00:00:00Z',
      favicon: 'https://typescriptlang.org/favicon.ico',
      isPrivate: false,
      isFavorite: true,
      visits: 32
    },
    {
      id: 'demo-3',
      title: 'Figma',
      url: 'https://figma.com',
      description: 'Collaborative design tool for UI/UX',
      tags: ['design', 'ui', 'ux', 'collaboration', 'prototyping'],
      collection: 'Design',
      dateAdded: '2024-01-03T00:00:00Z',
      favicon: 'https://figma.com/favicon.ico',
      isPrivate: false,
      isFavorite: false,
      visits: 18
    },
    {
      id: 'demo-4',
      title: 'CSS Tricks',
      url: 'https://css-tricks.com',
      description: 'Tips, tricks, and techniques on using CSS',
      tags: ['css', 'frontend', 'web-development', 'tutorials'],
      collection: 'Learning',
      dateAdded: '2024-01-04T00:00:00Z',
      favicon: 'https://css-tricks.com/favicon.ico',
      isPrivate: false,
      isFavorite: true,
      visits: 27
    },
    {
      id: 'demo-5',
      title: 'GitHub',
      url: 'https://github.com',
      description: 'Code hosting platform for version control',
      tags: ['git', 'version-control', 'development', 'collaboration'],
      collection: 'Tools',
      dateAdded: '2024-01-05T00:00:00Z',
      favicon: 'https://github.com/favicon.ico',
      isPrivate: false,
      isFavorite: true,
      visits: 89
    },
    {
      id: 'demo-6',
      title: 'Stack Overflow',
      url: 'https://stackoverflow.com',
      description: 'Q&A platform for programmers',
      tags: ['programming', 'qa', 'community', 'help'],
      collection: 'Learning',
      dateAdded: '2024-01-06T00:00:00Z',
      favicon: 'https://stackoverflow.com/favicon.ico',
      isPrivate: false,
      isFavorite: false,
      visits: 56
    }
  ]

  const demoPlaylists: Playlist[] = [
    {
      id: 'demo-playlist-1',
      name: 'Frontend Essentials',
      description: 'Essential resources for frontend development',
      color: '#3b82f6',
      bookmarkIds: ['demo-1', 'demo-4'],
      dateCreated: '2024-01-01T00:00:00Z'
    }
  ]

  return { demoBookmarks, demoPlaylists }
}

// Demo functions for testing
export const playlistDemo = {
  // Test smart suggestions
  async testSmartSuggestions() {
    console.log('🌟 Testing Smart Playlist Suggestions...')
    
    const { demoBookmarks, demoPlaylists } = generateDemoData()
    
    try {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        demoBookmarks,
        demoPlaylists,
        {
          maxSuggestions: 5,
          minConfidence: 0.6,
          includeTemporalAnalysis: true,
          includeBehavioralAnalysis: true,
          includeSemanticAnalysis: true,
          autoCreateThreshold: 0.8
        }
      )

      console.log(`✅ Generated ${suggestions.length} suggestions:`)
      suggestions.forEach((suggestion, index) => {
        console.log(`\n${index + 1}. ${suggestion.name}`)
        console.log(`   Description: ${suggestion.description}`)
        console.log(`   Confidence: ${(suggestion.confidence * 100).toFixed(1)}%`)
        console.log(`   Category: ${suggestion.category}`)
        console.log(`   Bookmarks: ${suggestion.bookmarkIds.length}`)
        console.log(`   Reasoning: ${suggestion.reasoning}`)
      })

      return suggestions
    } catch (error) {
      console.error('❌ Smart suggestions test failed:', error)
      return []
    }
  },

  // Test playlist analytics
  async testPlaylistAnalytics() {
    console.log('\n📊 Testing Playlist Analytics...')
    
    const { demoBookmarks, demoPlaylists } = generateDemoData()
    const playlist = demoPlaylists[0]
    
    try {
      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        demoBookmarks
      )

      console.log(`✅ Analytics for "${playlist.name}":`)
      console.log(`   Total Bookmarks: ${analytics.totalBookmarks}`)
      console.log(`   Average Age: ${analytics.averageAge.toFixed(1)} days`)
      console.log(`   Engagement Score: ${(analytics.engagementScore * 100).toFixed(1)}%`)
      console.log(`   Duplicates: ${analytics.duplicateCount}`)
      console.log(`   Broken Links: ${analytics.brokenLinksCount}`)
      
      console.log('\n   Content Types:')
      Object.entries(analytics.contentTypes).forEach(([type, count]) => {
        console.log(`     ${type}: ${count}`)
      })
      
      console.log('\n   Top Domains:')
      analytics.topDomains.slice(0, 3).forEach(domain => {
        console.log(`     ${domain.domain}: ${domain.count} bookmarks`)
      })
      
      console.log('\n   Recommendations:')
      analytics.recommendations.forEach(rec => {
        console.log(`     • ${rec}`)
      })

      return analytics
    } catch (error) {
      console.error('❌ Analytics test failed:', error)
      return null
    }
  },

  // Test auto-creation
  async testAutoCreation() {
    console.log('\n⚡ Testing Auto-Creation...')
    
    const { demoBookmarks, demoPlaylists } = generateDemoData()
    
    try {
      const autoPlaylists = await smartPlaylistService.autoCreatePlaylists(
        demoBookmarks,
        demoPlaylists,
        { autoCreateThreshold: 0.7 }
      )

      console.log(`✅ Auto-created ${autoPlaylists.length} playlists:`)
      autoPlaylists.forEach((playlist, index) => {
        console.log(`\n${index + 1}. ${playlist.name}`)
        console.log(`   Confidence: ${(playlist.confidence * 100).toFixed(1)}%`)
        console.log(`   Bookmarks: ${playlist.bookmarkIds.length}`)
        console.log(`   Description: ${playlist.description}`)
      })

      return autoPlaylists
    } catch (error) {
      console.error('❌ Auto-creation test failed:', error)
      return []
    }
  },

  // Run all tests
  async runAllTests() {
    console.log('🧪 PLAYLIST FEATURE DEMO - RUNNING ALL TESTS')
    console.log('===========================================')
    
    const results = {
      suggestions: await this.testSmartSuggestions(),
      analytics: await this.testPlaylistAnalytics(),
      autoCreation: await this.testAutoCreation()
    }

    console.log('\n📋 DEMO SUMMARY:')
    console.log(`✅ Smart Suggestions: ${results.suggestions.length} generated`)
    console.log(`✅ Analytics: ${results.analytics ? 'Generated successfully' : 'Failed'}`)
    console.log(`✅ Auto-Creation: ${results.autoCreation.length} playlists created`)
    
    return results
  },

  // Performance test
  async testPerformance() {
    console.log('\n⚡ Testing Performance...')
    
    // Generate larger dataset
    const largeBookmarks: Bookmark[] = Array.from({ length: 100 }, (_, i) => ({
      id: `perf-${i}`,
      title: `Bookmark ${i}`,
      url: `https://example${i % 10}.com/page-${i}`,
      description: `Description for bookmark ${i}`,
      tags: [`tag${i % 5}`, `category${i % 3}`],
      collection: `Collection ${i % 4}`,
      dateAdded: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
      favicon: `https://example${i % 10}.com/favicon.ico`,
      isPrivate: i % 10 === 0,
      isFavorite: i % 10 === 0,
      visits: Math.floor(Math.random() * 100)
    }))

    const startTime = performance.now()
    
    try {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        largeBookmarks,
        [],
        { maxSuggestions: 10 }
      )
      
      const endTime = performance.now()
      const processingTime = endTime - startTime
      
      console.log(`✅ Performance Test Results:`)
      console.log(`   Dataset Size: ${largeBookmarks.length} bookmarks`)
      console.log(`   Processing Time: ${processingTime.toFixed(2)}ms`)
      console.log(`   Suggestions Generated: ${suggestions.length}`)
      console.log(`   Performance: ${processingTime < 1000 ? '✅ Good' : '⚠️ Slow'}`)
      
      return { processingTime, suggestions: suggestions.length }
    } catch (error) {
      console.error('❌ Performance test failed:', error)
      return null
    }
  }
}

// Auto-run demo in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Add demo to window for manual testing
  (window as any).playlistDemo = playlistDemo
  
  console.log('🎯 Playlist Demo Available!')
  console.log('Run: playlistDemo.runAllTests() in console to test all features')
  console.log('Or run individual tests:')
  console.log('- playlistDemo.testSmartSuggestions()')
  console.log('- playlistDemo.testPlaylistAnalytics()')
  console.log('- playlistDemo.testAutoCreation()')
  console.log('- playlistDemo.testPerformance()')
}
