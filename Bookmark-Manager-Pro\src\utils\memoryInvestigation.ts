/**
 * Memory Investigation and Monitoring Utilities
 * Designed to track and prevent memory spikes like the 146% incident
 */

interface MemorySnapshot {
  timestamp: number
  usedMB: number
  limitMB: number
  usagePercentage: number
  operation?: string
  context?: string
}

interface MemoryInvestigationResult {
  snapshots: MemorySnapshot[]
  peakUsage: number
  averageUsage: number
  memoryGrowth: number
  suspiciousOperations: string[]
  recommendations: string[]
}

class MemoryInvestigator {
  private snapshots: MemorySnapshot[] = []
  private operationStack: string[] = []
  private readonly MAX_SNAPSHOTS = 100
  private readonly SUSPICIOUS_GROWTH_THRESHOLD = 20 // 20% growth
  private readonly CRITICAL_USAGE_THRESHOLD = 80 // 80% usage

  /**
   * Get current memory statistics
   */
  private getMemoryStats() {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usedMB = Math.round(memory.usedJSHeapSize / 1024 / 1024)
      const limitMB = Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      const usagePercentage = Math.round((usedMB / limitMB) * 100)
      return { usedMB, limitMB, usagePercentage }
    }
    return null
  }

  /**
   * Take a memory snapshot with context
   */
  takeSnapshot(operation?: string, context?: string): MemorySnapshot | null {
    const stats = this.getMemoryStats()
    if (!stats) return null

    const snapshot: MemorySnapshot = {
      timestamp: Date.now(),
      usedMB: stats.usedMB,
      limitMB: stats.limitMB,
      usagePercentage: stats.usagePercentage,
      operation,
      context
    }

    this.snapshots.push(snapshot)

    // Keep only recent snapshots
    if (this.snapshots.length > this.MAX_SNAPSHOTS) {
      this.snapshots = this.snapshots.slice(-this.MAX_SNAPSHOTS)
    }

    // Log critical usage
    if (stats.usagePercentage > this.CRITICAL_USAGE_THRESHOLD) {
      console.warn(`🚨 [Memory Investigation] Critical usage: ${stats.usagePercentage}% during ${operation || 'unknown operation'}`)
    }

    return snapshot
  }

  /**
   * Start monitoring an operation
   */
  startOperation(operationName: string, context?: string): () => MemorySnapshot | null {
    const startSnapshot = this.takeSnapshot(operationName, `${context} - START`)
    this.operationStack.push(operationName)

    console.log(`🔍 [Memory Investigation] Starting operation: ${operationName} - Memory: ${startSnapshot?.usagePercentage}%`)

    // Return end function
    return () => {
      const endSnapshot = this.takeSnapshot(operationName, `${context} - END`)
      this.operationStack.pop()

      if (startSnapshot && endSnapshot) {
        const memoryDelta = endSnapshot.usagePercentage - startSnapshot.usagePercentage
        const timeDelta = endSnapshot.timestamp - startSnapshot.timestamp

        console.log(`🔍 [Memory Investigation] Completed operation: ${operationName} - Memory: ${endSnapshot.usagePercentage}% (Δ${memoryDelta > 0 ? '+' : ''}${memoryDelta}%) in ${timeDelta}ms`)

        // Flag suspicious operations
        if (memoryDelta > this.SUSPICIOUS_GROWTH_THRESHOLD) {
          console.error(`🚨 [Memory Investigation] SUSPICIOUS: ${operationName} caused ${memoryDelta}% memory growth!`)
        }
      }

      return endSnapshot
    }
  }

  /**
   * Analyze memory usage patterns
   */
  analyzeMemoryPatterns(): MemoryInvestigationResult {
    if (this.snapshots.length < 2) {
      return {
        snapshots: this.snapshots,
        peakUsage: 0,
        averageUsage: 0,
        memoryGrowth: 0,
        suspiciousOperations: [],
        recommendations: ['Not enough data for analysis']
      }
    }

    const usageValues = this.snapshots.map(s => s.usagePercentage)
    const peakUsage = Math.max(...usageValues)
    const averageUsage = usageValues.reduce((sum, val) => sum + val, 0) / usageValues.length
    const memoryGrowth = usageValues[usageValues.length - 1] - usageValues[0]

    // Find suspicious operations
    const suspiciousOperations: string[] = []
    for (let i = 1; i < this.snapshots.length; i++) {
      const prev = this.snapshots[i - 1]
      const curr = this.snapshots[i]
      const growth = curr.usagePercentage - prev.usagePercentage

      if (growth > this.SUSPICIOUS_GROWTH_THRESHOLD && curr.operation) {
        suspiciousOperations.push(`${curr.operation}: +${growth}%`)
      }
    }

    // Generate recommendations
    const recommendations: string[] = []
    
    if (peakUsage > 90) {
      recommendations.push('Peak memory usage exceeded 90% - implement emergency cleanup')
    }
    
    if (memoryGrowth > 30) {
      recommendations.push('Significant memory growth detected - check for memory leaks')
    }
    
    if (suspiciousOperations.length > 0) {
      recommendations.push(`Optimize operations: ${suspiciousOperations.join(', ')}`)
    }
    
    if (averageUsage > 70) {
      recommendations.push('High average memory usage - consider reducing dataset size')
    }

    return {
      snapshots: this.snapshots,
      peakUsage,
      averageUsage,
      memoryGrowth,
      suspiciousOperations,
      recommendations
    }
  }

  /**
   * Generate detailed memory report
   */
  generateReport(): string {
    const analysis = this.analyzeMemoryPatterns()
    
    let report = `
🧠 MEMORY INVESTIGATION REPORT
===============================

📊 STATISTICS:
- Peak Usage: ${analysis.peakUsage.toFixed(1)}%
- Average Usage: ${analysis.averageUsage.toFixed(1)}%
- Memory Growth: ${analysis.memoryGrowth > 0 ? '+' : ''}${analysis.memoryGrowth.toFixed(1)}%
- Snapshots Taken: ${analysis.snapshots.length}

🚨 SUSPICIOUS OPERATIONS:
${analysis.suspiciousOperations.length > 0 
  ? analysis.suspiciousOperations.map(op => `- ${op}`).join('\n')
  : '- None detected'
}

💡 RECOMMENDATIONS:
${analysis.recommendations.map(rec => `- ${rec}`).join('\n')}

📈 RECENT SNAPSHOTS:
${analysis.snapshots.slice(-10).map(snapshot => 
  `${new Date(snapshot.timestamp).toLocaleTimeString()}: ${snapshot.usagePercentage}% ${snapshot.operation ? `(${snapshot.operation})` : ''}`
).join('\n')}
`

    return report
  }

  /**
   * Clear investigation data
   */
  clearData(): void {
    this.snapshots = []
    this.operationStack = []
    console.log('🧠 [Memory Investigation] Data cleared')
  }

  /**
   * Export data for external analysis
   */
  exportData(): MemoryInvestigationResult {
    return this.analyzeMemoryPatterns()
  }
}

// Global memory investigator instance
export const memoryInvestigator = new MemoryInvestigator()

/**
 * Convenience function to monitor an operation
 */
export const monitorOperation = <T>(
  operationName: string,
  operation: () => T,
  context?: string
): T => {
  const endMonitoring = memoryInvestigator.startOperation(operationName, context)
  
  try {
    const result = operation()
    endMonitoring()
    return result
  } catch (error) {
    endMonitoring()
    throw error
  }
}

/**
 * Convenience function to monitor async operations
 */
export const monitorAsyncOperation = async <T>(
  operationName: string,
  operation: () => Promise<T>,
  context?: string
): Promise<T> => {
  const endMonitoring = memoryInvestigator.startOperation(operationName, context)
  
  try {
    const result = await operation()
    endMonitoring()
    return result
  } catch (error) {
    endMonitoring()
    throw error
  }
}

/**
 * Quick memory check with automatic logging
 */
export const quickMemoryCheck = (context: string): number | null => {
  const snapshot = memoryInvestigator.takeSnapshot('Quick Check', context)
  return snapshot ? snapshot.usagePercentage : null
}
