/**
 * CANDIDATE GENERATOR SERVICE
 * Generates recommendation candidates from multiple sources
 */

import type {
  CandidateGenerator,
  Candidate,
  UserProfile,
  TemporalContext,
  RecommendationContext,
  Bookmark
} from './interfaces'

export class ModernCandidateGenerator implements CandidateGenerator {
  async generateContentBased(bookmarks: Bookmark[]): Promise<Candidate[]> {
    const candidates: Candidate[] = []
    
    // Group bookmarks by similar topics/tags
    const topicGroups = this.groupBookmarksByTopics(bookmarks)
    
    topicGroups.forEach((group, index) => {
      if (group.bookmarks.length >= 3) {
        candidates.push({
          id: `content_${index}`,
          type: 'playlist',
          name: `${group.topic} Collection`,
          description: `Bookmarks related to ${group.topic}`,
          bookmarkIds: group.bookmarks.map(b => b.id),
          confidence: Math.min(0.9, group.bookmarks.length / 10),
          source: 'content',
          features: {
            contentFeatures: [group.bookmarks.length, group.coherence],
            collaborativeFeatures: [],
            temporalFeatures: [],
            contextualFeatures: []
          }
        })
      }
    })
    
    return candidates
  }

  async generateCollaborative(userProfile: UserProfile): Promise<Candidate[]> {
    const candidates: Candidate[] = []
    
    // Generate candidates based on user clusters and similar users
    userProfile.clusters.forEach((cluster, index) => {
      candidates.push({
        id: `collaborative_${index}`,
        type: 'collection',
        name: `Recommended for ${cluster}`,
        description: `Based on users with similar interests`,
        bookmarkIds: this.generateBookmarkIdsForCluster(cluster, userProfile),
        confidence: 0.7,
        source: 'collaborative',
        features: {
          contentFeatures: [],
          collaborativeFeatures: [userProfile.interactions.length / 100],
          temporalFeatures: [],
          contextualFeatures: []
        }
      })
    })
    
    return candidates
  }

  async generateTemporal(timeContext: TemporalContext): Promise<Candidate[]> {
    const candidates: Candidate[] = []
    
    // Generate candidates based on temporal patterns
    timeContext.patterns.forEach((pattern, index) => {
      if (pattern.bookmarkIds.length >= 2) {
        candidates.push({
          id: `temporal_${index}`,
          type: 'tag-group',
          name: `${pattern.type} Trending`,
          description: `Popular during ${pattern.peak}`,
          bookmarkIds: pattern.bookmarkIds,
          confidence: pattern.confidence,
          source: 'temporal',
          features: {
            contentFeatures: [],
            collaborativeFeatures: [],
            temporalFeatures: [pattern.confidence, this.getTimeScore(pattern.type)],
            contextualFeatures: []
          }
        })
      }
    })
    
    return candidates
  }

  async generateHybrid(context: RecommendationContext): Promise<Candidate[]> {
    // Combine all generation methods
    const [contentCandidates, collaborativeCandidates, temporalCandidates] = await Promise.all([
      this.generateContentBased(context.bookmarks),
      this.generateCollaborative(context.userProfile),
      this.generateTemporal({
        timeRange: context.timeRange || { start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), end: new Date() },
        patterns: [],
        seasonality: { trends: [], cycles: [], anomalies: [] }
      })
    ])
    
    return [...contentCandidates, ...collaborativeCandidates, ...temporalCandidates]
  }

  private groupBookmarksByTopics(bookmarks: Bookmark[]): Array<{ topic: string; bookmarks: Bookmark[]; coherence: number }> {
    const topicGroups = new Map<string, Bookmark[]>()
    
    bookmarks.forEach(bookmark => {
      // Extract topics from tags and title
      const topics = this.extractTopics(bookmark)
      
      topics.forEach(topic => {
        if (!topicGroups.has(topic)) {
          topicGroups.set(topic, [])
        }
        topicGroups.get(topic)!.push(bookmark)
      })
    })
    
    return Array.from(topicGroups.entries()).map(([topic, bookmarks]) => ({
      topic,
      bookmarks,
      coherence: this.calculateTopicCoherence(bookmarks)
    }))
  }

  private extractTopics(bookmark: Bookmark): string[] {
    const topics: string[] = []
    
    // Extract from tags
    if (bookmark.tags) {
      topics.push(...bookmark.tags)
    }
    
    // Extract from title (simplified)
    const titleWords = bookmark.title.toLowerCase().split(' ')
    const techKeywords = ['javascript', 'react', 'typescript', 'css', 'html', 'node', 'api', 'design', 'tutorial']
    
    titleWords.forEach(word => {
      if (techKeywords.includes(word)) {
        topics.push(word)
      }
    })
    
    return Array.from(new Set(topics)) // Remove duplicates
  }

  private calculateTopicCoherence(bookmarks: Bookmark[]): number {
    // Simple coherence calculation based on shared tags
    const allTags = bookmarks.flatMap(b => b.tags || [])
    const uniqueTags = new Set(allTags)
    
    return allTags.length > 0 ? (allTags.length - uniqueTags.size) / allTags.length : 0
  }

  private generateBookmarkIdsForCluster(cluster: string, userProfile: UserProfile): string[] {
    // Generate bookmark IDs based on user interactions and cluster
    return userProfile.interactions
      .filter(interaction => interaction.action === 'save' || interaction.action === 'like')
      .slice(0, 5)
      .map(interaction => interaction.bookmarkId)
  }

  private getTimeScore(patternType: string): number {
    const scores: Record<string, number> = {
      'daily': 0.8,
      'weekly': 0.6,
      'monthly': 0.4,
      'seasonal': 0.3
    }
    return scores[patternType] || 0.5
  }
}