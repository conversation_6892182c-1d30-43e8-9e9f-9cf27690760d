describe('Performance Layout Testing - Layout Performance Issues', () => {
  const performanceThresholds = {
    layoutShift: 0.1, // CLS threshold
    renderTime: 2000, // Maximum render time in ms
    scrollPerformance: 16, // Target 60fps (16ms per frame)
    resizeTime: 500 // Maximum time for resize operations
  };

  beforeEach(() => {
    cy.visit('/');
    cy.loadTestBookmarks();
  });

  describe('Layout Shift Performance', () => {
    it('should have minimal layout shift during initial load', () => {
      cy.visit('/');
      
      cy.measureLayoutShift(() => {
        cy.loadTestBookmarks();
        cy.reload();
        cy.wait(2000); // Allow content to fully load
      });
    });

    it('should not cause layout shift when adding bookmarks dynamically', () => {
      cy.measureLayoutShift(() => {
        // Add bookmarks dynamically
        cy.window().then(win => {
          const newBookmarks = Array.from({ length: 5 }, (_, i) => ({
            id: `dynamic-${i}`,
            title: `🆕 Dynamic Bookmark ${i + 1}`,
            url: `https://dynamic${i}.com`,
            addDate: Date.now(),
            tags: ['dynamic']
          }));
          
          const existing = JSON.parse(win.localStorage.getItem('bookmarks') || '[]');
          existing.push(...newBookmarks);
          win.localStorage.setItem('bookmarks', JSON.stringify(existing));
          
          // Trigger re-render
          win.dispatchEvent(new Event('storage'));
        });
        
        cy.wait(1000);
      });
    });

    it('should maintain layout stability during search operations', () => {
      cy.get('body').then($body => {
        if ($body.find('[data-testid="search-input"], input[type="search"]').length > 0) {
          cy.measureLayoutShift(() => {
            cy.get('[data-testid="search-input"], input[type="search"]')
              .type('test', { delay: 100 });
            
            cy.wait(500);
            
            cy.get('[data-testid="search-input"], input[type="search"]')
              .clear()
              .type('bookmark', { delay: 100 });
            
            cy.wait(500);
          });
        }
      });
    });

    it('should handle viewport changes without excessive layout shift', () => {
      const viewports = [
        [1920, 1080],
        [1280, 720],
        [768, 1024],
        [375, 667],
        [1280, 720] // Back to original
      ];

      cy.measureLayoutShift(() => {
        viewports.forEach(([width, height]) => {
          cy.viewport(width, height);
          cy.wait(300); // Allow layout to settle
        });
      });
    });
  });

  describe('Scroll Performance Testing', () => {
    it('should maintain smooth scrolling with many bookmarks', () => {
      // Load many bookmarks for scroll testing
      const manyBookmarks = Array.from({ length: 200 }, (_, i) => ({
        id: `scroll-test-${i}`,
        title: `📜 Scroll Test Bookmark ${i + 1}`,
        url: `https://scroll-test${i}.com`,
        addDate: Date.now() - i * 1000,
        tags: [`category${i % 10}`, 'scroll-test']
      }));

      cy.loadTestBookmarks(manyBookmarks);
      cy.reload();
      cy.wait(2000);

      // Measure scroll performance
      cy.window().then(win => {
        let frameCount = 0;
        let startTime = performance.now();
        
        const measureFrameRate = () => {
          frameCount++;
          if (frameCount < 60) { // Measure for ~1 second at 60fps
            requestAnimationFrame(measureFrameRate);
          } else {
            const endTime = performance.now();
            const actualFPS = frameCount / ((endTime - startTime) / 1000);
            cy.log(`Scroll FPS: ${actualFPS.toFixed(2)}`);
            expect(actualFPS, 'Scroll should maintain reasonable frame rate').to.be.at.least(30);
          }
        };
        
        requestAnimationFrame(measureFrameRate);
        
        // Perform scroll operations
        cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
          .scrollTo('bottom', { duration: 2000 });
      });
    });

    it('should handle rapid scroll operations efficiently', () => {
      const manyBookmarks = Array.from({ length: 100 }, (_, i) => ({
        id: `rapid-scroll-${i}`,
        title: `⚡ Rapid Scroll Test ${i + 1}`,
        url: `https://rapid${i}.com`,
        addDate: Date.now(),
        tags: ['rapid-test']
      }));

      cy.loadTestBookmarks(manyBookmarks);
      cy.reload();
      cy.wait(1000);

      // Perform rapid scroll operations
      const startTime = Date.now();
      
      cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
        .scrollTo('bottom', { duration: 500 })
        .scrollTo('top', { duration: 500 })
        .scrollTo('center', { duration: 500 })
        .scrollTo('bottom', { duration: 500 })
        .then(() => {
          const endTime = Date.now();
          const totalTime = endTime - startTime;
          cy.log(`Rapid scroll operations completed in: ${totalTime}ms`);
          expect(totalTime, 'Rapid scroll should complete quickly').to.be.at.most(3000);
        });
    });

    it('should maintain performance during infinite scroll simulation', () => {
      // Simulate infinite scroll by progressively loading content
      cy.window().then(win => {
        let loadedCount = 0;
        const batchSize = 20;
        const maxBatches = 10;
        
        const loadBatch = () => {
          if (loadedCount >= maxBatches) return;
          
          const newBookmarks = Array.from({ length: batchSize }, (_, i) => ({
            id: `infinite-${loadedCount}-${i}`,
            title: `♾️ Infinite Scroll Item ${loadedCount * batchSize + i + 1}`,
            url: `https://infinite${loadedCount}-${i}.com`,
            addDate: Date.now(),
            tags: ['infinite']
          }));
          
          const existing = JSON.parse(win.localStorage.getItem('bookmarks') || '[]');
          existing.push(...newBookmarks);
          win.localStorage.setItem('bookmarks', JSON.stringify(existing));
          win.dispatchEvent(new Event('storage'));
          
          loadedCount++;
          
          // Simulate scroll to bottom triggering next load
          setTimeout(() => {
            cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
              .scrollTo('bottom', { duration: 200 });
            
            if (loadedCount < maxBatches) {
              setTimeout(loadBatch, 300);
            }
          }, 200);
        };
        
        loadBatch();
      });
      
      // Wait for all batches to load
      cy.wait(8000);
      
      // Verify performance hasn't degraded
      cy.get('[data-testid="bookmark-item"], .bookmark-item')
        .should('have.length.greaterThan', 100);
      
      // Test final scroll performance
      const scrollStartTime = Date.now();
      cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
        .scrollTo('top', { duration: 1000 })
        .then(() => {
          const scrollEndTime = Date.now();
          const scrollTime = scrollEndTime - scrollStartTime;
          expect(scrollTime, 'Scroll should remain performant with many items').to.be.at.most(2000);
        });
    });
  });

  describe('Resize Performance Testing', () => {
    it('should handle window resize operations efficiently', () => {
      const resizeSizes = [
        [1920, 1080],
        [1280, 720],
        [768, 1024],
        [375, 667],
        [1024, 768],
        [1440, 900]
      ];

      resizeSizes.forEach(([width, height]) => {
        const resizeStartTime = Date.now();
        
        cy.viewport(width, height);
        
        cy.then(() => {
          const resizeEndTime = Date.now();
          const resizeTime = resizeEndTime - resizeStartTime;
          cy.log(`Resize to ${width}x${height} took: ${resizeTime}ms`);
          expect(resizeTime, 'Resize should be quick').to.be.at.most(performanceThresholds.resizeTime);
        });
        
        // Verify layout is stable after resize
        cy.wait(200);
        cy.checkElementOverflow('body');
      });
    });

    it('should maintain performance during rapid resize operations', () => {
      const rapidResizes = [
        [1200, 800],
        [800, 600],
        [1000, 700],
        [600, 800],
        [1400, 900],
        [900, 600]
      ];

      const startTime = Date.now();
      
      rapidResizes.forEach(([width, height], index) => {
        cy.viewport(width, height);
        if (index < rapidResizes.length - 1) {
          cy.wait(100); // Minimal wait between resizes
        }
      });
      
      cy.then(() => {
        const endTime = Date.now();
        const totalTime = endTime - startTime;
        cy.log(`Rapid resize operations completed in: ${totalTime}ms`);
        expect(totalTime, 'Rapid resizes should complete quickly').to.be.at.most(2000);
      });
      
      // Final stability check
      cy.wait(500);
      cy.checkElementOverflow('body');
    });
  });

  describe('Memory and Resource Performance', () => {
    it('should not cause memory leaks during dynamic operations', () => {
      cy.window().then(win => {
        const initialMemory = (performance as any).memory?.usedJSHeapSize || 0;
        
        // Perform memory-intensive operations
        for (let i = 0; i < 10; i++) {
          const bookmarks = Array.from({ length: 50 }, (_, j) => ({
            id: `memory-test-${i}-${j}`,
            title: `🧠 Memory Test ${i}-${j}`,
            url: `https://memory${i}-${j}.com`,
            addDate: Date.now(),
            tags: ['memory-test']
          }));
          
          win.localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
          win.dispatchEvent(new Event('storage'));
        }
        
        cy.wait(2000);
        
        // Force garbage collection if available
        if ((win as any).gc) {
          (win as any).gc();
        }
        
        cy.then(() => {
          const finalMemory = (performance as any).memory?.usedJSHeapSize || 0;
          const memoryIncrease = finalMemory - initialMemory;
          
          cy.log(`Memory increase: ${(memoryIncrease / 1024 / 1024).toFixed(2)}MB`);
          
          // Memory increase should be reasonable (less than 50MB)
          expect(memoryIncrease, 'Memory usage should not increase excessively').to.be.at.most(50 * 1024 * 1024);
        });
      });
    });

    it('should handle large datasets efficiently', () => {
      const largeDataset = Array.from({ length: 1000 }, (_, i) => ({
        id: `large-dataset-${i}`,
        title: `📊 Large Dataset Item ${i + 1}`,
        url: `https://large-dataset${i}.com`,
        addDate: Date.now() - i * 1000,
        tags: [`category${i % 20}`, 'large-dataset'],
        description: `This is a detailed description for item ${i + 1} in the large dataset test. It contains multiple sentences to simulate real-world bookmark descriptions that users might have.`
      }));

      const loadStartTime = Date.now();
      
      cy.loadTestBookmarks(largeDataset);
      cy.reload();
      
      cy.get('[data-testid="bookmark-item"], .bookmark-item')
        .should('have.length.greaterThan', 100)
        .then(() => {
          const loadEndTime = Date.now();
          const loadTime = loadEndTime - loadStartTime;
          
          cy.log(`Large dataset loaded in: ${loadTime}ms`);
          expect(loadTime, 'Large dataset should load within reasonable time').to.be.at.most(5000);
        });
      
      // Test interaction performance with large dataset
      cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
        .scrollTo('bottom', { duration: 2000 })
        .scrollTo('top', { duration: 2000 });
      
      // Test search performance if available
      cy.get('body').then($body => {
        if ($body.find('[data-testid="search-input"], input[type="search"]').length > 0) {
          const searchStartTime = Date.now();
          
          cy.get('[data-testid="search-input"], input[type="search"]')
            .type('dataset', { delay: 50 })
            .then(() => {
              const searchEndTime = Date.now();
              const searchTime = searchEndTime - searchStartTime;
              
              cy.log(`Search completed in: ${searchTime}ms`);
              expect(searchTime, 'Search should be responsive').to.be.at.most(1000);
            });
        }
      });
    });
  });

  describe('Animation and Transition Performance', () => {
    it('should maintain smooth animations during interactions', () => {
      // Test hover animations if present
      cy.get('[data-testid="bookmark-item"], .bookmark-item')
        .first()
        .trigger('mouseover')
        .wait(300) // Allow animation to complete
        .trigger('mouseout')
        .wait(300);
      
      // Test focus animations
      cy.get('button, a, input').each($el => {
        cy.wrap($el)
          .focus()
          .wait(200)
          .blur()
          .wait(200);
      });
    });

    it('should handle modal animations efficiently', () => {
      cy.get('body').then($body => {
        if ($body.find('[data-testid="add-bookmark"], .add-bookmark').length > 0) {
          const modalStartTime = Date.now();
          
          cy.get('[data-testid="add-bookmark"], .add-bookmark')
            .click();
          
          cy.get('[data-testid="modal"], .modal, [role="dialog"]')
            .should('be.visible')
            .then(() => {
              const modalEndTime = Date.now();
              const modalTime = modalEndTime - modalStartTime;
              
              cy.log(`Modal opened in: ${modalTime}ms`);
              expect(modalTime, 'Modal should open quickly').to.be.at.most(1000);
            });
          
          const closeStartTime = Date.now();
          
          cy.get('[data-testid="close-modal"], .close, [aria-label="Close"]')
            .click();
          
          cy.get('[data-testid="modal"], .modal, [role="dialog"]')
            .should('not.be.visible')
            .then(() => {
              const closeEndTime = Date.now();
              const closeTime = closeEndTime - closeStartTime;
              
              cy.log(`Modal closed in: ${closeTime}ms`);
              expect(closeTime, 'Modal should close quickly').to.be.at.most(1000);
            });
        }
      });
    });
  });
});