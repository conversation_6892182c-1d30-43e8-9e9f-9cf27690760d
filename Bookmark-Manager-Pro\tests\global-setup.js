/**
 * Global Setup for Playwright Tests
 * Ensures the development server is running before tests
 */

import { chromium } from '@playwright/test';

async function globalSetup() {
  console.log('🚀 Starting global test setup...');
  
  try {
    // Launch browser to verify server is accessible
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    // Check if development server is running
    try {
      await page.goto('http://localhost:5173', { timeout: 10000 });
      console.log('✅ Development server is accessible');
    } catch (error) {
      console.log('⚠️ Development server may not be running');
      console.log('Please ensure "npm run dev" is running in another terminal');
    }
    
    await browser.close();
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error.message);
    // Don't fail the setup, just log the warning
  }
}

export default globalSetup;