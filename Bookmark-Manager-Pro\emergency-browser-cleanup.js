
// EMERGENCY BROWSER CLEANUP FOR LOCAL DEVELOPMENT
console.log('🚨 LOCAL DEV EMERGENCY CLEANUP STARTING...');

// Clear Vite HMR data
if (window.__vite_plugin_react_preamble_installed__) {
  delete window.__vite_plugin_react_preamble_installed__;
}

// Clear React DevTools
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null;
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null;
}

// Aggressive memory cleanup
for (let i = 0; i < 10; i++) {
  if (typeof gc !== 'undefined') gc();
  const temp = new Array(5000000).fill(null);
  temp.length = 0;
}

// Clear all storage
localStorage.clear();
sessionStorage.clear();

// Clear performance data
if (window.performance) {
  if (window.performance.clearMeasures) window.performance.clearMeasures();
  if (window.performance.clearMarks) window.performance.clearMarks();
  if (window.performance.clearResourceTimings) window.performance.clearResourceTimings();
}

// Clear console
console.clear();

console.log('✅ LOCAL DEV BROWSER CLEANUP COMPLETE');

// Check memory after cleanup
setTimeout(() => {
  if (performance.memory) {
    const usage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
    console.log(`📊 Memory usage after cleanup: ${usage.toFixed(1)}%`);
    
    if (usage < 60) {
      console.log('✅ Memory cleanup successful!');
    } else if (usage < 75) {
      console.log('⚠️ Memory improved but still high. Consider restarting dev server.');
    } else {
      console.log('🚨 Memory still critical. Restart browser and dev server immediately.');
    }
  }
}, 2000);
