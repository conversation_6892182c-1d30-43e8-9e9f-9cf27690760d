// Global Vibe Testing Setup
// Prepares environment for emotional response and micro-UX testing

import { chromium } from '@playwright/test';
import { PerformanceMonitor } from '../utils/performance-monitor.js';
import { VibeTestData } from '../utils/vibe-test-data.js';

async function globalSetup() {
  console.log('🧪 <PERSON><PERSON> Elena\'s Vibe Testing Setup - Initializing...');
  
  // Launch browser for setup
  const browser = await chromium.launch();
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Navigate to application
    await page.goto(process.env.BASE_URL || 'http://localhost:5173');
    
    // Setup test data for vibe testing
    console.log('📊 Creating vibe test data...');
    await VibeTestData.createEmotionalJourneyData(page);
    await VibeTestData.createBulkOperationTestData(page);
    await VibeTestData.createSuggestionTestData(page);
    
    // Initialize performance monitoring
    console.log('⚡ Setting up performance monitoring...');
    await PerformanceMonitor.initialize();
    
    // Setup user behavior simulation data
    await setupUserBehaviorPatterns(page);
    
    // Verify favorites system is ready
    await verifyFavoritesSystemReady(page);
    
    console.log('✅ Vibe testing environment ready!');
    
  } catch (error) {
    console.error('❌ Vibe setup failed:', error);
    throw error;
  } finally {
    await context.close();
    await browser.close();
  }
}

async function setupUserBehaviorPatterns(page) {
  // Create realistic user behavior patterns for testing
  const patterns = {
    powerUser: {
      bookmarksCount: 500,
      favoritesCount: 50,
      dailyUsage: 'high',
      preferredActions: ['bulk-operations', 'search', 'organization']
    },
    casualUser: {
      bookmarksCount: 50,
      favoritesCount: 5,
      dailyUsage: 'low',
      preferredActions: ['simple-starring', 'browsing']
    },
    researcher: {
      bookmarksCount: 200,
      favoritesCount: 30,
      dailyUsage: 'medium',
      preferredActions: ['rapid-starring', 'categorization', 'search']
    }
  };
  
  // Store patterns for test access
  await page.evaluate((patterns) => {
    window.vibeTestPatterns = patterns;
  }, patterns);
}

async function verifyFavoritesSystemReady(page) {
  // Verify core favorites functionality is available
  const checks = [
    () => page.locator('[data-testid="bookmark-star"]').first().isVisible(),
    () => page.locator('[data-testid="favorites-view"]').isVisible(),
    () => page.locator('[data-testid="bulk-operations"]').isVisible()
  ];
  
  for (const check of checks) {
    const isReady = await check().catch(() => false);
    if (!isReady) {
      throw new Error('Favorites system not ready for vibe testing');
    }
  }
}

export default globalSetup;