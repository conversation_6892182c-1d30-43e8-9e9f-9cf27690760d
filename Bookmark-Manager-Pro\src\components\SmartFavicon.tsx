import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getFaviconUrl, handleFaviconError } from '../services/faviconService';

interface SmartFaviconProps {
  url: string;
  title: string;
  favicon?: string;
  className?: string;
  priority?: 'high' | 'low';
  lazy?: boolean;
}

export const SmartFavicon: React.FC<SmartFaviconProps> = React.memo(({
  url,
  title,
  favicon,
  className = 'favicon-image',
  priority = 'low',
  lazy = true
}) => {
  const [imageError, setImageError] = useState(false);
  const [isVisible, setIsVisible] = useState(!lazy);
  const [faviconUrl, setFaviconUrl] = useState<string | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const stableUrlRef = useRef<string>(url); // Prevent unnecessary re-renders
  const loadedFaviconRef = useRef<string | null>(null); // Cache loaded favicon

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isVisible) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observerRef.current?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy, isVisible]);

  // Get favicon URL when visible (optimized to prevent flashing during updates)
  useEffect(() => {
    if (!isVisible || imageError) return;

    // Use cached favicon if available and URL hasn't changed
    if (stableUrlRef.current === url && loadedFaviconRef.current && isLoaded) {
      if (faviconUrl !== loadedFaviconRef.current) {
        setFaviconUrl(loadedFaviconRef.current);
      }
      return;
    }

    // Only reload if URL actually changed (prevent flashing during summary updates)
    if (stableUrlRef.current === url && faviconUrl) return;

    stableUrlRef.current = url;

    if (!faviconUrl) {
      const url_favicon = getFaviconUrl(url, favicon, priority);
      setFaviconUrl(url_favicon);
    }
  }, [isVisible, url, favicon, priority, faviconUrl, imageError, isLoaded]);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setIsLoaded(false);
    loadedFaviconRef.current = null;
    handleFaviconError(url);
  }, [url]);

  const handleImageLoad = useCallback(() => {
    // Track successful load for performance optimization
    setIsLoaded(true);
    if (faviconUrl) {
      loadedFaviconRef.current = faviconUrl;
      try {
        // Import the success handler dynamically
        import('../services/faviconService').then(({ handleFaviconSuccess }) => {
          handleFaviconSuccess(url, faviconUrl);
        }).catch(() => {
          // Silently fail if service is not available
        });
      } catch (error) {
        // Silently fail if service is not available
      }
    }
  }, [url, faviconUrl]);

  // Fallback letter icon
  const fallbackLetter = title.charAt(0).toUpperCase();

  // If not visible yet (lazy loading), show placeholder
  if (!isVisible) {
    return (
      <div
        ref={imgRef}
        className="favicon-fallback"
        style={{
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#e5e7eb',
          borderRadius: '4px',
          fontSize: '14px',
          fontWeight: '500',
          color: '#6b7280'
        }}
      >
        {fallbackLetter}
      </div>
    );
  }

  // Show favicon if available and no error
  if (faviconUrl && !imageError) {
    return (
      <img
        ref={imgRef}
        src={faviconUrl}
        alt={`${title} favicon`}
        className={className}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
        decoding="async"
        style={{
          width: '32px',
          height: '32px',
          objectFit: 'contain'
        }}
      />
    );
  }

  // Fallback to letter icon
  return (
    <div
      ref={imgRef}
      className="favicon-fallback"
      style={{
        width: '32px',
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        borderRadius: '4px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        border: '1px solid #e5e7eb'
      }}
    >
      {fallbackLetter}
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison to prevent re-renders during summary updates
  const shouldSkipRender = (
    prevProps.url === nextProps.url &&
    prevProps.title === nextProps.title &&
    prevProps.favicon === nextProps.favicon &&
    prevProps.className === nextProps.className &&
    prevProps.priority === nextProps.priority &&
    prevProps.lazy === nextProps.lazy
  );

  // Debug logging for favicon re-renders (only in development)
  if (process.env.NODE_ENV === 'development' && !shouldSkipRender) {
    console.log('🔄 SmartFavicon re-render triggered:', {
      urlChanged: prevProps.url !== nextProps.url,
      titleChanged: prevProps.title !== nextProps.title,
      faviconChanged: prevProps.favicon !== nextProps.favicon,
      classNameChanged: prevProps.className !== nextProps.className,
      priorityChanged: prevProps.priority !== nextProps.priority,
      lazyChanged: prevProps.lazy !== nextProps.lazy
    });
  }

  return shouldSkipRender;
});

export default SmartFavicon;
