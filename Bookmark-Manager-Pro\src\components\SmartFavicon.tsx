import React, { useCallback, useEffect, useRef, useState } from 'react';
import { getFaviconUrl, handleFaviconError } from '../services/faviconService';

// Global favicon cache to prevent multiple requests for the same favicon
const globalFaviconCache = new Map<string, string>();
const faviconLoadingPromises = new Map<string, Promise<string>>();

// Helper function to get cached favicon or load it
const getCachedFavicon = async (url: string, favicon?: string, priority: 'high' | 'low' = 'low'): Promise<string> => {
  const faviconUrl = getFaviconUrl(url, favicon, priority);

  // Return cached favicon if available
  if (globalFaviconCache.has(faviconUrl)) {
    return globalFaviconCache.get(faviconUrl)!;
  }

  // Return existing loading promise if already loading
  if (faviconLoadingPromises.has(faviconUrl)) {
    return faviconLoadingPromises.get(faviconUrl)!;
  }

  // Create new loading promise
  const loadingPromise = new Promise<string>((resolve) => {
    const img = new Image();
    img.onload = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('✅ Favicon loaded successfully:', faviconUrl);
      }
      globalFaviconCache.set(faviconUrl, faviconUrl);
      faviconLoadingPromises.delete(faviconUrl);
      resolve(faviconUrl);
    };
    img.onerror = () => {
      if (process.env.NODE_ENV === 'development') {
        console.log('❌ Favicon failed to load:', faviconUrl);
      }
      globalFaviconCache.set(faviconUrl, faviconUrl); // Cache even failed loads
      faviconLoadingPromises.delete(faviconUrl);
      resolve(faviconUrl);
    };
    img.src = faviconUrl;
  });

  faviconLoadingPromises.set(faviconUrl, loadingPromise);
  return loadingPromise;
};

interface SmartFaviconProps {
  url: string;
  title: string;
  favicon?: string;
  className?: string;
  priority?: 'high' | 'low';
  lazy?: boolean;
}

const SmartFaviconComponent: React.FC<SmartFaviconProps> = ({
  url,
  title,
  favicon,
  className = 'favicon-image',
  priority = 'low',
  lazy = true
}) => {
  const [imageError, setImageError] = useState(false);
  const [isVisible, setIsVisible] = useState(!lazy);
  const [isLoaded, setIsLoaded] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);
  const stableUrlRef = useRef<string>(url); // Prevent unnecessary re-renders
  const loadedFaviconRef = useRef<string | null>(null); // Cache loaded favicon

  // Initialize favicon URL immediately to prevent flashing
  const [faviconUrl, setFaviconUrl] = useState<string | null>(() => {
    if (!lazy) {
      // For non-lazy components, get favicon URL immediately
      const immediateUrl = getFaviconUrl(url, favicon, priority);
      // Check if it's already cached
      if (globalFaviconCache.has(immediateUrl)) {
        return immediateUrl;
      }
      // Start loading immediately for non-lazy components
      getCachedFavicon(url, favicon, priority).then(cachedUrl => {
        setFaviconUrl(cachedUrl);
      });
      return immediateUrl; // Return URL immediately to prevent flashing
    }
    return null;
  });

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (!lazy || isVisible) return;

    observerRef.current = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setIsVisible(true);
            observerRef.current?.disconnect();
          }
        });
      },
      {
        rootMargin: '50px', // Start loading 50px before entering viewport
        threshold: 0.1
      }
    );

    if (imgRef.current) {
      observerRef.current.observe(imgRef.current);
    }

    return () => {
      observerRef.current?.disconnect();
    };
  }, [lazy, isVisible]);

  // Optimized favicon loading with global cache
  useEffect(() => {
    if (!isVisible || imageError) return;

    const currentFaviconUrl = getFaviconUrl(url, favicon, priority);

    // Skip if URL hasn't changed
    if (stableUrlRef.current === url && loadedFaviconRef.current === currentFaviconUrl) {
      return;
    }

    stableUrlRef.current = url;
    loadedFaviconRef.current = currentFaviconUrl;

    // Use global cache for consistent loading
    getCachedFavicon(url, favicon, priority).then(cachedUrl => {
      // Only update if this is still the current URL (prevent race conditions)
      if (stableUrlRef.current === url) {
        setFaviconUrl(cachedUrl);
        setIsLoaded(true);
      }
    }).catch(() => {
      // Fallback on error
      if (stableUrlRef.current === url) {
        setFaviconUrl(currentFaviconUrl);
        setIsLoaded(true);
      }
    });
  }, [isVisible, url, favicon, priority]);

  const handleImageError = useCallback(() => {
    setImageError(true);
    setIsLoaded(false);
    loadedFaviconRef.current = null;
    handleFaviconError(url);
  }, [url]);

  const handleImageLoad = useCallback(() => {
    // Track successful load for performance optimization
    setIsLoaded(true);
    if (faviconUrl) {
      loadedFaviconRef.current = faviconUrl;
      try {
        // Import the success handler dynamically
        import('../services/faviconService').then(({ handleFaviconSuccess }) => {
          handleFaviconSuccess(url, faviconUrl);
        }).catch(() => {
          // Silently fail if service is not available
        });
      } catch (error) {
        // Silently fail if service is not available
      }
    }
  }, [url, faviconUrl]);

  // Fallback letter icon
  const fallbackLetter = title.charAt(0).toUpperCase();

  // If not visible yet (lazy loading), show placeholder
  if (!isVisible) {
    return (
      <div
        ref={imgRef}
        className="favicon-fallback"
        style={{
          width: '32px',
          height: '32px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: '#e5e7eb',
          borderRadius: '4px',
          fontSize: '14px',
          fontWeight: '500',
          color: '#6b7280'
        }}
      >
        {fallbackLetter}
      </div>
    );
  }

  // Show favicon if available and no error
  if (faviconUrl && !imageError) {
    return (
      <img
        ref={imgRef}
        src={faviconUrl}
        alt={`${title} favicon`}
        className={className}
        onError={handleImageError}
        onLoad={handleImageLoad}
        loading="lazy"
        decoding="async"
        style={{
          width: '32px',
          height: '32px',
          objectFit: 'contain'
        }}
      />
    );
  }

  // Fallback to letter icon
  return (
    <div
      ref={imgRef}
      className="favicon-fallback"
      style={{
        width: '32px',
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        borderRadius: '4px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        border: '1px solid #e5e7eb'
      }}
    >
      {fallbackLetter}
    </div>
  );
};

// Memoized component with custom comparison
export const SmartFavicon = React.memo(SmartFaviconComponent, (prevProps, nextProps) => {
  // Custom comparison to prevent re-renders during summary updates
  return (
    prevProps.url === nextProps.url &&
    prevProps.title === nextProps.title &&
    prevProps.favicon === nextProps.favicon &&
    prevProps.className === nextProps.className &&
    prevProps.priority === nextProps.priority &&
    prevProps.lazy === nextProps.lazy
  );
});

export default SmartFavicon;
