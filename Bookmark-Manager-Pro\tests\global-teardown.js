/**
 * Global Teardown for Playwright Tests
 * Cleanup after all tests are completed
 */

async function globalTeardown() {
  console.log('🧹 Starting global test teardown...');
  
  try {
    // Perform any necessary cleanup
    console.log('✅ Global teardown completed successfully');
  } catch (error) {
    console.error('❌ Global teardown failed:', error.message);
  }
}

export default globalTeardown;