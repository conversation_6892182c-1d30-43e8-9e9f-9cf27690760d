// Component Showcase
// Bookmark Manager Pro - Modern UI Framework Demo

import * as React from "react"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter, CardMedia, CardActions } from "@/components/ui/Card"
import { Button } from "@/components/ui/Button"
import { Input, SearchInput, Textarea } from "@/components/ui/Input"
import { Mail, Search, Heart, Share2, Download, Play, Pause, Volume2, Settings, User, Lock, Eye, Star, Bookmark, Calendar, Clock, MapPin, Tag, Filter, Grid, List, Plus, Minus, X, Check, AlertCircle, Info, ChevronRight, ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

/**
 * Component Showcase for demonstrating the modern UI framework
 * 
 * Features:
 * - All component variants and sizes
 * - Interactive examples
 * - Dark mode support
 * - Accessibility demonstrations
 * - Real-world usage patterns
 */
export function ComponentShowcase() {
  const [darkMode, setDarkMode] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState('')
  const [isPlaying, setIsPlaying] = React.useState(false)
  const [isLiked, setIsLiked] = React.useState(false)
  const [isBookmarked, setIsBookmarked] = React.useState(false)

  // Toggle dark mode
  const toggleDarkMode = () => {
    setDarkMode(!darkMode)
    document.documentElement.classList.toggle('dark')
  }

  return (
    <div className={cn("min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300", darkMode && "dark")}>
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Modern UI Framework Showcase
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              Bookmark Manager Pro - Enhanced Design System
            </p>
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={toggleDarkMode}
            leftIcon={darkMode ? <Eye className="w-4 h-4" /> : <Settings className="w-4 h-4" />}
          >
            {darkMode ? 'Light Mode' : 'Dark Mode'}
          </Button>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8 space-y-12">
        {/* Button Showcase */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Button Components
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Button Variants */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Button Variants</CardTitle>
                <CardDescription>
                  Different button styles for various use cases
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button variant="default">Default</Button>
                  <Button variant="primary">Primary</Button>
                  <Button variant="secondary">Secondary</Button>
                  <Button variant="destructive">Destructive</Button>
                  <Button variant="outline">Outline</Button>
                  <Button variant="ghost">Ghost</Button>
                  <Button variant="link">Link</Button>
                </div>
              </CardContent>
            </Card>

            {/* Button Sizes */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Button Sizes</CardTitle>
                <CardDescription>
                  Various button sizes for different contexts
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap items-center gap-3">
                  <Button size="sm">Small</Button>
                  <Button size="md">Medium</Button>
                  <Button size="lg">Large</Button>
                  <Button size="xl">Extra Large</Button>
                  <Button size="icon" variant="outline">
                    <Settings className="w-4 h-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Interactive Buttons */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Interactive Examples</CardTitle>
                <CardDescription>
                  Buttons with icons, loading states, and interactions
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button
                    variant="primary"
                    leftIcon={<Download className="w-4 h-4" />}
                  >
                    Download
                  </Button>
                  <Button
                    variant="outline"
                    rightIcon={<ExternalLink className="w-4 h-4" />}
                  >
                    Open Link
                  </Button>
                  <Button
                    variant={isPlaying ? "destructive" : "primary"}
                    leftIcon={isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                    onClick={() => setIsPlaying(!isPlaying)}
                  >
                    {isPlaying ? 'Pause' : 'Play'}
                  </Button>
                  <Button
                    variant={isLiked ? "destructive" : "outline"}
                    leftIcon={<Heart className={cn("w-4 h-4", isLiked && "fill-current")} />}
                    onClick={() => setIsLiked(!isLiked)}
                  >
                    {isLiked ? 'Liked' : 'Like'}
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Loading States */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Loading States</CardTitle>
                <CardDescription>
                  Buttons with loading indicators
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex flex-wrap gap-3">
                  <Button loading>Loading...</Button>
                  <Button variant="primary" loading leftIcon={<Download className="w-4 h-4" />}>
                    Downloading
                  </Button>
                  <Button variant="outline" loading size="sm">
                    Processing
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Input Showcase */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Input Components
          </h2>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Input Variants */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Input Variants</CardTitle>
                <CardDescription>
                  Different input styles and states
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Input
                  label="Default Input"
                  placeholder="Enter text..."
                  helperText="This is a helper text"
                />
                <Input
                  variant="outlined"
                  label="Outlined Input"
                  placeholder="Enter text..."
                />
                <Input
                  variant="filled"
                  label="Filled Input"
                  placeholder="Enter text..."
                />
                <Input
                  variant="ghost"
                  label="Ghost Input"
                  placeholder="Enter text..."
                />
              </CardContent>
            </Card>

            {/* Input States */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Input States</CardTitle>
                <CardDescription>
                  Error, success, and validation states
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Input
                  label="Error State"
                  placeholder="Enter email..."
                  error="Please enter a valid email address"
                  leftIcon={<Mail className="w-4 h-4" />}
                />
                <Input
                  label="Success State"
                  placeholder="Enter email..."
                  success="Email is valid!"
                  leftIcon={<Check className="w-4 h-4" />}
                />
                <Input
                  label="Disabled State"
                  placeholder="Disabled input"
                  disabled
                  leftIcon={<Lock className="w-4 h-4" />}
                />
              </CardContent>
            </Card>

            {/* Input with Icons */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Input with Icons</CardTitle>
                <CardDescription>
                  Inputs with left and right icons
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Input
                  label="Email"
                  type="email"
                  placeholder="Enter your email"
                  leftIcon={<Mail className="w-4 h-4" />}
                />
                <Input
                  label="Password"
                  type="password"
                  placeholder="Enter your password"
                  leftIcon={<Lock className="w-4 h-4" />}
                />
                <SearchInput
                  label="Search"
                  placeholder="Search bookmarks..."
                  onSearch={(value) => setSearchValue(value)}
                  clearable
                />
                <Input
                  label="Clearable Input"
                  placeholder="Type something..."
                  clearable
                  rightIcon={<User className="w-4 h-4" />}
                />
              </CardContent>
            </Card>

            {/* Textarea */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Textarea Component</CardTitle>
                <CardDescription>
                  Multi-line text input with various options
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <Textarea
                  label="Description"
                  placeholder="Enter a description..."
                  helperText="Maximum 500 characters"
                  rows={4}
                />
                <Textarea
                  label="Notes"
                  placeholder="Add your notes..."
                  variant="filled"
                  resize="vertical"
                  rows={3}
                />
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Card Showcase */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Card Components
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {/* Default Card */}
            <Card variant="default">
              <CardHeader>
                <CardTitle>Default Card</CardTitle>
                <CardDescription>
                  A simple card with basic styling
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  This is the default card variant with subtle shadow and clean design.
                </p>
              </CardContent>
              <CardFooter>
                <Button size="sm" variant="outline">Learn More</Button>
              </CardFooter>
            </Card>

            {/* Elevated Card */}
            <Card variant="elevated">
              <CardHeader>
                <CardTitle>Elevated Card</CardTitle>
                <CardDescription>
                  Card with prominent shadow and hover effects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  This card has elevated styling with enhanced shadows.
                </p>
              </CardContent>
              <CardActions>
                <Button size="sm" variant="primary">Action</Button>
                <Button size="sm" variant="ghost">Cancel</Button>
              </CardActions>
            </Card>

            {/* Interactive Card */}
            <Card 
              variant="interactive"
              onClick={() => alert('Card clicked!')}
            >
              <CardHeader>
                <CardTitle>Interactive Card</CardTitle>
                <CardDescription>
                  Clickable card with hover effects
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Click this card to see the interaction!
                </p>
              </CardContent>
              <CardFooter>
                <div className="flex items-center text-sm text-blue-600 dark:text-blue-400">
                  Click me <ChevronRight className="w-4 h-4 ml-1" />
                </div>
              </CardFooter>
            </Card>

            {/* Media Card */}
            <Card variant="outlined">
              <CardMedia aspectRatio="video">
                <div className="w-full h-full bg-gradient-to-br from-blue-400 to-purple-600 flex items-center justify-center">
                  <Play className="w-12 h-12 text-white" />
                </div>
              </CardMedia>
              <CardHeader>
                <CardTitle>Media Card</CardTitle>
                <CardDescription>
                  Card with media content area
                </CardDescription>
              </CardHeader>
              <CardActions justify="between">
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<Heart className="w-4 h-4" />}
                  >
                    24
                  </Button>
                  <Button
                    size="sm"
                    variant="ghost"
                    leftIcon={<Share2 className="w-4 h-4" />}
                  >
                    Share
                  </Button>
                </div>
                <Button
                  size="sm"
                  variant={isBookmarked ? "primary" : "outline"}
                  leftIcon={<Bookmark className={cn("w-4 h-4", isBookmarked && "fill-current")} />}
                  onClick={() => setIsBookmarked(!isBookmarked)}
                >
                  {isBookmarked ? 'Saved' : 'Save'}
                </Button>
              </CardActions>
            </Card>

            {/* Gradient Card */}
            <Card variant="gradient">
              <CardHeader>
                <CardTitle>Gradient Card</CardTitle>
                <CardDescription>
                  Modern card with gradient background
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    <Star className="w-8 h-8 text-yellow-500 fill-current" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">Premium Feature</p>
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      Unlock advanced functionality
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter>
                <Button size="sm" variant="primary" fullWidth>
                  Upgrade Now
                </Button>
              </CardFooter>
            </Card>

            {/* Filled Card */}
            <Card variant="filled">
              <CardHeader>
                <CardTitle>Statistics</CardTitle>
                <CardDescription>
                  Your bookmark analytics
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">1,234</div>
                    <div className="text-xs text-gray-500">Bookmarks</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600 dark:text-green-400">56</div>
                    <div className="text-xs text-gray-500">Collections</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* Real-world Example */}
        <section>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Real-world Example: Multimedia Manager
          </h2>
          
          <Card variant="elevated" size="lg">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Multimedia Collection</CardTitle>
                  <CardDescription>
                    Manage your media files with modern UI components
                  </CardDescription>
                </div>
                <div className="flex items-center space-x-2">
                  <Button size="sm" variant="outline" leftIcon={<Filter className="w-4 h-4" />}>
                    Filter
                  </Button>
                  <Button size="sm" variant="outline" leftIcon={<Grid className="w-4 h-4" />}>
                    Grid
                  </Button>
                  <Button size="sm" variant="primary" leftIcon={<Plus className="w-4 h-4" />}>
                    Add Media
                  </Button>
                </div>
              </div>
            </CardHeader>
            
            <CardContent>
              <div className="space-y-6">
                {/* Search and Filters */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <SearchInput
                      placeholder="Search media files..."
                      onSearch={(value) => console.log('Searching:', value)}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" leftIcon={<Calendar className="w-4 h-4" />}>
                      Date
                    </Button>
                    <Button variant="outline" size="sm" leftIcon={<Tag className="w-4 h-4" />}>
                      Tags
                    </Button>
                  </div>
                </div>

                {/* Media Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map((item) => (
                    <Card key={item} variant="interactive" size="sm">
                      <CardMedia aspectRatio="video">
                        <div className="w-full h-full bg-gradient-to-br from-purple-400 to-pink-600 flex items-center justify-center">
                          <Volume2 className="w-8 h-8 text-white" />
                        </div>
                      </CardMedia>
                      <CardContent>
                        <h4 className="font-medium text-sm mb-1">Media File {item}</h4>
                        <div className="flex items-center text-xs text-gray-500 space-x-2">
                          <Clock className="w-3 h-3" />
                          <span>2:34</span>
                          <MapPin className="w-3 h-3" />
                          <span>1.2 MB</span>
                        </div>
                      </CardContent>
                      <CardActions justify="between">
                        <Button size="sm" variant="ghost" leftIcon={<Play className="w-3 h-3" />}>
                          Play
                        </Button>
                        <div className="flex space-x-1">
                          <Button size="sm" variant="ghost">
                            <Heart className="w-3 h-3" />
                          </Button>
                          <Button size="sm" variant="ghost">
                            <Share2 className="w-3 h-3" />
                          </Button>
                        </div>
                      </CardActions>
                    </Card>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* Footer */}
        <footer className="text-center py-8">
          <p className="text-gray-600 dark:text-gray-400">
            Modern UI Framework for Bookmark Manager Pro
          </p>
          <p className="text-sm text-gray-500 dark:text-gray-500 mt-2">
            Built with React, TypeScript, Tailwind CSS, and class-variance-authority
          </p>
        </footer>
      </div>
    </div>
  )
}

export default ComponentShowcase