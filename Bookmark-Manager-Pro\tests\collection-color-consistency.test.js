import { test, expect } from '@playwright/test'

test.describe('Collection Color Consistency Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForSelector('.bookmark-grid-container')
  })

  test('should have matching border and indicator colors', async ({ page }) => {
    // Get all bookmark cards
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const cardCount = Math.min(await bookmarkCards.count(), 10)
      
      for (let i = 0; i < cardCount; i++) {
        const card = bookmarkCards.nth(i)
        
        // Get border color
        const borderColor = await card.evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        
        // Get collection dot color (if present)
        const collectionDot = card.locator('.collection-dot-indicator')
        
        if (await collectionDot.count() > 0) {
          const dotColor = await collectionDot.evaluate(el => 
            window.getComputedStyle(el).backgroundColor
          )
          
          // Convert colors to comparable format and verify they match
          console.log(`Card ${i}: Border: ${borderColor}, Dot: ${dotColor}`)
          
          // Both should be valid CSS colors
          expect(borderColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
          expect(dotColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
        }
      }
    }
  })

  test('should support large number of unique colors', async ({ page }) => {
    // This test would need a large dataset to be meaningful
    // For now, verify the structure supports it
    
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    const cardCount = await bookmarkCards.count()
    
    console.log(`Testing with ${cardCount} bookmark cards`)
    
    if (cardCount > 0) {
      const uniqueColors = new Set()
      const maxCardsToTest = Math.min(cardCount, 50)
      
      for (let i = 0; i < maxCardsToTest; i++) {
        const card = bookmarkCards.nth(i)
        const borderColor = await card.evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        uniqueColors.add(borderColor)
      }
      
      console.log(`Found ${uniqueColors.size} unique colors in ${maxCardsToTest} cards`)
      
      // Should have good color diversity
      expect(uniqueColors.size).toBeGreaterThan(1)
    }
  })

  test('should maintain color consistency across page reloads', async ({ page }) => {
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      // Get colors before reload
      const initialColors = []
      const cardCount = Math.min(await bookmarkCards.count(), 5)
      
      for (let i = 0; i < cardCount; i++) {
        const borderColor = await bookmarkCards.nth(i).evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        initialColors.push(borderColor)
      }
      
      // Reload page
      await page.reload()
      await page.waitForSelector('.bookmark-grid-container')
      
      // Get colors after reload
      const reloadedCards = page.locator('.bookmark-card-front-redesigned')
      
      if (await reloadedCards.count() >= cardCount) {
        for (let i = 0; i < cardCount; i++) {
          const borderColor = await reloadedCards.nth(i).evaluate(el => 
            window.getComputedStyle(el).borderColor
          )
          
          // Colors should be consistent after reload
          expect(borderColor).toBe(initialColors[i])
        }
      }
      
      console.log('Color consistency verified across reload')
    }
  })

  test('should show different colors for different collections', async ({ page }) => {
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 1) {
      const collectionColors = new Map()
      const cardCount = Math.min(await bookmarkCards.count(), 20)
      
      for (let i = 0; i < cardCount; i++) {
        const card = bookmarkCards.nth(i)
        
        // Get collection name
        const collectionElement = card.locator('.collection-redesigned')
        
        if (await collectionElement.count() > 0) {
          const collectionText = await collectionElement.textContent()
          const collectionName = collectionText?.trim()
          
          if (collectionName) {
            const borderColor = await card.evaluate(el => 
              window.getComputedStyle(el).borderColor
            )
            
            if (collectionColors.has(collectionName)) {
              // Same collection should have same color
              expect(borderColor).toBe(collectionColors.get(collectionName))
            } else {
              collectionColors.set(collectionName, borderColor)
            }
          }
        }
      }
      
      console.log(`Verified ${collectionColors.size} unique collection colors`)
      
      // Should have at least some color variety
      if (collectionColors.size > 1) {
        const uniqueColors = new Set(collectionColors.values())
        expect(uniqueColors.size).toBeGreaterThan(1)
      }
    }
  })

  test('should handle theme switching with consistent colors', async ({ page }) => {
    // Look for theme toggle
    const themeToggle = page.locator('.quick-modern-theme-switch')
    
    if (await themeToggle.count() > 0) {
      const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
      
      if (await bookmarkCards.count() > 0) {
        // Get initial colors
        const initialBorderColor = await bookmarkCards.first().evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        
        // Switch theme
        await themeToggle.click()
        await page.waitForTimeout(500)
        
        // Get colors after theme switch
        const newBorderColor = await bookmarkCards.first().evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        
        // Border colors should remain consistent (same collection = same color)
        // The styling might change but the color should be the same
        console.log(`Theme switch: ${initialBorderColor} -> ${newBorderColor}`)
        
        // Both should be valid colors
        expect(initialBorderColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
        expect(newBorderColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
      }
    }
  })

  test('should have proper color contrast for accessibility', async ({ page }) => {
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const cardCount = Math.min(await bookmarkCards.count(), 10)
      
      for (let i = 0; i < cardCount; i++) {
        const card = bookmarkCards.nth(i)
        
        // Get border and background colors
        const borderColor = await card.evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        const backgroundColor = await card.evaluate(el => 
          window.getComputedStyle(el).backgroundColor
        )
        
        // Verify colors are not the same (should have contrast)
        expect(borderColor).not.toBe(backgroundColor)
        
        // Both should be valid CSS colors
        expect(borderColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
        expect(backgroundColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)|rgba\(\d+,\s*\d+,\s*\d+,\s*[\d.]+\)/)
      }
    }
  })

  test('should handle collection dots visibility correctly', async ({ page }) => {
    // Check if we're in modern theme
    const isModern = await page.locator('html').evaluate(el => 
      el.classList.contains('theme-modern')
    )
    
    const collectionDots = page.locator('.collection-dot-indicator')
    const smallDots = page.locator('.collection-dot-small')
    
    if (isModern) {
      // Modern theme should show collection dots
      if (await collectionDots.count() > 0) {
        await expect(collectionDots.first()).toBeVisible()
        
        // Verify dot has background color
        const dotColor = await collectionDots.first().evaluate(el => 
          window.getComputedStyle(el).backgroundColor
        )
        expect(dotColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
      }
    } else {
      // Classic theme should not show collection dots
      expect(await collectionDots.count()).toBe(0)
    }
    
    console.log(`Theme: ${isModern ? 'Modern' : 'Classic'}, Dots: ${await collectionDots.count()}`)
  })

  test('should generate colors for large collections', async ({ page }) => {
    // This test verifies the system can handle many collections
    // In a real scenario with 200+ collections
    
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    const cardCount = await bookmarkCards.count()
    
    console.log(`Testing color generation with ${cardCount} cards`)
    
    // Verify all cards have valid border colors
    if (cardCount > 0) {
      const testCount = Math.min(cardCount, 100)
      
      for (let i = 0; i < testCount; i++) {
        const card = bookmarkCards.nth(i)
        const borderColor = await card.evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        
        // Should be a valid CSS color
        expect(borderColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
        
        // Should not be transparent or default
        expect(borderColor).not.toBe('rgba(0, 0, 0, 0)')
        expect(borderColor).not.toBe('transparent')
      }
      
      console.log(`Verified ${testCount} cards have valid border colors`)
    }
  })
})
