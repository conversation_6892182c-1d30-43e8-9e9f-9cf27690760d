// Vibe Test Data Generator
// Creates realistic test data for emotional response testing

export class VibeTestData {
  
  static async createEmotionalJourneyData(page) {
    // Create bookmarks that represent a user's learning journey
    const journeyBookmarks = [
      // Week 1: Beginner excitement
      {
        title: "JavaScript Basics - Your First Steps",
        url: "https://example.com/js-basics",
        category: "learning",
        emotionalValue: "high", // First learning resource
        accessPattern: "frequent"
      },
      {
        title: "HTML Tutorial for Complete Beginners",
        url: "https://example.com/html-tutorial", 
        category: "learning",
        emotionalValue: "medium",
        accessPattern: "moderate"
      },
      
      // Week 2: Building confidence
      {
        title: "React - Building Your First Component",
        url: "https://example.com/react-first-component",
        category: "frameworks",
        emotionalValue: "high", // Breakthrough moment
        accessPattern: "frequent"
      },
      {
        title: "CSS Grid Layout - Finally Understanding It",
        url: "https://example.com/css-grid-understanding",
        category: "styling",
        emotionalValue: "high", // "Aha!" moment
        accessPattern: "frequent"
      },
      
      // Week 3: Advanced exploration
      {
        title: "Advanced React Patterns That Changed My Code",
        url: "https://example.com/advanced-react-patterns",
        category: "advanced",
        emotionalValue: "very-high", // Game changer
        accessPattern: "reference"
      },
      {
        title: "Testing Strategies That Actually Work",
        url: "https://example.com/testing-strategies",
        category: "testing",
        emotionalValue: "high",
        accessPattern: "reference"
      },
      
      // Week 4: Professional growth
      {
        title: "Performance Optimization - Real World Results",
        url: "https://example.com/performance-optimization",
        category: "performance",
        emotionalValue: "very-high", // Career impact
        accessPattern: "reference"
      }
    ];
    
    // Add bookmarks with emotional context
    for (const bookmark of journeyBookmarks) {
      await this.addBookmarkWithContext(page, bookmark);
    }
  }
  
  static async createBulkOperationTestData(page) {
    // Create data for testing bulk operation anxiety
    const bulkTestBookmarks = [];
    
    // Mix of valuable and less valuable bookmarks
    for (let i = 0; i < 50; i++) {
      bulkTestBookmarks.push({
        title: `Test Bookmark ${i + 1} - ${this.getRandomTopic()}`,
        url: `https://example.com/test-${i + 1}`,
        value: Math.random() > 0.7 ? 'high' : 'medium', // 30% high value
        category: this.getRandomCategory()
      });
    }
    
    for (const bookmark of bulkTestBookmarks) {
      await this.addBookmarkWithContext(page, bookmark);
    }
  }
  
  static async createSuggestionTestData(page) {
    // Create data for testing AI suggestion timing and relevance
    const userInterests = ['javascript', 'react', 'testing', 'performance'];
    const suggestionBookmarks = [];
    
    // Create bookmarks that should trigger good suggestions
    userInterests.forEach((interest, index) => {
      for (let i = 0; i < 5; i++) {
        suggestionBookmarks.push({
          title: `${interest} - Advanced Topic ${i + 1}`,
          url: `https://example.com/${interest}-${i + 1}`,
          category: interest,
          suggestionTrigger: true,
          relevanceScore: 0.8 + (Math.random() * 0.2) // High relevance
        });
      }
    });
    
    // Add some noise (irrelevant bookmarks)
    const noiseTopics = ['cooking', 'gardening', 'photography'];
    noiseTopics.forEach(topic => {
      suggestionBookmarks.push({
        title: `${topic} - Random Content`,
        url: `https://example.com/${topic}`,
        category: 'other',
        suggestionTrigger: false,
        relevanceScore: 0.1 + (Math.random() * 0.3) // Low relevance
      });
    });
    
    for (const bookmark of suggestionBookmarks) {
      await this.addBookmarkWithContext(page, bookmark);
    }
  }
  
  static async addBookmarkWithContext(page, bookmarkData) {
    // Add bookmark with emotional and behavioral context
    await page.evaluate((data) => {
      // Simulate adding bookmark with metadata
      const bookmark = {
        id: Date.now() + Math.random(),
        title: data.title,
        url: data.url,
        category: data.category,
        dateAdded: new Date().toISOString(),
        
        // Vibe testing metadata
        emotionalValue: data.emotionalValue || 'medium',
        accessPattern: data.accessPattern || 'normal',
        userContext: data.userContext || 'general',
        suggestionTrigger: data.suggestionTrigger || false,
        relevanceScore: data.relevanceScore || 0.5
      };
      
      // Store in localStorage for test access
      const existingBookmarks = JSON.parse(localStorage.getItem('vibeTestBookmarks') || '[]');
      existingBookmarks.push(bookmark);
      localStorage.setItem('vibeTestBookmarks', JSON.stringify(existingBookmarks));
      
      // Trigger bookmark added event if system supports it
      if (window.bookmarkSystem) {
        window.bookmarkSystem.addBookmark(bookmark);
      }
    }, bookmarkData);
  }
  
  static getRandomTopic() {
    const topics = [
      'JavaScript Framework Comparison',
      'CSS Animation Techniques',
      'React Performance Tips',
      'Node.js Best Practices',
      'TypeScript Advanced Features',
      'Web Accessibility Guide',
      'API Design Principles',
      'Database Optimization',
      'Security Best Practices',
      'DevOps Automation'
    ];
    return topics[Math.floor(Math.random() * topics.length)];
  }
  
  static getRandomCategory() {
    const categories = [
      'javascript', 'css', 'react', 'nodejs', 'typescript',
      'accessibility', 'api', 'database', 'security', 'devops'
    ];
    return categories[Math.floor(Math.random() * categories.length)];
  }
  
  static async createLargeFavoritesCollection(page, count = 100) {
    // Create large collection for performance testing
    const favorites = [];
    
    for (let i = 0; i < count; i++) {
      favorites.push({
        title: `Favorite Resource ${i + 1} - ${this.getRandomTopic()}`,
        url: `https://example.com/favorite-${i + 1}`,
        category: this.getRandomCategory(),
        starred: true,
        starredDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
        accessCount: Math.floor(Math.random() * 50) + 1
      });
    }
    
    await page.evaluate((favorites) => {
      localStorage.setItem('vibeTestFavorites', JSON.stringify(favorites));
      
      // Trigger favorites loaded event
      if (window.favoritesSystem) {
        window.favoritesSystem.loadFavorites(favorites);
      }
    }, favorites);
    
    return favorites;
  }
  
  static async simulateUserActivity(page, pattern) {
    // Simulate realistic user activity patterns
    await page.evaluate((pattern) => {
      const activity = {
        bookmarksAdded: pattern.bookmarksAdded || 10,
        timeSpent: pattern.timeSpent || 300000, // 5 minutes
        activityType: pattern.activityType || 'browsing',
        timestamp: Date.now(),
        
        // Behavioral patterns
        clickPatterns: pattern.clickPatterns || 'normal',
        scrollBehavior: pattern.scrollBehavior || 'moderate',
        searchQueries: pattern.searchQueries || [],
        favoritingFrequency: pattern.favoritingFrequency || 'normal'
      };
      
      localStorage.setItem('vibeTestActivity', JSON.stringify(activity));
      
      // Trigger activity tracking if system supports it
      if (window.analyticsSystem) {
        window.analyticsSystem.recordActivity(activity);
      }
    }, pattern);
  }
  
  static async createUsagePattern(page, patternConfig) {
    // Create specific usage patterns for suggestion testing
    const pattern = {
      favoriteTopics: patternConfig.favoriteTopics || ['javascript'],
      accessFrequency: patternConfig.accessFrequency || 'medium',
      timeOfDay: patternConfig.timeOfDay || 'any',
      sessionDuration: patternConfig.sessionDuration || 'medium',
      
      // Behavioral indicators
      rapidStarring: patternConfig.rapidStarring || false,
      bulkOperations: patternConfig.bulkOperations || false,
      searchHeavy: patternConfig.searchHeavy || false,
      organizationFocused: patternConfig.organizationFocused || false
    };
    
    await page.evaluate((pattern) => {
      localStorage.setItem('vibeTestUsagePattern', JSON.stringify(pattern));
      
      // Set up pattern-based behavior simulation
      if (window.behaviorSimulator) {
        window.behaviorSimulator.applyPattern(pattern);
      }
    }, pattern);
  }
}