#!/usr/bin/env node

/**
 * EMERGENCY MEMORY CLEANUP FOR LOCAL DEVELOPMENT
 * Run this when memory usage exceeds 80% during development
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚨 EMERGENCY MEMORY CLEANUP FOR LOCAL DEVELOPMENT');
console.log('================================================');

// 1. Clear Node.js cache
console.log('🗑️ Step 1: Clearing Node.js cache...');
try {
  execSync('npm cache clean --force', { stdio: 'inherit' });
  console.log('✅ Node.js cache cleared');
} catch (error) {
  console.warn('⚠️ Failed to clear npm cache:', error.message);
}

// 2. Clear Vite cache
console.log('🗑️ Step 2: Clearing Vite cache...');
const viteCacheDir = path.join(process.cwd(), 'node_modules', '.vite');
if (fs.existsSync(viteCacheDir)) {
  try {
    fs.rmSync(viteCacheDir, { recursive: true, force: true });
    console.log('✅ Vite cache cleared');
  } catch (error) {
    console.warn('⚠️ Failed to clear Vite cache:', error.message);
  }
} else {
  console.log('ℹ️ No Vite cache found');
}

// 3. Clear TypeScript cache
console.log('🗑️ Step 3: Clearing TypeScript cache...');
const tsCacheFile = path.join(process.cwd(), 'tsconfig.tsbuildinfo');
if (fs.existsSync(tsCacheFile)) {
  try {
    fs.unlinkSync(tsCacheFile);
    console.log('✅ TypeScript cache cleared');
  } catch (error) {
    console.warn('⚠️ Failed to clear TypeScript cache:', error.message);
  }
}

// 4. Clear dist directory
console.log('🗑️ Step 4: Clearing dist directory...');
const distDir = path.join(process.cwd(), 'dist');
if (fs.existsSync(distDir)) {
  try {
    fs.rmSync(distDir, { recursive: true, force: true });
    console.log('✅ Dist directory cleared');
  } catch (error) {
    console.warn('⚠️ Failed to clear dist directory:', error.message);
  }
}

// 5. Clear temporary files
console.log('🗑️ Step 5: Clearing temporary files...');
const tempFiles = [
  '.eslintcache',
  'coverage',
  '.nyc_output',
  'logs',
  '*.log',
  'npm-debug.log*',
  'yarn-debug.log*',
  'yarn-error.log*'
];

tempFiles.forEach(pattern => {
  try {
    execSync(`find . -name "${pattern}" -type f -delete 2>/dev/null || true`, { stdio: 'pipe' });
  } catch (error) {
    // Ignore errors for temp file cleanup
  }
});
console.log('✅ Temporary files cleared');

// 6. Force garbage collection if available
console.log('🗑️ Step 6: Forcing garbage collection...');
if (global.gc) {
  for (let i = 0; i < 5; i++) {
    global.gc();
  }
  console.log('✅ Garbage collection completed');
} else {
  console.log('ℹ️ Garbage collection not available (run with --expose-gc)');
}

// 7. Generate memory optimization script for browser
console.log('📝 Step 7: Generating browser cleanup script...');
const browserScript = `
// EMERGENCY BROWSER CLEANUP FOR LOCAL DEVELOPMENT
console.log('🚨 LOCAL DEV EMERGENCY CLEANUP STARTING...');

// Clear Vite HMR data
if (window.__vite_plugin_react_preamble_installed__) {
  delete window.__vite_plugin_react_preamble_installed__;
}

// Clear React DevTools
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null;
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null;
}

// Aggressive memory cleanup
for (let i = 0; i < 10; i++) {
  if (typeof gc !== 'undefined') gc();
  const temp = new Array(5000000).fill(null);
  temp.length = 0;
}

// Clear all storage
localStorage.clear();
sessionStorage.clear();

// Clear performance data
if (window.performance) {
  if (window.performance.clearMeasures) window.performance.clearMeasures();
  if (window.performance.clearMarks) window.performance.clearMarks();
  if (window.performance.clearResourceTimings) window.performance.clearResourceTimings();
}

// Clear console
console.clear();

console.log('✅ LOCAL DEV BROWSER CLEANUP COMPLETE');

// Check memory after cleanup
setTimeout(() => {
  if (performance.memory) {
    const usage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
    console.log(\`📊 Memory usage after cleanup: \${usage.toFixed(1)}%\`);
    
    if (usage < 60) {
      console.log('✅ Memory cleanup successful!');
    } else if (usage < 75) {
      console.log('⚠️ Memory improved but still high. Consider restarting dev server.');
    } else {
      console.log('🚨 Memory still critical. Restart browser and dev server immediately.');
    }
  }
}, 2000);
`;

fs.writeFileSync(path.join(process.cwd(), 'emergency-browser-cleanup.js'), browserScript);
console.log('✅ Browser cleanup script generated: emergency-browser-cleanup.js');

console.log('\n🎯 EMERGENCY CLEANUP COMPLETE!');
console.log('===============================');
console.log('');
console.log('📋 NEXT STEPS:');
console.log('1. Copy and paste the contents of emergency-browser-cleanup.js into your browser console');
console.log('2. Restart your development server: npm run dev');
console.log('3. If memory is still high, restart your browser');
console.log('4. Consider reducing the number of bookmarks for development');
console.log('');
console.log('🔧 DEVELOPMENT OPTIMIZATIONS:');
console.log('- Vite config updated with memory optimizations');
console.log('- HMR overlay disabled to reduce memory usage');
console.log('- Build chunks optimized for better memory management');
console.log('');
console.log('⚠️ If memory issues persist:');
console.log('- Restart your computer');
console.log('- Use a different browser');
console.log('- Reduce bookmark dataset size');
