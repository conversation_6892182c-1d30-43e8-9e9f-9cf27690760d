import React, { useEffect, useState } from 'react';
import { Bookmark } from '../types';

interface SmartSuggestionsProps {
  bookmarks: Bookmark[];
  selectedBookmarks: string[];
  onApplySuggestion: (suggestion: Suggestion) => void;
  onDismissSuggestion?: (suggestionId: string) => void;
}

interface Suggestion {
  id: string;
  type: 'tag' | 'folder' | 'duplicate' | 'broken-link' | 'organization';
  title: string;
  description: string;
  icon: string;
  priority: 'high' | 'medium' | 'low';
  action: {
    type: string;
    data: Record<string, unknown>;
  };
  affectedBookmarks: string[];
}

const SmartSuggestions: React.FC<SmartSuggestionsProps> = ({
  bookmarks,
  selectedBookmarks,
  onApplySuggestion,
  onDismissSuggestion
}) => {
  const [suggestions, setSuggestions] = useState<Suggestion[]>([]);
  const [_isLoading] = useState(false);
  const [expandedSuggestion, setExpandedSuggestion] = useState<string | null>(null);

  // Generate smart suggestions based on bookmarks
  useEffect(() => {
    const generateSuggestions = () => {
      if (!bookmarks || !Array.isArray(bookmarks)) {
        setSuggestions([]);
        return;
      }

      if (!selectedBookmarks || !Array.isArray(selectedBookmarks)) {
        // selectedBookmarks is optional, so we can continue without it
      }
      
      const newSuggestions: Suggestion[] = [];

      // Duplicate detection
      const urlMap = new Map<string, Bookmark[]>();
      bookmarks.forEach(bookmark => {
        if (!bookmark.url) return; // Skip bookmarks without URLs
        try {
          const normalizedUrl = bookmark.url.toLowerCase().replace(/\/$/, '');
          if (!urlMap.has(normalizedUrl)) {
            urlMap.set(normalizedUrl, []);
          }
          urlMap.get(normalizedUrl)!.push(bookmark);
        } catch (error) {
          console.warn('Error processing bookmark URL:', bookmark.url, error);
        }
      });

      urlMap.forEach((duplicates, url) => {
        if (duplicates.length > 1) {
          newSuggestions.push({
            id: `duplicate-${url}`,
            type: 'duplicate',
            title: 'Duplicate Bookmarks Found',
            description: `Found ${duplicates.length} bookmarks with the same URL`,
            icon: '🔄',
            priority: 'medium',
            action: {
              type: 'merge-duplicates',
              data: { bookmarks: duplicates }
            },
            affectedBookmarks: duplicates.map(b => b.id)
          });
        }
      });

      // Tag suggestions for untagged bookmarks
      const untaggedBookmarks = bookmarks.filter(b => !b.tags || b.tags.length === 0);
      if (untaggedBookmarks.length > 0) {
        // Group by domain for tag suggestions
        const domainGroups = new Map<string, Bookmark[]>();
        untaggedBookmarks.forEach(bookmark => {
          if (!bookmark.url) return;
          try {
            const url = new URL(bookmark.url);
            const domain = url.hostname;
            if (domain && domain.length > 0) {
              if (!domainGroups.has(domain)) {
                domainGroups.set(domain, []);
              }
              domainGroups.get(domain)!.push(bookmark);
            }
          } catch (error) {
            console.warn('Invalid URL for bookmark:', bookmark.url, error);
          }
        });

        domainGroups.forEach((bookmarks, domain) => {
          if (bookmarks.length >= 3) {
            const suggestedTag = domain.replace('www.', '').split('.')[0];
            newSuggestions.push({
              id: `tag-${domain}`,
              type: 'tag',
              title: `Tag ${domain} Bookmarks`,
              description: `Add "${suggestedTag}" tag to ${bookmarks.length} bookmarks from ${domain}`,
              icon: '🏷️',
              priority: 'low',
              action: {
                type: 'add-tag',
                data: { tag: suggestedTag, bookmarks }
              },
              affectedBookmarks: bookmarks.map(b => b.id)
            });
          }
        });
      }

      // Folder organization suggestions
      const bookmarksWithoutFolder = bookmarks.filter(b =>
        (!b.path || b.path.length === 0) && (!b.folder || b.folder.trim() === '')
      );
      if (bookmarksWithoutFolder.length > 10) {
        newSuggestions.push({
          id: 'organize-folders',
          type: 'organization',
          title: 'Organize into Folders',
          description: `${bookmarksWithoutFolder.length} bookmarks could be organized into folders`,
          icon: '📁',
          priority: 'medium',
          action: {
            type: 'suggest-folders',
            data: { bookmarks: bookmarksWithoutFolder }
          },
          affectedBookmarks: bookmarksWithoutFolder.map(b => b.id)
        });
      }

      // Broken link detection (simulated)
      const oldBookmarks = bookmarks.filter(b => {
        const dateToCheck = b.addDate || b.createdAt || null;
        if (!dateToCheck) return false;
        try {
          // Handle both UNIX timestamp (seconds) and milliseconds
          const timestamp = parseInt(dateToCheck);
          const addedDate = new Date(timestamp > 1000000000000 ? timestamp : timestamp * 1000);
          const monthsAgo = (Date.now() - addedDate.getTime()) / (1000 * 60 * 60 * 24 * 30);
          return monthsAgo > 6;
        } catch {
          return false;
        }
      });

      if (oldBookmarks.length > 0) {
        newSuggestions.push({
          id: 'check-broken-links',
          type: 'broken-link',
          title: 'Check for Broken Links',
          description: `${oldBookmarks.length} old bookmarks may have broken links`,
          icon: '🔍',
          priority: 'low',
          action: {
            type: 'check-links',
            data: { bookmarks: oldBookmarks }
          },
          affectedBookmarks: oldBookmarks.map(b => b.id)
        });
      }

      // Smart tag suggestions based on content
      if (selectedBookmarks && Array.isArray(selectedBookmarks) && selectedBookmarks.length > 0) {
        const selected = bookmarks.filter(b => b.id && selectedBookmarks.includes(b.id));
        if (selected.length > 0) {
          const commonKeywords = getCommonKeywords(selected);

          if (commonKeywords.length > 0) {
            newSuggestions.push({
              id: 'smart-tag-selected',
              type: 'tag',
              title: 'Smart Tag Suggestion',
              description: `Add "${commonKeywords[0]}" tag to ${selected.length} selected bookmarks`,
              icon: '🤖',
              priority: 'high',
              action: {
                type: 'add-tag',
                data: { tag: commonKeywords[0], bookmarks: selected }
              },
              affectedBookmarks: selectedBookmarks
            });
          }
        }
      }

      setSuggestions(newSuggestions.slice(0, 5)); // Limit to 5 suggestions
    };

    try {
      if (bookmarks && bookmarks.length >= 0) {
        generateSuggestions();
      }
    } catch (error) {
      console.error('Error generating smart suggestions:', error);
      setSuggestions([]);
    }
  }, [bookmarks, selectedBookmarks]);

  const getCommonKeywords = (bookmarks: Bookmark[]): string[] => {
    if (!bookmarks || bookmarks.length === 0) return [];

    const keywords = new Map<string, number>();
    bookmarks.forEach(bookmark => {
      if (!bookmark) return;

      const title = bookmark.title || '';
      const summary = bookmark.summary || '';
      const text = `${title} ${summary}`.trim();

      if (text.length === 0) return;

      try {
        const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
        words.forEach(word => {
          if (word && !['http', 'https', 'www', 'html', 'page', 'site'].includes(word)) {
            keywords.set(word, (keywords.get(word) || 0) + 1);
          }
        });
      } catch (error) {
        console.warn('Error processing keywords for bookmark:', bookmark.id, error);
      }
    });

    const minCount = Math.max(1, Math.ceil(bookmarks.length / 2));
    return Array.from(keywords.entries())
      .filter(([_, count]) => count >= minCount)
      .sort((a, b) => b[1] - a[1])
      .map(([word]) => word)
      .slice(0, 3);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-red-200 bg-red-50';
      case 'medium': return 'border-yellow-200 bg-yellow-50';
      case 'low': return 'border-blue-200 bg-blue-50';
      default: return 'border-gray-200 bg-gray-50';
    }
  };

  const getPriorityTextColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-700';
      case 'medium': return 'text-yellow-700';
      case 'low': return 'text-blue-700';
      default: return 'text-gray-700';
    }
  };

  if (suggestions.length === 0) {
    return (
      <div className="smart-suggestions-empty">
        <div className="text-center py-6">
          <div className="text-3xl mb-2">✨</div>
          <p className="text-gray-500 text-sm">
            No suggestions at the moment
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="smart-suggestions">
      <div className="smart-suggestions-header">
        <h3 className="text-sm font-medium text-gray-900 flex items-center">
          <span className="mr-2">🤖</span>
          Smart Suggestions
        </h3>
        {_isLoading && (
          <div className="loading-spinner"></div>
        )}
      </div>

      <div className="smart-suggestions-list">
        {suggestions.map((suggestion) => (
          <div
            key={suggestion.id}
            className={`suggestion-card ${getPriorityColor(suggestion.priority)}`}
          >
            <div className="suggestion-header">
              <div className="flex items-start space-x-3">
                <div className="suggestion-icon">
                  {suggestion.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={`suggestion-title ${getPriorityTextColor(suggestion.priority)}`}>
                    {suggestion.title}
                  </h4>
                  <p className="suggestion-description">
                    {suggestion.description}
                  </p>
                  <div className="suggestion-meta">
                    <span className={`priority-badge priority-${suggestion.priority}`}>
                      {suggestion.priority} priority
                    </span>
                    <span className="affected-count">
                      {suggestion.affectedBookmarks.length} bookmark{suggestion.affectedBookmarks.length !== 1 ? 's' : ''}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <div className="suggestion-actions">
              <button
                onClick={() => {
                  setExpandedSuggestion(
                    expandedSuggestion === suggestion.id ? null : suggestion.id
                  );
                }}
                className="suggestion-action-btn secondary"
              >
                {expandedSuggestion === suggestion.id ? 'Hide' : 'Details'}
              </button>
              <button
                onClick={() => onApplySuggestion(suggestion)}
                className="suggestion-action-btn primary"
              >
                Apply
              </button>
              {onDismissSuggestion && (
                <button
                  onClick={() => onDismissSuggestion(suggestion.id)}
                  className="suggestion-action-btn dismiss"
                  title="Dismiss"
                >
                  ×
                </button>
              )}
            </div>

            {expandedSuggestion === suggestion.id && (
              <div className="suggestion-details">
                <h5 className="text-xs font-medium text-gray-700 mb-2">
                  Affected Bookmarks:
                </h5>
                <div className="space-y-1">
                  {suggestion.affectedBookmarks.slice(0, 5).map(bookmarkId => {
                    const bookmark = bookmarks.find(b => b.id === bookmarkId);
                    return bookmark ? (
                      <div key={bookmarkId} className="affected-bookmark">
                        <span className="bookmark-title">
                          {bookmark.title || 'Untitled'}
                        </span>
                        <span className="bookmark-url">
                          {bookmark.url}
                        </span>
                      </div>
                    ) : null;
                  })}
                  {suggestion.affectedBookmarks.length > 5 && (
                    <div className="text-xs text-gray-500">
                      +{suggestion.affectedBookmarks.length - 5} more
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default SmartSuggestions;