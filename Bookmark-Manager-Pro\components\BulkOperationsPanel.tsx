import React, { useState } from 'react';
import { Bookmark, BulkOperation, BulkOperationResult } from '../types';

interface BulkOperationsPanelProps {
  selectedBookmarks: Bookmark[];
  onBulkOperation: (operation: BulkOperation) => Promise<BulkOperationResult>;
  onClose: () => void;
  availableTags: string[];
  availableFolders: string[];
}

interface OperationData {
  newTags?: string[];
  tagsToRemove?: string[];
  targetFolder?: string;
  titlePrefix?: string;
  titleSuffix?: string;
}

const BulkOperationsPanel: React.FC<BulkOperationsPanelProps> = ({
  selectedBookmarks,
  onBulkOperation,
  onClose,
  availableTags,
  availableFolders
}) => {
  const [activeOperation, setActiveOperation] = useState<BulkOperation['type'] | null>(null);
  const [operationData, setOperationData] = useState<OperationData>({});
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<BulkOperationResult | null>(null);

  const handleOperation = async (type: BulkOperation['type'], data?: Record<string, unknown>) => {
    setIsProcessing(true);
    setResult(null);

    try {
      const operation: BulkOperation = {
        type,
        bookmarkIds: selectedBookmarks.map(b => b.id),
        data
      };

      const operationResult = await onBulkOperation(operation);
      setResult(operationResult);
    } catch (error) {
      setResult({
        success: false,
        processedCount: 0,
        errors: [error instanceof Error ? error.message : 'Operation failed']
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handleAddTags = () => {
    if (operationData.newTags && operationData.newTags.length > 0) {
      handleOperation('addTags', { tags: operationData.newTags });
    }
  };

  const handleRemoveTags = () => {
    if (operationData.tagsToRemove && operationData.tagsToRemove.length > 0) {
      handleOperation('removeTags', { tags: operationData.tagsToRemove });
    }
  };

  const handleMoveToFolder = () => {
    if (operationData.targetFolder !== undefined) {
      handleOperation('moveToFolder', { folder: operationData.targetFolder });
    }
  };

  const handleBulkEdit = () => {
    const editData: Record<string, unknown> = {};
    if (operationData.titlePrefix) editData.titlePrefix = operationData.titlePrefix;
    if (operationData.titleSuffix) editData.titleSuffix = operationData.titleSuffix;
    
    if (Object.keys(editData).length > 0) {
      handleOperation('edit', editData);
    }
  };

  const handleDelete = () => {
    if (window.confirm(`Are you sure you want to delete ${selectedBookmarks.length} bookmark(s)? This action cannot be undone.`)) {
      handleOperation('delete');
    }
  };

  const resetOperation = () => {
    setActiveOperation(null);
    setOperationData({});
    setResult(null);
  };

  if (selectedBookmarks.length === 0) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-6 w-full max-w-md">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            No Bookmarks Selected
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            Please select one or more bookmarks to perform bulk operations.
          </p>
          <button
            onClick={onClose}
            className="w-full px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Bulk Operations ({selectedBookmarks.length} selected)
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {!activeOperation && !result && (
            <div className="space-y-4">
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                Choose an operation to perform on the selected bookmarks:
              </p>

              {/* Operation Buttons */}
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={() => setActiveOperation('addTags')}
                  className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Add Tags</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Add tags to selected bookmarks</p>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setActiveOperation('removeTags')}
                  className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Remove Tags</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Remove tags from selected bookmarks</p>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setActiveOperation('moveToFolder')}
                  className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Move to Folder</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Move bookmarks to a folder</p>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setActiveOperation('edit')}
                  className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-left"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-white">Bulk Edit</h3>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Edit titles with prefix/suffix</p>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setActiveOperation('delete')}
                  className="p-4 border border-red-300 dark:border-red-600 rounded-lg hover:bg-red-50 dark:hover:bg-red-900 transition-colors text-left col-span-2"
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium text-red-900 dark:text-red-400">Delete Bookmarks</h3>
                      <p className="text-sm text-red-600 dark:text-red-500">Permanently delete selected bookmarks</p>
                    </div>
                  </div>
                </button>
              </div>
            </div>
          )}

          {/* Add Tags Operation */}
          {activeOperation === 'addTags' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Add Tags</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select tags to add to {selectedBookmarks.length} bookmark(s):
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Available Tags
                  </label>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {availableTags.map(tag => (
                      <button
                        key={tag}
                        onClick={() => {
                          const newTags = operationData.newTags || [];
                          setOperationData({
                            ...operationData,
                            newTags: newTags.includes(tag)
                              ? newTags.filter((t: string) => t !== tag)
                              : [...newTags, tag]
                          });
                        }}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          (operationData.newTags || []).includes(tag)
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                        }`}
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={resetOperation}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleAddTags}
                    disabled={!operationData.newTags?.length || isProcessing}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Adding...' : 'Add Tags'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Remove Tags Operation */}
          {activeOperation === 'removeTags' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Remove Tags</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select tags to remove from {selectedBookmarks.length} bookmark(s):
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Tags to Remove
                  </label>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {availableTags.map(tag => (
                      <button
                        key={tag}
                        onClick={() => {
                          const tagsToRemove = operationData.tagsToRemove || [];
                          setOperationData({
                            ...operationData,
                            tagsToRemove: tagsToRemove.includes(tag)
                              ? tagsToRemove.filter((t: string) => t !== tag)
                              : [...tagsToRemove, tag]
                          });
                        }}
                        className={`px-3 py-1 rounded-full text-sm transition-colors ${
                          (operationData.tagsToRemove || []).includes(tag)
                            ? 'bg-red-500 text-white'
                            : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-500'
                        }`}
                      >
                        {tag}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={resetOperation}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleRemoveTags}
                    disabled={!operationData.tagsToRemove?.length || isProcessing}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Removing...' : 'Remove Tags'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Move to Folder Operation */}
          {activeOperation === 'moveToFolder' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Move to Folder</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Select a folder to move {selectedBookmarks.length} bookmark(s) to:
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Target Folder
                  </label>
                  <select
                    value={operationData.targetFolder || ''}
                    onChange={(e) => setOperationData({ ...operationData, targetFolder: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  >
                    <option value="">Uncategorized</option>
                    {availableFolders.filter(f => f).map(folder => (
                      <option key={folder} value={folder}>{folder}</option>
                    ))}
                  </select>
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={resetOperation}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleMoveToFolder}
                    disabled={isProcessing}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Moving...' : 'Move to Folder'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Bulk Edit Operation */}
          {activeOperation === 'edit' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Bulk Edit</h3>
              <p className="text-gray-600 dark:text-gray-400">
                Add prefix or suffix to {selectedBookmarks.length} bookmark title(s):
              </p>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Title Prefix
                  </label>
                  <input
                    type="text"
                    value={operationData.titlePrefix || ''}
                    onChange={(e) => setOperationData({ ...operationData, titlePrefix: e.target.value })}
                    placeholder="Text to add before title..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Title Suffix
                  </label>
                  <input
                    type="text"
                    value={operationData.titleSuffix || ''}
                    onChange={(e) => setOperationData({ ...operationData, titleSuffix: e.target.value })}
                    placeholder="Text to add after title..."
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                  />
                </div>
                
                <div className="flex space-x-2">
                  <button
                    onClick={resetOperation}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Back
                  </button>
                  <button
                    onClick={handleBulkEdit}
                    disabled={(!operationData.titlePrefix && !operationData.titleSuffix) || isProcessing}
                    className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Updating...' : 'Update Titles'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Delete Operation */}
          {activeOperation === 'delete' && (
            <div className="space-y-4">
              <div className="text-center">
                <div className="w-16 h-16 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Confirm Deletion</h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Are you sure you want to delete {selectedBookmarks.length} bookmark(s)? This action cannot be undone.
                </p>
                
                <div className="flex justify-center space-x-4">
                  <button
                    onClick={resetOperation}
                    className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    disabled={isProcessing}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isProcessing ? 'Deleting...' : 'Delete Bookmarks'}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Result Display */}
          {result && (
            <div className={`p-4 rounded-lg ${
              result.success ? 'bg-green-50 dark:bg-green-900' : 'bg-red-50 dark:bg-red-900'
            }`}>
              <div className="flex items-center space-x-2 mb-2">
                <svg className={`w-5 h-5 ${
                  result.success ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {result.success ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  )}
                </svg>
                <h3 className={`font-medium ${
                  result.success ? 'text-green-900 dark:text-green-100' : 'text-red-900 dark:text-red-100'
                }`}>
                  {result.success ? 'Operation Completed' : 'Operation Failed'}
                </h3>
              </div>
              <p className={`text-sm ${
                result.success ? 'text-green-700 dark:text-green-300' : 'text-red-700 dark:text-red-300'
              }`}>
                {result.success 
                  ? `Successfully processed ${result.processedCount} bookmark(s)`
                  : `Failed to process bookmarks: ${result.errors.join(', ')}`
                }
              </p>
              
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={resetOperation}
                  className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
                >
                  Perform Another Operation
                </button>
                <button
                  onClick={onClose}
                  className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BulkOperationsPanel;