// Emotional Journey Reporter
// Generates visual reports of user emotional journey through favorites system

import fs from 'fs';
import path from 'path';

class EmotionalJourneyReporter {
  constructor(options = {}) {
    this.options = options;
    this.journeyData = {
      sessions: [],
      emotionalStates: [],
      satisfactionPoints: [],
      frustrationPoints: [],
      delightMoments: []
    };
  }
  
  onBegin(config, suite) {
    console.log('🎭 Emotional Journey Analysis - Starting...');
  }
  
  onTestEnd(test, result) {
    // Extract emotional journey data from test results
    if (result.attachments) {
      result.attachments.forEach(attachment => {
        if (attachment.name === 'emotional-journey') {
          try {
            const journeyData = JSON.parse(attachment.body.toString());
            this.processJourneyData(journeyData, test.title);
          } catch (error) {
            console.warn('Failed to parse emotional journey data:', error);
          }
        }
      });
    }
  }
  
  processJourneyData(data, testTitle) {
    if (data.session) {
      this.journeyData.sessions.push({
        test: testTitle,
        ...data.session
      });
    }
    
    if (data.emotionalStates) {
      this.journeyData.emotionalStates.push(...data.emotionalStates.map(state => ({
        test: testTitle,
        ...state
      })));
    }
    
    if (data.satisfactionPoints) {
      this.journeyData.satisfactionPoints.push(...data.satisfactionPoints.map(point => ({
        test: testTitle,
        ...point
      })));
    }
    
    if (data.frustrationPoints) {
      this.journeyData.frustrationPoints.push(...data.frustrationPoints.map(point => ({
        test: testTitle,
        ...point
      })));
    }
    
    if (data.delightMoments) {
      this.journeyData.delightMoments.push(...data.delightMoments.map(moment => ({
        test: testTitle,
        ...moment
      })));
    }
  }
  
  onEnd(result) {
    this.generateEmotionalJourneyReport();
    console.log('🎭 Emotional Journey Analysis Complete!');
  }
  
  generateEmotionalJourneyReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: this.generateJourneySummary(),
      emotionalFlow: this.analyzeEmotionalFlow(),
      satisfactionAnalysis: this.analyzeSatisfaction(),
      frustrationAnalysis: this.analyzeFrustration(),
      delightAnalysis: this.analyzeDelight(),
      recommendations: this.generateEmotionalRecommendations()
    };
    
    // Generate HTML report
    this.generateHTMLReport(report);
    
    // Save JSON data
    const reportDir = 'test-results';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(reportDir, 'emotional-journey-data.json'),
      JSON.stringify(report, null, 2)
    );
  }
  
  generateJourneySummary() {
    return {
      totalSessions: this.journeyData.sessions.length,
      totalEmotionalStates: this.journeyData.emotionalStates.length,
      satisfactionPoints: this.journeyData.satisfactionPoints.length,
      frustrationPoints: this.journeyData.frustrationPoints.length,
      delightMoments: this.journeyData.delightMoments.length,
      
      // Calculate ratios
      satisfactionRatio: this.journeyData.satisfactionPoints.length / (this.journeyData.satisfactionPoints.length + this.journeyData.frustrationPoints.length),
      delightFrequency: this.journeyData.delightMoments.length / this.journeyData.sessions.length
    };
  }
  
  analyzeEmotionalFlow() {
    const flow = {
      states: {},
      transitions: {},
      patterns: []
    };
    
    // Analyze emotional state distribution
    this.journeyData.emotionalStates.forEach(state => {
      flow.states[state.emotion] = (flow.states[state.emotion] || 0) + 1;
    });
    
    // Analyze emotional transitions
    for (let i = 1; i < this.journeyData.emotionalStates.length; i++) {
      const from = this.journeyData.emotionalStates[i - 1].emotion;
      const to = this.journeyData.emotionalStates[i].emotion;
      const transition = `${from} → ${to}`;
      
      flow.transitions[transition] = (flow.transitions[transition] || 0) + 1;
    }
    
    // Identify patterns
    flow.patterns = this.identifyEmotionalPatterns();
    
    return flow;
  }
  
  identifyEmotionalPatterns() {
    const patterns = [];
    
    // Pattern: Confidence building
    const confidenceBuilding = this.journeyData.emotionalStates.filter((state, index) => {
      if (index < 2) return false;
      const prev2 = this.journeyData.emotionalStates[index - 2].emotion;
      const prev1 = this.journeyData.emotionalStates[index - 1].emotion;
      const current = state.emotion;
      
      return (prev2 === 'uncertain' && prev1 === 'confident' && current === 'satisfied');
    });
    
    if (confidenceBuilding.length > 0) {
      patterns.push({
        name: 'Confidence Building',
        frequency: confidenceBuilding.length,
        description: 'Users gain confidence through successful interactions'
      });
    }
    
    // Pattern: Frustration recovery
    const frustrationRecovery = this.journeyData.emotionalStates.filter((state, index) => {
      if (index < 1) return false;
      const prev = this.journeyData.emotionalStates[index - 1].emotion;
      const current = state.emotion;
      
      return (prev === 'frustrated' && (current === 'satisfied' || current === 'delighted'));
    });
    
    if (frustrationRecovery.length > 0) {
      patterns.push({
        name: 'Frustration Recovery',
        frequency: frustrationRecovery.length,
        description: 'System successfully recovers from user frustration'
      });
    }
    
    // Pattern: Delight moments
    const delightSequences = this.journeyData.emotionalStates.filter(state => state.emotion === 'delighted');
    
    if (delightSequences.length > 0) {
      patterns.push({
        name: 'Delight Moments',
        frequency: delightSequences.length,
        description: 'Moments where system exceeds user expectations'
      });
    }
    
    return patterns;
  }
  
  analyzeSatisfaction() {
    const analysis = {
      totalPoints: this.journeyData.satisfactionPoints.length,
      categories: {},
      triggers: {},
      averageIntensity: 0
    };
    
    this.journeyData.satisfactionPoints.forEach(point => {
      // Categorize satisfaction points
      analysis.categories[point.category] = (analysis.categories[point.category] || 0) + 1;
      
      // Analyze triggers
      analysis.triggers[point.trigger] = (analysis.triggers[point.trigger] || 0) + 1;
      
      // Calculate average intensity
      analysis.averageIntensity += point.intensity || 5;
    });
    
    if (this.journeyData.satisfactionPoints.length > 0) {
      analysis.averageIntensity /= this.journeyData.satisfactionPoints.length;
    }
    
    return analysis;
  }
  
  analyzeFrustration() {
    const analysis = {
      totalPoints: this.journeyData.frustrationPoints.length,
      categories: {},
      causes: {},
      averageIntensity: 0,
      resolutionRate: 0
    };
    
    let resolvedFrustrations = 0;
    
    this.journeyData.frustrationPoints.forEach(point => {
      // Categorize frustration points
      analysis.categories[point.category] = (analysis.categories[point.category] || 0) + 1;
      
      // Analyze causes
      analysis.causes[point.cause] = (analysis.causes[point.cause] || 0) + 1;
      
      // Calculate average intensity
      analysis.averageIntensity += point.intensity || 5;
      
      // Check if frustration was resolved
      if (point.resolved) {
        resolvedFrustrations++;
      }
    });
    
    if (this.journeyData.frustrationPoints.length > 0) {
      analysis.averageIntensity /= this.journeyData.frustrationPoints.length;
      analysis.resolutionRate = resolvedFrustrations / this.journeyData.frustrationPoints.length;
    }
    
    return analysis;
  }
  
  analyzeDelight() {
    const analysis = {
      totalMoments: this.journeyData.delightMoments.length,
      categories: {},
      triggers: {},
      averageIntensity: 0,
      frequency: 0
    };
    
    this.journeyData.delightMoments.forEach(moment => {
      // Categorize delight moments
      analysis.categories[moment.category] = (analysis.categories[moment.category] || 0) + 1;
      
      // Analyze triggers
      analysis.triggers[moment.trigger] = (analysis.triggers[moment.trigger] || 0) + 1;
      
      // Calculate average intensity
      analysis.averageIntensity += moment.intensity || 8;
    });
    
    if (this.journeyData.delightMoments.length > 0) {
      analysis.averageIntensity /= this.journeyData.delightMoments.length;
      analysis.frequency = this.journeyData.delightMoments.length / this.journeyData.sessions.length;
    }
    
    return analysis;
  }
  
  generateEmotionalRecommendations() {
    const recommendations = [];
    
    // Analyze satisfaction vs frustration ratio
    const satisfactionRatio = this.journeyData.satisfactionPoints.length / 
      (this.journeyData.satisfactionPoints.length + this.journeyData.frustrationPoints.length);
    
    if (satisfactionRatio < 0.7) {
      recommendations.push({
        priority: 'high',
        category: 'satisfaction',
        issue: 'Low satisfaction to frustration ratio',
        recommendation: 'Focus on reducing friction points and enhancing positive feedback'
      });
    }
    
    // Analyze delight frequency
    const delightFrequency = this.journeyData.delightMoments.length / this.journeyData.sessions.length;
    
    if (delightFrequency < 0.5) {
      recommendations.push({
        priority: 'medium',
        category: 'delight',
        issue: 'Low frequency of delight moments',
        recommendation: 'Add more surprising and delightful micro-interactions'
      });
    }
    
    // Analyze frustration resolution
    const frustrationAnalysis = this.analyzeFrustration();
    
    if (frustrationAnalysis.resolutionRate < 0.8) {
      recommendations.push({
        priority: 'high',
        category: 'frustration',
        issue: 'Low frustration resolution rate',
        recommendation: 'Improve error recovery and user guidance systems'
      });
    }
    
    // Analyze emotional flow patterns
    const emotionalFlow = this.analyzeEmotionalFlow();
    
    if (emotionalFlow.transitions['confident → frustrated'] > 2) {
      recommendations.push({
        priority: 'critical',
        category: 'flow',
        issue: 'Users losing confidence during interactions',
        recommendation: 'Identify and fix confidence-breaking interaction patterns'
      });
    }
    
    return recommendations;
  }
  
  generateHTMLReport(report) {
    const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emotional Journey Report - Dr. Elena's Vibe Testing</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        .content {
            padding: 30px;
        }
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
        }
        .section h2 {
            margin-top: 0;
            color: #2c3e50;
            font-size: 1.5em;
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #666;
            font-size: 0.9em;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .recommendations {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
        }
        .recommendation {
            margin-bottom: 15px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #f39c12;
        }
        .recommendation.high {
            border-left-color: #e74c3c;
        }
        .recommendation.critical {
            border-left-color: #c0392b;
        }
        .recommendation h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        .timestamp {
            text-align: center;
            color: #666;
            font-size: 0.9em;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎭 Emotional Journey Report</h1>
            <p>Dr. Elena's Vibe Testing Analysis - Favorites System</p>
        </div>
        
        <div class="content">
            <div class="section">
                <h2>📊 Journey Summary</h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">${report.summary.totalSessions}</div>
                        <div class="metric-label">Total Sessions</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${report.summary.satisfactionPoints}</div>
                        <div class="metric-label">Satisfaction Points</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${report.summary.frustrationPoints}</div>
                        <div class="metric-label">Frustration Points</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${report.summary.delightMoments}</div>
                        <div class="metric-label">Delight Moments</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${(report.summary.satisfactionRatio * 100).toFixed(1)}%</div>
                        <div class="metric-label">Satisfaction Ratio</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">${report.summary.delightFrequency.toFixed(2)}</div>
                        <div class="metric-label">Delight Frequency</div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🌊 Emotional Flow Analysis</h2>
                <div class="chart-container">
                    <canvas id="emotionalFlowChart"></canvas>
                </div>
                <h3>Identified Patterns:</h3>
                ${report.emotionalFlow.patterns.map(pattern => `
                    <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px;">
                        <strong>${pattern.name}</strong> (${pattern.frequency} occurrences)<br>
                        <small>${pattern.description}</small>
                    </div>
                `).join('')}
            </div>
            
            <div class="section">
                <h2>😊 Satisfaction Analysis</h2>
                <div class="chart-container">
                    <canvas id="satisfactionChart"></canvas>
                </div>
                <p><strong>Average Intensity:</strong> ${report.satisfactionAnalysis.averageIntensity.toFixed(1)}/10</p>
            </div>
            
            <div class="section">
                <h2>😤 Frustration Analysis</h2>
                <div class="chart-container">
                    <canvas id="frustrationChart"></canvas>
                </div>
                <p><strong>Resolution Rate:</strong> ${(report.frustrationAnalysis.resolutionRate * 100).toFixed(1)}%</p>
                <p><strong>Average Intensity:</strong> ${report.frustrationAnalysis.averageIntensity.toFixed(1)}/10</p>
            </div>
            
            <div class="section">
                <h2>✨ Delight Analysis</h2>
                <div class="chart-container">
                    <canvas id="delightChart"></canvas>
                </div>
                <p><strong>Frequency:</strong> ${report.delightAnalysis.frequency.toFixed(2)} moments per session</p>
                <p><strong>Average Intensity:</strong> ${report.delightAnalysis.averageIntensity.toFixed(1)}/10</p>
            </div>
            
            <div class="section">
                <h2>💡 Recommendations</h2>
                <div class="recommendations">
                    ${report.recommendations.map(rec => `
                        <div class="recommendation ${rec.priority}">
                            <h4>${rec.priority.toUpperCase()}: ${rec.category}</h4>
                            <p><strong>Issue:</strong> ${rec.issue}</p>
                            <p><strong>Recommendation:</strong> ${rec.recommendation}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
        
        <div class="timestamp">
            Report generated on ${new Date(report.timestamp).toLocaleString()}
        </div>
    </div>
    
    <script>
        // Emotional Flow Chart
        const emotionalFlowCtx = document.getElementById('emotionalFlowChart').getContext('2d');
        new Chart(emotionalFlowCtx, {
            type: 'doughnut',
            data: {
                labels: ${JSON.stringify(Object.keys(report.emotionalFlow.states))},
                datasets: [{
                    data: ${JSON.stringify(Object.values(report.emotionalFlow.states))},
                    backgroundColor: ['#4facfe', '#00f2fe', '#43e97b', '#38f9d7', '#ffeaa7', '#fab1a0', '#fd79a8']
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Emotional State Distribution'
                    }
                }
            }
        });
        
        // Satisfaction Chart
        const satisfactionCtx = document.getElementById('satisfactionChart').getContext('2d');
        new Chart(satisfactionCtx, {
            type: 'bar',
            data: {
                labels: ${JSON.stringify(Object.keys(report.satisfactionAnalysis.categories))},
                datasets: [{
                    label: 'Satisfaction Points',
                    data: ${JSON.stringify(Object.values(report.satisfactionAnalysis.categories))},
                    backgroundColor: '#43e97b'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Satisfaction by Category'
                    }
                }
            }
        });
        
        // Frustration Chart
        const frustrationCtx = document.getElementById('frustrationChart').getContext('2d');
        new Chart(frustrationCtx, {
            type: 'bar',
            data: {
                labels: ${JSON.stringify(Object.keys(report.frustrationAnalysis.categories))},
                datasets: [{
                    label: 'Frustration Points',
                    data: ${JSON.stringify(Object.values(report.frustrationAnalysis.categories))},
                    backgroundColor: '#fd79a8'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Frustration by Category'
                    }
                }
            }
        });
        
        // Delight Chart
        const delightCtx = document.getElementById('delightChart').getContext('2d');
        new Chart(delightCtx, {
            type: 'bar',
            data: {
                labels: ${JSON.stringify(Object.keys(report.delightAnalysis.categories))},
                datasets: [{
                    label: 'Delight Moments',
                    data: ${JSON.stringify(Object.values(report.delightAnalysis.categories))},
                    backgroundColor: '#ffeaa7'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: 'Delight by Category'
                    }
                }
            }
        });
    </script>
</body>
</html>`;
    
    const reportDir = 'test-results';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(reportDir, 'emotional-journey-report.html'),
      html
    );
  }
}

export default EmotionalJourneyReporter;