// Gemini AI Service for content generation and analysis
export interface GeminiConfig {
  apiKey?: string
  model?: string
  temperature?: number
  maxTokens?: number
}

export interface GeminiResponse {
  content: string
  usage?: {
    promptTokens: number
    completionTokens: number
    totalTokens: number
  }
}

class GeminiService {
  private static instance: GeminiService
  private apiKey: string | null = null
  private baseUrl = 'https://generativelanguage.googleapis.com/v1beta'
  private model = 'gemini-pro'

  private constructor() {
    // Try to get API key from environment or localStorage
    this.apiKey = process.env.GEMINI_API_KEY || localStorage.getItem('geminiApiKey') || null
  }

  static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService()
    }
    return GeminiService.instance
  }

  setApiKey(apiKey: string): void {
    this.apiKey = apiKey
    localStorage.setItem('geminiApiKey', apiKey)
  }

  getApiKey(): string | null {
    return this.apiKey
  }

  isConfigured(): boolean {
    return !!this.apiKey
  }

  async generateContent(prompt: string, config?: GeminiConfig): Promise<string> {
    if (!this.apiKey) {
      throw new Error('Gemini API key not configured. Please set your API key first.')
    }

    try {
      const response = await fetch(`${this.baseUrl}/models/${config?.model || this.model}:generateContent?key=${this.apiKey}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [{
            parts: [{
              text: prompt
            }]
          }],
          generationConfig: {
            temperature: config?.temperature || 0.7,
            maxOutputTokens: config?.maxTokens || 1000,
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`Gemini API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`)
      }

      const data = await response.json()
      
      if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
        throw new Error('Invalid response format from Gemini API')
      }

      return data.candidates[0].content.parts[0].text
    } catch (error) {
      console.error('Gemini API error:', error)
      
      // Fallback to mock response for development
      if (error instanceof Error && error.message.includes('API key')) {
        return this.getMockResponse(prompt)
      }
      
      throw error
    }
  }

  async analyzeContent(content: string, analysisType: 'summary' | 'categorization' | 'sentiment' | 'keywords' = 'summary'): Promise<string> {
    const prompts = {
      summary: `Please provide a concise summary of the following content:\n\n${content}`,
      categorization: `Please categorize the following content and suggest appropriate tags:\n\n${content}`,
      sentiment: `Please analyze the sentiment of the following content:\n\n${content}`,
      keywords: `Please extract the key topics and keywords from the following content:\n\n${content}`
    }

    return this.generateContent(prompts[analysisType])
  }

  async generatePlaylistRecommendations(bookmarks: any[], preferences?: any): Promise<string> {
    const prompt = `Based on the following bookmarks and user preferences, suggest an optimal playlist organization:

Bookmarks: ${JSON.stringify(bookmarks.slice(0, 10))} ${bookmarks.length > 10 ? `... and ${bookmarks.length - 10} more` : ''}

Preferences: ${JSON.stringify(preferences || {})}

Please provide recommendations for:
1. Playlist grouping strategy
2. Optimal viewing/reading order
3. Content categorization
4. Estimated time requirements

Format the response as a structured recommendation.`

    return this.generateContent(prompt)
  }

  private getMockResponse(prompt: string): string {
    // Provide mock responses for development when API key is not available
    if (prompt.includes('summarize') || prompt.includes('summary')) {
      return 'This is a mock summary of the content. The actual content analysis would be performed by Gemini AI when an API key is configured.'
    }
    
    if (prompt.includes('categorize') || prompt.includes('category')) {
      return 'Technology, Web Development, Programming'
    }
    
    if (prompt.includes('playlist') || prompt.includes('recommend')) {
      return JSON.stringify({
        recommendations: [
          'Group similar content types together',
          'Start with foundational content',
          'Progress from basic to advanced topics',
          'Include breaks between intensive content'
        ],
        estimatedTime: '2-3 hours',
        categories: ['Educational', 'Reference', 'Entertainment']
      })
    }
    
    return 'Mock response: Gemini AI service is not configured. Please add your API key to enable AI-powered features.'
  }

  // Batch processing for multiple requests
  async batchGenerate(prompts: string[], config?: GeminiConfig): Promise<string[]> {
    const results: string[] = []
    
    // Process in batches to avoid rate limiting
    const batchSize = 5
    for (let i = 0; i < prompts.length; i += batchSize) {
      const batch = prompts.slice(i, i + batchSize)
      const batchPromises = batch.map(prompt => this.generateContent(prompt, config))
      
      try {
        const batchResults = await Promise.all(batchPromises)
        results.push(...batchResults)
      } catch (error) {
        console.error(`Batch processing error for batch starting at index ${i}:`, error)
        // Add error placeholders for failed batch
        results.push(...batch.map(() => 'Error: Failed to generate content'))
      }
      
      // Add delay between batches to respect rate limits
      if (i + batchSize < prompts.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }
    
    return results
  }

  // Validate API key format
  validateApiKey(apiKey: string): boolean {
    // Basic validation for Gemini API key format
    return typeof apiKey === 'string' && apiKey.length > 20 && apiKey.startsWith('AI')
  }

  // Get usage statistics (mock implementation)
  getUsageStats(): { requestsToday: number; tokensUsed: number; remainingQuota: number } {
    // This would typically come from the API response headers or a separate endpoint
    return {
      requestsToday: parseInt(localStorage.getItem('geminiRequestsToday') || '0'),
      tokensUsed: parseInt(localStorage.getItem('geminiTokensUsed') || '0'),
      remainingQuota: 1000 // Mock remaining quota
    }
  }
}

export const geminiService = GeminiService.getInstance()
