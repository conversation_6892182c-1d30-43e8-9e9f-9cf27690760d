// stringifyWorker.js - Web Worker for optimizing and stringifying bookmark data

// Define the worker context
const ctx = self;

ctx.addEventListener('message', (event) => {
  const { bookmarks, collections, playlists, isLargeDataset } = event.data;

  let optimizedData;
  if (isLargeDataset) {
    optimizedData = {
      bookmarks: bookmarks.map((b) => ({
        id: b.id,
        url: b.url,
        title: b.title,
        tags: b.tags,
        collectionId: b.collectionId
      })),
      collections: collections.map((c) => ({
        id: c.id,
        name: c.name
      })),
      playlists: playlists.map((p) => ({
        id: p.id,
        name: p.name,
        items: p.items
      }))
    };
  } else {
    optimizedData = {
      bookmarks: bookmarks.map((b) => {
        const { tempProp, ...rest } = b; // Remove temporary properties if any
        return rest;
      }),
      collections,
      playlists
    };
  }

  try {
    const stringified = JSON.stringify(optimizedData);
    ctx.postMessage({ success: true, data: stringified });
  } catch (error) {
    // Properly handle error
    const errorMessage = error instanceof Error ? error.message : String(error);
    ctx.postMessage({ success: false, error: errorMessage });
  }
});
