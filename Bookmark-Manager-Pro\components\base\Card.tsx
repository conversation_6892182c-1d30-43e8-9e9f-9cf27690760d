import React, { ReactNode } from 'react';

export interface CardProps {
  children: ReactNode;
  variant?: 'default' | 'outlined' | 'elevated' | 'flat';
  padding?: 'none' | 'sm' | 'md' | 'lg';
  className?: string;
  hover?: boolean;
  selected?: boolean;
  clickable?: boolean;
  onClick?: (e: React.MouseEvent) => void;
  onDragOver?: (e: React.DragEvent) => void;
  onDragLeave?: (e: React.DragEvent) => void;
  onDrop?: (e: React.DragEvent) => void;
  header?: ReactNode;
  footer?: ReactNode;
  actions?: ReactNode;
}

const Card: React.FC<CardProps> = ({
  children,
  variant = 'default',
  padding = 'md',
  className = '',
  hover = false,
  selected = false,
  clickable = false,
  onClick,
  onDragOver,
  onDragLeave,
  onDrop,
  header,
  footer,
  actions
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'outlined':
        return 'border-2 border-slate-200 dark:border-slate-700 bg-transparent';
      case 'elevated':
        return 'border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-lg';
      case 'flat':
        return 'bg-slate-50 dark:bg-slate-900';
      default: // default
        return 'border border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 shadow-sm';
    }
  };

  const getPaddingClasses = () => {
    switch (padding) {
      case 'none':
        return '';
      case 'sm':
        return 'p-3';
      case 'md':
        return 'p-4';
      case 'lg':
        return 'p-6';
      default:
        return 'p-4';
    }
  };

  const baseClasses = `
    rounded-lg transition-all duration-200
    ${getVariantClasses()}
    ${hover ? 'hover:shadow-md hover:border-slate-300 dark:hover:border-slate-600' : ''}
    ${selected ? 'ring-2 ring-primary-500 border-primary-500' : ''}
    ${clickable ? 'cursor-pointer' : ''}
  `;

  const CardComponent = clickable ? 'button' : 'div';

  return (
    <CardComponent
      className={`${baseClasses} ${className}`}
      onClick={clickable ? onClick : onClick}
      onDragOver={onDragOver}
      onDragLeave={onDragLeave}
      onDrop={onDrop}
      type={clickable ? 'button' : undefined}
    >
      {/* Header */}
      {header && (
        <div className={`${padding !== 'none' ? 'px-4 pt-4 pb-2' : ''} border-b border-slate-200 dark:border-slate-700`}>
          {header}
        </div>
      )}

      {/* Main Content */}
      <div className={padding !== 'none' ? getPaddingClasses() : ''}>
        {children}
      </div>

      {/* Footer */}
      {footer && (
        <div className={`${padding !== 'none' ? 'px-4 pb-4 pt-2' : ''} border-t border-slate-200 dark:border-slate-700`}>
          {footer}
        </div>
      )}

      {/* Actions */}
      {actions && (
        <div className={`${padding !== 'none' ? 'px-4 pb-4' : ''} flex items-center justify-end gap-2`}>
          {actions}
        </div>
      )}
    </CardComponent>
  );
};

export default Card;