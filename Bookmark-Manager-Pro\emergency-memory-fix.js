/**
 * EMERGENCY MEMORY FIX - Run this immediately in browser console
 * For 80.5% memory usage crisis
 */

console.log('🚨 EMERGENCY MEMORY FIX STARTING...')

// 1. IMMEDIATE GARBAGE COLLECTION
console.log('🗑️ Step 1: Aggressive garbage collection...')
for (let i = 0; i < 5; i++) {
  if (typeof gc !== 'undefined') {
    gc()
  }
  
  // Force memory release
  const temp = new Array(2000000).fill(null)
  temp.length = 0
}

// 2. CLEAR ALL STORAGE
console.log('💾 Step 2: Clearing storage...')
try {
  localStorage.clear()
  sessionStorage.clear()
  console.log('✅ Storage cleared')
} catch (e) {
  console.warn('Storage clear failed:', e)
}

// 3. CLEAR PERFORMANCE DATA
console.log('📊 Step 3: Clearing performance data...')
if (window.performance) {
  if (window.performance.clearMeasures) window.performance.clearMeasures()
  if (window.performance.clearMarks) window.performance.clearMarks()
  if (window.performance.clearResourceTimings) window.performance.clearResourceTimings()
  console.log('✅ Performance data cleared')
}

// 4. DOM CLEANUP
console.log('🧹 Step 4: DOM cleanup...')
const hiddenElements = document.querySelectorAll('[style*="display: none"], .hidden, [hidden]')
console.log(`Found ${hiddenElements.length} hidden elements to remove`)
hiddenElements.forEach(el => el.remove())

// Remove unnecessary script tags
const scripts = document.querySelectorAll('script[src]')
let removedScripts = 0
scripts.forEach(script => {
  const src = script.getAttribute('src')
  if (src && !src.includes('vite') && !src.includes('react') && !src.includes('main')) {
    script.remove()
    removedScripts++
  }
})
console.log(`✅ Removed ${removedScripts} unnecessary scripts`)

// 5. FORCE MEMORY RELEASE PATTERNS
console.log('🔄 Step 5: Force memory release patterns...')
for (let i = 0; i < 10; i++) {
  const largeArray = new Array(1000000).fill(null)
  largeArray.length = 0
  
  const largeObject = {}
  for (let j = 0; j < 50000; j++) {
    largeObject[`key${j}`] = null
  }
  Object.keys(largeObject).forEach(key => delete largeObject[key])
}

// 6. FINAL GARBAGE COLLECTION
console.log('🗑️ Step 6: Final garbage collection...')
for (let i = 0; i < 3; i++) {
  if (typeof gc !== 'undefined') {
    gc()
  }
}

// 7. CHECK MEMORY AFTER CLEANUP
setTimeout(() => {
  if (performance.memory) {
    const memory = performance.memory
    const usageAfter = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
    console.log(`📊 Memory usage after cleanup: ${usageAfter.toFixed(1)}%`)
    
    if (usageAfter < 70) {
      console.log('✅ EMERGENCY CLEANUP SUCCESSFUL!')
      console.log('💡 Consider enabling virtual scrolling and reducing visible bookmarks')
    } else {
      console.log('⚠️ Memory still high. Consider refreshing the page.')
      console.log('🔄 Run: window.location.reload() to force refresh')
    }
  }
}, 2000)

console.log('🚨 EMERGENCY MEMORY FIX COMPLETED')
console.log('⏳ Checking results in 2 seconds...')

// Show user notification
const notification = document.createElement('div')
notification.style.cssText = `
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-weight: bold;
  z-index: 10000;
  box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
  max-width: 300px;
`

notification.innerHTML = `
  ✅ Emergency Memory Cleanup Complete!<br>
  <small style="font-weight: normal; margin-top: 8px; display: block;">
    Check console for results. Click to dismiss.
  </small>
`

notification.onclick = () => notification.remove()
document.body.appendChild(notification)

setTimeout(() => {
  if (notification.parentNode) {
    notification.remove()
  }
}, 5000)
