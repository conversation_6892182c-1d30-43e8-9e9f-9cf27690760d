import { Bookmark, Playlist } from '../../types';
import { summaryService } from './summaryService';

export interface SmartPlaylistSuggestion {
  id: string;
  name: string;
  description: string;
  color: string;
  confidence: number;
  bookmarkIds: string[];
  reasoning: string;
  category: 'content-based' | 'temporal' | 'behavioral' | 'semantic';
}

export interface PlaylistAnalytics {
  totalBookmarks: number;
  contentTypes: Record<string, number>;
  domains: Record<string, number>;
  tags: Record<string, number>;
  averageAge: number;
  lastUpdated: Date;
  engagementScore: number;
  duplicateCount: number;
  brokenLinksCount: number;
  temporalPatterns: Array<{
    period: string;
    count: number;
  }>;
  topDomains: Array<{
    domain: string;
    count: number;
  }>;
  recommendations: string[];
}

export interface SmartPlaylistOptions {
  maxSuggestions: number;
  minConfidence: number;
  includeTemporalAnalysis: boolean;
  includeBehavioralAnalysis: boolean;
  includeSemanticAnalysis: boolean;
  autoCreateThreshold: number;
}

class SmartPlaylistService {
  private readonly DEFAULT_OPTIONS: SmartPlaylistOptions = {
    maxSuggestions: 5,
    minConfidence: 0.7,
    includeTemporalAnalysis: true,
    includeBehavioralAnalysis: true,
    includeSemanticAnalysis: true,
    autoCreateThreshold: 0.9
  };

  private readonly SMART_COLORS = [
    '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
    '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
  ];

  /**
   * Generate smart playlist suggestions based on bookmark analysis
   */
  async generateSmartSuggestions(
    bookmarks: Bookmark[],
    existingPlaylists: Playlist[],
    options: Partial<SmartPlaylistOptions> = {}
  ): Promise<SmartPlaylistSuggestion[]> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const suggestions: SmartPlaylistSuggestion[] = [];

    try {
      console.log('🧠 Generating smart playlist suggestions...');

      // Content-based suggestions
      if (opts.includeSemanticAnalysis) {
        const contentSuggestions = await this.generateContentBasedSuggestions(bookmarks, opts);
        suggestions.push(...contentSuggestions);
      }

      // Temporal suggestions
      if (opts.includeTemporalAnalysis) {
        const temporalSuggestions = this.generateTemporalSuggestions(bookmarks, opts);
        suggestions.push(...temporalSuggestions);
      }

      // Behavioral suggestions
      if (opts.includeBehavioralAnalysis) {
        const behavioralSuggestions = this.generateBehavioralSuggestions(bookmarks, opts);
        suggestions.push(...behavioralSuggestions);
      }

      // Filter out suggestions that conflict with existing playlists
      const filteredSuggestions = this.filterExistingPlaylists(suggestions, existingPlaylists);

      // Sort by confidence and return top suggestions
      return filteredSuggestions
        .filter(s => s.confidence >= opts.minConfidence)
        .sort((a, b) => b.confidence - a.confidence)
        .slice(0, opts.maxSuggestions);

    } catch (error) {
      console.error('Failed to generate smart suggestions:', error);
      return [];
    }
  }

  /**
   * Generate content-based playlist suggestions using AI analysis
   */
  private async generateContentBasedSuggestions(
    bookmarks: Bookmark[],
    options: SmartPlaylistOptions
  ): Promise<SmartPlaylistSuggestion[]> {
    const suggestions: SmartPlaylistSuggestion[] = [];

    // Group by content similarity
    const contentGroups = await this.analyzeContentSimilarity(bookmarks);

    for (const [topic, bookmarkIds] of Object.entries(contentGroups)) {
      if (bookmarkIds.length >= 3) {
        suggestions.push({
          id: `content-${topic.toLowerCase().replace(/\s+/g, '-')}`,
          name: `${topic} Collection`,
          description: `Curated collection of ${topic.toLowerCase()} resources`,
          color: this.getSmartColor(suggestions.length),
          confidence: this.calculateContentConfidence(bookmarkIds, bookmarks),
          bookmarkIds,
          reasoning: `Found ${bookmarkIds.length} bookmarks with similar ${topic.toLowerCase()} content`,
          category: 'content-based'
        });
      }
    }

    return suggestions;
  }

  /**
   * Generate temporal-based playlist suggestions
   */
  private generateTemporalSuggestions(
    bookmarks: Bookmark[],
    options: SmartPlaylistOptions
  ): SmartPlaylistSuggestion[] {
    const suggestions: SmartPlaylistSuggestion[] = [];
    const now = new Date();

    // Recent additions (last 7 days)
    const recentBookmarks = bookmarks.filter(b => {
      const addedDate = new Date(b.dateAdded);
      const daysDiff = (now.getTime() - addedDate.getTime()) / (1000 * 3600 * 24);
      return daysDiff <= 7;
    });

    if (recentBookmarks.length >= 3) {
      suggestions.push({
        id: 'temporal-recent',
        name: 'Recently Added',
        description: 'Your latest bookmark discoveries',
        color: this.getSmartColor(0),
        confidence: 0.85,
        bookmarkIds: recentBookmarks.map(b => b.id),
        reasoning: `${recentBookmarks.length} bookmarks added in the last week`,
        category: 'temporal'
      });
    }

    // Seasonal content
    const seasonalBookmarks = this.identifySeasonalContent(bookmarks);
    if (seasonalBookmarks.length >= 3) {
      const currentSeason = this.getCurrentSeason();
      suggestions.push({
        id: 'temporal-seasonal',
        name: `${currentSeason} Resources`,
        description: `Relevant content for ${currentSeason.toLowerCase()}`,
        color: this.getSeasonalColor(currentSeason),
        confidence: 0.75,
        bookmarkIds: seasonalBookmarks.map(b => b.id),
        reasoning: `Found ${seasonalBookmarks.length} ${currentSeason.toLowerCase()}-related bookmarks`,
        category: 'temporal'
      });
    }

    return suggestions;
  }

  /**
   * Generate behavioral-based playlist suggestions
   */
  private generateBehavioralSuggestions(
    bookmarks: Bookmark[],
    options: SmartPlaylistOptions
  ): SmartPlaylistSuggestion[] {
    const suggestions: SmartPlaylistSuggestion[] = [];

    // Frequently accessed domains
    const domainFrequency = this.analyzeDomainFrequency(bookmarks);
    const topDomains = Object.entries(domainFrequency)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3);

    topDomains.forEach(([domain, count], index) => {
      if (count >= 3) {
        const domainBookmarks = bookmarks.filter(b => {
          try {
            return new URL(b.url).hostname.includes(domain);
          } catch {
            return false;
          }
        });

        suggestions.push({
          id: `behavioral-domain-${domain}`,
          name: `${this.formatDomainName(domain)} Hub`,
          description: `Your ${this.formatDomainName(domain)} bookmark collection`,
          color: this.getSmartColor(index + 2),
          confidence: Math.min(0.9, 0.6 + (count / bookmarks.length)),
          bookmarkIds: domainBookmarks.map(b => b.id),
          reasoning: `${count} bookmarks from ${domain} - your most visited source`,
          category: 'behavioral'
        });
      }
    });

    return suggestions;
  }

  /**
   * Analyze content similarity using AI insights
   */
  private async analyzeContentSimilarity(bookmarks: Bookmark[]): Promise<Record<string, string[]>> {
    const contentGroups: Record<string, string[]> = {};

    // Use existing summary service for content analysis
    for (const bookmark of bookmarks) {
      try {
        const summary = await summaryService.generateSummary(bookmark);
        const topics = this.extractTopicsFromSummary(summary);
        
        topics.forEach(topic => {
          if (!contentGroups[topic]) {
            contentGroups[topic] = [];
          }
          contentGroups[topic].push(bookmark.id);
        });
      } catch (error) {
        // Fallback to tag and title analysis
        const fallbackTopics = this.extractTopicsFromMetadata(bookmark);
        fallbackTopics.forEach(topic => {
          if (!contentGroups[topic]) {
            contentGroups[topic] = [];
          }
          contentGroups[topic].push(bookmark.id);
        });
      }
    }

    return contentGroups;
  }

  /**
   * Extract topics from AI-generated summary
   */
  private extractTopicsFromSummary(summary: any): string[] {
    const topics: string[] = [];
    
    // Extract from key points
    if (summary.keyPoints) {
      summary.keyPoints.forEach((point: string) => {
        const extractedTopics = this.extractKeywords(point);
        topics.push(...extractedTopics);
      });
    }

    // Extract from content type
    if (summary.contentType) {
      topics.push(summary.contentType);
    }

    return Array.from(new Set(topics)); // Remove duplicates
  }

  /**
   * Extract topics from bookmark metadata (fallback)
   */
  private extractTopicsFromMetadata(bookmark: Bookmark): string[] {
    const topics: string[] = [];
    
    // From tags
    if (bookmark.tags) {
      topics.push(...bookmark.tags);
    }

    // From title keywords
    const titleKeywords = this.extractKeywords(bookmark.title);
    topics.push(...titleKeywords);

    // From URL domain
    try {
      const domain = new URL(bookmark.url).hostname;
      const domainTopic = this.getDomainTopic(domain);
      if (domainTopic) {
        topics.push(domainTopic);
      }
    } catch {}

    return Array.from(new Set(topics));
  }

  /**
   * Extract keywords from text
   */
  private extractKeywords(text: string): string[] {
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should']);
    
    return text
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word))
      .slice(0, 3); // Top 3 keywords
  }

  /**
   * Get topic from domain
   */
  private getDomainTopic(domain: string): string | null {
    const domainTopics: Record<string, string> = {
      'github.com': 'Development',
      'stackoverflow.com': 'Programming',
      'youtube.com': 'Video',
      'medium.com': 'Articles',
      'dev.to': 'Development',
      'codepen.io': 'Frontend',
      'dribbble.com': 'Design',
      'behance.net': 'Design',
      'figma.com': 'Design'
    };

    return domainTopics[domain] || null;
  }

  /**
   * Calculate confidence score for content-based suggestions
   */
  private calculateContentConfidence(bookmarkIds: string[], allBookmarks: Bookmark[]): number {
    const groupSize = bookmarkIds.length;
    const totalBookmarks = allBookmarks.length;
    
    // Base confidence on group size and relevance
    let confidence = Math.min(0.9, 0.5 + (groupSize / totalBookmarks) * 2);
    
    // Boost confidence if bookmarks have rich metadata
    const groupBookmarks = allBookmarks.filter(b => bookmarkIds.includes(b.id));
    const avgTagCount = groupBookmarks.reduce((sum, b) => sum + (b.tags?.length || 0), 0) / groupSize;
    
    if (avgTagCount > 2) {
      confidence += 0.1;
    }
    
    return Math.min(0.95, confidence);
  }

  /**
   * Analyze domain frequency
   */
  private analyzeDomainFrequency(bookmarks: Bookmark[]): Record<string, number> {
    const frequency: Record<string, number> = {};
    
    bookmarks.forEach(bookmark => {
      try {
        const domain = new URL(bookmark.url).hostname.replace('www.', '');
        frequency[domain] = (frequency[domain] || 0) + 1;
      } catch {}
    });
    
    return frequency;
  }

  /**
   * Identify seasonal content
   */
  private identifySeasonalContent(bookmarks: Bookmark[]): Bookmark[] {
    const currentSeason = this.getCurrentSeason();
    const seasonalKeywords = this.getSeasonalKeywords(currentSeason);
    
    return bookmarks.filter(bookmark => {
      const text = `${bookmark.title} ${bookmark.description || ''} ${bookmark.tags?.join(' ') || ''}`.toLowerCase();
      return seasonalKeywords.some(keyword => text.includes(keyword));
    });
  }

  /**
   * Get current season
   */
  private getCurrentSeason(): string {
    const month = new Date().getMonth();
    if (month >= 2 && month <= 4) return 'Spring';
    if (month >= 5 && month <= 7) return 'Summer';
    if (month >= 8 && month <= 10) return 'Fall';
    return 'Winter';
  }

  /**
   * Get seasonal keywords
   */
  private getSeasonalKeywords(season: string): string[] {
    const keywords: Record<string, string[]> = {
      'Spring': ['spring', 'easter', 'garden', 'fresh', 'new'],
      'Summer': ['summer', 'vacation', 'travel', 'beach', 'outdoor'],
      'Fall': ['fall', 'autumn', 'halloween', 'thanksgiving', 'harvest'],
      'Winter': ['winter', 'christmas', 'holiday', 'snow', 'cozy']
    };
    
    return keywords[season] || [];
  }

  /**
   * Get seasonal color
   */
  private getSeasonalColor(season: string): string {
    const colors: Record<string, string> = {
      'Spring': '#96CEB4',
      'Summer': '#FFEAA7',
      'Fall': '#E17055',
      'Winter': '#74B9FF'
    };
    
    return colors[season] || this.SMART_COLORS[0];
  }

  /**
   * Format domain name for display
   */
  private formatDomainName(domain: string): string {
    return domain
      .split('.')
      .map(part => part.charAt(0).toUpperCase() + part.slice(1))
      .join(' ');
  }

  /**
   * Get smart color for suggestions
   */
  private getSmartColor(index: number): string {
    return this.SMART_COLORS[index % this.SMART_COLORS.length];
  }

  /**
   * Filter suggestions that conflict with existing playlists
   */
  private filterExistingPlaylists(
    suggestions: SmartPlaylistSuggestion[],
    existingPlaylists: Playlist[]
  ): SmartPlaylistSuggestion[] {
    const existingNames = new Set(existingPlaylists.map(p => p.name.toLowerCase()));
    
    return suggestions.filter(suggestion => {
      const nameExists = existingNames.has(suggestion.name.toLowerCase());
      if (nameExists) {
        console.log(`Filtering out suggestion "${suggestion.name}" - playlist already exists`);
      }
      return !nameExists;
    });
  }

  /**
   * Generate playlist analytics
   */
  async generatePlaylistAnalytics(
    playlist: Playlist,
    bookmarks: Bookmark[]
  ): Promise<PlaylistAnalytics> {
    const playlistBookmarks = bookmarks.filter(b => playlist.bookmarkIds.includes(b.id));
    
    const contentTypes: Record<string, number> = {};
    const domains: Record<string, number> = {};
    const tags: Record<string, number> = {};
    
    let totalAge = 0;
    let duplicateCount = 0;
    let brokenLinksCount = 0;
    
    for (const bookmark of playlistBookmarks) {
      // Content type analysis
      const contentType = this.getContentType(bookmark);
      contentTypes[contentType] = (contentTypes[contentType] || 0) + 1;
      
      // Domain analysis
      try {
        const domain = new URL(bookmark.url).hostname;
        domains[domain] = (domains[domain] || 0) + 1;
      } catch {}
      
      // Tag analysis
      if (bookmark.tags) {
        bookmark.tags.forEach(tag => {
          tags[tag] = (tags[tag] || 0) + 1;
        });
      }
      
      // Age calculation
      const age = Date.now() - new Date(bookmark.dateAdded).getTime();
      totalAge += age;
      
      // Check for duplicates (simplified)
      const duplicates = playlistBookmarks.filter(b => b.url === bookmark.url);
      if (duplicates.length > 1) {
        duplicateCount++;
      }
    }
    
    const averageAge = playlistBookmarks.length > 0 ? totalAge / playlistBookmarks.length : 0;
    const engagementScore = this.calculateEngagementScore(playlistBookmarks);
    
    // Generate temporal patterns
    const temporalPatterns = this.generateTemporalPatterns(playlistBookmarks);

    // Generate top domains
    const topDomains = Object.entries(domains)
      .map(([domain, count]) => ({ domain, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    // Generate recommendations based on analysis
    const recommendations: string[] = [];

    if (duplicateCount > 0) {
      recommendations.push(`Remove ${duplicateCount} duplicate bookmarks to clean up the playlist`);
    }

    if (brokenLinksCount > 0) {
      recommendations.push(`Check ${brokenLinksCount} potentially broken links`);
    }

    if (playlistBookmarks.length > 50) {
      recommendations.push('Consider splitting this large playlist into smaller, more focused collections');
    }

    if (engagementScore < 0.3) {
      recommendations.push('Low engagement detected - consider reviewing and updating bookmark relevance');
    }

    return {
      totalBookmarks: playlistBookmarks.length,
      contentTypes,
      domains,
      tags,
      averageAge: averageAge / (1000 * 60 * 60 * 24), // Convert to days
      lastUpdated: new Date(playlist.dateCreated),
      engagementScore,
      duplicateCount,
      brokenLinksCount,
      temporalPatterns,
      topDomains,
      recommendations
    };
  }

  /**
   * Generate temporal patterns for bookmarks
   */
  private generateTemporalPatterns(bookmarks: Bookmark[]): Array<{ period: string; count: number }> {
    const patterns: Record<string, number> = {};

    bookmarks.forEach(bookmark => {
      const date = new Date(bookmark.dateAdded);
      const month = date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      patterns[month] = (patterns[month] || 0) + 1;
    });

    return Object.entries(patterns)
      .map(([period, count]) => ({ period, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 12); // Last 12 months
  }

  /**
   * Get content type from bookmark
   */
  private getContentType(bookmark: Bookmark): string {
    const url = bookmark.url.toLowerCase();
    
    if (url.includes('youtube.com') || url.includes('youtu.be')) return 'Video';
    if (url.includes('github.com')) return 'Code Repository';
    if (url.includes('medium.com') || url.includes('blog')) return 'Article';
    if (url.includes('docs.') || url.includes('/docs/')) return 'Documentation';
    if (url.includes('stackoverflow.com')) return 'Q&A';
    
    return 'Web Page';
  }

  /**
   * Calculate engagement score based on bookmark metadata richness
   */
  private calculateEngagementScore(bookmarks: Bookmark[]): number {
    if (bookmarks.length === 0) return 0;
    
    let totalScore = 0;
    
    bookmarks.forEach(bookmark => {
      let score = 0;
      
      // Has description
      if (bookmark.description && bookmark.description.length > 10) score += 20;
      
      // Has tags
      if (bookmark.tags && bookmark.tags.length > 0) score += 15;
      
      // Has summary
      if (bookmark.summary) score += 25;
      
      // Recently added (within 30 days)
      const daysSinceAdded = (Date.now() - new Date(bookmark.dateAdded).getTime()) / (1000 * 60 * 60 * 24);
      if (daysSinceAdded <= 30) score += 10;
      
      // Has favicon
      if (bookmark.favicon) score += 5;
      
      totalScore += Math.min(100, score);
    });
    
    return Math.round(totalScore / bookmarks.length);
  }

  /**
   * Auto-create playlists based on high-confidence suggestions
   */
  async autoCreatePlaylists(
    bookmarks: Bookmark[],
    existingPlaylists: Playlist[],
    options: Partial<SmartPlaylistOptions> = {}
  ): Promise<SmartPlaylistSuggestion[]> {
    const opts = { ...this.DEFAULT_OPTIONS, ...options };
    const suggestions = await this.generateSmartSuggestions(bookmarks, existingPlaylists, opts);
    
    return suggestions.filter(s => s.confidence >= opts.autoCreateThreshold);
  }
}

export const smartPlaylistService = new SmartPlaylistService();
export default smartPlaylistService;