# WSL Setup Guide for Bookmark Manager Pro

## Overview

This guide provides step-by-step instructions for setting up the development environment in Windows Subsystem for Linux (WSL) for the Bookmark Manager Pro project.

## 🚨 Critical Prerequisites

### PATH Environment Variable Fix

**⚠️ MANDATORY FIRST STEP** - This MUST be run in every WSL session before any other commands:

```bash
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
```

**Why this is required:**
- WSL sometimes has PATH issues that prevent `sudo` and other system commands from working
- This ensures all system binaries are accessible
- Must be run before ANY `sudo`, `npm`, or system commands

### Making PATH Fix Permanent

To avoid running the export command every time:

```bash
# Add to your bashrc file
echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
source ~/.bashrc
```

## Node.js Installation in WSL

### Step 1: Fix PATH (Required)
```bash
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
```

### Step 2: Verify sudo works
```bash
sudo --version
```

### Step 3: Update package manager
```bash
sudo apt update
```

### Step 4: Install Node.js (Recommended Method)
```bash
# Using snap (recommended)
sudo snap install node --channel=20/stable --classic
```

### Step 5: Verify installation
```bash
node --version
npm --version
```

## Project Setup in WSL

### Step 1: Navigate to project (from WSL)
```bash
# Windows drives are mounted under /mnt/
cd /mnt/c/Nexicon/Bookmark-Manager-Pro
```

### Step 2: Set PATH (if not permanent)
```bash
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
```

### Step 3: Install dependencies
```bash
npm install
```

### Step 4: Set up environment
```bash
# Create .env.local file
cp .env.example .env.local
# Edit with your Gemini API key
```

### Step 5: Start development
```bash
npm run dev
```

## Development Workflow in WSL

### Every WSL Session Checklist
1. ✅ Run PATH export command (if not permanent)
2. ✅ Navigate to project directory
3. ✅ Verify Node.js/npm work
4. ✅ Run development commands

### Common Commands (All require PATH fix first)
```bash
# Development
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"  # FIRST!
npm run dev              # Start development server
npm run build            # Production build
npm run test             # Run tests
npm run lint             # Code linting
```

## Troubleshooting

### "Command not found" errors
**Problem:** `sudo: command not found` or similar
**Solution:** Run the PATH export command:
```bash
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
```

### Node.js installation issues
**Problem:** Package dependency conflicts
**Solution:** Try alternative installation methods:
```bash
# Method 1: Clean package cache
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
sudo apt clean
sudo apt update
sudo apt install nodejs npm

# Method 2: Use NodeSource repository
curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash -
sudo apt-get install -y nodejs

# Method 3: Use NVM
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.bashrc
nvm install --lts
nvm use --lts
```

### Terminal timeout issues
**Problem:** Commands hang or timeout
**Solution:** Use emergency alternatives:
```bash
# Stop hanging command: Ctrl+C
# Then try:
export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
npm run lint         # Quick syntax check
npm run build:check  # Development build
npm run dev          # Real-time errors
```

## Best Practices

1. **Always set PATH first** - Before any sudo or npm commands
2. **Make PATH permanent** - Add to ~/.bashrc to avoid repetition
3. **Use WSL for all development** - Consistent Linux environment
4. **Keep sessions active** - Avoid frequent WSL restarts
5. **Monitor for timeouts** - Stop commands after 20 seconds of inactivity

## Integration with VS Code

### WSL Extension Setup
1. Install "WSL" extension in VS Code
2. Open project in WSL: `code .` from WSL terminal
3. Ensure TypeScript extension works in WSL context

### Terminal Configuration
- Set WSL as default terminal in VS Code
- Add PATH export to terminal startup if needed

## Emergency Protocols

If WSL becomes unresponsive:
1. **Stop current command:** Ctrl+C
2. **Restart WSL:** `wsl --shutdown` (from Windows)
3. **Re-export PATH:** First command in new session
4. **Use alternatives:** Native Windows Node.js as fallback

---

**Remember:** The PATH export command is the foundation of all WSL operations in this project. When in doubt, run it first!