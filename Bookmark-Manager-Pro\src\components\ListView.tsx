import { ExternalLink, Globe, Star, Tag } from 'lucide-react'
import React, { useState } from 'react'
import { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'

interface ListViewProps {
  searchQuery?: string
  selectedCollection?: string
}

type SortField = 'title' | 'url' | 'collection' | 'dateAdded' | 'visits' | 'favorite' | 'recent'
type SortDirection = 'asc' | 'desc'

export const ListView: React.FC<ListViewProps> = ({ searchQuery, selectedCollection }) => {
  const { filteredBookmarks, toggleFavorite, isSelectMode, toggleBookmarkSelection, clearRecentStatus, incrementVisitCount } = useBookmarks()

  // Optimized localization - memory efficient
  const [locale, setLocale] = React.useState<'en-US' | 'en-GB'>(() =>
    localStorage.getItem('bookmark-manager-locale') as 'en-US' | 'en-GB' || 'en-US'
  )

  // Optimized event listener
  React.useEffect(() => {
    const handleLocaleChange = (event: CustomEvent) => {
      setLocale(event.detail as 'en-US' | 'en-GB')
    }

    window.addEventListener('localeChanged', handleLocaleChange as EventListener)
    return () => window.removeEventListener('localeChanged', handleLocaleChange as EventListener)
  }, [])

  // Memoized translation function
  const t = React.useCallback((key: string) => {
    const translations = {
      'en-US': { 'filter.favorites': 'Favorites', 'bookmark.favorited': 'favorited' },
      'en-GB': { 'filter.favorites': 'Favourites', 'bookmark.favorited': 'favourited' }
    }
    return (translations[locale] as any)?.[key] || key
  }, [locale])

  // Sorting state
  const [sortField, setSortField] = useState<SortField>('dateAdded')
  const [sortDirection, setSortDirection] = useState<SortDirection>('desc')

  // Handle sort column click
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortDirection('asc')
    }
  }

  // Sort and filter bookmarks based on current sort settings
  const sortedBookmarks = React.useMemo(() => {
    // First filter based on sort type
    let bookmarksToSort = [...filteredBookmarks]

    if (sortField === 'visits') {
      // Only show bookmarks with visits > 0
      bookmarksToSort = bookmarksToSort.filter(bookmark => (bookmark.visits || 0) > 0)
    } else if (sortField === 'favorite') {
      // Only show favorited bookmarks
      bookmarksToSort = bookmarksToSort.filter(bookmark => bookmark.isFavorite)
    } else if (sortField === 'recent') {
      // Only show recently added bookmarks (dragged/dropped)
      bookmarksToSort = bookmarksToSort.filter(bookmark => bookmark.isRecentlyAdded)
    }

    // Then sort the filtered results
    const sorted = bookmarksToSort.sort((a, b) => {
      let aValue: any
      let bValue: any

      switch (sortField) {
        case 'title':
          aValue = a.title.toLowerCase()
          bValue = b.title.toLowerCase()
          break
        case 'url':
          aValue = a.url.toLowerCase()
          bValue = b.url.toLowerCase()
          break
        case 'collection':
          aValue = a.collection.toLowerCase()
          bValue = b.collection.toLowerCase()
          break
        case 'dateAdded':
          aValue = new Date(a.dateAdded).getTime()
          bValue = new Date(b.dateAdded).getTime()
          break
        case 'visits':
          aValue = a.visits || 0
          bValue = b.visits || 0
          break
        case 'favorite':
          aValue = a.isFavorite ? 1 : 0
          bValue = b.isFavorite ? 1 : 0
          break
        case 'recent':
          // Sort by date added for recently added items (newest first by default)
          aValue = new Date(a.dateAdded).getTime()
          bValue = new Date(b.dateAdded).getTime()
          break
        default:
          return 0
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1
      return 0
    })

    return sorted
  }, [filteredBookmarks, sortField, sortDirection])

  // Render sort icon
  const renderSortIcon = (field: SortField) => {
    if (sortField !== field) return null
    return sortDirection === 'asc' ?
      <span style={{ marginLeft: '4px', fontSize: '12px' }}>↑</span> :
      <span style={{ marginLeft: '4px', fontSize: '12px' }}>↓</span>
  }

  // Smart domain extraction logic (matches BookmarkCard.tsx)
  const extractSmartDomain = (url: string) => {
    try {
      const urlObj = new URL(url)
      const hostname = urlObj.hostname.replace('www.', '')

      // For very long URLs like dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/
      // Show domain + truncated path to prevent layout issues
      if (urlObj.pathname && urlObj.pathname.length > 20) {
        const pathParts = urlObj.pathname.split('/').filter(Boolean)
        if (pathParts.length > 0) {
          const firstPath = pathParts[0]
          if (firstPath.length > 15) {
            return `${hostname}/${firstPath.substring(0, 15)}...`
          }
          return `${hostname}/${firstPath}`
        }
      }

      return hostname
    } catch {
      // Fallback for invalid URLs - truncate if too long
      const truncated = url.length > 30 ? url.substring(0, 30) + '...' : url
      return truncated
    }
  }

  const handleBookmarkClick = (bookmark: Bookmark) => {
    if (isSelectMode) {
      toggleBookmarkSelection(bookmark.id)
    } else {
      // Increment visit count
      incrementVisitCount(bookmark.id)

      // Clear recently added status when user visits the bookmark
      if (bookmark.isRecentlyAdded) {
        clearRecentStatus(bookmark.id)
      }
      window.open(bookmark.url, '_blank', 'noopener,noreferrer')
    }
  }

  const handleFavoriteClick = (e: React.MouseEvent, bookmark: Bookmark) => {
    e.stopPropagation()
    toggleFavorite(bookmark.id)
  }

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString()
    } catch {
      return 'Unknown'
    }
  }

  // Emergency DOM protection: Aggressive limiting for performance
  const [emergencyMode, setEmergencyMode] = useState(false)
  const maxRenderItems = emergencyMode ? 10 : 25 // Even more aggressive in emergency mode
  const displayBookmarks = sortedBookmarks.slice(0, maxRenderItems)
  const hasMoreItems = sortedBookmarks.length > maxRenderItems

  // Emergency DOM cleanup function
  const triggerEmergencyCleanup = React.useCallback(() => {
    console.warn('🚨 EMERGENCY CLEANUP: Activating ultra-aggressive DOM protection')
    setEmergencyMode(true)

    // Force garbage collection if available
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc()
    }

    // Reset emergency mode after 5 seconds
    setTimeout(() => {
      setEmergencyMode(false)
      console.log('✅ Emergency mode deactivated')
    }, 5000)
  }, [])

  // Log performance metrics and DOM node count
  React.useEffect(() => {
    const nodeCount = document.querySelectorAll('*').length
    console.log(`📊 ListView Performance: Rendering ${displayBookmarks.length} of ${sortedBookmarks.length} bookmarks`)
    console.log(`🔍 Total DOM nodes: ${nodeCount.toLocaleString()}`)

    if (nodeCount > 80000 && !emergencyMode) {
      console.error(`🚨 CRITICAL: DOM node count is ${nodeCount.toLocaleString()} - triggering emergency cleanup!`)
      triggerEmergencyCleanup()
    } else if (nodeCount > 50000) {
      console.warn(`⚠️ WARNING: DOM node count is ${nodeCount.toLocaleString()} - performance may be affected`)
    }

    if (hasMoreItems) {
      console.warn(`⚠️ ListView: Limited to ${maxRenderItems} items for performance (${sortedBookmarks.length - maxRenderItems} hidden)`)
    }
  }, [displayBookmarks.length, sortedBookmarks.length, hasMoreItems, emergencyMode, triggerEmergencyCleanup])

  return (
    <div className="list-view">
      {/* Sort Controls - Styled like Import Panel */}
      <div className="import-panel" style={{
        width: '100%',
        borderLeft: 'none',
        borderRight: 'none',
        borderTop: 'none',
        borderRadius: '0',
        marginBottom: '0',
        backgroundColor: 'var(--secondary-bg)'
      }}>
        <div className="import-header" style={{ borderBottom: '1px solid var(--border-color)' }}>
          <h3 className="import-title" style={{ fontSize: '16px' }}>Sort & Filter</h3>
          <span style={{ color: 'var(--text-secondary)', fontSize: '14px' }}>
            {hasMoreItems ? `${displayBookmarks.length} of ${sortedBookmarks.length}` : sortedBookmarks.length}
            {sortField === 'visits' ? ' visited' :
              sortField === 'favorite' ? ` ${t('bookmark.favorited')}` :
                sortField === 'recent' ? ' recently added' : ''}
            bookmark{sortedBookmarks.length !== 1 ? 's' : ''}
            {(sortField === 'visits' || sortField === 'favorite' || sortField === 'recent') &&
              ` (of ${filteredBookmarks.length} total)`
            }
            {hasMoreItems && ' - Limited for performance'}
          </span>
        </div>

        <div className="import-content" style={{ padding: 'var(--padding-lg)' }}>
          <div style={{ marginBottom: 'var(--spacing-md)' }}>
            <label style={{
              display: 'block',
              marginBottom: 'var(--spacing-sm)',
              fontSize: '14px',
              fontWeight: '500',
              color: 'var(--text-primary)'
            }}>
              Sort by:
            </label>

            <div style={{
              display: 'grid',
              gridTemplateColumns: 'repeat(auto-fit, minmax(120px, 1fr))',
              gap: 'var(--spacing-sm)'
            }}>
              <button
                onClick={() => handleSort('title')}
                className={`btn-secondary ${sortField === 'title' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'title' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'title' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'title' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
              >
                Title {sortField === 'title' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>

              <button
                onClick={() => handleSort('collection')}
                className={`btn-secondary ${sortField === 'collection' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'collection' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'collection' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'collection' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
              >
                Collection {sortField === 'collection' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>

              <button
                onClick={() => handleSort('dateAdded')}
                className={`btn-secondary ${sortField === 'dateAdded' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'dateAdded' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'dateAdded' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'dateAdded' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
              >
                Date {sortField === 'dateAdded' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>

              <button
                onClick={() => handleSort('visits')}
                className={`btn-secondary ${sortField === 'visits' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'visits' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'visits' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'visits' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
                title="Show only bookmarks with visits > 0"
              >
                Visited {sortField === 'visits' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>

              <button
                onClick={() => handleSort('favorite')}
                className={`btn-secondary ${sortField === 'favorite' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'favorite' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'favorite' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'favorite' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
                title="Show only favorited bookmarks"
              >
                {t('filter.favorites')} {sortField === 'favorite' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>

              <button
                onClick={() => handleSort('recent')}
                className={`btn-secondary ${sortField === 'recent' ? 'active' : ''}`}
                style={{
                  backgroundColor: sortField === 'recent' ? 'var(--accent-color)' : 'var(--tertiary-bg)',
                  color: sortField === 'recent' ? 'white' : 'var(--text-secondary)',
                  borderColor: sortField === 'recent' ? 'var(--accent-color)' : 'var(--border-color)',
                  fontSize: '13px',
                  padding: '8px 12px',
                  justifyContent: 'center'
                }}
                title="Show only recently added bookmarks (dragged & dropped)"
              >
                Recent {sortField === 'recent' && (sortDirection === 'asc' ? '↑' : '↓')}
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="list-container">
        <div className="list-table">
          <div className="list-table-header">
            <div className="list-col-favicon"></div>
            <div
              className="list-col-title sortable-header"
              onClick={() => handleSort('title')}
              style={{ cursor: 'pointer', userSelect: 'none' }}
            >
              Title{renderSortIcon('title')}
            </div>
            <div
              className="list-col-url sortable-header"
              onClick={() => handleSort('url')}
              style={{ cursor: 'pointer', userSelect: 'none' }}
            >
              URL{renderSortIcon('url')}
            </div>
            <div
              className="list-col-collection sortable-header"
              onClick={() => handleSort('collection')}
              style={{ cursor: 'pointer', userSelect: 'none' }}
            >
              Collection{renderSortIcon('collection')}
            </div>
            <div
              className="list-col-date sortable-header"
              onClick={() => handleSort('dateAdded')}
              style={{ cursor: 'pointer', userSelect: 'none' }}
            >
              Date Added{renderSortIcon('dateAdded')}
            </div>
            <div className="list-col-actions">Actions</div>
          </div>

          <div className="list-table-body">
            {displayBookmarks.map((bookmark) => (
              <div
                key={bookmark.id}
                className={`list-row ${bookmark.selected ? 'selected' : ''}`}
                data-testid="bookmark-item"
                onClick={() => handleBookmarkClick(bookmark)}
              >
                {/* Selection indicator */}
                {isSelectMode && (
                  <div className="list-col-select">
                    <div className={`selection-checkbox ${bookmark.selected ? 'checked' : ''}`}>
                      {bookmark.selected && <div className="checkbox-mark" />}
                    </div>
                  </div>
                )}

                <div className="list-col-favicon">
                  {bookmark.favicon ? (
                    <img
                      src={bookmark.favicon}
                      alt=""
                      className="list-favicon"
                      onError={(e) => {
                        e.currentTarget.style.display = 'none'
                        e.currentTarget.nextElementSibling!.style.display = 'block'
                      }}
                    />
                  ) : null}
                  <Globe size={16} className="list-fallback-icon" style={{ display: bookmark.favicon ? 'none' : 'block' }} />
                </div>

                <div className="list-col-title">
                  <div className="list-title-content">
                    <span className="list-bookmark-title">{bookmark.title}</span>
                    {bookmark.description && (
                      <span className="list-bookmark-description">{bookmark.description}</span>
                    )}
                    {bookmark.tags && bookmark.tags.length > 0 && (
                      <div className="list-tags">
                        <Tag size={12} />
                        {bookmark.tags.slice(0, 3).map((tag, index) => (
                          <span key={index} className="list-tag">{tag}</span>
                        ))}
                        {bookmark.tags.length > 3 && (
                          <span className="list-tag-more">+{bookmark.tags.length - 3}</span>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="list-col-url">
                  <span className="list-url" title={bookmark.url}>
                    {extractSmartDomain(bookmark.url)}
                  </span>
                </div>

                <div className="list-col-collection">
                  <span className="list-collection">{bookmark.collection}</span>
                </div>

                <div className="list-col-date">
                  <span className="list-date">{formatDate(bookmark.dateAdded)}</span>
                </div>

                <div className="list-col-actions">
                  <button
                    data-testid="bookmark-star"
                    className={`list-action-btn favorite ${bookmark.isFavorite ? 'active starred' : ''}`}
                    onClick={(e) => handleFavoriteClick(e, bookmark)}
                    title={bookmark.isFavorite ? 'Remove from favorites' : 'Add to favorites'}
                  >
                    <Star size={16} fill={bookmark.isFavorite ? 'currentColor' : 'none'} />
                  </button>
                  <button
                    className="list-action-btn external"
                    onClick={(e) => {
                      e.stopPropagation()
                      window.open(bookmark.url, '_blank')
                    }}
                    title="Open in new tab"
                  >
                    <ExternalLink size={16} />
                  </button>
                </div>
              </div>
            ))}
          </div>

          {/* Performance Warning */}
          {(hasMoreItems || emergencyMode) && (
            <div style={{
              padding: 'var(--padding-lg)',
              backgroundColor: emergencyMode ? '#fef2f2' : '#fef3c7',
              border: `1px solid ${emergencyMode ? '#ef4444' : '#f59e0b'}`,
              borderRadius: 'var(--radius-lg)',
              margin: 'var(--spacing-md)',
              textAlign: 'center'
            }}>
              <p style={{
                margin: 0,
                color: emergencyMode ? '#dc2626' : '#92400e',
                fontSize: '14px',
                fontWeight: '500'
              }}>
                {emergencyMode ? '🚨 EMERGENCY MODE: ' : '⚡ Performance Protection: '}
                Showing first {maxRenderItems} of {sortedBookmarks.length} bookmarks
              </p>
              <p style={{
                margin: '4px 0 0 0',
                color: emergencyMode ? '#dc2626' : '#92400e',
                fontSize: '12px'
              }}>
                {emergencyMode
                  ? 'Ultra-aggressive DOM protection active due to high node count'
                  : 'Use filters or search to narrow results for better performance'
                }
              </p>
            </div>
          )}
        </div>

        {filteredBookmarks.length === 0 && (
          <div className="list-empty">
            <Globe size={48} className="list-empty-icon" />
            <h3 className="list-empty-title">No bookmarks found</h3>
            <p className="list-empty-description">
              {searchQuery
                ? `No bookmarks match "${searchQuery}"`
                : 'No bookmarks in this collection'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
