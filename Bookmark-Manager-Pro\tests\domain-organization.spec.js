/**
 * Domain Organization Feature - Comprehensive Test Suite
 * Dr<PERSON> - World-Renowned Test Expert
 * 
 * This test suite validates the Domain Organization feature across multiple browsers
 * and scenarios, ensuring robust functionality and performance.
 */

import { test, expect } from '@playwright/test';

// Test configuration
const TEST_CONFIG = {
  baseURL: 'http://localhost:5173',
  timeout: 30000,
  testDataPath: './test-data/test-domain-organization-bookmarks.json'
};

// Test data helper
async function loadTestData() {
  try {
    const fs = await import('fs');
    const path = await import('path');
    const testDataPath = path.resolve(TEST_CONFIG.testDataPath);
    const data = fs.readFileSync(testDataPath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.warn('Test data file not found, using fallback data');
    return {
      bookmarks: [
        { url: 'https://github.com/microsoft/vscode', title: 'VS Code', domain: 'github.com' },
        { url: 'https://docs.github.com/en', title: 'GitHub Docs', domain: 'docs.github.com' },
        { url: 'https://stackoverflow.com/questions', title: 'Stack Overflow', domain: 'stackoverflow.com' }
      ]
    };
  }
}

test.describe('Domain Organization Feature', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto(TEST_CONFIG.baseURL);
    await page.waitForLoadState('networkidle');
  });

  test.describe('Foundation Testing - Core Functionality', () => {
    test('should display domain organization panel', async ({ page }) => {
      // Navigate to domain organization view
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Verify domain panel is visible
      const domainPanel = page.locator('.domain-panel, [data-testid="domain-panel"]');
      await expect(domainPanel).toBeVisible({ timeout: 10000 });
      
      // Verify basic UI elements
      await expect(page.locator('.domain-group, .domain-item')).toHaveCount({ min: 1 });
    });

    test('should group bookmarks by domain correctly', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Wait for domain grouping to complete
      await page.waitForSelector('.domain-group, .domain-item', { timeout: 10000 });
      
      // Verify domain groups exist
      const domainGroups = page.locator('.domain-group, .domain-item');
      const groupCount = await domainGroups.count();
      expect(groupCount).toBeGreaterThan(0);
      
      // Verify each group has domain label
      for (let i = 0; i < Math.min(groupCount, 5); i++) {
        const group = domainGroups.nth(i);
        await expect(group.locator('.domain-name, .domain-label, h3, h4')).toBeVisible();
      }
    });

    test('should handle empty bookmark state gracefully', async ({ page }) => {
      // Clear any existing bookmarks (if possible)
      await page.goto(TEST_CONFIG.baseURL);
      
      // Navigate to domain view
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Should show appropriate empty state or handle gracefully
      const emptyState = page.locator('.empty-state, .no-bookmarks, .no-domains');
      const domainGroups = page.locator('.domain-group, .domain-item');
      
      // Either show empty state or have domain groups
      const hasEmptyState = await emptyState.isVisible();
      const hasGroups = await domainGroups.count() > 0;
      
      expect(hasEmptyState || hasGroups).toBeTruthy();
    });
  });

  test.describe('Advanced Feature Testing', () => {
    test('should support domain configuration options', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Look for configuration/settings options
      const configButton = page.locator('.config-button, .settings-button, [data-testid="domain-config"]');
      const sortOptions = page.locator('.sort-options, .domain-sort');
      const viewOptions = page.locator('.view-options, .domain-view');
      
      // At least one configuration option should be available
      const hasConfig = await configButton.isVisible();
      const hasSort = await sortOptions.isVisible();
      const hasView = await viewOptions.isVisible();
      
      expect(hasConfig || hasSort || hasView).toBeTruthy();
    });

    test('should handle subdomain grouping intelligently', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Wait for domain processing
      await page.waitForTimeout(2000);
      
      // Look for evidence of intelligent subdomain handling
      const domainGroups = page.locator('.domain-group, .domain-item');
      const groupCount = await domainGroups.count();
      
      if (groupCount > 0) {
        // Check if subdomains are properly grouped or separated
        const firstGroup = domainGroups.first();
        const domainLabel = await firstGroup.locator('.domain-name, .domain-label, h3, h4').textContent();
        
        expect(domainLabel).toBeTruthy();
        expect(domainLabel.length).toBeGreaterThan(0);
      }
    });

    test('should provide search and filter capabilities', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Look for search/filter functionality
      const searchInput = page.locator('input[type="search"], input[placeholder*="search"], .search-input');
      const filterButton = page.locator('.filter-button, .domain-filter, [data-testid="filter"]');
      
      // At least search or filter should be available
      const hasSearch = await searchInput.isVisible();
      const hasFilter = await filterButton.isVisible();
      
      expect(hasSearch || hasFilter).toBeTruthy();
    });
  });

  test.describe('Performance & Edge Cases', () => {
    test('should handle large datasets efficiently', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Measure initial load time
      const startTime = Date.now();
      await page.waitForSelector('.domain-group, .domain-item', { timeout: 15000 });
      const loadTime = Date.now() - startTime;
      
      // Should load within reasonable time (15 seconds max)
      expect(loadTime).toBeLessThan(15000);
      
      // Verify UI remains responsive
      const domainGroups = page.locator('.domain-group, .domain-item');
      await expect(domainGroups.first()).toBeVisible();
    });

    test('should handle international domains correctly', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Wait for domain processing
      await page.waitForTimeout(2000);
      
      // Verify the system handles domains gracefully
      const domainGroups = page.locator('.domain-group, .domain-item');
      const groupCount = await domainGroups.count();
      
      // Should not crash or show errors
      const errorMessages = page.locator('.error, .error-message, [data-testid="error"]');
      await expect(errorMessages).toHaveCount(0);
      
      // Should show some content or appropriate empty state
      expect(groupCount >= 0).toBeTruthy();
    });

    test('should maintain accessibility standards', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Check for basic accessibility features
      const domainPanel = page.locator('.domain-panel, [data-testid="domain-panel"]');
      await expect(domainPanel).toBeVisible();
      
      // Verify keyboard navigation works
      await page.keyboard.press('Tab');
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Check for ARIA labels or semantic HTML
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const buttons = page.locator('button');
      const links = page.locator('a');
      
      // Should have proper semantic structure
      const hasHeadings = await headings.count() > 0;
      const hasButtons = await buttons.count() > 0;
      const hasLinks = await links.count() > 0;
      
      expect(hasHeadings || hasButtons || hasLinks).toBeTruthy();
    });

    test('should handle malformed URLs gracefully', async ({ page }) => {
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      // Wait for processing
      await page.waitForTimeout(2000);
      
      // Should not show JavaScript errors
      const errors = [];
      page.on('pageerror', error => errors.push(error));
      
      // Wait a bit more to catch any delayed errors
      await page.waitForTimeout(3000);
      
      // Should have minimal or no JavaScript errors
      expect(errors.length).toBeLessThan(5);
      
      // UI should still be functional
      const domainPanel = page.locator('.domain-panel, [data-testid="domain-panel"]');
      await expect(domainPanel).toBeVisible();
    });
  });

  test.describe('Integration Testing', () => {
    test('should integrate with bookmark management features', async ({ page }) => {
      // Test navigation between different views
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      await page.waitForSelector('.domain-group, .domain-item', { timeout: 10000 });
      
      // Try to navigate to other views and back
      const allTab = page.locator('[data-testid="all-tab"], .all-tab, button:has-text("All")');
      if (await allTab.isVisible()) {
        await allTab.click();
        await page.waitForTimeout(1000);
        
        // Navigate back to domain view
        await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
        await expect(page.locator('.domain-panel, [data-testid="domain-panel"]')).toBeVisible();
      }
    });

    test('should work across different screen sizes', async ({ page }) => {
      // Test mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
      
      const domainPanel = page.locator('.domain-panel, [data-testid="domain-panel"]');
      await expect(domainPanel).toBeVisible();
      
      // Test tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      await expect(domainPanel).toBeVisible();
      
      // Test desktop viewport
      await page.setViewportSize({ width: 1920, height: 1080 });
      await expect(domainPanel).toBeVisible();
    });
  });
});

// Performance benchmark test
test.describe('Performance Benchmarks', () => {
  test('domain organization performance metrics', async ({ page }) => {
    const startTime = Date.now();
    
    await page.goto(TEST_CONFIG.baseURL);
    await page.click('[data-testid="domain-tab"], .domain-tab, button:has-text("Domain")');
    
    // Wait for domain organization to complete
    await page.waitForSelector('.domain-group, .domain-item', { timeout: 15000 });
    
    const totalTime = Date.now() - startTime;
    
    // Log performance metrics
    console.log(`Domain Organization Performance:`);
    console.log(`- Total Load Time: ${totalTime}ms`);
    console.log(`- Target: <5000ms for optimal UX`);
    console.log(`- Status: ${totalTime < 5000 ? '✅ PASS' : '⚠️ NEEDS OPTIMIZATION'}`);
    
    // Performance should be reasonable
    expect(totalTime).toBeLessThan(15000); // 15 second max
  });
});