import React from 'react';
import { BookmarkItem } from '../../src/components/BookmarkItem';
import type { Bookmark } from '../../src/types';

const mockBookmark: Bookmark = {
  id: '1',
  title: 'Test Bookmark',
  url: 'https://example.com',
  dateAdded: '2024-01-01T00:00:00.000Z',
  tags: ['test', 'example'],
  summary: 'This is a test bookmark for component testing',
  favicon: 'https://example.com/favicon.ico',
};

const mockBookmarkWithoutSummary: Bookmark = {
  id: '2',
  title: 'Bookmark Without Summary',
  url: 'https://nosummary.com',
  dateAdded: '2024-01-02T00:00:00.000Z',
  tags: [],
};

const mockYouTubeBookmark: Bookmark = {
  id: '3',
  title: 'YouTube Video',
  url: 'https://www.youtube.com/watch?v=dQw4w9WgXcQ',
  dateAdded: '2024-01-03T00:00:00.000Z',
  tags: ['video', 'youtube'],
  summary: 'A popular YouTube video',
  youtubeData: {
    videoId: 'dQw4w9WgXcQ',
    title: 'Never Gonna Give You Up',
    channelTitle: 'Rick Astley',
    thumbnailUrl: 'https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg',
    duration: 'PT3M33S',
    viewCount: '**********',
    publishedAt: '2009-10-25T06:57:33Z',
  },
};

describe('BookmarkItem Component', () => {
  it('should render bookmark with all data', () => {
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    // Check basic elements
    cy.getByTestId('bookmark-item').should('exist');
    cy.getByTestId('bookmark-title').should('contain', 'Test Bookmark');
    cy.getByTestId('bookmark-url').should('contain', 'https://example.com');
    cy.getByTestId('bookmark-summary').should('contain', 'This is a test bookmark');
    cy.getByTestId('bookmark-tags').should('contain', 'test');
    cy.getByTestId('bookmark-tags').should('contain', 'example');
  });

  it('should render bookmark without summary', () => {
    const mockProps = {
      bookmark: mockBookmarkWithoutSummary,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('bookmark-title').should('contain', 'Bookmark Without Summary');
    cy.getByTestId('bookmark-summary').should('not.exist');
    cy.getByTestId('generate-summary-button').should('be.visible');
  });

  it('should render YouTube bookmark with video data', () => {
    const mockProps = {
      bookmark: mockYouTubeBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('youtube-thumbnail').should('be.visible');
    cy.getByTestId('youtube-channel').should('contain', 'Rick Astley');
    cy.getByTestId('youtube-duration').should('exist');
  });

  it('should handle selection', () => {
    const onSelectSpy = cy.stub();
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: onSelectSpy,
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('bookmark-checkbox').click();
    cy.then(() => {
      expect(onSelectSpy).to.have.been.calledWith('1', true);
    });
  });

  it('should show selected state', () => {
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: true,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('bookmark-checkbox').should('be.checked');
    cy.getByTestId('bookmark-item').should('have.class', 'selected');
  });

  it('should handle edit action', () => {
    const onEditSpy = cy.stub();
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: onEditSpy,
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('edit-bookmark-button').click();
    cy.then(() => {
      expect(onEditSpy).to.have.been.calledWith(mockBookmark);
    });
  });

  it('should handle delete action', () => {
    const onDeleteSpy = cy.stub();
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: onDeleteSpy,
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('delete-bookmark-button').click();
    cy.then(() => {
      expect(onDeleteSpy).to.have.been.calledWith('1');
    });
  });

  it('should handle generate summary action', () => {
    const onGenerateSummarySpy = cy.stub();
    const mockProps = {
      bookmark: mockBookmarkWithoutSummary,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: onGenerateSummarySpy,
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('generate-summary-button').click();
    cy.then(() => {
      expect(onGenerateSummarySpy).to.have.been.calledWith('2');
    });
  });

  it('should handle generate tags action', () => {
    const onGenerateTagsSpy = cy.stub();
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: onGenerateTagsSpy,
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('generate-tags-button').click();
    cy.then(() => {
      expect(onGenerateTagsSpy).to.have.been.calledWith('1');
    });
  });

  it('should be accessible', () => {
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);
    cy.injectAxe();
    cy.checkA11y();
  });

  it('should handle keyboard navigation', () => {
    const mockProps = {
      bookmark: mockBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    // Test tab navigation
    cy.getByTestId('bookmark-checkbox').focus();
    cy.focused().should('have.attr', 'data-testid', 'bookmark-checkbox');

    cy.focused().tab();
    cy.focused().should('have.attr', 'data-testid', 'edit-bookmark-button');

    cy.focused().tab();
    cy.focused().should('have.attr', 'data-testid', 'delete-bookmark-button');
  });

  it('should handle long titles gracefully', () => {
    const longTitleBookmark: Bookmark = {
      ...mockBookmark,
      title: 'This is a very long bookmark title that should be truncated or wrapped properly to maintain the layout integrity of the bookmark item component',
    };

    const mockProps = {
      bookmark: longTitleBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('bookmark-title').should('be.visible');
    cy.getByTestId('bookmark-item').should('have.css', 'overflow', 'hidden');
  });

  it('should handle many tags', () => {
    const manyTagsBookmark: Bookmark = {
      ...mockBookmark,
      tags: ['tag1', 'tag2', 'tag3', 'tag4', 'tag5', 'tag6', 'tag7', 'tag8'],
    };

    const mockProps = {
      bookmark: manyTagsBookmark,
      isSelected: false,
      onSelect: cy.stub(),
      onEdit: cy.stub(),
      onDelete: cy.stub(),
      onGenerateSummary: cy.stub(),
      onGenerateTags: cy.stub(),
    };

    cy.mountWithProviders(<BookmarkItem {...mockProps} />);

    cy.getByTestId('bookmark-tags').should('be.visible');
    // Should show some tags and potentially a "show more" indicator
    cy.getByTestId('bookmark-tags').find('.tag').should('have.length.at.least', 3);
  });
});