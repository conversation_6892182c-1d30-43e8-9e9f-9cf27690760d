/**
 * MEMORY PROTECTION SYSTEM
 * Prevents memory issues before they become critical
 */


interface MemoryThreshold {
  level: 'safe' | 'warning' | 'critical' | 'emergency'
  percentage: number
  action: () => void
}

class MemoryProtectionManager {
  private isActive = false
  private monitoringInterval: number | null = null
  private lastCleanup = 0
  private cleanupHistory: number[] = []
  
  // Configuration
  private readonly MONITORING_INTERVAL = 30000 // 30 seconds
  private readonly CLEANUP_COOLDOWN = 60000 // 1 minute between cleanups
  private readonly HISTORY_LIMIT = 10
  private readonly AUTO_CLEANUP_THRESHOLD = 82 // Auto-trigger comprehensive cleanup at 82%
  
  // Memory thresholds with progressive actions - ENHANCED FOR CRITICAL SITUATIONS
  private readonly thresholds: MemoryThreshold[] = [
    {
      level: 'safe',
      percentage: 50,
      action: () => this.logMemoryStatus('safe')
    },
    {
      level: 'warning',
      percentage: 60,
      action: () => this.performLightCleanup()
    },
    {
      level: 'critical',
      percentage: 70,
      action: () => this.performAggressiveCleanup()
    },
    {
      level: 'emergency',
      percentage: 80,
      action: () => this.performEmergencyCleanup()
    },
    {
      level: 'emergency',
      percentage: 85,
      action: () => this.performEmergencyCleanup()
    }
  ]

  constructor() {
    this.startProtection()
  }

  startProtection(): void {
    if (this.isActive) return
    
    this.isActive = true
    
    // Start monitoring
    this.monitoringInterval = setInterval(() => {
      this.checkMemoryStatus()
    }, this.MONITORING_INTERVAL) as any
    
    // Initial check
    this.checkMemoryStatus()
    
    console.log('🛡️ Memory protection activated')
  }

  stopProtection(): void {
    if (!this.isActive) return
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
      this.monitoringInterval = null
    }
    
    this.isActive = false
    console.log('🛡️ Memory protection deactivated')
  }

  private checkMemoryStatus(): void {
    const memoryInfo = this.getMemoryInfo()
    if (!memoryInfo) return

    const usagePercentage = memoryInfo.usagePercentage

    // Auto-trigger comprehensive cleanup at critical threshold
    if (usagePercentage >= this.AUTO_CLEANUP_THRESHOLD) {
      const now = Date.now()
      if (now - this.lastCleanup > this.CLEANUP_COOLDOWN) {
        // Only log in development mode and make it less alarming
        if (process.env.NODE_ENV === 'development') {
          console.log(`🛡️ Automatic memory protection activated (${usagePercentage.toFixed(1)}% usage)`)
        }
        this.runComprehensiveEmergencyCleanup()
        this.lastCleanup = now
        this.cleanupHistory.push(usagePercentage)
        return
      }
    }

    // Find appropriate threshold for normal cleanup
    const threshold = this.thresholds
      .reverse()
      .find(t => usagePercentage >= t.percentage)

    if (threshold) {
      // Check cooldown to prevent spam
      const now = Date.now()
      if (threshold.level !== 'safe' && now - this.lastCleanup < this.CLEANUP_COOLDOWN) {
        return // Skip action due to cooldown
      }

      threshold.action()

      if (threshold.level !== 'safe') {
        this.lastCleanup = now
        this.cleanupHistory.push(usagePercentage)

        // Keep history limited
        if (this.cleanupHistory.length > this.HISTORY_LIMIT) {
          this.cleanupHistory.shift()
        }
      }
    }
  }

  private getMemoryInfo(): { usagePercentage: number; used: number; limit: number } | null {
    if (!('memory' in performance)) return null
    
    const memory = (performance as any).memory
    const used = memory.usedJSHeapSize
    const limit = memory.jsHeapSizeLimit
    const usagePercentage = (used / limit) * 100
    
    return { usagePercentage, used, limit }
  }

  private logMemoryStatus(level: string): void {
    const memoryInfo = this.getMemoryInfo()
    if (!memoryInfo) return
    
    // Only log occasionally to prevent spam
    if (Math.random() < 0.1) { // 10% chance
      console.log(`📊 Memory status: ${memoryInfo.usagePercentage.toFixed(1)}% (${level})`)
    }
  }

  private performLightCleanup(): void {
    // Only log in development mode
    if (process.env.NODE_ENV === 'development') {
      console.log('🧹 Memory optimization running (background cleanup)')
    }

    // Clear small caches
    this.clearSmallCaches()

    // Force single garbage collection
    if (typeof (window as any).gc === 'function') {
      (window as any).gc()
    }
  }

  private performAggressiveCleanup(): void {
    // Only log in development mode, and make it less alarming
    if (process.env.NODE_ENV === 'development') {
      console.log('🧹 Enhanced memory optimization (background cleanup)')
    }

    // Clear all non-essential caches
    this.clearAllCaches()

    // Multiple garbage collection cycles
    for (let i = 0; i < 3; i++) {
      if (typeof (window as any).gc === 'function') {
        (window as any).gc()
      }
    }

    // Clear some browser data
    try {
      sessionStorage.clear()
    } catch (e) {
      // Ignore errors
    }
  }

  private performEmergencyCleanup(): void {
    // Less alarming message, only in development
    if (process.env.NODE_ENV === 'development') {
      console.log('🛡️ Automatic memory protection activated')
    }

    // Run comprehensive emergency cleanup automatically
    this.runComprehensiveEmergencyCleanup()
  }

  private async runComprehensiveEmergencyCleanup(): Promise<void> {
    console.log('🛡️ Running automatic comprehensive emergency cleanup...')

    try {
      // Phase 1: Stop all activity
      this.stopAllActivity()

      // Phase 2: Clear system caches
      this.clearAllCaches()
      this.clearSystemCaches()

      // Phase 3: Clear storage
      this.clearAllStorage()

      // Phase 4: Clear performance data
      this.clearPerformanceData()

      // Phase 5: Clear development tools
      this.clearDevelopmentTools()

      // Phase 6: Remove DOM elements
      this.removeLargeDOMElements()

      // Phase 7: Aggressive garbage collection
      this.performAggressiveGarbageCollection()

      // Phase 8: Clear global variables
      this.clearGlobalVariables()

      // Check results after cleanup
      setTimeout(() => {
        const memoryInfo = this.getMemoryInfo()
        if (memoryInfo && memoryInfo.usagePercentage > 75) {
          console.error('🚨 Memory still critical after comprehensive cleanup')
          this.showEmergencyNotification()
        } else {
          console.log('✅ Comprehensive emergency cleanup successful')
        }
      }, 2000)

    } catch (error) {
      console.error('❌ Emergency cleanup error:', error)
    }
  }

  private stopAllActivity(): void {
    // Stop all intervals and timeouts
    for (let i = 1; i < 99999; i++) {
      try {
        clearInterval(i)
        clearTimeout(i)
      } catch (e) {
        // Ignore errors
      }
    }

    // Stop animation frames
    let rafId = 0
    while (rafId < 10000) {
      try {
        cancelAnimationFrame(rafId)
        rafId++
      } catch (e) {
        break
      }
    }

    // Stop interval manager
    if ((window as any).intervalManager) {
      try {
        (window as any).intervalManager.emergencyStop()
      } catch (e) {
        // Ignore errors
      }
    }
  }

  private clearSystemCaches(): void {
    // Clear recommendation cache
    if ((window as any).recommendationCache) {
      try {
        (window as any).recommendationCache.emergencyCleanup()
      } catch (e) {
        // Ignore errors
      }
    }

    // Clear content analyzer cache
    if ((window as any).contentAnalyzer) {
      try {
        (window as any).contentAnalyzer.clearCache?.()
      } catch (e) {
        // Ignore errors
      }
    }
  }

  private clearAllStorage(): void {
    try {
      localStorage.clear()
      sessionStorage.clear()
    } catch (e) {
      // Ignore errors
    }

    // Clear IndexedDB
    try {
      if ('indexedDB' in window) {
        (indexedDB as any).databases?.().then((databases: any[]) => {
          databases.forEach(db => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name)
            }
          })
        })
      }
    } catch (e) {
      // Ignore errors
    }
  }

  private clearPerformanceData(): void {
    if (window.performance) {
      try {
        if (window.performance.clearMeasures) window.performance.clearMeasures()
        if (window.performance.clearMarks) window.performance.clearMarks()
        if (window.performance.clearResourceTimings) window.performance.clearResourceTimings()
      } catch (e) {
        // Ignore errors
      }
    }
  }

  private clearDevelopmentTools(): void {
    // Clear React DevTools
    if ((window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      try {
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null;
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null;
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberMount = null
      } catch (e) {
        // Ignore errors
      }
    }

    // Clear Vite HMR
    try {
      if ((window as any).__vite_plugin_react_preamble_installed__) {
        delete (window as any).__vite_plugin_react_preamble_installed__
      }
      if ((window as any).__vite_is_modern_browser) {
        delete (window as any).__vite_is_modern_browser
      }
    } catch (e) {
      // Ignore errors
    }
  }

  private removeLargeDOMElements(): void {
    try {
      // Remove streaming elements
      document.querySelectorAll('[data-streaming="true"], .streaming, .recommendation-card').forEach(el => el.remove())

      // Remove memory monitors
      document.querySelectorAll('.memory-monitor, .memory-stats').forEach(el => el.remove())

      // Remove large images
      document.querySelectorAll('img[src*="data:"], img[src*="blob:"]').forEach(el => el.remove())

      // Remove canvas elements
      document.querySelectorAll('canvas').forEach(el => el.remove())
    } catch (e) {
      // Ignore errors
    }
  }

  private performAggressiveGarbageCollection(): void {
    // Multiple garbage collection cycles
    for (let i = 0; i < 15; i++) {
      try {
        if (typeof (window as any).gc === 'function') {
          (window as any).gc()
        }

        // Force memory release patterns
        const temp = new Array(5000000).fill(null)
        temp.length = 0

        // Create and destroy objects to trigger GC
        const obj: any = {}
        for (let j = 0; j < 100000; j++) {
          obj[`key${j}`] = null
        }
        Object.keys(obj).forEach(key => delete obj[key])

      } catch (e) {
        // Continue even if GC fails
      }
    }
  }

  private clearGlobalVariables(): void {
    try {
      // Clear common global variables that might hold references
      const globalsToClean = [
        'playlistDemo', 'bookmarkData', 'cachedBookmarks', 'searchResults',
        'filteredBookmarks', 'recommendationData', 'summaryCache', 'imageCache'
      ]

      globalsToClean.forEach(global => {
        if ((window as any)[global]) {
          delete (window as any)[global]
        }
      })
    } catch (e) {
      // Ignore errors
    }
  }

  private clearSmallCaches(): void {
    // Clear recommendation cache if available
    if ((window as any).recommendationCache) {
      (window as any).recommendationCache.optimizeCache()
    }
    
    // Clear any other small caches
    if ((window as any).contentAnalyzer) {
      (window as any).contentAnalyzer.cleanupCache?.()
    }
  }

  private clearAllCaches(): void {
    // Clear recommendation cache
    if ((window as any).recommendationCache) {
      (window as any).recommendationCache.aggressiveCleanup()
    }
    
    // Clear content analyzer cache
    if ((window as any).contentAnalyzer) {
      (window as any).contentAnalyzer.clearCache?.()
    }
    
    // Clear any other caches
    if ((window as any).playlistDemo) {
      delete (window as any).playlistDemo
    }
  }

  private showEmergencyNotification(): void {
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(135deg, #dc2626, #b91c1c);
      color: white;
      padding: 16px 24px;
      border-radius: 8px;
      font-weight: bold;
      font-size: 16px;
      z-index: 10000;
      box-shadow: 0 8px 32px rgba(220, 38, 38, 0.4);
      border: 2px solid #fca5a5;
      animation: pulse 2s infinite;
      font-family: system-ui, -apple-system, sans-serif;
      max-width: 400px;
      text-align: center;
    `
    
    notification.innerHTML = `
      🚨 CRITICAL MEMORY WARNING<br>
      <small style="font-weight: normal; margin-top: 8px; display: block;">
        Memory usage is still critical. Click to refresh the page.
      </small>
    `
    
    notification.onclick = () => {
      window.location.reload()
    }
    
    document.body.appendChild(notification)
    
    // Auto-remove after 15 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove()
      }
    }, 15000)
  }

  // Public methods
  getStats(): {
    currentUsage: number | null
    isActive: boolean
    lastCleanup: number
    cleanupHistory: number[]
    nextCheck: number
  } {
    const memoryInfo = this.getMemoryInfo()
    
    return {
      currentUsage: memoryInfo?.usagePercentage || null,
      isActive: this.isActive,
      lastCleanup: this.lastCleanup,
      cleanupHistory: [...this.cleanupHistory],
      nextCheck: this.lastCleanup + this.CLEANUP_COOLDOWN
    }
  }

  forceCleanup(): void {
    console.log('🧹 Manual memory cleanup triggered')
    this.performAggressiveCleanup()
  }

  emergencyCleanup(): void {
    console.error('🚨 Manual emergency cleanup triggered')
    this.performEmergencyCleanup()
  }
}

// Global instance
export const memoryProtection = new MemoryProtectionManager()

// Auto-start protection
if (typeof window !== 'undefined') {
  // Make available globally for debugging
  ;(window as any).memoryProtection = memoryProtection
  
  // Start protection immediately
  memoryProtection.startProtection()
}
