/**
 * CRITICAL MEMORY CLEANUP - Emergency Response for 80%+ Memory Usage
 * This module provides immediate, aggressive memory cleanup for critical situations
 */

/**
 * EMERGENCY: Immediate critical memory cleanup
 * Use when memory usage exceeds 80%
 */
export const emergencyCriticalCleanup = async (): Promise<void> => {
  console.error('🔥 EMERGENCY CRITICAL MEMORY CLEANUP INITIATED')
  
  try {
    // 1. AGGRESSIVE GARBAGE COLLECTION (Multiple cycles)
    console.log('🗑️ Phase 1: Aggressive garbage collection...')
    for (let i = 0; i < 5; i++) {
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc()
      }
      
      // Force memory release patterns
      const temp = new Array(2000000).fill(null)
      temp.length = 0
      
      // Small delay between cycles
      await new Promise(resolve => setTimeout(resolve, 100))
    }
    
    // 2. CLEAR ALL BROWSER STORAGE
    console.log('💾 Phase 2: Clearing all storage...')
    try {
      // Clear all storage (emergency situation)
      localStorage.clear()
      sessionStorage.clear()
      
      // Clear IndexedDB if available
      if ('indexedDB' in window) {
        const databases = await indexedDB.databases()
        for (const db of databases) {
          if (db.name) {
            indexedDB.deleteDatabase(db.name)
          }
        }
      }
    } catch (error) {
      console.warn('Storage cleanup failed:', error)
    }
    
    // 3. AGGRESSIVE DOM CLEANUP
    console.log('🧹 Phase 3: Aggressive DOM cleanup...')
    
    // Remove all hidden elements
    const hiddenElements = document.querySelectorAll('[style*="display: none"], .hidden, [hidden]')
    hiddenElements.forEach(el => el.remove())
    
    // Remove all script tags that aren't essential
    const scripts = document.querySelectorAll('script[src]')
    scripts.forEach(script => {
      const src = script.getAttribute('src')
      if (src && !src.includes('vite') && !src.includes('react')) {
        script.remove()
      }
    })
    
    // Remove all style elements except critical ones
    const styles = document.querySelectorAll('style')
    styles.forEach((style, index) => {
      if (index > 2) { // Keep first 2 style elements
        style.remove()
      }
    })
    
    // 4. CLEAR ALL PERFORMANCE DATA
    console.log('📊 Phase 4: Clearing performance data...')
    if (window.performance) {
      if (window.performance.clearMeasures) window.performance.clearMeasures()
      if (window.performance.clearMarks) window.performance.clearMarks()
      if (window.performance.clearResourceTimings) window.performance.clearResourceTimings()
      
      // Clear navigation timing
      try {
        (window.performance as any).clearNavigationTimings?.()
      } catch (e) { /* ignore */ }
    }
    
    // 5. CLEAR BROWSER CACHES
    console.log('🗂️ Phase 5: Clearing browser caches...')
    try {
      // Clear application cache if available
      if ('applicationCache' in window) {
        (window as any).applicationCache.update()
      }
      
      // Clear service worker caches
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
      }
    } catch (error) {
      console.warn('Cache cleanup failed:', error)
    }
    
    // 6. FORCE MEMORY RELEASE PATTERNS
    console.log('🔄 Phase 6: Force memory release...')
    
    // Create and destroy large objects to force cleanup
    for (let i = 0; i < 10; i++) {
      const largeArray = new Array(5000000).fill(null)
      largeArray.length = 0
      
      const largeObject: any = {}
      for (let j = 0; j < 100000; j++) {
        largeObject[`key${j}`] = null
      }
      
      // Clear object
      Object.keys(largeObject).forEach(key => delete largeObject[key])
    }
    
    // 7. FINAL GARBAGE COLLECTION BURST
    console.log('🗑️ Phase 7: Final cleanup burst...')
    for (let i = 0; i < 3; i++) {
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc()
      }
      await new Promise(resolve => setTimeout(resolve, 200))
    }
    
    console.log('✅ EMERGENCY CRITICAL CLEANUP COMPLETED')
    
    // Wait for cleanup to take effect
    await new Promise(resolve => setTimeout(resolve, 1000))
    
  } catch (error) {
    console.error('❌ Emergency cleanup failed:', error)
  }
}

/**
 * Force immediate page reload with memory cleanup
 */
export const forceMemoryReload = (): void => {
  console.warn('🔄 FORCING PAGE RELOAD FOR MEMORY CLEANUP')
  
  // Clear everything before reload
  try {
    localStorage.clear()
    sessionStorage.clear()
  } catch (e) { /* ignore */ }
  
  // Force reload
  window.location.reload()
}

/**
 * Show critical memory warning to user
 */
export const showCriticalMemoryWarning = (): void => {
  const warning = document.createElement('div')
  warning.style.cssText = `
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    padding: 16px 24px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 16px;
    z-index: 10000;
    box-shadow: 0 8px 32px rgba(220, 38, 38, 0.4);
    border: 2px solid #fca5a5;
    animation: pulse 2s infinite;
  `
  
  warning.innerHTML = `
    🚨 CRITICAL MEMORY WARNING: 80%+ Usage!<br>
    <small style="font-weight: normal; margin-top: 8px; display: block;">
      Click here for emergency cleanup or refresh the page
    </small>
  `
  
  warning.onclick = () => {
    emergencyCriticalCleanup()
    warning.remove()
  }
  
  document.body.appendChild(warning)
  
  // Auto-remove after 10 seconds
  setTimeout(() => {
    if (warning.parentNode) {
      warning.remove()
    }
  }, 10000)
}

/**
 * Monitor for critical memory and auto-trigger cleanup
 */
export const startCriticalMemoryMonitoring = (): (() => void) => {
  let isMonitoring = true
  
  const monitor = () => {
    if (!isMonitoring) return
    
    // Check memory usage
    if ('memory' in performance) {
      const memory = (performance as any).memory
      const usagePercentage = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      
      if (usagePercentage > 85) {
        console.error(`🔥 CRITICAL: ${usagePercentage}% memory usage! Auto-triggering emergency cleanup!`)
        emergencyCriticalCleanup()
        showCriticalMemoryWarning()
      }
    }
    
    // Check again in 5 seconds
    setTimeout(monitor, 5000)
  }
  
  // Start monitoring
  monitor()
  
  // Return stop function
  return () => {
    isMonitoring = false
  }
}

// Auto-start critical monitoring
if (typeof window !== 'undefined') {
  startCriticalMemoryMonitoring()
}
