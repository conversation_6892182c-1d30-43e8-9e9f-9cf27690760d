import React, { useState, useRef, useEffect } from 'react';
import { Bookmark } from '../types';
import { answerBookmarkQuestion, findRelatedBookmarks, generateBookmarkInsights } from '../services/bookmarkQAService';
import SpinnerIcon from './icons/SpinnerIcon';

interface BookmarkQAProps {
  bookmarks: Bookmark[];
  onBookmarkSelect?: (bookmark: Bookmark) => void;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  relatedBookmarks?: Bookmark[];
  timestamp: Date;
}

const BookmarkQA: React.FC<BookmarkQAProps> = ({ bookmarks, onBookmarkSelect }) => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showInsights, setShowInsights] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    if (bookmarks.length > 0 && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: 'welcome',
        type: 'assistant',
        content: `Hello! I can help you explore and find information in your ${bookmarks.length} bookmarks. You can ask me questions like:\n\n• "Show me bookmarks about React"\n• "What programming resources do I have?"\n• "Find bookmarks from last month"\n• "What are my most common topics?"\n\nWhat would you like to know?`,
        timestamp: new Date()
      };
      setMessages([welcomeMessage]);
    }
  }, [bookmarks.length, messages.length]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setIsLoading(true);

    try {
      // Get AI response and related bookmarks in parallel
      const [aiResponse, relatedBookmarks] = await Promise.all([
        answerBookmarkQuestion(userMessage.content, bookmarks),
        findRelatedBookmarks(userMessage.content, bookmarks)
      ]);

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: aiResponse,
        relatedBookmarks: relatedBookmarks.slice(0, 5), // Limit to 5 related bookmarks
        timestamp: new Date()
      };

      setMessages(prev => [...prev, assistantMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: `Sorry, I encountered an error: ${(error as Error).message}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
      inputRef.current?.focus();
    }
  };

  const handleGenerateInsights = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    setShowInsights(true);

    try {
      const insights = await generateBookmarkInsights(bookmarks);
      const insightsMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `Here are some insights about your bookmark collection:\n\n${insights}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, insightsMessage]);
    } catch (error) {
      const errorMessage: ChatMessage = {
        id: Date.now().toString(),
        type: 'assistant',
        content: `Sorry, I couldn't generate insights: ${(error as Error).message}`,
        timestamp: new Date()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const clearChat = () => {
    setMessages([]);
    setShowInsights(false);
  };

  if (bookmarks.length === 0) {
    return (
      <div className="bg-slate-800/50 rounded-lg p-6 text-center">
        <div className="text-slate-400 mb-4">
          <svg className="w-16 h-16 mx-auto mb-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8.625 12a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H8.25m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0H12m4.125 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm0 0h-.375M21 12c0 4.556-4.03 8.25-9 8.25a9.764 9.764 0 01-2.555-.337A5.972 5.972 0 015.41 20.97a5.969 5.969 0 01-.474-.065 4.48 4.48 0 00.978-2.025c.09-.457-.133-.901-.467-1.226C3.93 16.178 3 14.189 3 12c0-4.556 4.03-8.25 9-8.25s9 3.694 9 8.25z" />
          </svg>
        </div>
        <h3 className="text-lg font-medium text-slate-300 mb-2">Bookmark Q&A</h3>
        <p className="text-slate-400">
          Upload some bookmarks first, then come back here to ask questions about your collection!
        </p>
      </div>
    );
  }

  return (
    <div className="bg-slate-800/50 rounded-lg flex flex-col h-[600px]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-600">
        <div>
          <h3 className="text-lg font-medium text-slate-200">Bookmark Q&A</h3>
          <p className="text-sm text-slate-400">{bookmarks.length} bookmarks indexed</p>
        </div>
        <div className="flex gap-2">
          {!showInsights && (
            <button
              onClick={handleGenerateInsights}
              disabled={isLoading}
              className="px-3 py-1.5 text-sm bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 text-white rounded-md transition-colors"
            >
              Generate Insights
            </button>
          )}
          <button
            onClick={clearChat}
            className="px-3 py-1.5 text-sm bg-slate-600 hover:bg-slate-700 text-slate-200 rounded-md transition-colors"
          >
            Clear Chat
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message) => (
          <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
            <div className={`max-w-[80%] rounded-lg p-3 ${
              message.type === 'user' 
                ? 'bg-sky-600 text-white' 
                : 'bg-slate-700 text-slate-200'
            }`}>
              <div className="whitespace-pre-wrap">{message.content}</div>
              
              {/* Related Bookmarks */}
              {message.relatedBookmarks && message.relatedBookmarks.length > 0 && (
                <div className="mt-3 pt-3 border-t border-slate-600">
                  <p className="text-sm font-medium text-slate-300 mb-2">Related Bookmarks:</p>
                  <div className="space-y-2">
                    {message.relatedBookmarks.map((bookmark) => (
                      <div 
                        key={bookmark.id}
                        className="p-2 bg-slate-600/50 rounded cursor-pointer hover:bg-slate-600 transition-colors"
                        onClick={() => onBookmarkSelect?.(bookmark)}
                      >
                        <div className="text-sm font-medium text-slate-200 truncate">{bookmark.title}</div>
                        <div className="text-xs text-slate-400 truncate">{bookmark.url}</div>
                        {bookmark.tags && bookmark.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-1">
                            {bookmark.tags.slice(0, 3).map((tag) => (
                              <span key={tag} className="text-xs bg-slate-500 text-slate-200 px-1.5 py-0.5 rounded">
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
              
              <div className="text-xs text-slate-400 mt-2">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-slate-700 text-slate-200 rounded-lg p-3 flex items-center gap-2">
              <SpinnerIcon className="w-4 h-4" />
              <span>Thinking...</span>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <form onSubmit={handleSubmit} className="p-4 border-t border-slate-600">
        <div className="flex gap-2">
          <input
            ref={inputRef}
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask me anything about your bookmarks..."
            disabled={isLoading}
            className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-200 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent disabled:opacity-50"
          />
          <button
            type="submit"
            disabled={!inputValue.trim() || isLoading}
            className="px-4 py-2 bg-sky-600 hover:bg-sky-700 disabled:bg-sky-800 text-white rounded-md transition-colors disabled:opacity-50"
          >
            Send
          </button>
        </div>
      </form>
    </div>
  );
};

export default BookmarkQA;