/**
 * COLLABORATIVE FILTERING SERVICE
 * Implements user-based and item-based collaborative filtering
 */

import type {
    CollaborativeFilteringService,
    Interaction,
    Recommendation,
    UserProfile,
    UserSimilarity
} from './interfaces'

export class ModernCollaborativeFilteringService implements CollaborativeFilteringService {
  private userProfiles = new Map<string, UserProfile>()
  private userSimilarities = new Map<string, UserSimilarity[]>()
  private itemSimilarities = new Map<string, Map<string, number>>()
  private userItemMatrix = new Map<string, Map<string, number>>()
  
  async generateUserBasedRecommendations(userId: string): Promise<Recommendation[]> {
    const userProfile = this.userProfiles.get(userId)
    if (!userProfile) {
      return []
    }
    
    // Find similar users
    const similarUsers = await this.calculateUserSimilarities(userId)
    const topSimilarUsers = similarUsers
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, 50) // Top 50 similar users
    
    // Aggregate recommendations from similar users
    const recommendations = await this.aggregateRecommendations(userId, topSimilarUsers)
    
    return recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 10)
  }

  async generateItemBasedRecommendations(bookmarkIds: string[]): Promise<Recommendation[]> {
    const recommendations: Recommendation[] = []
    
    for (const bookmarkId of bookmarkIds) {
      const similarItems = this.itemSimilarities.get(bookmarkId)
      if (!similarItems) continue
      
      // Get top similar items
      const topSimilar = Array.from(similarItems.entries())
        .sort(([, a], [, b]) => b - a)
        .slice(0, 20)
        .map(([itemId, similarity]) => ({ itemId, similarity }))
      
      // Group similar items into potential playlists
      const clusteredItems = this.clusterSimilarItems(topSimilar)
      
      clusteredItems.forEach((cluster, index) => {
        if (cluster.items.length >= 3) { // Minimum 3 items for a playlist
          recommendations.push({
            id: `item_based_${bookmarkId}_${index}`,
            name: this.generatePlaylistName(cluster.items),
            description: `Items similar to your bookmarked content`,
            bookmarkIds: cluster.items.map(item => item.itemId),
            confidence: cluster.averageSimilarity,
            reasoning: `Based on items similar to "${bookmarkId}"`,
            category: 'collaborative_item_based'
          })
        }
      })
    }
    
    return recommendations
      .sort((a, b) => b.confidence - a.confidence)
      .slice(0, 5)
  }

  async calculateUserSimilarities(userId: string): Promise<UserSimilarity[]> {
    // Check cache first
    if (this.userSimilarities.has(userId)) {
      return this.userSimilarities.get(userId)!
    }
    
    const targetUser = this.userProfiles.get(userId)
    if (!targetUser) return []
    
    const similarities: UserSimilarity[] = []
    
    // Calculate similarity with all other users
    for (const [otherUserId, otherUser] of Array.from(this.userProfiles)) {
      if (otherUserId === userId) continue
      
      const similarity = this.calculateUserSimilarity(targetUser, otherUser)
      if (similarity > 0.1) { // Minimum similarity threshold
        similarities.push({
          userId: otherUserId,
          similarity,
          commonInterests: this.findCommonInterests(targetUser, otherUser),
          interactionOverlap: this.calculateInteractionOverlap(targetUser, otherUser)
        })
      }
    }
    
    // Cache the results
    this.userSimilarities.set(userId, similarities)
    
    return similarities
  }

  async updateUserProfile(userId: string, interactions: Interaction[]): Promise<void> {
    let profile = this.userProfiles.get(userId)
    
    if (!profile) {
      profile = {
        userId,
        preferences: {
          favoriteTopics: [],
          preferredContentTypes: [],
          timePatterns: [],
          diversityPreference: 0.5,
          noveltyPreference: 0.5
        },
        interactions: [],
        embeddings: { id: userId, vector: [], dimension: 0, model: 'user_profile' },
        clusters: [],
        lastUpdated: new Date()
      }
    }
    
    // Update interactions
    profile.interactions.push(...interactions)
    
    // Update preferences based on interactions
    await this.updatePreferencesFromInteractions(profile, interactions)
    
    // Update user-item matrix
    this.updateUserItemMatrix(userId, interactions)
    
    // Invalidate similarity cache
    this.userSimilarities.delete(userId)
    
    profile.lastUpdated = new Date()
    this.userProfiles.set(userId, profile)
  }

  private calculateUserSimilarity(user1: UserProfile, user2: UserProfile): number {
    // Cosine similarity based on user-item interactions
    const user1Items = new Map<string, number>()
    const user2Items = new Map<string, number>()
    
    // Build user-item vectors
    user1.interactions.forEach(interaction => {
      const weight = this.getInteractionWeight(interaction.action)
      user1Items.set(interaction.bookmarkId, 
        (user1Items.get(interaction.bookmarkId) || 0) + weight)
    })
    
    user2.interactions.forEach(interaction => {
      const weight = this.getInteractionWeight(interaction.action)
      user2Items.set(interaction.bookmarkId, 
        (user2Items.get(interaction.bookmarkId) || 0) + weight)
    })
    
    // Calculate cosine similarity
    const commonItems = new Set(Array.from(user1Items.keys()).filter(item => user2Items.has(item)))
    if (commonItems.size === 0) return 0
    
    let dotProduct = 0
    let norm1 = 0
    let norm2 = 0
    
    const allItems = new Set([...Array.from(user1Items.keys()), ...Array.from(user2Items.keys())])
    
    for (const item of Array.from(allItems)) {
      const rating1 = user1Items.get(item) || 0
      const rating2 = user2Items.get(item) || 0
      
      dotProduct += rating1 * rating2
      norm1 += rating1 * rating1
      norm2 += rating2 * rating2
    }
    
    const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2)
    return magnitude > 0 ? dotProduct / magnitude : 0
  }

  private getInteractionWeight(action: string): number {
    const weights: Record<string, number> = {
      'view': 1,
      'save': 2,
      'share': 3,
      'like': 4,
      'create_playlist': 5,
      'dislike': -2,
      'not_interested': -1
    }
    return weights[action] || 1
  }

  private findCommonInterests(user1: UserProfile, user2: UserProfile): string[] {
    const interests1 = new Set(user1.preferences.favoriteTopics)
    const interests2 = new Set(user2.preferences.favoriteTopics)
    return Array.from(interests1).filter(interest => interests2.has(interest))
  }

  private calculateInteractionOverlap(user1: UserProfile, user2: UserProfile): number {
    const items1 = new Set(user1.interactions.map(i => i.bookmarkId))
    const items2 = new Set(user2.interactions.map(i => i.bookmarkId))
    
    const intersection = new Set(Array.from(items1).filter(item => items2.has(item)))
    const union = new Set([...Array.from(items1), ...Array.from(items2)])
    
    return union.size > 0 ? intersection.size / union.size : 0
  }

  private async aggregateRecommendations(
    userId: string, 
    similarUsers: UserSimilarity[]
  ): Promise<Recommendation[]> {
    const userItems = new Set(
      this.userProfiles.get(userId)?.interactions.map(i => i.bookmarkId) || []
    )
    
    const itemScores = new Map<string, number>()
    const itemReasons = new Map<string, string[]>()
    
    // Aggregate scores from similar users
    for (const similarUser of similarUsers) {
      const otherUserProfile = this.userProfiles.get(similarUser.userId)
      if (!otherUserProfile) continue
      
      for (const interaction of otherUserProfile.interactions) {
        if (userItems.has(interaction.bookmarkId)) continue // Skip items user already has
        
        const weight = this.getInteractionWeight(interaction.action)
        const score = weight * similarUser.similarity
        
        itemScores.set(interaction.bookmarkId, 
          (itemScores.get(interaction.bookmarkId) || 0) + score)
        
        if (!itemReasons.has(interaction.bookmarkId)) {
          itemReasons.set(interaction.bookmarkId, [])
        }
        itemReasons.get(interaction.bookmarkId)!.push(
          `Similar user ${similarUser.userId} ${interaction.action}d this`
        )
      }
    }
    
    // Convert to recommendations
    const recommendations: Recommendation[] = []
    const sortedItems = Array.from(itemScores.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 20)
    
    // Group items into potential playlists
    const clusters = this.clusterRecommendedItems(sortedItems)
    
    clusters.forEach((cluster, index) => {
      if (cluster.items.length >= 2) {
        recommendations.push({
          id: `user_based_${userId}_${index}`,
          name: this.generatePlaylistName(cluster.items),
          description: `Recommended based on users with similar interests`,
          bookmarkIds: cluster.items.map(item => item.itemId),
          confidence: cluster.averageScore,
          reasoning: `Users similar to you also saved these items`,
          category: 'collaborative_user_based'
        })
      }
    })
    
    return recommendations
  }

  private clusterSimilarItems(items: Array<{ itemId: string; similarity: number }>): Array<{
    items: Array<{ itemId: string; similarity: number }>
    averageSimilarity: number
  }> {
    // Simple clustering based on similarity thresholds
    const clusters: Array<{
      items: Array<{ itemId: string; similarity: number }>
      averageSimilarity: number
    }> = []
    
    const used = new Set<string>()
    
    for (const item of items) {
      if (used.has(item.itemId)) continue
      
      const cluster = [item]
      used.add(item.itemId)
      
      // Find similar items for this cluster
      for (const otherItem of items) {
        if (used.has(otherItem.itemId)) continue
        if (Math.abs(item.similarity - otherItem.similarity) < 0.2) {
          cluster.push(otherItem)
          used.add(otherItem.itemId)
        }
      }
      
      if (cluster.length >= 2) {
        const averageSimilarity = cluster.reduce((sum, item) => sum + item.similarity, 0) / cluster.length
        clusters.push({ items: cluster, averageSimilarity })
      }
    }
    
    return clusters
  }

  private clusterRecommendedItems(items: Array<[string, number]>): Array<{
    items: Array<{ itemId: string; score: number }>
    averageScore: number
  }> {
    // Group items by score ranges
    const clusters: Array<{
      items: Array<{ itemId: string; score: number }>
      averageScore: number
    }> = []
    
    const scoreRanges = [
      { min: 0.8, max: 1.0 },
      { min: 0.6, max: 0.8 },
      { min: 0.4, max: 0.6 },
      { min: 0.2, max: 0.4 }
    ]
    
    for (const range of scoreRanges) {
      const rangeItems = items
        .filter(([, score]) => score >= range.min && score < range.max)
        .map(([itemId, score]) => ({ itemId, score }))
      
      if (rangeItems.length >= 2) {
        const averageScore = rangeItems.reduce((sum, item) => sum + item.score, 0) / rangeItems.length
        clusters.push({ items: rangeItems, averageScore })
      }
    }
    
    return clusters
  }

  private generatePlaylistName(items: Array<{ itemId: string }>): string {
    // Generate a meaningful name based on item characteristics
    const names = [
      'Recommended Collection',
      'Similar Interests',
      'Curated Selection',
      'Trending Picks',
      'Community Favorites'
    ]
    
    return names[Math.floor(Math.random() * names.length)]
  }

  private async updatePreferencesFromInteractions(
    profile: UserProfile, 
    interactions: Interaction[]
  ): Promise<void> {
    // Extract topics and content types from interactions
    const topicCounts = new Map<string, number>()
    const contentTypeCounts = new Map<string, number>()
    
    interactions.forEach(interaction => {
      // This would typically involve analyzing the bookmark content
      // For now, we'll use simplified logic
      if (interaction.action === 'like' || interaction.action === 'save') {
        // Increment topic and content type preferences
        // This would be enhanced with actual content analysis
      }
    })
    
    // Update preferences based on interaction patterns
    // Note: lastUpdated is handled at the profile level, not preferences level
  }

  private updateUserItemMatrix(userId: string, interactions: Interaction[]): void {
    if (!this.userItemMatrix.has(userId)) {
      this.userItemMatrix.set(userId, new Map())
    }

    const userItems = this.userItemMatrix.get(userId)!

    interactions.forEach(interaction => {
      const weight = this.getInteractionWeight(interaction.action)
      userItems.set(interaction.bookmarkId,
        (userItems.get(interaction.bookmarkId) || 0) + weight)
    })
  }

  // Public method to get user similarities for external use
  getUserSimilarities(userId: string): UserSimilarity[] {
    return this.userSimilarities.get(userId) || []
  }

  // Public method to get item similarities
  getItemSimilarities(itemId: string): Map<string, number> {
    return this.itemSimilarities.get(itemId) || new Map()
  }

  // Method to precompute item similarities (should be run periodically)
  async precomputeItemSimilarities(): Promise<void> {
    const allItems = new Set<string>()

    // Collect all items from user interactions
    for (const [, userItems] of Array.from(this.userItemMatrix)) {
      for (const itemId of Array.from(userItems.keys())) {
        allItems.add(itemId)
      }
    }

    // Calculate item-item similarities
    const itemArray = Array.from(allItems)

    for (let i = 0; i < itemArray.length; i++) {
      const item1 = itemArray[i]
      const similarities = new Map<string, number>()

      for (let j = i + 1; j < itemArray.length; j++) {
        const item2 = itemArray[j]
        const similarity = this.calculateItemSimilarity(item1, item2)

        if (similarity > 0.1) { // Minimum similarity threshold
          similarities.set(item2, similarity)
        }
      }

      this.itemSimilarities.set(item1, similarities)
    }
  }

  private calculateItemSimilarity(item1: string, item2: string): number {
    // Calculate similarity based on users who interacted with both items
    const users1 = new Set<string>()
    const users2 = new Set<string>()

    // Find users who interacted with each item
    for (const [userId, userItems] of Array.from(this.userItemMatrix)) {
      if (userItems.has(item1)) users1.add(userId)
      if (userItems.has(item2)) users2.add(userId)
    }

    // Calculate Jaccard similarity
    const intersection = new Set(Array.from(users1).filter(user => users2.has(user)))
    const union = new Set([...Array.from(users1), ...Array.from(users2)])

    return union.size > 0 ? intersection.size / union.size : 0
  }
}
