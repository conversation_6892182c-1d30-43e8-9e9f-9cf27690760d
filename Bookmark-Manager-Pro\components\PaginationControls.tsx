
import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from './icons/HeroIcons'; // Assuming these exist or can be added

interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

const PageButton: React.FC<{
  page?: number;
  onClick: () => void;
  isActive?: boolean;
  isDisabled?: boolean;
  children: React.ReactNode;
  ariaLabel?: string;
}> = ({ page, onClick, isActive, isDisabled, children, ariaLabel }) => (
  <button
    onClick={onClick}
    disabled={isDisabled}
    aria-label={ariaLabel || (page ? `Go to page ${page}` : undefined)}
    aria-current={isActive ? 'page' : undefined}
    className={`min-w-[36px] px-3 py-1.5 text-sm font-medium rounded-md transition-colors
      ${isDisabled ? 'text-slate-600 cursor-not-allowed' : 'text-slate-300 hover:bg-sky-700'}
      ${isActive ? 'bg-sky-600 text-white ring-1 ring-sky-500' : 'bg-slate-700'}
    `}
  >
    {children}
  </button>
);

const PaginationControls: React.FC<PaginationControlsProps> = ({ currentPage, totalPages, onPageChange }) => {
  const pageNumbers = [];
  const maxPagesToShow = 5; // Max number of page buttons to show (excluding prev/next, first/last)
  const halfPagesToShow = Math.floor(maxPagesToShow / 2);

  let startPage = Math.max(1, currentPage - halfPagesToShow);
  let endPage = Math.min(totalPages, currentPage + halfPagesToShow);

  if (currentPage - halfPagesToShow < 1) {
    endPage = Math.min(totalPages, maxPagesToShow);
  }
  if (currentPage + halfPagesToShow > totalPages) {
    startPage = Math.max(1, totalPages - maxPagesToShow + 1);
  }
  
  if (totalPages <= maxPagesToShow) {
    startPage = 1;
    endPage = totalPages;
  }

  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <nav aria-label="Pagination" className="flex items-center justify-between mt-8 pt-4 border-t border-slate-700">
      <div className="text-sm text-slate-400">
        Page <span className="font-semibold text-slate-200">{currentPage}</span> of <span className="font-semibold text-slate-200">{totalPages}</span>
      </div>
      <div className="flex items-center space-x-2">
        <PageButton
          onClick={() => onPageChange(currentPage - 1)}
          isDisabled={currentPage === 1}
          ariaLabel="Previous page"
        >
          <ChevronLeftIcon className="w-5 h-5" />
          <span className="sr-only">Previous</span>
        </PageButton>

        {startPage > 1 && (
          <>
            <PageButton page={1} onClick={() => onPageChange(1)}>1</PageButton>
            {startPage > 2 && <span className="text-slate-500 px-1">...</span>}
          </>
        )}

        {pageNumbers.map(number => (
          <PageButton
            key={number}
            page={number}
            onClick={() => onPageChange(number)}
            isActive={currentPage === number}
          >
            {number}
          </PageButton>
        ))}

        {endPage < totalPages && (
          <>
            {endPage < totalPages - 1 && <span className="text-slate-500 px-1">...</span>}
            <PageButton page={totalPages} onClick={() => onPageChange(totalPages)}>{totalPages}</PageButton>
          </>
        )}

        <PageButton
          onClick={() => onPageChange(currentPage + 1)}
          isDisabled={currentPage === totalPages}
          ariaLabel="Next page"
        >
          <span className="sr-only">Next</span>
          <ChevronRightIcon className="w-5 h-5" />
        </PageButton>
      </div>
    </nav>
  );
};

export default PaginationControls;
