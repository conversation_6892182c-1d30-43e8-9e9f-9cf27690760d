import React, { useRef, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface ImportPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const ImportPanelOptimized: React.FC<ImportPanelProps> = ({ isOpen: _isOpen, onClose, onOpenPanel }) => {
  const { addBookmarksBulk: _addBookmarksBulk, replaceAllBookmarks: _replaceAllBookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [isDragging, setIsDragging] = useState(false)
  const [importFormat, setImportFormat] = useState<'html' | 'json' | 'csv'>('html')
  const [importProgress, _setImportProgress] = useState<number | null>(null)
  const [importStatus, _setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [importedCount, _setImportedCount] = useState(0)
  const [replaceExisting, setReplaceExisting] = useState(true)
  const [advancedOptionsExpanded, setAdvancedOptionsExpanded] = useState(false)
  const [multimediaOptionsExpanded, setMultimediaOptionsExpanded] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragging(false)
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleFileSelect = (file: File) => {
    // File processing logic here
    console.log('Processing file:', file.name)
  }

  const downloadTemplate = (format: string) => {
    // Template download logic here
    console.log('Downloading template:', format)
  }

  return (
    <div className="import-panel">
      {/* Quick Upload Section */}
      <div className="import-section">
        <h3 className="section-title">{t('import.quickUpload')}</h3>
        
        <div 
          className={`drop-zone-compact ${isDragging ? 'active' : ''}`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <p className="drop-zone-text">
            {isDragging ? t('import.dropHere') : t('import.dragOrClick')}
          </p>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          accept=".html,.json,.csv"
          onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
          className="visually-hidden"
        />

        <div className="form-row">
          <label>{t('import.format')}:</label>
          <select 
            value={importFormat} 
            onChange={(e) => setImportFormat(e.target.value as 'html' | 'json' | 'csv')}
            className="select-compact"
          >
            <option value="html">HTML</option>
            <option value="json">JSON</option>
            <option value="csv">CSV</option>
          </select>
        </div>
      </div>

      {/* Import Options */}
      <div className="import-section">
        <h3 className="section-title">{t('import.options')}</h3>
        
        <div className="form-row">
          <label>
            <input 
              type="checkbox" 
              checked={replaceExisting} 
              onChange={(e) => setReplaceExisting(e.target.checked)}
            />
            {t('import.replaceExisting')}
          </label>
        </div>

        <div className="button-grid">
          <button className="btn-compact primary">{t('import.start')}</button>
          <button className="btn-compact" onClick={() => downloadTemplate('html')}>
            {t('import.template')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>

      {/* Progress Section */}
      {importProgress !== null && (
        <div className="import-section">
          <h3 className="section-title">{t('import.progress')}</h3>
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${importProgress}%` }} />
          </div>
          <div className="status-compact">
            <div className={`status-icon ${importStatus}`} />
            <span>{importedCount} {t('import.imported')}</span>
          </div>
        </div>
      )}

      {/* Advanced Options - Collapsible */}
      <div className={`collapsible-section ${advancedOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setAdvancedOptionsExpanded(!advancedOptionsExpanded)}
        >
          <span>{t('import.advancedOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('import.batchSize')}:</label>
            <input type="number" defaultValue="100" className="input-compact" />
          </div>
          <div className="form-row">
            <label>{t('import.timeout')}:</label>
            <input type="number" defaultValue="30" className="input-compact" />
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('import.validateUrls')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('import.skipDuplicates')}
            </label>
          </div>
        </div>
      </div>

      {/* Multimedia Analysis - Collapsible */}
      <div className={`collapsible-section ${multimediaOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setMultimediaOptionsExpanded(!multimediaOptionsExpanded)}
        >
          <span>{t('import.multimediaAnalysis')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('import.detectVideos')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('import.detectImages')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('import.generateThumbnails')}
            </label>
          </div>
          <div className="controls-grid">
            <button className="btn-compact" onClick={() => onOpenPanel?.('multimedia')}>
              {t('import.viewMultimedia')}
            </button>
            <button className="btn-compact" onClick={() => onOpenPanel?.('playlist')}>
              {t('import.createPlaylist')}
            </button>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('import.quickActions')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('export')}>
            {t('import.export')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('health')}>
            {t('import.healthCheck')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('domain')}>
            {t('import.domainAnalysis')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('ai')}>
            {t('import.smartAI')}
          </button>
        </div>
      </div>
    </div>
  )
}