
import React, { useState, useMemo } from 'react';
import { FunnelIcon, XCircleIcon, FolderIcon, FolderOpenIcon, ChevronRightIcon } from './icons/HeroIcons';

interface FolderTreeNode {
  name: string;
  pathKey: string; // "Segment1||Segment2"
  fullPath: string[]; // ["Segment1", "Segment2"]
  children: FolderTreeNode[];
  depth: number;
}

interface FolderFilterProps {
  allFolderPaths: string[][]; // e.g., [["Work"], ["Work", "Project A"], ["Personal"]]
  selectedFolderPaths: string[][];
  onToggleFolder: (path: string[]) => void;
  onClearFilters: () => void;
}

const buildTreeFromPaths = (paths: string[][]): FolderTreeNode[] => {
  const root: { children: FolderTreeNode[] } = { children: [] };

  if (!paths || !Array.isArray(paths)) {
    return [];
  }

  paths.forEach(path => {
    let currentLevel = root.children;
    let currentPathKey = '';
    let currentFullPath: string[] = [];

    path.forEach((segment, index) => {
      currentPathKey = currentPathKey ? `${currentPathKey}||${segment}` : segment;
      currentFullPath.push(segment);

      let node = currentLevel.find(child => child.name === segment && child.depth === index);
      if (!node) {
        node = {
          name: segment,
          pathKey: currentPathKey,
          fullPath: [...currentFullPath],
          children: [],
          depth: index,
        };
        currentLevel.push(node);
        currentLevel.sort((a,b) => a.name.localeCompare(b.name)); // Keep sorted
      }
      currentLevel = node.children;
    });
  });
  return root.children;
};

interface RenderTreeNodeProps {
  node: FolderTreeNode;
  selectedPathKeys: Set<string>;
  expandedNodeKeys: Set<string>;
  onToggleFolder: (path: string[]) => void;
  onToggleExpand: (pathKey: string) => void;
}

const RenderTreeNode: React.FC<RenderTreeNodeProps> = ({
  node,
  selectedPathKeys,
  expandedNodeKeys,
  onToggleFolder,
  onToggleExpand,
}) => {
  const isSelected = selectedPathKeys.has(node.pathKey);
  const isExpanded = expandedNodeKeys.has(node.pathKey);
  const hasChildren = node.children.length > 0;

  return (
    <div className="my-0.5" style={{ paddingLeft: `${node.depth * 1.25}rem` }}>
      <div className="flex items-center group">
        <input
          type="checkbox"
          id={`folder-cb-${node.pathKey}`}
          checked={isSelected}
          onChange={() => onToggleFolder(node.fullPath)}
          className="h-4 w-4 rounded border-slate-500 text-sky-500 focus:ring-sky-400 accent-sky-500 cursor-pointer mr-2 flex-shrink-0"
        />
        {hasChildren ? (
          <ChevronRightIcon
            className={`w-4 h-4 mr-1 text-slate-400 group-hover:text-sky-300 cursor-pointer flex-shrink-0 transform transition-transform duration-150 ${isExpanded ? 'rotate-90' : ''}`}
            onClick={() => onToggleExpand(node.pathKey)}
            aria-hidden={true}
          />
        ) : (
          <span className="w-4 h-4 mr-1 flex-shrink-0"></span> // Placeholder for alignment
        )}
        {isExpanded && hasChildren ? <FolderOpenIcon className="w-5 h-5 mr-1.5 text-sky-400 flex-shrink-0" /> : <FolderIcon className="w-5 h-5 mr-1.5 text-sky-400 flex-shrink-0" />}
        <label
          htmlFor={`folder-cb-${node.pathKey}`}
          className="text-sm text-slate-300 cursor-pointer group-hover:text-sky-300 transition-colors truncate"
          title={node.name}
          onClick={(e) => { // Allow clicking label to toggle checkbox
            if (e.target === e.currentTarget) { // Ensure not clicking an inner element by mistake
                 onToggleFolder(node.fullPath);
            }
          }}
        >
          {node.name}
        </label>
      </div>
      {isExpanded && hasChildren && (
        <div className="mt-0.5">
          {node.children.map(childNode => (
            <RenderTreeNode
              key={childNode.pathKey}
              node={childNode}
              selectedPathKeys={selectedPathKeys}
              expandedNodeKeys={expandedNodeKeys}
              onToggleFolder={onToggleFolder}
              onToggleExpand={onToggleExpand}
            />
          ))}
        </div>
      )}
    </div>
  );
};


const FolderFilter: React.FC<FolderFilterProps> = ({ 
    allFolderPaths, 
    selectedFolderPaths, 
    onToggleFolder,
    onClearFilters
}) => {
  const [expandedNodeKeys, setExpandedNodeKeys] = useState<Set<string>>(new Set());
  
  const folderTree = useMemo(() => buildTreeFromPaths(allFolderPaths), [allFolderPaths]);
  const selectedPathKeys = useMemo(() => new Set(selectedFolderPaths.map(p => p.join('||'))), [selectedFolderPaths]);

  const handleToggleExpand = (pathKey: string) => {
    setExpandedNodeKeys(prev => {
      const newSet = new Set(prev);
      if (newSet.has(pathKey)) {
        newSet.delete(pathKey);
      } else {
        newSet.add(pathKey);
      }
      return newSet;
    });
  };
  
  // Auto-expand parents of selected folders on initial load or when selectedFolderPaths changes
  // This is a bit complex; for now, keeping manual expansion.
  // Can be added later if desired.

  return (
    <details className="my-6 p-4 bg-card rounded-lg shadow-md group" open>
      <summary className="flex items-center justify-between cursor-pointer text-lg font-semibold text-sky-300 hover:text-sky-200 transition-colors list-none -m-1 p-1"> {/* Remove default marker, adjust padding */}
        <div className="flex items-center">
          <FunnelIcon className="w-5 h-5 mr-2" />
          Filter by Folder 
          {selectedFolderPaths.length > 0 && (
            <span className="ml-2 px-2 py-0.5 bg-sky-600 text-xs rounded-full">{selectedFolderPaths.length} active</span>
          )}
        </div>
        <ChevronRightIcon className="w-5 h-5 text-slate-400 group-open:rotate-90 transform transition-transform duration-200" aria-hidden={true} />
      </summary>
      {folderTree.length > 0 ? (
        <>
          <div className="mt-4 max-h-72 overflow-y-auto pr-1 custom-scrollbar"> {/* Increased max-height slightly */}
            {folderTree.map(node => (
              <RenderTreeNode
                key={node.pathKey}
                node={node}
                selectedPathKeys={selectedPathKeys}
                expandedNodeKeys={expandedNodeKeys}
                onToggleFolder={onToggleFolder}
                onToggleExpand={handleToggleExpand}
              />
            ))}
          </div>
        </>
      ) : (
        <p className="mt-3 text-sm text-slate-400">No folder structures found in the imported file, or all bookmarks are at the root level.</p>
      )}
      {selectedFolderPaths.length > 0 && (
        <button
          onClick={onClearFilters}
          className="mt-4 flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md shadow-sm text-white bg-rose-600 hover:bg-rose-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-rose-500 transition-colors"
        >
          <XCircleIcon className="w-4 h-4 mr-1.5" />
          Clear All Filters
        </button>
      )}
    </details>
  );
};

export default FolderFilter;
