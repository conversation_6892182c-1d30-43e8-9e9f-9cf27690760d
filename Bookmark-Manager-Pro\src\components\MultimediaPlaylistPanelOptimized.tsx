import React, { useState, useEffect, useRef } from 'react'
import { Video, Play, Pause, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Shuffle, Repeat, Volume2, Mic, <PERSON>c<PERSON><PERSON>, <PERSON>tings, Download, Eye, ChevronDown, ChevronUp, Headphones, FileText, BookOpen, Image, ExternalLink, CheckCircle, AlertCircle, BarChart3, TrendingUp, X } from 'lucide-react'
import './optimized-panels.css'
import type { Bookmark } from '../../types'

interface MultimediaPlaylistPanelOptimizedProps {
  isOpen: boolean
  onClose: () => void
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
  bookmarks: Bookmark[]
  onCreatePlaylist: (config: any) => Promise<any>
}

interface PlaylistItem {
  id: string
  title: string
  url: string
  type: 'video' | 'audio' | 'document' | 'article' | 'image' | 'other'
  duration?: number
  readingTime?: number
  thumbnail?: string
}

interface PlaylistState {
  isProcessing: boolean
  progress: number
  completed: number
  total: number
  errors: string[]
}

export const MultimediaPlaylistPanelOptimized: React.FC<MultimediaPlaylistPanelOptimizedProps> = ({
  isOpen,
  onClose,
  selectedBookmarks,
  collectionId,
  mindMapSelection,
  bookmarks,
  onCreatePlaylist
}) => {
  const [playlistItems, setPlaylistItems] = useState<PlaylistItem[]>([])
  const [currentItem, setCurrentItem] = useState<PlaylistItem | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(80)
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0)
  const [gymMode, setGymMode] = useState(false)
  const [ttsEnabled, setTtsEnabled] = useState(false)
  const [autoAdvance, setAutoAdvance] = useState(true)
  const [shuffle, setShuffle] = useState(false)
  const [repeat, setRepeat] = useState<'none' | 'one' | 'all'>('none')
  const [state, setState] = useState<PlaylistState>({
    isProcessing: false,
    progress: 0,
    completed: 0,
    total: 0,
    errors: []
  })
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showExport, setShowExport] = useState(false)
  const [showQueue, setShowQueue] = useState(true)
  const [exportFormat, setExportFormat] = useState('email')
  const [exportDestination, setExportDestination] = useState('')
  const [exportStatus, setExportStatus] = useState<'idle' | 'exporting' | 'success' | 'error'>('idle')

  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const ttsRef = useRef<SpeechSynthesisUtterance | null>(null)

  const videoCount = playlistItems.filter(item => item.type === 'video').length
  const audioCount = playlistItems.filter(item => item.type === 'audio').length
  const documentCount = playlistItems.filter(item => ['document', 'article'].includes(item.type)).length

  useEffect(() => {
    if (isOpen && playlistItems.length === 0) {
      initializePlaylist()
    }
  }, [isOpen, selectedBookmarks, collectionId, mindMapSelection])

  useEffect(() => {
    if (!isOpen) {
      stopPlayback()
      setPlaylistItems([])
      setCurrentItem(null)
    }
  }, [isOpen])

  const initializePlaylist = async () => {
    setState(prev => ({ ...prev, isProcessing: true, errors: [] }))

    try {
      let targetBookmarks: Bookmark[] = []

      if (selectedBookmarks && selectedBookmarks.length > 0) {
        targetBookmarks = selectedBookmarks
      } else if (collectionId) {
        targetBookmarks = bookmarks.filter(b => b.collection === collectionId)
      } else if (mindMapSelection && mindMapSelection.length > 0) {
        targetBookmarks = bookmarks.filter(b => mindMapSelection.includes(b.id))
      } else {
        targetBookmarks = bookmarks.slice(0, 20)
      }

      if (targetBookmarks.length === 0) {
        setState(prev => ({ ...prev, errors: ['No bookmarks available for playlist creation'] }))
        return
      }

      const items: PlaylistItem[] = targetBookmarks.map((bookmark, index) => ({
        id: bookmark.id,
        title: bookmark.title || bookmark.url,
        url: bookmark.url,
        type: detectMediaType(bookmark.url),
        duration: Math.floor(Math.random() * 300) + 60, // Mock duration
        readingTime: Math.floor(Math.random() * 10) + 2, // Mock reading time
        thumbnail: bookmark.favicon
      }))

      setPlaylistItems(items)
      if (items.length > 0) {
        setCurrentItem(items[0])
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, error instanceof Error ? error.message : 'Failed to create playlist']
      }))
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }))
    }
  }

  const detectMediaType = (url: string): PlaylistItem['type'] => {
    const urlLower = url.toLowerCase()
    if (urlLower.includes('youtube.com') || urlLower.includes('vimeo.com') || urlLower.includes('.mp4')) {
      return 'video'
    }
    if (urlLower.includes('spotify.com') || urlLower.includes('soundcloud.com') || urlLower.includes('.mp3')) {
      return 'audio'
    }
    if (urlLower.includes('.pdf') || urlLower.includes('docs.google.com')) {
      return 'document'
    }
    if (urlLower.includes('medium.com') || urlLower.includes('blog') || urlLower.includes('article')) {
      return 'article'
    }
    if (urlLower.includes('.jpg') || urlLower.includes('.png') || urlLower.includes('.gif')) {
      return 'image'
    }
    return 'other'
  }

  const playItem = async (item: PlaylistItem) => {
    setCurrentItem(item)
    setIsPlaying(true)

    try {
      switch (item.type) {
        case 'video':
          await playVideo(item)
          break
        case 'audio':
          await playAudio(item)
          break
        case 'document':
        case 'article':
          if (ttsEnabled) {
            await playTextToSpeech(item)
          } else {
            window.open(item.url, '_blank')
          }
          break
        default:
          window.open(item.url, '_blank')
      }
    } catch (error) {
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, `Failed to play ${item.title}`]
      }))
    }
  }

  const playVideo = async (item: PlaylistItem) => {
    if (videoRef.current) {
      videoRef.current.src = item.url
      videoRef.current.playbackRate = playbackSpeed
      videoRef.current.volume = volume / 100
      await videoRef.current.play()
    }
  }

  const playAudio = async (item: PlaylistItem) => {
    if (audioRef.current) {
      audioRef.current.src = item.url
      audioRef.current.playbackRate = playbackSpeed
      audioRef.current.volume = volume / 100
      await audioRef.current.play()
    }
  }

  const playTextToSpeech = async (item: PlaylistItem) => {
    if (!('speechSynthesis' in window)) {
      setState(prev => ({ ...prev, errors: [...prev.errors, 'Text-to-speech not supported'] }))
      return
    }

    const text = `${item.title}. ${item.url}`
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = playbackSpeed
    utterance.volume = volume / 100

    utterance.onend = () => {
      if (autoAdvance) {
        playNextItem()
      }
    }

    ttsRef.current = utterance
    speechSynthesis.speak(utterance)
  }

  const stopPlayback = () => {
    setIsPlaying(false)
    
    if (videoRef.current) {
      videoRef.current.pause()
    }
    if (audioRef.current) {
      audioRef.current.pause()
    }
    if (ttsRef.current) {
      speechSynthesis.cancel()
    }
  }

  const playNextItem = () => {
    if (!currentItem) return

    const currentIndex = playlistItems.findIndex(item => item.id === currentItem.id)
    let nextIndex = currentIndex + 1

    if (shuffle) {
      nextIndex = Math.floor(Math.random() * playlistItems.length)
    } else if (nextIndex >= playlistItems.length) {
      if (repeat === 'all') {
        nextIndex = 0
      } else {
        setIsPlaying(false)
        return
      }
    }

    const nextItem = playlistItems[nextIndex]
    if (nextItem) {
      playItem(nextItem)
    }
  }

  const playPreviousItem = () => {
    if (!currentItem) return

    const currentIndex = playlistItems.findIndex(item => item.id === currentItem.id)
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : playlistItems.length - 1
    const prevItem = playlistItems[prevIndex]
    
    if (prevItem) {
      playItem(prevItem)
    }
  }

  const exportPlaylist = async () => {
    setExportStatus('exporting')

    try {
      const config = {
        format: exportFormat,
        destination: exportDestination,
        items: playlistItems
      }

      await onCreatePlaylist(config)
      setExportStatus('success')
    } catch (error) {
      setExportStatus('error')
    }
  }

  const getItemIcon = (type: PlaylistItem['type']) => {
    switch (type) {
      case 'video': return <Video size={16} />
      case 'audio': return <Headphones size={16} />
      case 'document': return <FileText size={16} />
      case 'article': return <BookOpen size={16} />
      case 'image': return <Image size={16} />
      default: return <ExternalLink size={16} />
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!isOpen) return null

  return (
    <div className="modal-overlay">
      <div className={`optimized-panel modal-panel ${gymMode ? 'gym-mode' : ''}`}>
        <div className="panel-header">
          <div className="header-content">
            <div className="header-title">
              <Video className="header-icon" />
              <h2>Multimedia Playlist</h2>
            </div>
            <div className="header-stats">
              <div className="stat-item">
                <span className="stat-value">{playlistItems.length}</span>
                <span className="stat-label">Items</span>
              </div>
              <div className="stat-item">
                <span className="stat-value">{gymMode ? 'Gym' : 'Standard'}</span>
                <span className="stat-label">Mode</span>
              </div>
            </div>
          </div>
          <div className="header-actions">
            <button
              onClick={() => setGymMode(!gymMode)}
              className={`action-button ${gymMode ? 'success' : 'secondary'}`}
            >
              🏃‍♂️ {gymMode ? 'Exit Gym' : 'Gym Mode'}
            </button>
            <button onClick={onClose} className="close-button">
              <X size={16} />
            </button>
          </div>
        </div>

        <div className="panel-content">
          {/* Quick Actions */}
          <div className="section">
            <div className="section-header">
              <h3>Quick Actions</h3>
            </div>
            <div className="action-grid">
              <button
                onClick={() => currentItem && playItem(currentItem)}
                disabled={state.isProcessing || !currentItem}
                className="action-button primary"
              >
                <Play size={16} />
                <span>Start Playlist</span>
              </button>
              <button
                onClick={() => setGymMode(!gymMode)}
                className="action-button secondary"
              >
                <Settings size={16} />
                <span>Toggle Gym Mode</span>
              </button>
            </div>
          </div>

          {/* Progress */}
          {state.isProcessing && (
            <div className="section">
              <div className="progress-container">
                <div className="progress-header">
                  <span>Creating playlist...</span>
                  <span>{state.completed}/{state.total}</span>
                </div>
                <div className="progress-bar">
                  <div 
                    className="progress-fill"
                    style={{ width: `${(state.completed / state.total) * 100}%` }}
                  />
                </div>
              </div>
            </div>
          )}

          {/* Statistics */}
          <div className="section">
            <div className="section-header">
              <h3>Media Statistics</h3>
            </div>
            <div className="stats-grid">
              <div className="stat-card">
                <Video className="stat-icon" />
                <div className="stat-content">
                  <span className="stat-number">{videoCount}</span>
                  <span className="stat-text">Videos</span>
                </div>
              </div>
              <div className="stat-card">
                <Headphones className="stat-icon" />
                <div className="stat-content">
                  <span className="stat-number">{audioCount}</span>
                  <span className="stat-text">Audio</span>
                </div>
              </div>
              <div className="stat-card">
                <FileText className="stat-icon" />
                <div className="stat-content">
                  <span className="stat-number">{documentCount}</span>
                  <span className="stat-text">Documents</span>
                </div>
              </div>
            </div>
          </div>

          {/* Current Playing */}
          {currentItem && (
            <div className="section">
              <div className="section-header">
                <h3>Now Playing</h3>
              </div>
              <div className="current-item-card">
                <div className="item-icon">
                  {getItemIcon(currentItem.type)}
                </div>
                <div className="item-content">
                  <h4>{currentItem.title}</h4>
                  <div className="item-meta">
                    <span className="item-type">{currentItem.type}</span>
                    {currentItem.duration && (
                      <span className="item-duration">{formatDuration(currentItem.duration)}</span>
                    )}
                  </div>
                </div>
                <div className="item-actions">
                  <button
                    onClick={() => isPlaying ? stopPlayback() : playItem(currentItem)}
                    className={`play-button ${isPlaying ? 'playing' : ''}`}
                  >
                    {isPlaying ? <Pause size={16} /> : <Play size={16} />}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Playback Controls */}
          <div className="section">
            <div className="section-header">
              <h3>Playback Controls</h3>
            </div>
            <div className="controls-grid">
              <div className="control-group">
                <div className="control-buttons">
                  <button
                    onClick={playPreviousItem}
                    className="control-button"
                    disabled={!currentItem}
                  >
                    <SkipBack size={16} />
                  </button>
                  <button
                    onClick={() => isPlaying ? stopPlayback() : currentItem && playItem(currentItem)}
                    className={`control-button primary ${isPlaying ? 'playing' : ''}`}
                    disabled={!currentItem}
                  >
                    {isPlaying ? <Pause size={20} /> : <Play size={20} />}
                  </button>
                  <button
                    onClick={playNextItem}
                    className="control-button"
                    disabled={!currentItem}
                  >
                    <SkipForward size={16} />
                  </button>
                </div>
              </div>
              
              <div className="control-group">
                <div className="control-toggles">
                  <button
                    onClick={() => setShuffle(!shuffle)}
                    className={`toggle-button ${shuffle ? 'active' : ''}`}
                  >
                    <Shuffle size={16} />
                  </button>
                  <button
                    onClick={() => {
                      const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
                      const currentIndex = modes.indexOf(repeat)
                      setRepeat(modes[(currentIndex + 1) % modes.length])
                    }}
                    className={`toggle-button ${repeat !== 'none' ? 'active' : ''}`}
                  >
                    <Repeat size={16} />
                  </button>
                  <button
                    onClick={() => setTtsEnabled(!ttsEnabled)}
                    className={`toggle-button ${ttsEnabled ? 'active' : ''}`}
                  >
                    {ttsEnabled ? <Mic size={16} /> : <MicOff size={16} />}
                  </button>
                </div>
              </div>
              
              <div className="control-group">
                <div className="volume-control">
                  <Volume2 size={16} />
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={volume}
                    onChange={(e) => setVolume(parseInt(e.target.value))}
                    className="volume-slider"
                  />
                  <span className="volume-value">{volume}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Playlist Queue */}
          <div className="section">
            <button
              onClick={() => setShowQueue(!showQueue)}
              className="section-toggle"
            >
              <h3>Playlist Queue ({playlistItems.length})</h3>
              {showQueue ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            {showQueue && (
              <div className="collapsible-content">
                {playlistItems.length === 0 ? (
                  <div className="empty-state">
                    <Video className="empty-icon" />
                    <p>No items in playlist</p>
                    <button
                      onClick={initializePlaylist}
                      className="action-button primary"
                    >
                      Create Playlist
                    </button>
                  </div>
                ) : (
                  <div className="queue-list">
                    {playlistItems.slice(0, 10).map((item, index) => (
                      <div
                        key={item.id}
                        onClick={() => playItem(item)}
                        className={`queue-item ${currentItem?.id === item.id ? 'active' : ''}`}
                      >
                        <div className="queue-index">{index + 1}</div>
                        <div className="queue-icon">
                          {getItemIcon(item.type)}
                        </div>
                        <div className="queue-content">
                          <span className="queue-title">{item.title}</span>
                          <div className="queue-meta">
                            <span className="queue-type">{item.type}</span>
                            {item.duration && (
                              <span className="queue-duration">{formatDuration(item.duration)}</span>
                            )}
                          </div>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation()
                            playItem(item)
                          }}
                          className="queue-play"
                        >
                          <Play size={14} />
                        </button>
                      </div>
                    ))}
                    {playlistItems.length > 10 && (
                      <div className="queue-more">
                        +{playlistItems.length - 10} more items
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Advanced Options */}
          <div className="section">
            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="section-toggle"
            >
              <h3>Advanced Options</h3>
              {showAdvanced ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            {showAdvanced && (
              <div className="collapsible-content">
                <div className="config-grid">
                  <label className="config-item">
                    <input
                      type="checkbox"
                      checked={autoAdvance}
                      onChange={(e) => setAutoAdvance(e.target.checked)}
                    />
                    <div className="config-content">
                      <span className="config-label">Auto Advance</span>
                      <span className="config-description">Automatically play next item</span>
                    </div>
                  </label>
                  <div className="config-item">
                    <div className="config-content">
                      <span className="config-label">Playback Speed</span>
                      <select
                        value={playbackSpeed}
                        onChange={(e) => setPlaybackSpeed(parseFloat(e.target.value))}
                        className="config-select"
                      >
                        <option value={0.5}>0.5x</option>
                        <option value={0.75}>0.75x</option>
                        <option value={1.0}>1.0x</option>
                        <option value={1.25}>1.25x</option>
                        <option value={1.5}>1.5x</option>
                        <option value={2.0}>2.0x</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Export Options */}
          <div className="section">
            <button
              onClick={() => setShowExport(!showExport)}
              className="section-toggle"
            >
              <h3>Export Playlist</h3>
              {showExport ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
            </button>
            {showExport && (
              <div className="collapsible-content">
                <div className="export-grid">
                  <div className="export-field">
                    <label className="export-label">Format</label>
                    <select
                      value={exportFormat}
                      onChange={(e) => setExportFormat(e.target.value)}
                      className="export-select"
                    >
                      <option value="email">📧 Email</option>
                      <option value="pdf">📄 PDF</option>
                      <option value="epub">📚 EPUB</option>
                      <option value="json">💾 JSON</option>
                      <option value="youtube">🎥 YouTube Playlist</option>
                      <option value="podcast">🎧 Podcast Feed</option>
                    </select>
                  </div>
                  <div className="export-field">
                    <label className="export-label">Destination</label>
                    <input
                      type="text"
                      value={exportDestination}
                      onChange={(e) => setExportDestination(e.target.value)}
                      placeholder="<EMAIL> or device"
                      className="export-input"
                    />
                  </div>
                  <div className="export-actions">
                    <button
                      onClick={exportPlaylist}
                      disabled={exportStatus === 'exporting' || !exportDestination}
                      className="action-button primary"
                    >
                      <Download size={16} />
                      <span>{exportStatus === 'exporting' ? 'Exporting...' : 'Export'}</span>
                    </button>
                  </div>
                </div>
                
                {exportStatus === 'success' && (
                  <div className="status-message success">
                    <CheckCircle size={16} />
                    <span>Playlist exported successfully!</span>
                  </div>
                )}
                
                {exportStatus === 'error' && (
                  <div className="status-message error">
                    <AlertCircle size={16} />
                    <span>Export failed. Please try again.</span>
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Errors */}
          {state.errors.length > 0 && (
            <div className="section">
              <div className="section-header">
                <h3>Errors ({state.errors.length})</h3>
              </div>
              <div className="error-list">
                {state.errors.map((error, index) => (
                  <div key={index} className="error-item">
                    <AlertCircle className="error-icon" />
                    <span>{error}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Hidden Media Elements */}
        <video ref={videoRef} style={{ display: 'none' }} />
        <audio ref={audioRef} style={{ display: 'none' }} />
      </div>
    </div>
  )
}

export default MultimediaPlaylistPanelOptimized