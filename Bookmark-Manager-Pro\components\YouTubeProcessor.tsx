import { FC, useCallback, useEffect, useState } from 'react';
import { youtubeService } from '../services/youtubeService';
import { Bookmark, YouTubeProcessingResult } from '../types';
import { CalendarIcon, ClockIcon, DocumentTextIcon, ExclamationTriangleIcon, EyeIcon, PlayIcon, SparklesIcon, UserIcon } from './icons/HeroIcons';
import InstructionSlideViewer from './InstructionSlideViewer';
import LoadingSpinner from './LoadingSpinner';
import ProgressBar from './ProgressBar';

interface YouTubeProcessorProps {
  bookmark: Bookmark;
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  onClose?: () => void;
}

interface ProcessingState {
  isProcessing: boolean;
  stage: 'idle' | 'fetching-info' | 'extracting-transcript' | 'processing-ai' | 'complete' | 'error';
  progress: number;
  error?: string;
}

const YouTubeProcessor: FC<YouTubeProcessorProps> = ({
  bookmark,
  onUpdateBookmark,
  onClose
}) => {
  const [processingState, setProcessingState] = useState<ProcessingState>({
    isProcessing: false,
    stage: 'idle',
    progress: 0
  });
  const [result, setResult] = useState<YouTubeProcessingResult | null>(null);
  const [showInstructions, setShowInstructions] = useState(false);
  const [mcpAgentAvailable, setMcpAgentAvailable] = useState<boolean | null>(null);

  // Check MCP agent availability on mount
  useEffect(() => {
    const checkAgent = async () => {
      const available = await youtubeService.checkMCPAgentStatus();
      setMcpAgentAvailable(available);
    };
    checkAgent();
  }, []);

  const isYouTubeUrl = youtubeService.isYouTubeUrl(bookmark.url);

  const processVideo = useCallback(async () => {
    if (!isYouTubeUrl) return;

    setProcessingState({
      isProcessing: true,
      stage: 'fetching-info',
      progress: 10
    });

    try {
      // Simulate progress updates
      const updateProgress = (stage: ProcessingState['stage'], progress: number) => {
        setProcessingState(prev => ({ ...prev, stage, progress }));
      };

      updateProgress('fetching-info', 25);
      await new Promise(resolve => setTimeout(resolve, 500)); // Visual delay

      updateProgress('extracting-transcript', 50);
      await new Promise(resolve => setTimeout(resolve, 500));

      updateProgress('processing-ai', 75);
      await new Promise(resolve => setTimeout(resolve, 500));

      const processingResult = await youtubeService.processYouTubeUrl(bookmark.url);
      
      if (!processingResult) {
        throw new Error('Failed to process YouTube video');
      }

      setResult(processingResult);
      updateProgress('complete', 100);

      // Update bookmark with processed information
      const updates: Partial<Bookmark> = {
        title: processingResult.videoInfo.title,
        summary: processingResult.transcript.summary,
        tags: [
          ...(bookmark.tags || []),
          'YouTube',
          'Video',
          ...(processingResult.transcript.instructions ? ['Tutorial', 'Instructions'] : [])
        ].filter((tag, index, arr) => arr.indexOf(tag) === index) // Remove duplicates
      };

      onUpdateBookmark(bookmark.id, updates);

    } catch (error) {
      setProcessingState({
        isProcessing: false,
        stage: 'error',
        progress: 0,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  }, [bookmark, isYouTubeUrl, onUpdateBookmark]);

  const getStageText = (stage: ProcessingState['stage']): string => {
    switch (stage) {
      case 'fetching-info': return 'Fetching video information...';
      case 'extracting-transcript': return 'Extracting transcript...';
      case 'processing-ai': return 'Processing with AI...';
      case 'complete': return 'Processing complete!';
      case 'error': return 'Processing failed';
      default: return 'Ready to process';
    }
  };

  const formatDuration = (duration: string): string => {
    // Convert ISO 8601 duration to readable format
    const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
    if (!match) return duration;
    
    const hours = parseInt(match[1] || '0');
    const minutes = parseInt(match[2] || '0');
    const seconds = parseInt(match[3] || '0');
    
    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatNumber = (num: number): string => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  if (!isYouTubeUrl) {
    return (
      <div className="bg-slate-800 border border-slate-700 rounded-lg p-4">
        <div className="flex items-center text-amber-400">
          <ExclamationTriangleIcon className="w-5 h-5 mr-2" />
          <span className="text-sm">This bookmark is not a YouTube video URL.</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-slate-800 border border-slate-700 rounded-lg p-6">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white flex items-center">
          <PlayIcon className="w-5 h-5 mr-2 text-red-500" />
          YouTube Video Processor
        </h3>
        {onClose && (
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white transition-colors"
          >
            ×
          </button>
        )}
      </div>

      {/* MCP Agent Status */}
      {mcpAgentAvailable !== null && (
        <div className={`mb-4 p-3 rounded-md ${mcpAgentAvailable ? 'bg-green-900/20 border border-green-700' : 'bg-amber-900/20 border border-amber-700'}`}>
          <div className="flex items-center text-sm">
            <div className={`w-2 h-2 rounded-full mr-2 ${mcpAgentAvailable ? 'bg-green-400' : 'bg-amber-400'}`}></div>
            <span className={mcpAgentAvailable ? 'text-green-300' : 'text-amber-300'}>
              MCP Build Agent: {mcpAgentAvailable ? 'Available' : 'Unavailable (using fallback methods)'}
            </span>
          </div>
        </div>
      )}

      {/* Processing Controls */}
      {!result && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-slate-300">
              <div className="font-medium mb-1">Current URL:</div>
              <div className="text-sky-400 break-all">{bookmark.url}</div>
            </div>
          </div>

          {processingState.isProcessing ? (
            <div className="space-y-3">
              <div className="flex items-center text-sm text-slate-300 mb-2">
                <LoadingSpinner size="sm" className="mr-2" />
                {getStageText(processingState.stage)}
              </div>
              <ProgressBar 
                progress={processingState.progress} 
                size="md" 
                color="primary" 
                showPercentage={false}
                animated={true}
              />
            </div>
          ) : (
            <div className="flex gap-3">
              <button
                onClick={processVideo}
                disabled={processingState.stage === 'error'}
                className="flex items-center px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                <SparklesIcon className="w-4 h-4 mr-2" />
                Process Video
              </button>
            </div>
          )}

          {processingState.stage === 'error' && processingState.error && (
            <div className="bg-red-900/20 border border-red-700 rounded-md p-3">
              <div className="flex items-center text-red-300 text-sm">
                <ExclamationTriangleIcon className="w-4 h-4 mr-2" />
                {processingState.error}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Processing Results */}
      {result && (
        <div className="space-y-6">
          {/* Video Information */}
          <div className="bg-slate-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-3 flex items-center">
              <DocumentTextIcon className="w-4 h-4 mr-2" />
              Video Information
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <div>
                  <span className="text-xs text-slate-400">Title:</span>
                  <div className="text-sm text-white">{result.videoInfo.title}</div>
                </div>
                <div>
                  <span className="text-xs text-slate-400">Channel:</span>
                  <div className="text-sm text-slate-300 flex items-center">
                    <UserIcon className="w-3 h-3 mr-1" />
                    {result.videoInfo.channelName}
                  </div>
                </div>
                <div>
                  <span className="text-xs text-slate-400">Duration:</span>
                  <div className="text-sm text-slate-300 flex items-center">
                    <ClockIcon className="w-3 h-3 mr-1" />
                    {formatDuration(result.videoInfo.duration)}
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <div>
                  <span className="text-xs text-slate-400">Views:</span>
                  <div className="text-sm text-slate-300 flex items-center">
                    <EyeIcon className="w-3 h-3 mr-1" />
                    {formatNumber(result.videoInfo.viewCount)}
                  </div>
                </div>
                <div>
                  <span className="text-xs text-slate-400">Published:</span>
                  <div className="text-sm text-slate-300 flex items-center">
                    <CalendarIcon className="w-3 h-3 mr-1" />
                    {new Date(result.videoInfo.publishedAt).toLocaleDateString()}
                  </div>
                </div>
                <div>
                  <span className="text-xs text-slate-400">Processing Time:</span>
                  <div className="text-sm text-slate-300">
                    {(result.processingTime / 1000).toFixed(1)}s
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Summary */}
          <div className="bg-slate-700 rounded-lg p-4">
            <h4 className="text-white font-medium mb-2">Summary</h4>
            <p className="text-sm text-slate-300 leading-relaxed">
              {result.transcript.summary}
            </p>
          </div>

          {/* Key Points */}
          {result.transcript.keyPoints.length > 0 && (
            <div className="bg-slate-700 rounded-lg p-4">
              <h4 className="text-white font-medium mb-3">Key Points</h4>
              <ul className="space-y-2">
                {result.transcript.keyPoints.map((point, index) => (
                  <li key={index} className="text-sm text-slate-300 flex items-start">
                    <span className="text-sky-400 mr-2">•</span>
                    {point}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Instructions */}
          {result.transcript.instructions && result.transcript.instructions.length > 0 && (
            <div className="bg-slate-700 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="text-white font-medium">Instructions Detected</h4>
                <button
                  onClick={() => setShowInstructions(true)}
                  className="px-3 py-1 bg-sky-600 text-white text-sm rounded hover:bg-sky-700 transition-colors"
                >
                  View Slides
                </button>
              </div>
              <div className="text-sm text-slate-300 mb-3">
                Found {result.transcript.instructions.length} instruction steps
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {result.transcript.instructions.slice(0, 3).map((step) => (
                  <div key={step.id} className="bg-slate-600 rounded p-2">
                    <div className="font-medium text-white text-xs mb-1">
                      {step.title}
                    </div>
                    <div className="text-xs text-slate-300 line-clamp-2">
                      {step.description}
                    </div>
                  </div>
                ))}
                {result.transcript.instructions.length > 3 && (
                  <div className="text-xs text-slate-400 text-center py-1">
                    +{result.transcript.instructions.length - 3} more steps
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Confidence Indicator */}
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center">
              <span className="text-slate-400 mr-2">Processing Confidence:</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                result.confidence === 'high' ? 'bg-green-900/20 text-green-300' :
                result.confidence === 'medium' ? 'bg-yellow-900/20 text-yellow-300' :
                'bg-red-900/20 text-red-300'
              }`}>
                {result.confidence.toUpperCase()}
              </span>
            </div>
            <button
              onClick={() => setResult(null)}
              className="text-slate-400 hover:text-white transition-colors"
            >
              Process Again
            </button>
          </div>
        </div>
      )}

      {/* Instruction Slide Viewer Modal */}
      {showInstructions && result?.transcript.instructions && (
        <InstructionSlideViewer
          instructions={result.transcript.instructions}
          videoInfo={result.videoInfo}
          onClose={() => setShowInstructions(false)}
        />
      )}
    </div>
  );
};

export default YouTubeProcessor;