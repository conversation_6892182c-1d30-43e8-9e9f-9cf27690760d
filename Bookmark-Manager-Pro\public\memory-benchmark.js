
// Simple memory benchmark
console.log('🧠 Memory Benchmark Starting...')

const measureMemory = () => {
  if (performance.memory) {
    const memory = performance.memory
    console.log(`Memory Usage: ${Math.round(memory.usedJSHeapSize / 1024 / 1024)}MB`)
    console.log(`Memory Limit: ${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)}MB`)
    console.log(`Usage: ${Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100)}%`)
  }
}

// Run benchmark every 10 seconds
setInterval(measureMemory, 10000)
measureMemory() // Initial measurement
