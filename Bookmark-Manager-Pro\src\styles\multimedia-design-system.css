/* ==========================================================================
   MULTIMEDIA ORGANIZATION PANEL - DESIGN SYSTEM (PHASE 1)
   Modern UI Foundation with Professional Styling
   ========================================================================== */

/* Design Tokens */
:root {
  /* Color Palette - Modern & Professional */
  --multimedia-primary: #6366f1;
  --multimedia-primary-hover: #5855eb;
  --multimedia-primary-light: #e0e7ff;
  --multimedia-secondary: #64748b;
  --multimedia-accent: #f59e0b;
  --multimedia-success: #10b981;
  --multimedia-warning: #f59e0b;
  --multimedia-error: #ef4444;
  
  /* Neutral Colors */
  --multimedia-bg-primary: #ffffff;
  --multimedia-bg-secondary: #f8fafc;
  --multimedia-bg-tertiary: #f1f5f9;
  --multimedia-surface: #ffffff;
  --multimedia-surface-hover: #f8fafc;
  --multimedia-border: #e2e8f0;
  --multimedia-border-light: #f1f5f9;
  
  /* Text Colors */
  --multimedia-text-primary: #1e293b;
  --multimedia-text-secondary: #64748b;
  --multimedia-text-muted: #94a3b8;
  --multimedia-text-inverse: #ffffff;
  
  /* Typography */
  --multimedia-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --multimedia-font-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  /* Font Sizes */
  --multimedia-text-xs: 0.75rem;
  --multimedia-text-sm: 0.875rem;
  --multimedia-text-base: 1rem;
  --multimedia-text-lg: 1.125rem;
  --multimedia-text-xl: 1.25rem;
  --multimedia-text-2xl: 1.5rem;
  
  /* Spacing Scale */
  --multimedia-space-1: 0.25rem;
  --multimedia-space-2: 0.5rem;
  --multimedia-space-3: 0.75rem;
  --multimedia-space-4: 1rem;
  --multimedia-space-5: 1.25rem;
  --multimedia-space-6: 1.5rem;
  --multimedia-space-8: 2rem;
  --multimedia-space-10: 2.5rem;
  --multimedia-space-12: 3rem;
  
  /* Border Radius */
  --multimedia-radius-sm: 0.375rem;
  --multimedia-radius-md: 0.5rem;
  --multimedia-radius-lg: 0.75rem;
  --multimedia-radius-xl: 1rem;
  
  /* Shadows */
  --multimedia-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --multimedia-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --multimedia-shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --multimedia-shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --multimedia-transition-fast: 150ms ease-in-out;
  --multimedia-transition-normal: 250ms ease-in-out;
  --multimedia-transition-slow: 350ms ease-in-out;
}

/* Dark Theme Overrides */
[data-theme="dark"] {
  --multimedia-bg-primary: #0f172a;
  --multimedia-bg-secondary: #1e293b;
  --multimedia-bg-tertiary: #334155;
  --multimedia-surface: #1e293b;
  --multimedia-surface-hover: #334155;
  --multimedia-border: #334155;
  --multimedia-border-light: #475569;
  
  --multimedia-text-primary: #f8fafc;
  --multimedia-text-secondary: #cbd5e1;
  --multimedia-text-muted: #94a3b8;
}

/* ==========================================================================
   COMPONENT ARCHITECTURE - MODERN LAYOUT SYSTEM
   ========================================================================== */

/* Panel Container */
.multimedia-panel {
  font-family: var(--multimedia-font-family);
  background: var(--multimedia-bg-primary);
  border-radius: var(--multimedia-radius-lg);
  box-shadow: var(--multimedia-shadow-xl);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 90vh;
}

/* Header Section */
.multimedia-header {
  background: linear-gradient(135deg, var(--multimedia-primary) 0%, var(--multimedia-primary-hover) 100%);
  color: var(--multimedia-text-inverse);
  padding: var(--multimedia-space-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--multimedia-border);
}

.multimedia-header-title {
  font-size: var(--multimedia-text-xl);
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
}

.multimedia-header-icon {
  width: 24px;
  height: 24px;
  opacity: 0.9;
}

.multimedia-close-button {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: var(--multimedia-radius-md);
  color: var(--multimedia-text-inverse);
  cursor: pointer;
  padding: var(--multimedia-space-2);
  transition: background-color var(--multimedia-transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
}

.multimedia-close-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Content Area */
.multimedia-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--multimedia-space-6);
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-space-6);
}

/* Section Components */
.multimedia-section {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  overflow: hidden;
  transition: box-shadow var(--multimedia-transition-normal);
}

.multimedia-section:hover {
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-section-header {
  background: var(--multimedia-bg-secondary);
  padding: var(--multimedia-space-4) var(--multimedia-space-5);
  border-bottom: 1px solid var(--multimedia-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.multimedia-section-title {
  font-size: var(--multimedia-text-lg);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-2);
}

.multimedia-section-content {
  padding: var(--multimedia-space-5);
}

/* Button System */
.multimedia-button {
  font-family: var(--multimedia-font-family);
  font-size: var(--multimedia-text-sm);
  font-weight: 500;
  border: none;
  border-radius: var(--multimedia-radius-md);
  cursor: pointer;
  transition: all var(--multimedia-transition-fast);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--multimedia-space-2);
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
}

.multimedia-button:focus-visible {
  outline: 2px solid var(--multimedia-primary);
  outline-offset: 2px;
}

/* Button Variants */
.multimedia-button--primary {
  background: var(--multimedia-primary);
  color: var(--multimedia-text-inverse);
  padding: var(--multimedia-space-3) var(--multimedia-space-5);
  box-shadow: var(--multimedia-shadow-sm);
}

.multimedia-button--primary:hover {
  background: var(--multimedia-primary-hover);
  box-shadow: var(--multimedia-shadow-md);
  transform: translateY(-1px);
}

.multimedia-button--secondary {
  background: var(--multimedia-surface);
  color: var(--multimedia-text-primary);
  border: 1px solid var(--multimedia-border);
  padding: var(--multimedia-space-3) var(--multimedia-space-5);
}

.multimedia-button--secondary:hover {
  background: var(--multimedia-surface-hover);
  border-color: var(--multimedia-primary);
  color: var(--multimedia-primary);
}

.multimedia-button--ghost {
  background: transparent;
  color: var(--multimedia-text-secondary);
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
}

.multimedia-button--ghost:hover {
  background: var(--multimedia-surface-hover);
  color: var(--multimedia-text-primary);
}

/* Button Sizes */
.multimedia-button--sm {
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
  font-size: var(--multimedia-text-xs);
}

.multimedia-button--lg {
  padding: var(--multimedia-space-4) var(--multimedia-space-6);
  font-size: var(--multimedia-text-base);
}

/* Input System */
.multimedia-input {
  font-family: var(--multimedia-font-family);
  font-size: var(--multimedia-text-sm);
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  color: var(--multimedia-text-primary);
  transition: all var(--multimedia-transition-fast);
  width: 100%;
  outline: none;
}

.multimedia-input:focus {
  border-color: var(--multimedia-primary);
  box-shadow: 0 0 0 3px var(--multimedia-primary-light);
}

.multimedia-input::placeholder {
  color: var(--multimedia-text-muted);
}

/* Form Groups */
.multimedia-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-space-2);
}

.multimedia-label {
  font-size: var(--multimedia-text-sm);
  font-weight: 500;
  color: var(--multimedia-text-primary);
  margin: 0;
}

/* Grid System */
.multimedia-grid {
  display: grid;
  gap: var(--multimedia-space-4);
}

.multimedia-grid--2 {
  grid-template-columns: repeat(2, 1fr);
}

.multimedia-grid--3 {
  grid-template-columns: repeat(3, 1fr);
}

/* Responsive Grid */
@media (max-width: 768px) {
  .multimedia-grid--2,
  .multimedia-grid--3 {
    grid-template-columns: 1fr;
  }
}

/* Status Indicators */
.multimedia-status {
  display: inline-flex;
  align-items: center;
  gap: var(--multimedia-space-2);
  padding: var(--multimedia-space-1) var(--multimedia-space-3);
  border-radius: var(--multimedia-radius-sm);
  font-size: var(--multimedia-text-xs);
  font-weight: 500;
}

.multimedia-status--success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--multimedia-success);
}

.multimedia-status--warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--multimedia-warning);
}

.multimedia-status--error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--multimedia-error);
}

/* Loading States */
.multimedia-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--multimedia-space-8);
  color: var(--multimedia-text-muted);
}

.multimedia-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--multimedia-border);
  border-top: 2px solid var(--multimedia-primary);
  border-radius: 50%;
  animation: multimedia-spin 1s linear infinite;
}

@keyframes multimedia-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility Classes */
.multimedia-flex {
  display: flex;
}

.multimedia-flex-col {
  flex-direction: column;
}

.multimedia-items-center {
  align-items: center;
}

.multimedia-justify-between {
  justify-content: space-between;
}

.multimedia-gap-2 {
  gap: var(--multimedia-space-2);
}

.multimedia-gap-4 {
  gap: var(--multimedia-space-4);
}

.multimedia-text-center {
  text-align: center;
}

.multimedia-w-full {
  width: 100%;
}

.multimedia-h-full {
  height: 100%;
}

/* Animation Classes */
.multimedia-fade-in {
  animation: multimedia-fadeIn 0.3s ease-in-out;
}

@keyframes multimedia-fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.multimedia-slide-up {
  animation: multimedia-slideUp 0.3s ease-out;
}

@keyframes multimedia-slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==========================================================================
   PHASE 2: ENHANCED COMPONENT LIBRARY
   Advanced UI Components & Interactive Elements
   ========================================================================== */

/* ==========================================================================
   PHASE 3: ADVANCED MEDIA PLAYER COMPONENTS
   ========================================================================== */

/* Media Player Container */
.multimedia-media-player {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  overflow: hidden;
  box-shadow: var(--multimedia-shadow-md);
  transition: var(--multimedia-transition-normal);
}

.multimedia-media-player:hover {
  box-shadow: var(--multimedia-shadow-lg);
}

/* Empty State */
.multimedia-media-player--empty {
  padding: var(--multimedia-space-12);
  text-align: center;
}

.multimedia-media-player__empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--multimedia-space-6);
  max-width: 400px;
  margin: 0 auto;
}

.multimedia-media-player__empty-icon {
  font-size: 4rem;
  opacity: 0.5;
}

.multimedia-media-player__empty-title {
  font-size: var(--multimedia-text-xl);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0;
}

.multimedia-media-player__empty-description {
  color: var(--multimedia-text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Video Container */
.multimedia-media-player__video-container {
  position: relative;
  background: #000;
  aspect-ratio: 16/9;
  overflow: hidden;
}

.multimedia-media-player__video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  cursor: pointer;
}

.multimedia-media-player__video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: var(--multimedia-transition-normal);
  pointer-events: none;
}

.multimedia-media-player__video-container:hover .multimedia-media-player__video-overlay {
  opacity: 1;
}

/* Audio Visualization */
.multimedia-media-player__audio-visual {
  position: relative;
  background: linear-gradient(135deg, var(--multimedia-primary-light), var(--multimedia-primary));
  padding: var(--multimedia-space-12);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.multimedia-media-player__album-art {
  width: 120px;
  height: 120px;
  background: var(--multimedia-surface);
  border-radius: var(--multimedia-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--multimedia-shadow-lg);
  margin-bottom: var(--multimedia-space-6);
}

.multimedia-media-player__audio-icon {
  font-size: 3rem;
  opacity: 0.7;
}

/* Loading and Error States */
.multimedia-media-player__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--multimedia-space-3);
  color: white;
  font-weight: 500;
  pointer-events: auto;
}

.multimedia-media-player__error {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
  color: var(--multimedia-error);
  background: rgba(239, 68, 68, 0.1);
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  border-radius: var(--multimedia-radius-md);
  font-weight: 500;
  pointer-events: auto;
}

/* Media Controls Container */
.multimedia-media-controls {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-6);
  box-shadow: var(--multimedia-shadow-md);
  transition: var(--multimedia-transition-normal);
  position: relative;
  overflow: hidden;
}

.multimedia-media-controls:hover {
  box-shadow: var(--multimedia-shadow-lg);
  transform: translateY(-1px);
}

/* Media Controls Integration */
.multimedia-media-player__controls {
  border-top: 1px solid var(--multimedia-border);
  border-radius: 0;
  box-shadow: none;
}

/* Playlist Info */
.multimedia-media-player__playlist-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  background: var(--multimedia-bg-secondary);
  border-top: 1px solid var(--multimedia-border);
  font-size: var(--multimedia-text-sm);
  color: var(--multimedia-text-secondary);
}

.multimedia-media-player__playlist-count {
  font-weight: 500;
}

.multimedia-media-player__loop-indicator {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-2);
  color: var(--multimedia-primary);
  font-weight: 500;
}

/* Progress Section */
.multimedia-media-controls__progress {
  margin-bottom: var(--multimedia-space-4);
}

.multimedia-media-controls__progress-slider {
  margin-bottom: var(--multimedia-space-2);
}

.multimedia-media-controls__time-display {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: var(--multimedia-text-sm);
  color: var(--multimedia-text-secondary);
  font-family: var(--multimedia-font-mono);
}

.multimedia-media-controls__current-time,
.multimedia-media-controls__duration {
  min-width: 45px;
  text-align: center;
}

/* Control Buttons Section */
.multimedia-media-controls__buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--multimedia-space-4);
  margin-bottom: var(--multimedia-space-4);
}

.multimedia-media-controls__btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 50%;
  background: var(--multimedia-surface);
  border: 2px solid var(--multimedia-border);
  color: var(--multimedia-text-primary);
  cursor: pointer;
  transition: var(--multimedia-transition-fast);
  position: relative;
  overflow: hidden;
}

.multimedia-media-controls__btn:hover {
  background: var(--multimedia-primary-light);
  border-color: var(--multimedia-primary);
  color: var(--multimedia-primary);
  transform: scale(1.05);
}

.multimedia-media-controls__btn:active {
  transform: scale(0.95);
}

.multimedia-media-controls__btn:focus {
  outline: 2px solid var(--multimedia-primary);
  outline-offset: 2px;
}

.multimedia-media-controls__btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Play/Pause Button - Larger and More Prominent */
.multimedia-media-controls__btn--play-pause {
  width: 56px;
  height: 56px;
  background: var(--multimedia-primary);
  color: white;
  border-color: var(--multimedia-primary);
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-media-controls__btn--play-pause:hover {
  background: var(--multimedia-primary-hover);
  border-color: var(--multimedia-primary-hover);
  color: white;
  box-shadow: var(--multimedia-shadow-lg);
}

/* Volume Controls */
.multimedia-media-controls__volume {
  position: relative;
  display: flex;
  align-items: center;
}

.multimedia-media-controls__volume-slider {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-2);
  box-shadow: var(--multimedia-shadow-lg);
  z-index: 10;
  min-width: 120px;
  margin-bottom: var(--multimedia-space-1);
}

.multimedia-media-controls__volume-slider::before {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--multimedia-border);
}

.multimedia-media-controls__volume-slider::after {
  content: '';
  position: absolute;
  top: calc(100% - 1px);
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--multimedia-surface);
}

/* Current Item Info */
.multimedia-media-controls__info {
  text-align: center;
  border-top: 1px solid var(--multimedia-border);
  padding-top: var(--multimedia-space-4);
}

.multimedia-media-controls__title {
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin-bottom: var(--multimedia-space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.multimedia-media-controls__meta {
  font-size: var(--multimedia-text-sm);
  color: var(--multimedia-text-secondary);
}

/* Custom Slider Component */
.multimedia-slider {
  position: relative;
  width: 100%;
  height: 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  outline: none;
}

.multimedia-slider:focus {
  outline: 2px solid var(--multimedia-primary);
  outline-offset: 2px;
  border-radius: var(--multimedia-radius-sm);
}

.multimedia-slider__track {
  position: relative;
  width: 100%;
  height: 4px;
  background: var(--multimedia-border);
  border-radius: 2px;
  overflow: hidden;
}

.multimedia-slider__progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--multimedia-primary), var(--multimedia-accent));
  border-radius: 2px;
  transition: width 0.1s ease;
}

.multimedia-slider__thumb {
  position: absolute;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background: var(--multimedia-primary);
  border: 2px solid white;
  border-radius: 50%;
  box-shadow: var(--multimedia-shadow-sm);
  transition: var(--multimedia-transition-fast);
  opacity: 0;
}

.multimedia-slider:hover .multimedia-slider__thumb,
.multimedia-slider:focus .multimedia-slider__thumb {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.2);
}

.multimedia-slider:active .multimedia-slider__thumb {
  transform: translate(-50%, -50%) scale(1.4);
}

/* Volume Slider Specific Styling */
.multimedia-media-controls__volume-slider-input {
  height: 16px;
}

.multimedia-media-controls__volume-slider-input .multimedia-slider__track {
  height: 3px;
}

.multimedia-media-controls__volume-slider-input .multimedia-slider__thumb {
  width: 12px;
  height: 12px;
}

/* Responsive Design for Media Controls */
@media (max-width: 768px) {
  .multimedia-media-controls {
    padding: var(--multimedia-space-4);
  }
  
  .multimedia-media-controls__buttons {
    gap: var(--multimedia-space-2);
  }
  
  .multimedia-media-controls__btn {
    width: 40px;
    height: 40px;
  }
  
  .multimedia-media-controls__btn--play-pause {
    width: 48px;
    height: 48px;
  }
  
  .multimedia-media-controls__volume-slider {
    min-width: 100px;
  }
}

/* Animation for Media Controls */
@keyframes multimedia-pulse-play {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.multimedia-media-controls__btn--play-pause.multimedia-playing {
  animation: multimedia-pulse-play 2s ease-in-out infinite;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  .multimedia-media-controls__btn {
    border-width: 3px;
  }
  
  .multimedia-slider__progress {
    background: var(--multimedia-text-primary);
  }
  
  .multimedia-slider__thumb {
    border-width: 3px;
    border-color: var(--multimedia-bg-primary);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .multimedia-media-controls,
  .multimedia-media-controls__btn,
  .multimedia-slider__thumb,
  .multimedia-slider__progress {
    transition: none;
  }
  
  .multimedia-media-controls__btn--play-pause.multimedia-playing {
    animation: none;
  }
}

/* Enhanced Button System with Advanced States */
.multimedia-btn {
  font-family: var(--multimedia-font-family);
  font-size: var(--multimedia-text-sm);
  font-weight: 500;
  border: none;
  border-radius: var(--multimedia-radius-md);
  cursor: pointer;
  transition: all var(--multimedia-transition-normal);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--multimedia-space-2);
  text-decoration: none;
  outline: none;
  position: relative;
  overflow: hidden;
  padding: var(--multimedia-space-3) var(--multimedia-space-5);
  min-height: 40px;
  user-select: none;
}

.multimedia-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--multimedia-transition-slow);
}

.multimedia-btn:hover::before {
  left: 100%;
}

.multimedia-btn:focus-visible {
  outline: 2px solid var(--multimedia-primary);
  outline-offset: 2px;
}

.multimedia-btn:active {
  transform: translateY(1px);
}

/* Button Variants with Enhanced Styling */
.multimedia-btn--primary {
  background: linear-gradient(135deg, var(--multimedia-primary) 0%, var(--multimedia-primary-hover) 100%);
  color: var(--multimedia-text-inverse);
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-btn--primary:hover {
  box-shadow: var(--multimedia-shadow-lg);
  transform: translateY(-2px);
}

.multimedia-btn--secondary {
  background: var(--multimedia-surface);
  color: var(--multimedia-text-primary);
  border: 2px solid var(--multimedia-border);
  box-shadow: var(--multimedia-shadow-sm);
}

.multimedia-btn--secondary:hover {
  background: var(--multimedia-surface-hover);
  border-color: var(--multimedia-primary);
  color: var(--multimedia-primary);
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-btn--danger {
  background: linear-gradient(135deg, var(--multimedia-error) 0%, #dc2626 100%);
  color: var(--multimedia-text-inverse);
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-btn--danger:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: var(--multimedia-shadow-lg);
  transform: translateY(-2px);
}

.multimedia-btn--success {
  background: linear-gradient(135deg, var(--multimedia-success) 0%, #059669 100%);
  color: var(--multimedia-text-inverse);
  box-shadow: var(--multimedia-shadow-md);
}

.multimedia-btn--success:hover {
  background: linear-gradient(135deg, #059669 0%, #047857 100%);
  box-shadow: var(--multimedia-shadow-lg);
  transform: translateY(-2px);
}

.multimedia-btn--ghost {
  background: transparent;
  color: var(--multimedia-text-secondary);
  border: 1px solid transparent;
}

.multimedia-btn--ghost:hover {
  background: var(--multimedia-surface-hover);
  color: var(--multimedia-text-primary);
  border-color: var(--multimedia-border);
}

/* Button Sizes */
.multimedia-btn--sm {
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
  font-size: var(--multimedia-text-xs);
  min-height: 32px;
}

.multimedia-btn--lg {
  padding: var(--multimedia-space-4) var(--multimedia-space-6);
  font-size: var(--multimedia-text-base);
  min-height: 48px;
}

.multimedia-btn--disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Enhanced Card System */
.multimedia-card {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-5);
  box-shadow: var(--multimedia-shadow-sm);
  transition: all var(--multimedia-transition-normal);
  position: relative;
  overflow: hidden;
}

.multimedia-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--multimedia-primary), var(--multimedia-accent));
  transform: scaleX(0);
  transition: transform var(--multimedia-transition-normal);
}

.multimedia-card:hover {
  box-shadow: var(--multimedia-shadow-lg);
  transform: translateY(-2px);
}

.multimedia-card:hover::before {
  transform: scaleX(1);
}

.multimedia-card--interactive {
  cursor: pointer;
}

.multimedia-card--highlighted {
  border-color: var(--multimedia-primary);
  box-shadow: 0 0 0 3px var(--multimedia-primary-light);
}

/* Enhanced Form Components */
.multimedia-form-input {
  font-family: var(--multimedia-font-family);
  font-size: var(--multimedia-text-sm);
  background: var(--multimedia-surface);
  border: 2px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  color: var(--multimedia-text-primary);
  transition: all var(--multimedia-transition-normal);
  width: 100%;
  outline: none;
  position: relative;
}

.multimedia-form-input:focus {
  border-color: var(--multimedia-primary);
  box-shadow: 0 0 0 3px var(--multimedia-primary-light);
  transform: translateY(-1px);
}

.multimedia-form-input::placeholder {
  color: var(--multimedia-text-muted);
}

.multimedia-form-textarea {
  font-family: var(--multimedia-font-family);
  font-size: var(--multimedia-text-sm);
  background: var(--multimedia-surface);
  border: 2px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  color: var(--multimedia-text-primary);
  transition: all var(--multimedia-transition-normal);
  width: 100%;
  outline: none;
  resize: vertical;
  min-height: 80px;
}

.multimedia-form-textarea:focus {
  border-color: var(--multimedia-primary);
  box-shadow: 0 0 0 3px var(--multimedia-primary-light);
}

.multimedia-form-group {
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-space-2);
  position: relative;
}

.multimedia-form-label {
  font-size: var(--multimedia-text-sm);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-2);
}

.multimedia-form-label--required::after {
  content: '*';
  color: var(--multimedia-error);
  margin-left: var(--multimedia-space-1);
}

/* Enhanced Radio Group System */
.multimedia-radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-space-3);
}

.multimedia-radio-option {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
  padding: var(--multimedia-space-3);
  border: 2px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  cursor: pointer;
  transition: all var(--multimedia-transition-normal);
  background: var(--multimedia-surface);
}

.multimedia-radio-option:hover {
  border-color: var(--multimedia-primary);
  background: var(--multimedia-surface-hover);
}

.multimedia-radio-option--selected {
  border-color: var(--multimedia-primary);
  background: var(--multimedia-primary-light);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.multimedia-radio-input {
  width: 18px;
  height: 18px;
  border: 2px solid var(--multimedia-border);
  border-radius: 50%;
  position: relative;
  transition: all var(--multimedia-transition-fast);
}

.multimedia-radio-input--checked {
  border-color: var(--multimedia-primary);
  background: var(--multimedia-primary);
}

.multimedia-radio-input--checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 6px;
  height: 6px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.multimedia-radio-label {
  font-size: var(--multimedia-text-sm);
  font-weight: 500;
  color: var(--multimedia-text-primary);
  cursor: pointer;
  flex: 1;
}

/* Advanced Bookmark Selector System */
.multimedia-bookmark-selector {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  overflow: hidden;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.multimedia-bookmark-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--multimedia-space-2);
}

.multimedia-collection-group {
  margin-bottom: var(--multimedia-space-4);
  border: 1px solid var(--multimedia-border-light);
  border-radius: var(--multimedia-radius-md);
  overflow: hidden;
  background: var(--multimedia-bg-secondary);
}

.multimedia-collection-header {
  background: var(--multimedia-bg-tertiary);
  padding: var(--multimedia-space-3) var(--multimedia-space-4);
  border-bottom: 1px solid var(--multimedia-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all var(--multimedia-transition-fast);
}

.multimedia-collection-header:hover {
  background: var(--multimedia-surface-hover);
}

.multimedia-collection-info {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
  flex: 1;
}

.multimedia-collection-icon {
  width: 20px;
  height: 20px;
  color: var(--multimedia-primary);
  flex-shrink: 0;
}

.multimedia-collection-name {
  font-size: var(--multimedia-text-sm);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0;
}

.multimedia-collection-count {
  background: var(--multimedia-primary-light);
  color: var(--multimedia-primary);
  font-size: var(--multimedia-text-xs);
  font-weight: 600;
  padding: var(--multimedia-space-1) var(--multimedia-space-2);
  border-radius: var(--multimedia-radius-sm);
  min-width: 24px;
  text-align: center;
}

.multimedia-collection-actions {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-2);
}

.multimedia-collection-select-btn {
  background: var(--multimedia-primary);
  color: var(--multimedia-text-inverse);
  border: none;
  border-radius: var(--multimedia-radius-sm);
  padding: var(--multimedia-space-1) var(--multimedia-space-2);
  font-size: var(--multimedia-text-xs);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--multimedia-transition-fast);
}

.multimedia-collection-select-btn:hover {
  background: var(--multimedia-primary-hover);
  transform: scale(1.05);
}

.multimedia-collection-bookmarks {
  padding: var(--multimedia-space-2);
  background: var(--multimedia-surface);
  max-height: 200px;
  overflow-y: auto;
}

.multimedia-bookmark-item {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
  border-radius: var(--multimedia-radius-sm);
  cursor: pointer;
  transition: all var(--multimedia-transition-fast);
  border: 1px solid transparent;
}

.multimedia-bookmark-item:hover {
  background: var(--multimedia-surface-hover);
  border-color: var(--multimedia-border);
}

.multimedia-bookmark-item--selected {
  background: var(--multimedia-primary-light);
  border-color: var(--multimedia-primary);
}

.multimedia-bookmark-checkbox {
  width: 16px;
  height: 16px;
  border: 2px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-sm);
  position: relative;
  transition: all var(--multimedia-transition-fast);
  flex-shrink: 0;
}

.multimedia-bookmark-checkbox--checked {
  background: var(--multimedia-primary);
  border-color: var(--multimedia-primary);
}

.multimedia-bookmark-checkbox--checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 10px;
  font-weight: bold;
}

.multimedia-bookmark-content {
  flex: 1;
  min-width: 0;
}

.multimedia-bookmark-title {
  font-size: var(--multimedia-text-sm);
  font-weight: 500;
  color: var(--multimedia-text-primary);
  margin: 0 0 var(--multimedia-space-1) 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multimedia-bookmark-url {
  font-size: var(--multimedia-text-xs);
  color: var(--multimedia-text-muted);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.multimedia-bookmark-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--multimedia-text-muted);
  flex-shrink: 0;
  transition: all var(--multimedia-transition-fast);
}

.multimedia-selected-indicator {
  background: var(--multimedia-success);
  box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
}

.multimedia-bookmark-limit-notice {
  background: var(--multimedia-warning);
  color: white;
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
  font-size: var(--multimedia-text-xs);
  font-weight: 500;
  text-align: center;
  border-radius: var(--multimedia-radius-sm);
  margin: var(--multimedia-space-2);
}

/* Results and Playlist Components */
.multimedia-results-container {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-4);
  max-height: 300px;
  overflow-y: auto;
}

.multimedia-result-item {
  display: flex;
  align-items: center;
  gap: var(--multimedia-space-3);
  padding: var(--multimedia-space-3);
  border: 1px solid var(--multimedia-border-light);
  border-radius: var(--multimedia-radius-md);
  margin-bottom: var(--multimedia-space-2);
  background: var(--multimedia-bg-secondary);
  transition: all var(--multimedia-transition-fast);
}

.multimedia-result-item:hover {
  background: var(--multimedia-surface-hover);
  border-color: var(--multimedia-primary);
  transform: translateX(4px);
}

.multimedia-playlist-info {
  background: linear-gradient(135deg, var(--multimedia-primary-light) 0%, var(--multimedia-surface) 100%);
  border: 1px solid var(--multimedia-primary);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-4);
  margin-bottom: var(--multimedia-space-4);
}

.multimedia-playlist-items {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-3);
  max-height: 200px;
  overflow-y: auto;
}

.multimedia-playlist-manager {
  background: var(--multimedia-bg-secondary);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-4);
}

.multimedia-saved-playlist-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--multimedia-space-3);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  margin-bottom: var(--multimedia-space-2);
  background: var(--multimedia-surface);
  transition: all var(--multimedia-transition-normal);
}

.multimedia-saved-playlist-item:hover {
  background: var(--multimedia-surface-hover);
  border-color: var(--multimedia-primary);
  box-shadow: var(--multimedia-shadow-sm);
}

/* Advanced Animation System */
.multimedia-pulse {
  animation: multimedia-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes multimedia-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.multimedia-bounce {
  animation: multimedia-bounce 1s infinite;
}

@keyframes multimedia-bounce {
  0%, 20%, 53%, 80%, 100% {
    animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -8px, 0);
  }
  70% {
    animation-timing-function: cubic-bezier(0.755, 0.05, 0.855, 0.06);
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

.multimedia-shake {
  animation: multimedia-shake 0.82s cubic-bezier(0.36, 0.07, 0.19, 0.97) both;
}

@keyframes multimedia-shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }
  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }
  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

/* Enhanced Section Headers */
.multimedia-section__header {
  background: linear-gradient(135deg, var(--multimedia-bg-secondary) 0%, var(--multimedia-surface) 100%);
  padding: var(--multimedia-space-4) var(--multimedia-space-5);
  border-bottom: 1px solid var(--multimedia-border);
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.multimedia-section__header::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--multimedia-primary), var(--multimedia-accent));
  transform: scaleX(0);
  transition: transform var(--multimedia-transition-normal);
}

.multimedia-section:hover .multimedia-section__header::before {
  transform: scaleX(1);
}

.multimedia-section__icon {
  width: 20px;
  height: 20px;
  color: var(--multimedia-primary);
  margin-right: var(--multimedia-space-2);
}

.multimedia-section__title {
  font-size: var(--multimedia-text-lg);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0;
  display: flex;
  align-items: center;
}

.multimedia-section__description {
  font-size: var(--multimedia-text-sm);
  color: var(--multimedia-text-secondary);
  margin: var(--multimedia-space-2) 0 0 0;
  line-height: 1.5;
}

/* Advanced Interactive States & Micro-interactions */
.multimedia-interactive {
  cursor: pointer;
  transition: all var(--multimedia-transition-normal);
  position: relative;
}

.multimedia-interactive::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
  opacity: 0;
  transform: scale(0.8);
  transition: all var(--multimedia-transition-fast);
  pointer-events: none;
  border-radius: inherit;
}

.multimedia-interactive:hover::after {
  opacity: 1;
  transform: scale(1);
}

.multimedia-interactive:active {
  transform: scale(0.98);
}

/* Enhanced Focus States for Accessibility */
.multimedia-focus-ring {
  outline: none;
  position: relative;
}

.multimedia-focus-ring:focus-visible {
  outline: 2px solid var(--multimedia-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px var(--multimedia-primary-light);
}

/* Advanced Loading States */
.multimedia-skeleton {
  background: linear-gradient(90deg, var(--multimedia-bg-secondary) 25%, var(--multimedia-surface-hover) 50%, var(--multimedia-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: multimedia-skeleton 1.5s infinite;
  border-radius: var(--multimedia-radius-md);
}

@keyframes multimedia-skeleton {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.multimedia-progress-bar {
  width: 100%;
  height: 4px;
  background: var(--multimedia-bg-secondary);
  border-radius: var(--multimedia-radius-sm);
  overflow: hidden;
  position: relative;
}

.multimedia-progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--multimedia-primary), var(--multimedia-accent));
  border-radius: var(--multimedia-radius-sm);
  transition: width var(--multimedia-transition-normal);
  position: relative;
}

.multimedia-progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: multimedia-progress-shine 2s infinite;
}

@keyframes multimedia-progress-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Enhanced Tooltip System */
.multimedia-tooltip {
  position: relative;
  display: inline-block;
}

.multimedia-tooltip::before {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-8px);
  background: var(--multimedia-text-primary);
  color: var(--multimedia-text-inverse);
  padding: var(--multimedia-space-2) var(--multimedia-space-3);
  border-radius: var(--multimedia-radius-md);
  font-size: var(--multimedia-text-xs);
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  transition: all var(--multimedia-transition-fast);
  z-index: 1000;
  box-shadow: var(--multimedia-shadow-lg);
}

.multimedia-tooltip::after {
  content: '';
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%) translateY(-2px);
  border: 4px solid transparent;
  border-top-color: var(--multimedia-text-primary);
  opacity: 0;
  visibility: hidden;
  transition: all var(--multimedia-transition-fast);
  z-index: 1000;
}

.multimedia-tooltip:hover::before,
.multimedia-tooltip:hover::after {
  opacity: 1;
  visibility: visible;
}

/* Advanced Badge System */
.multimedia-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--multimedia-space-1);
  padding: var(--multimedia-space-1) var(--multimedia-space-2);
  border-radius: var(--multimedia-radius-sm);
  font-size: var(--multimedia-text-xs);
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.multimedia-badge--primary {
  background: var(--multimedia-primary-light);
  color: var(--multimedia-primary);
}

.multimedia-badge--success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--multimedia-success);
}

.multimedia-badge--warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--multimedia-warning);
}

.multimedia-badge--error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--multimedia-error);
}

.multimedia-badge--pulse {
  animation: multimedia-badge-pulse 2s infinite;
}

@keyframes multimedia-badge-pulse {
  0%, 100% {
    box-shadow: 0 0 0 0 currentColor;
  }
  50% {
    box-shadow: 0 0 0 4px transparent;
  }
}

/* Enhanced Notification System */
.multimedia-notification {
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-lg);
  padding: var(--multimedia-space-4);
  box-shadow: var(--multimedia-shadow-xl);
  display: flex;
  align-items: flex-start;
  gap: var(--multimedia-space-3);
  position: relative;
  overflow: hidden;
}

.multimedia-notification::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: var(--multimedia-primary);
}

.multimedia-notification--success::before {
  background: var(--multimedia-success);
}

.multimedia-notification--warning::before {
  background: var(--multimedia-warning);
}

.multimedia-notification--error::before {
  background: var(--multimedia-error);
}

.multimedia-notification-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  margin-top: var(--multimedia-space-1);
}

.multimedia-notification-content {
  flex: 1;
}

.multimedia-notification-title {
  font-size: var(--multimedia-text-sm);
  font-weight: 600;
  color: var(--multimedia-text-primary);
  margin: 0 0 var(--multimedia-space-1) 0;
}

.multimedia-notification-message {
  font-size: var(--multimedia-text-sm);
  color: var(--multimedia-text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Performance Optimizations */
.multimedia-gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.multimedia-smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* Dark Mode Enhancements */
[data-theme="dark"] .multimedia-card::before {
  background: linear-gradient(90deg, var(--multimedia-primary), var(--multimedia-accent));
}

[data-theme="dark"] .multimedia-notification {
  background: var(--multimedia-surface);
  border-color: var(--multimedia-border);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 8px 10px -6px rgba(0, 0, 0, 0.2);
}

/* Print Styles */
@media print {
  .multimedia-panel {
    box-shadow: none;
    border: 1px solid #000;
  }
  
  .multimedia-btn,
  .multimedia-interactive {
    background: transparent !important;
    color: #000 !important;
    border: 1px solid #000 !important;
  }
  
  .multimedia-section {
    break-inside: avoid;
  }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
  :root {
    --multimedia-border: #000;
    --multimedia-text-primary: #000;
    --multimedia-text-secondary: #333;
  }
  
  .multimedia-btn--primary {
    background: #000;
    color: #fff;
    border: 2px solid #000;
  }
  
  .multimedia-btn--secondary {
    background: #fff;
    color: #000;
    border: 2px solid #000;
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .multimedia-skeleton {
    animation: none;
    background: var(--multimedia-bg-secondary);
  }
}

/* DEMO COMPONENT STYLING */
.multimedia-media-player-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--multimedia-space-8);
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-space-8);
}

.multimedia-demo-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--multimedia-space-4);
  align-items: center;
}

.multimedia-demo-actions {
  display: flex;
  gap: var(--multimedia-space-2);
  flex-wrap: wrap;
}

.multimedia-demo-player {
  border: 2px solid var(--multimedia-primary);
  border-radius: var(--multimedia-radius-lg);
}

.multimedia-demo-log {
  background: var(--multimedia-bg-secondary);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-space-4);
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--multimedia-border);
}

.multimedia-demo-log__empty {
  color: var(--multimedia-text-secondary);
  font-style: italic;
  margin: 0;
  text-align: center;
  padding: var(--multimedia-space-4);
}

.multimedia-demo-log__list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.multimedia-demo-log__item {
  font-family: var(--multimedia-font-mono);
  font-size: var(--multimedia-text-sm);
  padding: var(--multimedia-space-1) 0;
  border-bottom: 1px solid var(--multimedia-border);
  color: var(--multimedia-text-primary);
}

.multimedia-demo-log__item:last-child {
  border-bottom: none;
}

.multimedia-demo-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--multimedia-space-4);
}

.multimedia-demo-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--multimedia-space-2);
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
  transition: var(--multimedia-transition-normal);
}

.multimedia-demo-stat:hover {
  border-color: var(--multimedia-primary);
  transform: translateY(-1px);
  box-shadow: var(--multimedia-shadow-sm);
}

.multimedia-demo-stat__label {
  font-weight: 500;
  color: var(--multimedia-text-secondary);
}

.multimedia-demo-stat__value {
  font-weight: 600;
  color: var(--multimedia-primary);
  font-size: var(--multimedia-text-lg);
}

.multimedia-checkbox {
  margin-right: var(--multimedia-space-1);
  accent-color: var(--multimedia-primary);
}

/* Dark mode enhancements for demo */
[data-theme="dark"] .multimedia-demo-log {
  background: var(--multimedia-surface);
}

[data-theme="dark"] .multimedia-demo-stat {
  background: var(--multimedia-bg-secondary);
}

[data-theme="dark"] .multimedia-demo-stat:hover {
  background: var(--multimedia-surface);
}

/* Responsive demo layout */
@media (max-width: 768px) {
  .multimedia-media-player-demo {
    padding: var(--multimedia-space-4);
  }
  
  .multimedia-demo-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .multimedia-demo-actions {
    justify-content: center;
  }
  
  .multimedia-demo-info {
    grid-template-columns: 1fr;
  }
}

/* ==========================================================================
   PHASE 4: MOBILE OPTIMIZATION & ENHANCED DESIGN SYSTEM
   ========================================================================== */

/* ENHANCED COLOR PALETTE */
:root {
  /* Primary Gradient System */
  --multimedia-primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --multimedia-primary-500: #6366f1;
  --multimedia-primary-600: #4f46e5;
  --multimedia-primary-400: #818cf8;
  --multimedia-primary-700: #3730a3;
  
  /* Enhanced Neutral Palette */
  --multimedia-gray-50: #f9fafb;
  --multimedia-gray-100: #f3f4f6;
  --multimedia-gray-200: #e5e7eb;
  --multimedia-gray-300: #d1d5db;
  --multimedia-gray-400: #9ca3af;
  --multimedia-gray-500: #6b7280;
  --multimedia-gray-600: #4b5563;
  --multimedia-gray-700: #374151;
  --multimedia-gray-800: #1f2937;
  --multimedia-gray-900: #111827;
  
  /* Semantic Color System */
  --multimedia-success-50: #ecfdf5;
  --multimedia-success-100: #d1fae5;
  --multimedia-success-500: #10b981;
  --multimedia-success-600: #059669;
  --multimedia-success-700: #047857;
  
  --multimedia-warning-50: #fffbeb;
  --multimedia-warning-100: #fef3c7;
  --multimedia-warning-500: #f59e0b;
  --multimedia-warning-600: #d97706;
  --multimedia-warning-700: #b45309;
  
  --multimedia-error-50: #fef2f2;
  --multimedia-error-100: #fee2e2;
  --multimedia-error-500: #ef4444;
  --multimedia-error-600: #dc2626;
  --multimedia-error-700: #b91c1c;
  
  /* Info Colors */
  --multimedia-info-50: #eff6ff;
  --multimedia-info-100: #dbeafe;
  --multimedia-info-500: #3b82f6;
  --multimedia-info-600: #2563eb;
  --multimedia-info-700: #1d4ed8;
}

/* ENHANCED TYPOGRAPHY SCALE */
.multimedia-text-display {
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.025em;
}

.multimedia-text-heading-xl {
  font-size: 1.875rem; /* 30px */
  font-weight: 700;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

.multimedia-text-heading {
  font-size: 1.5rem; /* 24px */
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.025em;
}

.multimedia-text-heading-sm {
  font-size: 1.25rem; /* 20px */
  font-weight: 600;
  line-height: 1.35;
}

.multimedia-text-body-lg {
  font-size: 1.125rem; /* 18px */
  font-weight: 400;
  line-height: 1.5;
}

.multimedia-text-body {
  font-size: 1rem; /* 16px */
  font-weight: 400;
  line-height: 1.5;
}

.multimedia-text-body-sm {
  font-size: 0.875rem; /* 14px */
  font-weight: 400;
  line-height: 1.5;
}

.multimedia-text-caption {
  font-size: 0.75rem; /* 12px */
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

/* MOBILE-FIRST RESPONSIVE DESIGN */
.multimedia-panel {
  /* Mobile (default) */
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--multimedia-space-4);
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
}

.multimedia-grid-2,
.multimedia-grid-3 {
  /* Mobile: Single column */
  grid-template-columns: 1fr;
  gap: var(--multimedia-space-4);
}

/* TOUCH-FRIENDLY CONTROLS */
.multimedia-control-btn,
.multimedia-btn {
  min-height: 44px;
  min-width: 44px;
  touch-action: manipulation;
  -webkit-tap-highlight-color: transparent;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.multimedia-control-btn:active,
.multimedia-btn:active {
  transform: scale(0.98);
  transition: transform 0.1s ease;
}

/* Touch feedback */
.multimedia-control-btn::after,
.multimedia-btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
  pointer-events: none;
}

.multimedia-control-btn:active::after,
.multimedia-btn:active::after {
  width: 100px;
  height: 100px;
}

/* TABLET RESPONSIVE DESIGN */
@media (min-width: 768px) {
  .multimedia-panel {
    padding: 20px;
    grid-template-columns: 1fr 1fr;
    gap: var(--multimedia-space-5);
  }
  
  .multimedia-grid-2 {
    grid-template-columns: 1fr 1fr;
  }
  
  .multimedia-grid-3 {
    grid-template-columns: 1fr 1fr;
  }
  
  .multimedia-text-display {
    font-size: 2.5rem;
  }
  
  .multimedia-text-heading {
    font-size: 1.75rem;
  }
  
  /* Enhanced button sizing for tablet */
  .multimedia-btn {
    min-height: 48px;
    padding: 12px 24px;
  }
  
  .multimedia-control-btn {
    min-height: 48px;
    min-width: 48px;
  }
}

/* DESKTOP RESPONSIVE DESIGN */
@media (min-width: 1024px) {
  .multimedia-panel {
    padding: 24px;
    grid-template-columns: 1fr 1fr 1fr;
    gap: var(--multimedia-space-6);
  }
  
  .multimedia-grid-3 {
    grid-template-columns: 1fr 1fr 1fr;
  }
  
  .multimedia-text-display {
    font-size: 3rem;
  }
  
  .multimedia-text-heading {
    font-size: 2rem;
  }
  
  /* Desktop-optimized controls */
  .multimedia-btn {
    min-height: 40px;
    padding: 10px 20px;
  }
  
  .multimedia-control-btn {
    min-height: 40px;
    min-width: 40px;
  }
  
  /* Hover effects for desktop */
  .multimedia-control-btn:hover,
  .multimedia-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--multimedia-shadow-md);
  }
}

/* LARGE DESKTOP */
@media (min-width: 1280px) {
  .multimedia-panel {
    padding: 32px;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .multimedia-text-display {
    font-size: 3.5rem;
  }
}

/* MOBILE LANDSCAPE OPTIMIZATION */
@media (max-height: 500px) and (orientation: landscape) {
  .multimedia-panel {
    padding: 12px;
  }
  
  .multimedia-text-display {
    font-size: 1.75rem;
  }
  
  .multimedia-text-heading {
    font-size: 1.25rem;
  }
  
  .multimedia-btn {
    min-height: 36px;
    padding: 8px 16px;
  }
}

/* HIGH DPI DISPLAYS */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .multimedia-btn,
  .multimedia-control-btn {
    border-width: 0.5px;
  }
}

/* ==========================================================================
   LEGACY RESPONSIVE DESIGN FOUNDATION (PRESERVED)
   ========================================================================== */

/* Mobile First Approach */
@media (max-width: 640px) {
  .multimedia-panel {
    border-radius: 0;
    height: 100vh;
    max-height: 100vh;
  }
  
  .multimedia-header {
    padding: var(--multimedia-space-4);
  }
  
  .multimedia-content {
    padding: var(--multimedia-space-4);
    gap: var(--multimedia-space-4);
  }
  
  .multimedia-section-content {
    padding: var(--multimedia-space-4);
  }
}

/* Tablet */
@media (min-width: 641px) and (max-width: 1024px) {
  .multimedia-content {
    padding: var(--multimedia-space-5);
    gap: var(--multimedia-space-5);
  }
}

/* Desktop */
@media (min-width: 1025px) {
  .multimedia-panel {
    max-width: 1200px;
    margin: 0 auto;
  }
}