import React from 'react'
import { LocaleType, useLocalization } from '../contexts/LocalizationContext'

export const LocalizationToggle: React.FC = () => {
  const { locale, setLocale } = useLocalization()

  const toggleLocale = () => {
    const newLocale: LocaleType = locale === 'en-US' ? 'en-GB' : 'en-US'
    console.log(`🌍 Toggling locale from ${locale} to ${newLocale}`)
    setLocale(newLocale)
  }

  const getFlag = (localeType: LocaleType) => {
    return localeType === 'en-US' ? '🇺🇸' : '🇬🇧'
  }

  const getLabel = (localeType: LocaleType) => {
    return localeType === 'en-US' ? 'US' : 'UK'
  }

  return (
    <button
      onClick={toggleLocale}
      className="localization-toggle"
      title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
      style={{
        display: 'flex',
        alignItems: 'center',
        gap: '4px',
        padding: '6px 10px',
        background: 'var(--tertiary-bg)',
        border: '1px solid var(--border-color)',
        borderRadius: 'var(--radius-md)',
        color: 'var(--text-secondary)',
        cursor: 'pointer',
        fontSize: '13px',
        fontWeight: '500',
        height: '44px', // Match header button height
        transition: 'all var(--transition-fast)',
        whiteSpace: 'nowrap',
        userSelect: 'none'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = 'var(--secondary-bg)'
        e.currentTarget.style.color = 'var(--text-primary)'
        e.currentTarget.style.borderColor = 'var(--accent-color)'
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = 'var(--tertiary-bg)'
        e.currentTarget.style.color = 'var(--text-secondary)'
        e.currentTarget.style.borderColor = 'var(--border-color)'
      }}
    >
      <span style={{ fontSize: '16px' }}>{getFlag(locale)}</span>
      <span>{getLabel(locale)}</span>
    </button>
  )
}
