import React, { useEffect, useState } from 'react';
import { Bookmark } from '../types';

interface ContextPanelProps {
  selectedBookmark: Bookmark | null;
  onUpdateBookmark: (bookmark: Bookmark) => void;
  onDeleteBookmark: (id: string) => void;
  onDuplicateBookmark: (bookmark: Bookmark) => void;
  relatedBookmarks: Bookmark[];
  onBookmarkSelect: (bookmark: Bookmark) => void;
}

const ContextPanel: React.FC<ContextPanelProps> = ({
  selectedBookmark,
  onUpdateBookmark,
  onDeleteBookmark,
  onDuplicateBookmark,
  relatedBookmarks,
  onBookmarkSelect
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedBookmark, setEditedBookmark] = useState<Bookmark | null>(null);
  const [newTag, setNewTag] = useState('');
  const [showPreview, setShowPreview] = useState(false);

  useEffect(() => {
    if (selectedBookmark) {
      setEditedBookmark({ ...selectedBookmark });
      setIsEditing(false);
    }
  }, [selectedBookmark]);

  const handleSave = () => {
    if (editedBookmark) {
      onUpdateBookmark(editedBookmark);
      setIsEditing(false);
    }
  };

  const handleCancel = () => {
    if (selectedBookmark) {
      setEditedBookmark({ ...selectedBookmark });
    }
    setIsEditing(false);
  };

  const handleAddTag = () => {
    if (newTag.trim() && editedBookmark) {
      const updatedTags = [...(editedBookmark.tags || []), newTag.trim()];
      setEditedBookmark({
        ...editedBookmark,
        tags: updatedTags
      });
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    if (editedBookmark) {
      const updatedTags = editedBookmark.tags?.filter(tag => tag !== tagToRemove) || [];
      setEditedBookmark({
        ...editedBookmark,
        tags: updatedTags
      });
    }
  };

  const formatDate = (dateString: string) => {
    // Handle UNIX timestamp (convert from string to number, then to milliseconds)
    const timestamp = parseInt(dateString) * 1000;
    return new Date(timestamp).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getDomainFromUrl = (url: string) => {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  };

  if (!selectedBookmark) {
    return (
      <aside className="workspace-context">
        <div className="context-panel-empty">
          <div className="text-6xl mb-4">📖</div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Select a bookmark
          </h3>
          <p className="text-gray-500 text-center">
            Choose a bookmark from the list to view its details, edit properties, and see related bookmarks.
          </p>
        </div>
      </aside>
    );
  }

  return (
    <aside className="workspace-context">
      <div className="context-panel">
        {/* Header */}
        <div className="context-panel-header">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">
              Bookmark Details
            </h2>
            <div className="flex space-x-2">
              <button
                onClick={() => setShowPreview(!showPreview)}
                className="context-action-btn"
                title="Toggle Preview"
              >
                {showPreview ? '📝' : '👁️'}
              </button>
              <button
                onClick={() => setIsEditing(!isEditing)}
                className="context-action-btn"
                title={isEditing ? 'Cancel Edit' : 'Edit Bookmark'}
              >
                {isEditing ? '✕' : '✏️'}
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="context-panel-content">
          {/* Bookmark Health Indicator */}
          <div className="bookmark-health-status">
            <div className="flex items-center space-x-2">
              <div className="bookmark-health-indicator healthy"></div>
              <span className="text-sm text-gray-600">Healthy</span>
              <span className="text-xs text-gray-400">Last checked 2 hours ago</span>
            </div>
          </div>

          {/* Favicon and Title */}
          <div className="flex items-start space-x-3 mb-6">
            <div className="bookmark-favicon">
              {selectedBookmark.icon ? (
                <img 
                  src={selectedBookmark.icon} 
                  alt="" 
                  className="bookmark-favicon"
                />
              ) : (
                <div className="bookmark-favicon">
                  {selectedBookmark.title?.[0]?.toUpperCase() || '🔖'}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              {isEditing ? (
                <input
                  type="text"
                  value={editedBookmark?.title || ''}
                  onChange={(e) => setEditedBookmark(prev => prev ? {
                    ...prev,
                    title: e.target.value
                  } : null)}
                  className="context-input"
                  placeholder="Bookmark title"
                />
              ) : (
                <h3 className="font-medium text-gray-900 truncate">
                  {selectedBookmark.title || 'Untitled'}
                </h3>
              )}
              <p className="text-sm text-gray-500 truncate mt-1">
                {getDomainFromUrl(selectedBookmark.url)}
              </p>
            </div>
          </div>

          {/* URL */}
          <div className="mb-6">
            <label className="context-label">URL</label>
            {isEditing ? (
              <input
                type="url"
                value={editedBookmark?.url || ''}
                onChange={(e) => setEditedBookmark(prev => prev ? {
                  ...prev,
                  url: e.target.value
                } : null)}
                className="context-input"
                placeholder="https://example.com"
              />
            ) : (
              <div className="context-value">
                <a 
                  href={selectedBookmark.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-blue-600 hover:text-blue-800 break-all"
                >
                  {selectedBookmark.url}
                </a>
              </div>
            )}
          </div>

          {/* Description */}
          <div className="mb-6">
            <label className="context-label">Description</label>
            {isEditing ? (
                <textarea
                  value={editedBookmark?.summary || ''}
                  onChange={(e) => setEditedBookmark(prev => prev ? {
                    ...prev,
                    summary: e.target.value
                  } : null)}
                  className="context-textarea"
                  placeholder="Add a description..."
                  rows={3}
                />
              ) : (
                <div className="context-value">
                  {selectedBookmark.summary || (
                    <span className="text-gray-400 italic">No description</span>
                  )}
                </div>
              )}
          </div>

          {/* Tags */}
          <div className="mb-6">
            <label className="context-label">Tags</label>
            <div className="flex flex-wrap gap-2 mb-2">
              {editedBookmark?.tags?.map((tag, index) => (
                <span key={index} className="suggestion-chip">
                  {tag}
                  {isEditing && (
                    <button
                      onClick={() => handleRemoveTag(tag)}
                      className="ml-1 text-red-500 hover:text-red-700"
                    >
                      ×
                    </button>
                  )}
                </span>
              )) || (
                <span className="text-gray-400 italic text-sm">No tags</span>
              )}
            </div>
            {isEditing && (
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                  className="context-input flex-1"
                  placeholder="Add tag..."
                />
                <button
                  onClick={handleAddTag}
                  className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                >
                  Add
                </button>
              </div>
            )}
          </div>

          {/* Metadata */}
          <div className="mb-6">
            <label className="context-label">Metadata</label>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Added:</span>
                <span>{selectedBookmark.addDate ? formatDate(selectedBookmark.addDate) : 'Unknown'}</span>
              </div>
              {selectedBookmark.lastModified && (
                <div className="flex justify-between">
                  <span className="text-gray-500">Modified:</span>
                  <span>{formatDate(selectedBookmark.lastModified)}</span>
                </div>
              )}
              <div className="flex justify-between">
                <span className="text-gray-500">Folder:</span>
                <span>{selectedBookmark.path?.join(' > ') || 'None'}</span>
              </div>
            </div>
          </div>

          {/* Actions */}
          <div className="mb-6">
            <label className="context-label">Actions</label>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={() => window.open(selectedBookmark.url, '_blank')}
                className="context-action-button"
              >
                🌐 Open
              </button>
              <button
                onClick={() => onDuplicateBookmark(selectedBookmark)}
                className="context-action-button"
              >
                📋 Duplicate
              </button>
              <button
                onClick={() => navigator.clipboard.writeText(selectedBookmark.url)}
                className="context-action-button"
              >
                📎 Copy URL
              </button>
              <button
                onClick={() => onDeleteBookmark(selectedBookmark.id)}
                className="context-action-button text-red-600 hover:bg-red-50"
              >
                🗑️ Delete
              </button>
            </div>
          </div>

          {/* Save/Cancel buttons for editing */}
          {isEditing && (
            <div className="flex space-x-2 mb-6">
              <button
                onClick={handleSave}
                className="flex-1 bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
              >
                Save Changes
              </button>
              <button
                onClick={handleCancel}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          )}

          {/* Related Bookmarks */}
          {relatedBookmarks.length > 0 && (
            <div>
              <label className="context-label">Related Bookmarks</label>
              <div className="space-y-2">
                {relatedBookmarks.slice(0, 5).map((bookmark) => (
                  <div
                    key={bookmark.id}
                    onClick={() => onBookmarkSelect(bookmark)}
                    className="related-bookmark-item"
                  >
                    <div className="bookmark-favicon-small">
                      {bookmark.icon ? (
                        <img src={bookmark.icon} alt="" />
                      ) : (
                        <span>{bookmark.title?.[0]?.toUpperCase() || '🔖'}</span>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm truncate">
                        {bookmark.title || 'Untitled'}
                      </div>
                      <div className="text-xs text-gray-500 truncate">
                        {getDomainFromUrl(bookmark.url)}
                      </div>
                    </div>
                  </div>
                ))}
                {relatedBookmarks.length > 5 && (
                  <div className="text-xs text-gray-500 text-center py-2">
                    +{relatedBookmarks.length - 5} more related bookmarks
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </aside>
  );
};

export default ContextPanel;