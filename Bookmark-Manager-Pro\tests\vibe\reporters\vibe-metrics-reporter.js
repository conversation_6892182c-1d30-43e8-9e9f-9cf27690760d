// Vibe Metrics Reporter
// Custom Playwright reporter for emotional response and micro-UX metrics

class VibeMetricsReporter {
  constructor(options = {}) {
    this.options = options;
    this.vibeMetrics = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      emotionalResponseTests: 0,
      flowStateTests: 0,
      cognitiveLoadTests: 0,
      performanceTests: 0,
      
      // Detailed metrics
      responseTimeMetrics: [],
      emotionalFeedbackMetrics: [],
      flowDisruptionMetrics: [],
      cognitiveLoadMetrics: [],
      bulkOperationMetrics: [],
      suggestionQualityMetrics: []
    };
  }
  
  onBegin(config, suite) {
    console.log('🧪 Dr. Elena\'s Vibe Testing Report - Starting Analysis...\n');
    this.startTime = Date.now();
  }
  
  onTestBegin(test, result) {
    this.vibeMetrics.totalTests++;
    
    // Categorize test types
    if (test.title.includes('emotional') || test.title.includes('satisfaction')) {
      this.vibeMetrics.emotionalResponseTests++;
    }
    if (test.title.includes('flow') || test.title.includes('workflow')) {
      this.vibeMetrics.flowStateTests++;
    }
    if (test.title.includes('cognitive') || test.title.includes('overload')) {
      this.vibeMetrics.cognitiveLoadTests++;
    }
    if (test.title.includes('performance') || test.title.includes('response')) {
      this.vibeMetrics.performanceTests++;
    }
  }
  
  onTestEnd(test, result) {
    if (result.status === 'passed') {
      this.vibeMetrics.passedTests++;
    } else {
      this.vibeMetrics.failedTests++;
    }
    
    // Extract vibe-specific metrics from test attachments
    this.extractVibeMetrics(test, result);
  }
  
  extractVibeMetrics(test, result) {
    // Extract metrics from test execution
    if (result.attachments) {
      result.attachments.forEach(attachment => {
        if (attachment.name === 'vibe-metrics') {
          try {
            const metrics = JSON.parse(attachment.body.toString());
            this.categorizeMetrics(metrics, test.title);
          } catch (error) {
            console.warn('Failed to parse vibe metrics:', error);
          }
        }
      });
    }
  }
  
  categorizeMetrics(metrics, testTitle) {
    if (metrics.responseTime) {
      this.vibeMetrics.responseTimeMetrics.push({
        test: testTitle,
        ...metrics.responseTime
      });
    }
    
    if (metrics.emotionalFeedback) {
      this.vibeMetrics.emotionalFeedbackMetrics.push({
        test: testTitle,
        ...metrics.emotionalFeedback
      });
    }
    
    if (metrics.flowDisruption) {
      this.vibeMetrics.flowDisruptionMetrics.push({
        test: testTitle,
        ...metrics.flowDisruption
      });
    }
    
    if (metrics.cognitiveLoad) {
      this.vibeMetrics.cognitiveLoadMetrics.push({
        test: testTitle,
        ...metrics.cognitiveLoad
      });
    }
    
    if (metrics.bulkOperation) {
      this.vibeMetrics.bulkOperationMetrics.push({
        test: testTitle,
        ...metrics.bulkOperation
      });
    }
    
    if (metrics.suggestionQuality) {
      this.vibeMetrics.suggestionQualityMetrics.push({
        test: testTitle,
        ...metrics.suggestionQuality
      });
    }
  }
  
  onEnd(result) {
    const duration = Date.now() - this.startTime;
    
    console.log('\n🧪 Dr. Elena\'s Vibe Testing Analysis Complete!\n');
    console.log('═'.repeat(60));
    
    this.printExecutionSummary(duration);
    this.printVibeMetricsSummary();
    this.printDetailedAnalysis();
    this.printRecommendations();
    this.generateVibeReport();
    
    console.log('═'.repeat(60));
    console.log('📊 Full vibe metrics report saved to: test-results/vibe-metrics-report.json');
    console.log('🎯 Emotional journey analysis saved to: test-results/emotional-journey-report.html\n');
  }
  
  printExecutionSummary(duration) {
    const passRate = (this.vibeMetrics.passedTests / this.vibeMetrics.totalTests * 100).toFixed(1);
    
    console.log('📈 EXECUTION SUMMARY');
    console.log('─'.repeat(30));
    console.log(`Total Tests: ${this.vibeMetrics.totalTests}`);
    console.log(`Passed: ${this.vibeMetrics.passedTests} (${passRate}%)`);
    console.log(`Failed: ${this.vibeMetrics.failedTests}`);
    console.log(`Duration: ${(duration / 1000).toFixed(1)}s`);
    console.log('');
  }
  
  printVibeMetricsSummary() {
    console.log('🎭 VIBE METRICS BREAKDOWN');
    console.log('─'.repeat(30));
    console.log(`Emotional Response Tests: ${this.vibeMetrics.emotionalResponseTests}`);
    console.log(`Flow State Tests: ${this.vibeMetrics.flowStateTests}`);
    console.log(`Cognitive Load Tests: ${this.vibeMetrics.cognitiveLoadTests}`);
    console.log(`Performance Tests: ${this.vibeMetrics.performanceTests}`);
    console.log('');
  }
  
  printDetailedAnalysis() {
    console.log('🔍 DETAILED VIBE ANALYSIS');
    console.log('─'.repeat(30));
    
    // Response Time Analysis
    if (this.vibeMetrics.responseTimeMetrics.length > 0) {
      const avgResponseTime = this.vibeMetrics.responseTimeMetrics.reduce((sum, m) => sum + m.duration, 0) / this.vibeMetrics.responseTimeMetrics.length;
      const excellentResponses = this.vibeMetrics.responseTimeMetrics.filter(m => m.quality === 'excellent').length;
      
      console.log(`⚡ Response Time Quality:`);
      console.log(`   Average: ${avgResponseTime.toFixed(1)}ms`);
      console.log(`   Excellent: ${excellentResponses}/${this.vibeMetrics.responseTimeMetrics.length}`);
    }
    
    // Emotional Feedback Analysis
    if (this.vibeMetrics.emotionalFeedbackMetrics.length > 0) {
      const excellentFeedback = this.vibeMetrics.emotionalFeedbackMetrics.filter(m => m.overallQuality === 'excellent').length;
      
      console.log(`💝 Emotional Feedback Quality:`);
      console.log(`   Excellent: ${excellentFeedback}/${this.vibeMetrics.emotionalFeedbackMetrics.length}`);
    }
    
    // Flow Disruption Analysis
    if (this.vibeMetrics.flowDisruptionMetrics.length > 0) {
      const excellentFlow = this.vibeMetrics.flowDisruptionMetrics.filter(m => m.flowQuality === 'excellent').length;
      const totalDisruptions = this.vibeMetrics.flowDisruptionMetrics.reduce((sum, m) => sum + m.disruptionCount, 0);
      
      console.log(`🌊 Flow State Quality:`);
      console.log(`   Excellent Flow: ${excellentFlow}/${this.vibeMetrics.flowDisruptionMetrics.length}`);
      console.log(`   Total Disruptions: ${totalDisruptions}`);
    }
    
    // Cognitive Load Analysis
    if (this.vibeMetrics.cognitiveLoadMetrics.length > 0) {
      const lowLoad = this.vibeMetrics.cognitiveLoadMetrics.filter(m => m.overallLoad === 'low').length;
      
      console.log(`🧠 Cognitive Load Quality:`);
      console.log(`   Low Load: ${lowLoad}/${this.vibeMetrics.cognitiveLoadMetrics.length}`);
    }
    
    // Bulk Operation Analysis
    if (this.vibeMetrics.bulkOperationMetrics.length > 0) {
      const lowAnxiety = this.vibeMetrics.bulkOperationMetrics.filter(m => m.anxietyLevel === 'very-low' || m.anxietyLevel === 'low').length;
      
      console.log(`📦 Bulk Operation Confidence:`);
      console.log(`   Low Anxiety: ${lowAnxiety}/${this.vibeMetrics.bulkOperationMetrics.length}`);
    }
    
    // Suggestion Quality Analysis
    if (this.vibeMetrics.suggestionQualityMetrics.length > 0) {
      const excellentSuggestions = this.vibeMetrics.suggestionQualityMetrics.filter(m => m.quality === 'excellent').length;
      const avgRelevance = this.vibeMetrics.suggestionQualityMetrics.reduce((sum, m) => sum + m.rates.relevance, 0) / this.vibeMetrics.suggestionQualityMetrics.length;
      
      console.log(`🤖 AI Suggestion Quality:`);
      console.log(`   Excellent: ${excellentSuggestions}/${this.vibeMetrics.suggestionQualityMetrics.length}`);
      console.log(`   Avg Relevance: ${(avgRelevance * 100).toFixed(1)}%`);
    }
    
    console.log('');
  }
  
  printRecommendations() {
    console.log('💡 DR. ELENA\'S RECOMMENDATIONS');
    console.log('─'.repeat(30));
    
    const recommendations = this.generateRecommendations();
    
    if (recommendations.length === 0) {
      console.log('🎉 Excellent! No critical vibe issues detected.');
      console.log('   Your favorites system provides an outstanding user experience!');
    } else {
      recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.priority.toUpperCase()}: ${rec.issue}`);
        console.log(`   💊 Solution: ${rec.solution}`);
        console.log('');
      });
    }
  }
  
  generateRecommendations() {
    const recommendations = [];
    
    // Response Time Recommendations
    const slowResponses = this.vibeMetrics.responseTimeMetrics.filter(m => m.quality === 'poor' || m.quality === 'unacceptable');
    if (slowResponses.length > 0) {
      recommendations.push({
        priority: 'critical',
        issue: `${slowResponses.length} operations have poor response times`,
        solution: 'Implement optimistic UI updates and background processing'
      });
    }
    
    // Emotional Feedback Recommendations
    const poorFeedback = this.vibeMetrics.emotionalFeedbackMetrics.filter(m => m.overallQuality === 'poor');
    if (poorFeedback.length > 0) {
      recommendations.push({
        priority: 'high',
        issue: `${poorFeedback.length} interactions lack satisfying emotional feedback`,
        solution: 'Add micro-animations and visual feedback for user actions'
      });
    }
    
    // Flow Disruption Recommendations
    const poorFlow = this.vibeMetrics.flowDisruptionMetrics.filter(m => m.flowQuality === 'poor' || m.flowQuality === 'unacceptable');
    if (poorFlow.length > 0) {
      recommendations.push({
        priority: 'critical',
        issue: `${poorFlow.length} workflows have significant flow disruptions`,
        solution: 'Remove confirmation dialogs and implement non-blocking feedback'
      });
    }
    
    // Cognitive Load Recommendations
    const highLoad = this.vibeMetrics.cognitiveLoadMetrics.filter(m => m.overallLoad === 'high' || m.overallLoad === 'overwhelming');
    if (highLoad.length > 0) {
      recommendations.push({
        priority: 'medium',
        issue: `${highLoad.length} interfaces have high cognitive load`,
        solution: 'Simplify UI, reduce visual noise, and improve information hierarchy'
      });
    }
    
    // Bulk Operation Recommendations
    const highAnxiety = this.vibeMetrics.bulkOperationMetrics.filter(m => m.anxietyLevel === 'high' || m.anxietyLevel === 'very-high');
    if (highAnxiety.length > 0) {
      recommendations.push({
        priority: 'high',
        issue: `${highAnxiety.length} bulk operations create user anxiety`,
        solution: 'Add operation previews, undo functionality, and clear progress indicators'
      });
    }
    
    // Suggestion Quality Recommendations
    const poorSuggestions = this.vibeMetrics.suggestionQualityMetrics.filter(m => m.quality === 'poor' || m.quality === 'unacceptable');
    if (poorSuggestions.length > 0) {
      recommendations.push({
        priority: 'medium',
        issue: `${poorSuggestions.length} AI suggestions are poorly timed or irrelevant`,
        solution: 'Improve suggestion algorithms and timing based on user context'
      });
    }
    
    return recommendations.sort((a, b) => {
      const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
      return priorityOrder[a.priority] - priorityOrder[b.priority];
    });
  }
  
  generateVibeReport() {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: this.vibeMetrics.totalTests,
        passedTests: this.vibeMetrics.passedTests,
        failedTests: this.vibeMetrics.failedTests,
        passRate: (this.vibeMetrics.passedTests / this.vibeMetrics.totalTests * 100).toFixed(1)
      },
      categories: {
        emotionalResponse: this.vibeMetrics.emotionalResponseTests,
        flowState: this.vibeMetrics.flowStateTests,
        cognitiveLoad: this.vibeMetrics.cognitiveLoadTests,
        performance: this.vibeMetrics.performanceTests
      },
      detailedMetrics: {
        responseTime: this.vibeMetrics.responseTimeMetrics,
        emotionalFeedback: this.vibeMetrics.emotionalFeedbackMetrics,
        flowDisruption: this.vibeMetrics.flowDisruptionMetrics,
        cognitiveLoad: this.vibeMetrics.cognitiveLoadMetrics,
        bulkOperation: this.vibeMetrics.bulkOperationMetrics,
        suggestionQuality: this.vibeMetrics.suggestionQualityMetrics
      },
      recommendations: this.generateRecommendations(),
      vibeScore: this.calculateOverallVibeScore()
    };
    
    // Save detailed report
    const fs = require('fs');
    const path = require('path');
    
    const reportDir = 'test-results';
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    fs.writeFileSync(
      path.join(reportDir, 'vibe-metrics-report.json'),
      JSON.stringify(report, null, 2)
    );
  }
  
  calculateOverallVibeScore() {
    let totalScore = 0;
    let maxScore = 0;
    
    // Response Time Score (25% weight)
    if (this.vibeMetrics.responseTimeMetrics.length > 0) {
      const excellentCount = this.vibeMetrics.responseTimeMetrics.filter(m => m.quality === 'excellent').length;
      totalScore += (excellentCount / this.vibeMetrics.responseTimeMetrics.length) * 25;
    }
    maxScore += 25;
    
    // Emotional Feedback Score (20% weight)
    if (this.vibeMetrics.emotionalFeedbackMetrics.length > 0) {
      const excellentCount = this.vibeMetrics.emotionalFeedbackMetrics.filter(m => m.overallQuality === 'excellent').length;
      totalScore += (excellentCount / this.vibeMetrics.emotionalFeedbackMetrics.length) * 20;
    }
    maxScore += 20;
    
    // Flow State Score (25% weight)
    if (this.vibeMetrics.flowDisruptionMetrics.length > 0) {
      const excellentCount = this.vibeMetrics.flowDisruptionMetrics.filter(m => m.flowQuality === 'excellent').length;
      totalScore += (excellentCount / this.vibeMetrics.flowDisruptionMetrics.length) * 25;
    }
    maxScore += 25;
    
    // Cognitive Load Score (15% weight)
    if (this.vibeMetrics.cognitiveLoadMetrics.length > 0) {
      const lowLoadCount = this.vibeMetrics.cognitiveLoadMetrics.filter(m => m.overallLoad === 'low').length;
      totalScore += (lowLoadCount / this.vibeMetrics.cognitiveLoadMetrics.length) * 15;
    }
    maxScore += 15;
    
    // Bulk Operation Score (15% weight)
    if (this.vibeMetrics.bulkOperationMetrics.length > 0) {
      const lowAnxietyCount = this.vibeMetrics.bulkOperationMetrics.filter(m => m.anxietyLevel === 'very-low' || m.anxietyLevel === 'low').length;
      totalScore += (lowAnxietyCount / this.vibeMetrics.bulkOperationMetrics.length) * 15;
    }
    maxScore += 15;
    
    const vibeScore = maxScore > 0 ? (totalScore / maxScore * 100).toFixed(1) : 0;
    
    return {
      score: parseFloat(vibeScore),
      grade: this.getVibeGrade(vibeScore),
      interpretation: this.getVibeInterpretation(vibeScore)
    };
  }
  
  getVibeGrade(score) {
    if (score >= 90) return 'A+';
    if (score >= 85) return 'A';
    if (score >= 80) return 'B+';
    if (score >= 75) return 'B';
    if (score >= 70) return 'C+';
    if (score >= 65) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  }
  
  getVibeInterpretation(score) {
    if (score >= 90) return 'Outstanding user experience - users will love this system!';
    if (score >= 85) return 'Excellent user experience with minor room for improvement';
    if (score >= 80) return 'Good user experience - solid foundation with some enhancements needed';
    if (score >= 75) return 'Acceptable user experience - several areas need attention';
    if (score >= 70) return 'Below average user experience - significant improvements required';
    if (score >= 60) return 'Poor user experience - major UX issues need immediate attention';
    return 'Unacceptable user experience - complete UX overhaul required';
  }
}

module.exports = VibeMetricsReporter;