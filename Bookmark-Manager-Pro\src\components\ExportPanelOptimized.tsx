import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface ExportPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const ExportPanelOptimized: React.FC<ExportPanelProps> = ({ isOpen: _isOpen, onClose, onOpenPanel }) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [exportFormat, setExportFormat] = useState('html')
  const [isExporting, setIsExporting] = useState(false)
  const [exportProgress, setExportProgress] = useState(0)
  const [_selectedFolders, _setSelectedFolders] = useState<string[]>([])
  const [advancedOptionsExpanded, setAdvancedOptionsExpanded] = useState(false)
  const [filterOptionsExpanded, setFilterOptionsExpanded] = useState(false)

  const startExport = () => {
    setIsExporting(true)
    setExportProgress(0)
    // Export logic here
    console.log('Starting export...')
  }

  const downloadTemplate = () => {
    console.log('Downloading template...')
  }

  return (
    <div className="import-panel">
      {/* Quick Export */}
      <div className="import-section">
        <h3 className="section-title">{t('export.quickExport')}</h3>
        <p className="section-description">{t('export.quickExportDescription')}</p>
        
        <div className="form-row">
          <label>{t('export.format')}:</label>
          <select 
            value={exportFormat} 
            onChange={(e) => setExportFormat(e.target.value)}
            className="input-compact"
          >
            <option value="html">{t('export.html')}</option>
            <option value="json">{t('export.json')}</option>
            <option value="csv">{t('export.csv')}</option>
            <option value="xml">{t('export.xml')}</option>
            <option value="markdown">{t('export.markdown')}</option>
          </select>
        </div>

        <div className="controls-grid">
          <button 
            className="btn-compact primary" 
            onClick={startExport}
            disabled={isExporting}
          >
            {isExporting ? t('export.exporting') : t('export.startExport')}
          </button>
          <button className="btn-compact" onClick={downloadTemplate}>
            {t('export.downloadTemplate')}
          </button>
        </div>

        {isExporting && (
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${exportProgress}%` }} />
            <span className="progress-text">{exportProgress}%</span>
          </div>
        )}
      </div>

      {/* Export Statistics */}
      <div className="import-section">
        <h3 className="section-title">{t('export.statistics')}</h3>
        
        <div className="controls-grid">
          <div className="status-compact">
            <div className="status-icon info" />
            <span>{bookmarks.length} {t('export.totalBookmarks')}</span>
          </div>
          <div className="status-compact">
            <div className="status-icon success" />
            <span>{_selectedFolders.length} {t('export.selectedFolders')}</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('export.quickActions')}</h3>
        
        <div className="button-grid">
          <button className="btn-compact">{t('export.exportAll')}</button>
          <button className="btn-compact">{t('export.exportFavorites')}</button>
          <button className="btn-compact">{t('export.exportRecent')}</button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('health')}>
            {t('export.healthCheck')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('summary')}>
            {t('export.generateSummary')}
          </button>
        </div>
      </div>

      {/* Filter Options - Collapsible */}
      <div className={`collapsible-section ${filterOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setFilterOptionsExpanded(!filterOptionsExpanded)}
        >
          <span>{t('export.filterOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('export.dateRange')}:</label>
            <div className="controls-grid">
              <input type="date" className="input-compact" />
              <input type="date" className="input-compact" />
            </div>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('export.includeMetadata')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('export.includeFavicons')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('export.includeDescriptions')}
            </label>
          </div>
          <div className="form-row">
            <label>{t('export.tags')}:</label>
            <input type="text" placeholder={t('export.tagsPlaceholder')} className="input-compact" />
          </div>
        </div>
      </div>

      {/* Advanced Options - Collapsible */}
      <div className={`collapsible-section ${advancedOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setAdvancedOptionsExpanded(!advancedOptionsExpanded)}
        >
          <span>{t('export.advancedOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('export.encoding')}:</label>
            <select className="input-compact">
              <option value="utf8">UTF-8</option>
              <option value="utf16">UTF-16</option>
              <option value="ascii">ASCII</option>
            </select>
          </div>
          <div className="form-row">
            <label>{t('export.compression')}:</label>
            <select className="input-compact">
              <option value="none">{t('export.none')}</option>
              <option value="zip">ZIP</option>
              <option value="gzip">GZIP</option>
            </select>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('export.preserveStructure')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('export.includePrivate')}
            </label>
          </div>
          <div className="form-row">
            <label>{t('export.batchSize')}:</label>
            <input type="number" defaultValue="1000" className="input-compact" />
          </div>
          <div className="form-row">
            <label>{t('export.customTemplate')}:</label>
            <input type="file" accept=".html,.json,.xml" className="input-compact" />
          </div>
        </div>
      </div>

      {/* Folder Selection - Collapsible */}
      <div className="collapsible-section">
        <button className="collapsible-header">
          <span>{t('export.selectFolders')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="scrollable-content">
            <div className="form-row">
              <label>
                <input type="checkbox" />
                📁 {t('export.bookmarksBar')}
              </label>
            </div>
            <div className="form-row">
              <label>
                <input type="checkbox" />
                📁 {t('export.otherBookmarks')}
              </label>
            </div>
            <div className="form-row">
              <label>
                <input type="checkbox" />
                📁 {t('export.mobileBookmarks')}
              </label>
            </div>
            <div className="form-row">
              <label>
                <input type="checkbox" />
                📁 {t('export.workFolder')}
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="import-section">
        <h3 className="section-title">{t('export.relatedTools')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('import')}>
            {t('export.importBookmarks')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('split')}>
            {t('export.splitBookmarks')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('playlist')}>
            {t('export.createPlaylist')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  )
}