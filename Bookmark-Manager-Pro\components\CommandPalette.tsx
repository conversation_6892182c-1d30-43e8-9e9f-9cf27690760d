import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Bookmark } from '../types';

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  bookmarks: Bookmark[];
  onBookmarkSelect: (bookmark: Bookmark) => void;
  onAction: (action: string, data?: unknown) => void;
}

interface Command {
  id: string;
  title: string;
  description?: string;
  icon: string;
  shortcut?: string;
  action: () => void;
  category: 'bookmark' | 'action' | 'navigation';
}

const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  bookmarks,
  onBookmarkSelect,
  onAction
}) => {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Generate commands based on current context
  const commands = useMemo((): Command[] => {
    const baseCommands: Command[] = [
      {
        id: 'import-bookmarks',
        title: 'Import Bookmarks',
        description: 'Import bookmarks from browser or file',
        icon: '📥',
        shortcut: 'Ctrl+I',
        action: () => onAction('import'),
        category: 'action'
      },
      {
        id: 'export-bookmarks',
        title: 'Export Bookmarks',
        description: 'Export bookmarks to various formats',
        icon: '📤',
        shortcut: 'Ctrl+E',
        action: () => onAction('export'),
        category: 'action'
      },
      {
        id: 'add-bookmark',
        title: 'Add New Bookmark',
        description: 'Manually add a new bookmark',
        icon: '➕',
        shortcut: 'Ctrl+N',
        action: () => onAction('add'),
        category: 'action'
      },
      {
        id: 'ai-search',
        title: 'AI Search',
        description: 'Search bookmarks using AI',
        icon: '🤖',
        shortcut: 'Ctrl+Shift+F',
        action: () => onAction('ai-search'),
        category: 'action'
      },
      {
        id: 'ai-qa',
        title: 'AI Q&A',
        description: 'Ask questions about your bookmarks',
        icon: '💬',
        shortcut: 'Ctrl+Shift+Q',
        action: () => onAction('ai-qa'),
        category: 'action'
      },
      {
        id: 'bulk-tag',
        title: 'Bulk Tag Bookmarks',
        description: 'Add tags to multiple bookmarks',
        icon: '🏷️',
        action: () => onAction('bulk-tag'),
        category: 'action'
      },
      {
        id: 'health-check',
        title: 'Check Bookmark Health',
        description: 'Scan for broken links and duplicates',
        icon: '🔍',
        action: () => onAction('health-check'),
        category: 'action'
      },
      {
        id: 'create-collection',
        title: 'Create Smart Collection',
        description: 'Create a new smart collection',
        icon: '📁',
        action: () => onAction('create-collection'),
        category: 'action'
      }
    ];

    // Add bookmark commands
    const bookmarkCommands: Command[] = bookmarks
      .filter(bookmark => 
        bookmark.title?.toLowerCase().includes(query.toLowerCase()) ||
        bookmark.url?.toLowerCase().includes(query.toLowerCase()) ||
        bookmark.tags?.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
      )
      .slice(0, 10) // Limit to top 10 results
      .map(bookmark => ({
        id: `bookmark-${bookmark.id}`,
        title: bookmark.title || 'Untitled',
        description: bookmark.url,
        icon: '🔖',
        action: () => onBookmarkSelect(bookmark),
        category: 'bookmark' as const
      }));

    return [...baseCommands, ...bookmarkCommands];
  }, [bookmarks, query, onAction, onBookmarkSelect]);

  // Filter commands based on query
  const filteredCommands = useMemo(() => {
    if (!query.trim()) {
      return commands.filter(cmd => cmd.category === 'action').slice(0, 8);
    }

    return commands.filter(command =>
      command.title.toLowerCase().includes(query.toLowerCase()) ||
      command.description?.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 10);
  }, [commands, query]);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < filteredCommands.length - 1 ? prev + 1 : 0
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev > 0 ? prev - 1 : filteredCommands.length - 1
          );
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            filteredCommands[selectedIndex].action();
            onClose();
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filteredCommands, selectedIndex, onClose]);

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
      setQuery('');
      setSelectedIndex(0);
    }
  }, [isOpen]);

  // Scroll selected item into view
  useEffect(() => {
    if (resultsRef.current) {
      const selectedElement = resultsRef.current.children[selectedIndex] as HTMLElement;
      if (selectedElement) {
        selectedElement.scrollIntoView({
          block: 'nearest',
          behavior: 'smooth'
        });
      }
    }
  }, [selectedIndex]);

  if (!isOpen) return null;

  return (
    <>
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity"
        onClick={onClose}
      />
      
      {/* Command Palette */}
      <div className="modal-content max-w-2xl">
        {/* Search Input */}
        <div className="relative">
          <input
            ref={inputRef}
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Type a command or search bookmarks..."
            className="input text-lg"
          />
          <div className="absolute right-4 top-1/2 transform -translate-y-1/2 text-muted">
            <span className="text-xs">ESC to close</span>
          </div>
        </div>

        {/* Results */}
        <div ref={resultsRef} className="max-h-96 overflow-y-auto border-t border-border">
          {filteredCommands.length === 0 ? (
            <div className="p-4 text-center text-muted">
              <div className="text-2xl mb-2">🔍</div>
              <p>No commands or bookmarks found</p>
              <p className="text-xs mt-1">Try a different search term</p>
            </div>
          ) : (
            filteredCommands.map((command, index) => (
              <div
                key={command.id}
                className={`p-3 cursor-pointer hover:bg-surface-hover border-b border-border last:border-b-0 transition-colors ${
                  index === selectedIndex ? 'bg-primary text-primary-foreground' : ''
                }`}
                onClick={() => {
                  command.action();
                  onClose();
                }}
              >
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    {command.icon}
                  </div>
                  <div className="flex-1">
                    <div className="font-medium">
                      {command.title}
                  </div>
                    {command.description && (
                      <div className="text-sm text-muted truncate">
                        {command.description}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    {command.shortcut && (
                      <div className="badge badge-outline text-xs">
                        {command.shortcut}
                      </div>
                    )}
                    {command.category === 'bookmark' && (
                      <div className="badge badge-primary text-xs">
                        Bookmark
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Footer */}
        <div className="px-4 py-2 border-t border-border bg-surface text-xs text-muted flex justify-between">
          <div className="flex space-x-4">
            <span>↑↓ Navigate</span>
            <span>↵ Select</span>
            <span>ESC Close</span>
          </div>
          <div>
            {filteredCommands.length} result{filteredCommands.length !== 1 ? 's' : ''}
          </div>
        </div>
      </div>
    </>
  );
};

export default CommandPalette;