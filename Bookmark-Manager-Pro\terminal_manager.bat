@echo off
REM Enhanced Terminal Manager for Trae.ai IDE
REM Provides easy access to Windows Terminal session management

setlocal enabledelayedexpansion

REM Configuration
set "SCRIPT_DIR=%~dp0"
set "CONFIG_FILE=%SCRIPT_DIR%terminal_rules_enhanced.json"
set "POWERSHELL_SCRIPT=%SCRIPT_DIR%run_tests_with_terminal.ps1"
set "PYTHON_SCRIPT=%SCRIPT_DIR%wt_session_manager.py"
set "COMPLIANCE_SCRIPT=%SCRIPT_DIR%terminal_compliance.py"

REM Colors for output
set "COLOR_SUCCESS=0A"
set "COLOR_WARNING=0E"
set "COLOR_ERROR=0C"
set "COLOR_INFO=0B"
set "COLOR_RESET=07"

REM Function to display colored output
:ColorEcho
color %1
echo %~2
color %COLOR_RESET%
goto :eof

REM Display banner
call :ColorEcho %COLOR_INFO% "========================================"
call :ColorEcho %COLOR_INFO% "  Enhanced Terminal Manager v2.0"
call :ColorEcho %COLOR_INFO% "  Trae.ai IDE Integration"
call :ColorEcho %COLOR_INFO% "========================================"
echo.

REM Check if no arguments provided
if "%1"=="" goto :ShowHelp

REM Parse command line arguments
set "COMMAND=%1"
shift

REM Handle different commands
if /i "%COMMAND%"=="help" goto :ShowHelp
if /i "%COMMAND%"=="--help" goto :ShowHelp
if /i "%COMMAND%"=="-h" goto :ShowHelp
if /i "%COMMAND%"=="check" goto :RunCompliance
if /i "%COMMAND%"=="compliance" goto :RunCompliance
if /i "%COMMAND%"=="create" goto :CreateSession
if /i "%COMMAND%"=="new" goto :CreateSession
if /i "%COMMAND%"=="list" goto :ListSessions
if /i "%COMMAND%"=="ls" goto :ListSessions
if /i "%COMMAND%"=="attach" goto :AttachSession
if /i "%COMMAND%"=="connect" goto :AttachSession
if /i "%COMMAND%"=="kill" goto :KillSession
if /i "%COMMAND%"=="stop" goto :KillSession
if /i "%COMMAND%"=="cleanup" goto :CleanupSessions
if /i "%COMMAND%"=="clean" goto :CleanupSessions
if /i "%COMMAND%"=="parallel" goto :RunParallel
if /i "%COMMAND%"=="test" goto :RunTest
if /i "%COMMAND%"=="status" goto :ShowStatus

REM Unknown command
call :ColorEcho %COLOR_ERROR% "Error: Unknown command '%COMMAND%'"
echo.
goto :ShowHelp

:ShowHelp
echo Usage: terminal_manager.bat [COMMAND] [OPTIONS]
echo.
echo COMMANDS:
echo   help, --help, -h     Show this help message
echo   check, compliance    Run terminal compliance check
echo   create, new          Create a new test session
echo   list, ls             List all active sessions
echo   attach, connect      Attach to an existing session
echo   kill, stop           Stop a specific session
echo   cleanup, clean       Clean up stale sessions
echo   parallel             Run parallel test sessions
echo   test                 Run a single test session
echo   status               Show system status
echo.
echo EXAMPLES:
echo   terminal_manager.bat check
echo   terminal_manager.bat create unit-test
echo   terminal_manager.bat list
echo   terminal_manager.bat parallel unit,integration
echo   terminal_manager.bat attach my-session
echo   terminal_manager.bat kill my-session
echo   terminal_manager.bat cleanup
echo.
goto :End

:RunCompliance
call :ColorEcho %COLOR_INFO% "Running terminal compliance check..."
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Error: Python is not installed or not in PATH"
    call :ColorEcho %COLOR_WARNING% "Please install Python and ensure it's accessible from command line"
    goto :End
)

REM Run compliance check
python "%COMPLIANCE_SCRIPT%" --config "%CONFIG_FILE%"
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Compliance check failed. Please address the issues above."
) else (
    call :ColorEcho %COLOR_SUCCESS% "Compliance check passed successfully!"
)
goto :End

:CreateSession
set "SESSION_NAME=%1"
set "TEST_TYPE=%2"

if "%SESSION_NAME%"=="" (
    call :ColorEcho %COLOR_ERROR% "Error: Session name is required"
    echo Usage: terminal_manager.bat create [SESSION_NAME] [TEST_TYPE]
    goto :End
)

if "%TEST_TYPE%"=="" set "TEST_TYPE=unit"

call :ColorEcho %COLOR_INFO% "Creating session '%SESSION_NAME%' with type '%TEST_TYPE%'..."
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -SessionMode -SessionName "%SESSION_NAME%" -TestType "%TEST_TYPE%"
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Failed to create session"
) else (
    call :ColorEcho %COLOR_SUCCESS% "Session created successfully!"
)
goto :End

:ListSessions
call :ColorEcho %COLOR_INFO% "Listing active sessions..."
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -ListSessions
goto :End

:AttachSession
set "SESSION_NAME=%1"

if "%SESSION_NAME%"=="" (
    call :ColorEcho %COLOR_ERROR% "Error: Session name is required"
    echo Usage: terminal_manager.bat attach [SESSION_NAME]
    goto :End
)

call :ColorEcho %COLOR_INFO% "Attaching to session '%SESSION_NAME%'..."
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -AttachSession "%SESSION_NAME%"
goto :End

:KillSession
set "SESSION_NAME=%1"

if "%SESSION_NAME%"=="" (
    call :ColorEcho %COLOR_ERROR% "Error: Session name is required"
    echo Usage: terminal_manager.bat kill [SESSION_NAME]
    goto :End
)

call :ColorEcho %COLOR_WARNING% "Stopping session '%SESSION_NAME%'..."
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -KillSession "%SESSION_NAME%"
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Failed to stop session"
) else (
    call :ColorEcho %COLOR_SUCCESS% "Session stopped successfully!"
)
goto :End

:CleanupSessions
call :ColorEcho %COLOR_INFO% "Cleaning up stale sessions..."
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -CleanupSessions
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Cleanup failed"
) else (
    call :ColorEcho %COLOR_SUCCESS% "Cleanup completed successfully!"
)
goto :End

:RunParallel
set "TEST_TYPES=%1"

if "%TEST_TYPES%"=="" set "TEST_TYPES=unit,integration"

call :ColorEcho %COLOR_INFO% "Running parallel test sessions for types: %TEST_TYPES%"
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -Parallel -TestTypes "%TEST_TYPES%" -Verbose
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Parallel execution failed"
) else (
    call :ColorEcho %COLOR_SUCCESS% "Parallel sessions started successfully!"
)
goto :End

:RunTest
set "TEST_TYPE=%1"

if "%TEST_TYPE%"=="" set "TEST_TYPE=unit"

call :ColorEcho %COLOR_INFO% "Running single test session for type: %TEST_TYPE%"
echo.

powershell -ExecutionPolicy Bypass -File "%POWERSHELL_SCRIPT%" -TestType "%TEST_TYPE%" -Verbose
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "Test execution failed"
) else (
    call :ColorEcho %COLOR_SUCCESS% "Test session started successfully!"
)
goto :End

:ShowStatus
call :ColorEcho %COLOR_INFO% "System Status Check"
echo ==================
echo.

REM Check Python
echo Checking Python...
python --version 2>nul
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "❌ Python: Not available"
) else (
    call :ColorEcho %COLOR_SUCCESS% "✅ Python: Available"
)

REM Check PowerShell
echo.
echo Checking PowerShell...
powershell -Command "$PSVersionTable.PSVersion" >nul 2>&1
if errorlevel 1 (
    call :ColorEcho %COLOR_ERROR% "❌ PowerShell: Not available"
) else (
    call :ColorEcho %COLOR_SUCCESS% "✅ PowerShell: Available"
)

REM Check Windows Terminal
echo.
echo Checking Windows Terminal...
wt.exe --version >nul 2>&1
if errorlevel 1 (
    call :ColorEcho %COLOR_WARNING% "⚠️  Windows Terminal: Not available"
) else (
    call :ColorEcho %COLOR_SUCCESS% "✅ Windows Terminal: Available"
)

REM Check configuration files
echo.
echo Checking configuration files...
if exist "%CONFIG_FILE%" (
    call :ColorEcho %COLOR_SUCCESS% "✅ Configuration: %CONFIG_FILE%"
) else (
    call :ColorEcho %COLOR_ERROR% "❌ Configuration: %CONFIG_FILE% not found"
)

if exist "%POWERSHELL_SCRIPT%" (
    call :ColorEcho %COLOR_SUCCESS% "✅ PowerShell Script: Available"
) else (
    call :ColorEcho %COLOR_ERROR% "❌ PowerShell Script: Not found"
)

if exist "%PYTHON_SCRIPT%" (
    call :ColorEcho %COLOR_SUCCESS% "✅ Python Script: Available"
) else (
    call :ColorEcho %COLOR_ERROR% "❌ Python Script: Not found"
)

if exist "%COMPLIANCE_SCRIPT%" (
    call :ColorEcho %COLOR_SUCCESS% "✅ Compliance Script: Available"
) else (
    call :ColorEcho %COLOR_ERROR% "❌ Compliance Script: Not found"
)

echo.
call :ColorEcho %COLOR_INFO% "Status check completed."
goto :End

:End
echo.
color %COLOR_RESET%
endlocal