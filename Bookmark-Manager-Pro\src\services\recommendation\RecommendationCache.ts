/**
 * RECOMMENDATION CACHING SERVICE
 * High-performance caching for recommendations and user profiles
 */

import type {
    RankedRecommendation,
    RecommendationCache,
    SimilarityScore,
    UserProfile
} from './interfaces'

export class ModernRecommendationCache implements RecommendationCache {
  private similarityCache = new Map<string, SimilarityScore[]>()
  private userProfileCache = new Map<string, UserProfile>()
  private recommendationCache = new Map<string, RankedRecommendation[]>()
  private embeddingCache = new Map<string, number[]>()
  private recommendationCacheTimes = new Map<string, number>()
  
  // Cache TTL settings (in milliseconds)
  private readonly SIMILARITY_TTL = 24 * 60 * 60 * 1000 // 24 hours
  private readonly USER_PROFILE_TTL = 60 * 60 * 1000 // 1 hour
  private readonly RECOMMENDATION_TTL = 30 * 60 * 1000 // 30 minutes
  private readonly EMBEDDING_TTL = 7 * 24 * 60 * 60 * 1000 // 7 days

  // Cache size limits
  private readonly MAX_SIMILARITY_ENTRIES = 10000
  private readonly MAX_USER_PROFILE_ENTRIES = 1000
  private readonly MAX_RECOMMENDATION_ENTRIES = 5000
  private readonly MAX_EMBEDDING_ENTRIES = 50000

  async getCachedSimilarities(itemId: string): Promise<SimilarityScore[]> {
    const cached = this.similarityCache.get(itemId)
    
    if (cached) {
      // Check if cache is still valid
      const isValid = cached.every(score => 
        Date.now() - score.lastUpdated.getTime() < this.SIMILARITY_TTL
      )
      
      if (isValid) {
        return cached
      } else {
        this.similarityCache.delete(itemId)
      }
    }
    
    return []
  }

  async cacheSimilarities(itemId: string, similarities: SimilarityScore[]): Promise<void> {
    // Implement LRU eviction if cache is full
    if (this.similarityCache.size >= this.MAX_SIMILARITY_ENTRIES) {
      this.evictOldestSimilarities()
    }
    
    this.similarityCache.set(itemId, similarities)
  }

  async cacheUserProfile(userId: string, profile: UserProfile): Promise<void> {
    // Implement LRU eviction if cache is full
    if (this.userProfileCache.size >= this.MAX_USER_PROFILE_ENTRIES) {
      this.evictOldestUserProfiles()
    }
    
    this.userProfileCache.set(userId, {
      ...profile,
      lastUpdated: new Date()
    })
  }

  async getCachedUserProfile(userId: string): Promise<UserProfile | null> {
    const cached = this.userProfileCache.get(userId)
    
    if (cached) {
      // Check if cache is still valid
      const isValid = Date.now() - cached.lastUpdated.getTime() < this.USER_PROFILE_TTL
      
      if (isValid) {
        return cached
      } else {
        this.userProfileCache.delete(userId)
      }
    }
    
    return null
  }

  async getCachedRecommendations(userId: string, context: string): Promise<RankedRecommendation[]> {
    const cacheKey = this.generateRecommendationCacheKey(userId, context)
    const cached = this.recommendationCache.get(cacheKey)
    
    if (cached) {
      // Check if cache is still valid (recommendations expire quickly)
      const cacheTime = this.getRecommendationCacheTime(cacheKey)
      const isValid = Date.now() - cacheTime < this.RECOMMENDATION_TTL
      
      if (isValid) {
        return cached
      } else {
        this.recommendationCache.delete(cacheKey)
      }
    }
    
    return []
  }

  async cacheRecommendations(
    userId: string, 
    context: string, 
    recommendations: RankedRecommendation[]
  ): Promise<void> {
    const cacheKey = this.generateRecommendationCacheKey(userId, context)
    
    // Implement LRU eviction if cache is full
    if (this.recommendationCache.size >= this.MAX_RECOMMENDATION_ENTRIES) {
      this.evictOldestRecommendations()
    }
    
    this.recommendationCache.set(cacheKey, recommendations)
    this.setRecommendationCacheTime(cacheKey, Date.now())
  }

  async invalidateUserCache(userId: string): Promise<void> {
    // Remove user profile
    this.userProfileCache.delete(userId)
    
    // Remove user's recommendations
    const keysToDelete: string[] = []
    for (const key of Array.from(this.recommendationCache.keys())) {
      if (key.startsWith(`${userId}:`)) {
        keysToDelete.push(key)
      }
    }
    keysToDelete.forEach(key => this.recommendationCache.delete(key))
  }

  // Embedding cache methods
  async getCachedEmbedding(text: string): Promise<number[] | null> {
    const cacheKey = this.hashText(text)
    const cached = this.embeddingCache.get(cacheKey)
    
    if (cached) {
      return cached
    }
    
    return null
  }

  async cacheEmbedding(text: string, embedding: number[]): Promise<void> {
    const cacheKey = this.hashText(text)
    
    // Implement LRU eviction if cache is full
    if (this.embeddingCache.size >= this.MAX_EMBEDDING_ENTRIES) {
      this.evictOldestEmbeddings()
    }
    
    this.embeddingCache.set(cacheKey, embedding)
  }

  // Cache statistics and management
  getCacheStats(): {
    similarities: { size: number; maxSize: number }
    userProfiles: { size: number; maxSize: number }
    recommendations: { size: number; maxSize: number }
    embeddings: { size: number; maxSize: number }
    totalMemoryUsage: number
  } {
    return {
      similarities: {
        size: this.similarityCache.size,
        maxSize: this.MAX_SIMILARITY_ENTRIES
      },
      userProfiles: {
        size: this.userProfileCache.size,
        maxSize: this.MAX_USER_PROFILE_ENTRIES
      },
      recommendations: {
        size: this.recommendationCache.size,
        maxSize: this.MAX_RECOMMENDATION_ENTRIES
      },
      embeddings: {
        size: this.embeddingCache.size,
        maxSize: this.MAX_EMBEDDING_ENTRIES
      },
      totalMemoryUsage: this.estimateMemoryUsage()
    }
  }

  async clearAllCaches(): Promise<void> {
    this.similarityCache.clear()
    this.userProfileCache.clear()
    this.recommendationCache.clear()
    this.embeddingCache.clear()
  }

  async optimizeCache(): Promise<void> {
    // Remove expired entries
    await this.removeExpiredEntries()

    // Compact caches if they're getting large
    if (this.similarityCache.size > this.MAX_SIMILARITY_ENTRIES * 0.8) {
      this.evictOldestSimilarities(Math.floor(this.MAX_SIMILARITY_ENTRIES * 0.2))
    }

    if (this.userProfileCache.size > this.MAX_USER_PROFILE_ENTRIES * 0.8) {
      this.evictOldestUserProfiles(Math.floor(this.MAX_USER_PROFILE_ENTRIES * 0.2))
    }

    if (this.recommendationCache.size > this.MAX_RECOMMENDATION_ENTRIES * 0.8) {
      this.evictOldestRecommendations(Math.floor(this.MAX_RECOMMENDATION_ENTRIES * 0.2))
    }

    if (this.embeddingCache.size > this.MAX_EMBEDDING_ENTRIES * 0.8) {
      this.evictOldestEmbeddings(Math.floor(this.MAX_EMBEDDING_ENTRIES * 0.2))
    }
  }

  async aggressiveCleanup(): Promise<void> {
    console.log('🧹 Aggressive cache cleanup starting...')

    // Remove 50% of all cache entries
    this.evictOldestSimilarities(Math.floor(this.similarityCache.size * 0.5))
    this.evictOldestUserProfiles(Math.floor(this.userProfileCache.size * 0.5))
    this.evictOldestRecommendations(Math.floor(this.recommendationCache.size * 0.5))
    this.evictOldestEmbeddings(Math.floor(this.embeddingCache.size * 0.5))

    // Force garbage collection if available
    if (typeof (window as any).gc === 'function') {
      (window as any).gc()
    }

    console.log('✅ Aggressive cleanup completed')
  }

  async emergencyCleanup(): Promise<void> {
    console.error('🚨 EMERGENCY: Clearing all recommendation caches!')

    // Clear everything except essential user profiles
    this.similarityCache.clear()
    this.recommendationCache.clear()
    this.embeddingCache.clear()
    this.recommendationCacheTimes.clear()

    // Keep only the 10 most recent user profiles
    if (this.userProfileCache.size > 10) {
      const profiles = Array.from(this.userProfileCache.entries())
      profiles.sort(([, a], [, b]) => b.lastUpdated.getTime() - a.lastUpdated.getTime())

      this.userProfileCache.clear()
      profiles.slice(0, 10).forEach(([key, value]) => {
        this.userProfileCache.set(key, value)
      })
    }

    // Multiple garbage collection cycles
    for (let i = 0; i < 5; i++) {
      if (typeof (window as any).gc === 'function') {
        (window as any).gc()
      }
    }

    console.error('🚨 Emergency cleanup completed - all caches cleared')
  }

  // Private helper methods
  private generateRecommendationCacheKey(userId: string, context: string): string {
    return `${userId}:${this.hashText(context)}`
  }

  private getRecommendationCacheTime(cacheKey: string): number {
    return this.recommendationCacheTimes.get(cacheKey) || 0
  }

  private setRecommendationCacheTime(cacheKey: string, time: number): void {
    this.recommendationCacheTimes.set(cacheKey, time)
  }

  private evictOldestSimilarities(count: number = 1): void {
    const entries = Array.from(this.similarityCache.entries())
    
    // Sort by oldest lastUpdated time
    entries.sort(([, a], [, b]) => {
      const aTime = Math.min(...a.map(score => score.lastUpdated.getTime()))
      const bTime = Math.min(...b.map(score => score.lastUpdated.getTime()))
      return aTime - bTime
    })
    
    // Remove oldest entries
    for (let i = 0; i < count && i < entries.length; i++) {
      this.similarityCache.delete(entries[i][0])
    }
  }

  private evictOldestUserProfiles(count: number = 1): void {
    const entries = Array.from(this.userProfileCache.entries())
    
    // Sort by oldest lastUpdated time
    entries.sort(([, a], [, b]) => 
      a.lastUpdated.getTime() - b.lastUpdated.getTime()
    )
    
    // Remove oldest entries
    for (let i = 0; i < count && i < entries.length; i++) {
      this.userProfileCache.delete(entries[i][0])
    }
  }

  private evictOldestRecommendations(count: number = 1): void {
    const entries = Array.from(this.recommendationCache.keys())
    
    // Sort by cache time
    entries.sort((a, b) => 
      this.getRecommendationCacheTime(a) - this.getRecommendationCacheTime(b)
    )
    
    // Remove oldest entries
    for (let i = 0; i < count && i < entries.length; i++) {
      this.recommendationCache.delete(entries[i])
      this.recommendationCacheTimes.delete(entries[i])
    }
  }

  private evictOldestEmbeddings(count: number = 1): void {
    // Simple LRU eviction for embeddings
    const entries = Array.from(this.embeddingCache.keys())
    
    // Remove first N entries (oldest in insertion order)
    for (let i = 0; i < count && i < entries.length; i++) {
      this.embeddingCache.delete(entries[i])
    }
  }

  private async removeExpiredEntries(): Promise<void> {
    const now = Date.now()
    
    // Remove expired similarities
    for (const [key, similarities] of Array.from(this.similarityCache.entries())) {
      const isExpired = similarities.some(score => 
        now - score.lastUpdated.getTime() > this.SIMILARITY_TTL
      )
      if (isExpired) {
        this.similarityCache.delete(key)
      }
    }
    
    // Remove expired user profiles
    for (const [key, profile] of Array.from(this.userProfileCache.entries())) {
      if (now - profile.lastUpdated.getTime() > this.USER_PROFILE_TTL) {
        this.userProfileCache.delete(key)
      }
    }
    
    // Remove expired recommendations
    for (const [key] of Array.from(this.recommendationCache.entries())) {
      const cacheTime = this.getRecommendationCacheTime(key)
      if (now - cacheTime > this.RECOMMENDATION_TTL) {
        this.recommendationCache.delete(key)
        this.recommendationCacheTimes.delete(key)
      }
    }
  }

  private estimateMemoryUsage(): number {
    // Rough estimation of memory usage in bytes
    let totalSize = 0
    
    // Similarities cache
    for (const similarities of Array.from(this.similarityCache.values())) {
      totalSize += similarities.length * 100 // Rough estimate per similarity score
    }
    
    // User profiles cache
    for (const profile of Array.from(this.userProfileCache.values())) {
      totalSize += JSON.stringify(profile).length * 2 // UTF-16 encoding
    }
    
    // Recommendations cache
    for (const recommendations of Array.from(this.recommendationCache.values())) {
      totalSize += recommendations.length * 500 // Rough estimate per recommendation
    }
    
    // Embeddings cache
    for (const embedding of Array.from(this.embeddingCache.values())) {
      totalSize += embedding.length * 8 // 8 bytes per number
    }
    
    return totalSize
  }

  private hashText(text: string): string {
    // Simple hash function for cache keys
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString()
  }
}

// Singleton instance for global use
export const recommendationCache = new ModernRecommendationCache()

// Memory monitoring and auto-optimization using IntervalManager
if (typeof window !== 'undefined') {
  // Import interval manager for proper interval management
  import('../../utils/intervalManager').then(({ intervalManager }: any) => {
    // Aggressive optimization every 2 minutes during high memory usage
    intervalManager.createInterval(() => {
    const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0
    const memoryLimit = (performance as any).memory?.jsHeapSizeLimit || 1
    const usagePercentage = (memoryUsage / memoryLimit) * 100

    if (usagePercentage > 60) {
      // Only log once per 5-minute period to prevent spam
      const now = Date.now()
      const lastLog = (window as any)._lastMemoryLog || 0
      if (now - lastLog > 5 * 60 * 1000) {
        console.warn(`🧹 High memory usage (${usagePercentage.toFixed(1)}%) - Aggressive cache cleanup`)
        ;(window as any)._lastMemoryLog = now
      }
      recommendationCache.aggressiveCleanup()
    } else {
      recommendationCache.optimizeCache()
    }
    }, 2 * 60 * 1000, 'recommendation-cache-optimizer') // Every 2 minutes

    // Emergency cleanup at 75%+ memory usage
    intervalManager.createInterval(() => {
    const memoryUsage = (performance as any).memory?.usedJSHeapSize || 0
    const memoryLimit = (performance as any).memory?.jsHeapSizeLimit || 1
    const usagePercentage = (memoryUsage / memoryLimit) * 100

    if (usagePercentage > 75) {
      console.error(`🚨 CRITICAL MEMORY: ${usagePercentage.toFixed(1)}% - Emergency cache clear`)
      recommendationCache.emergencyCleanup()
    }
    }, 30 * 1000, 'emergency-memory-monitor') // Every 30 seconds

    // Make cache available globally for emergency cleanup
    ;(window as any).recommendationCache = recommendationCache
  })
}
