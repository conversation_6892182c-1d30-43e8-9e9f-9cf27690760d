import { Activity, AlertOctagon, Al<PERSON><PERSON>riangle, CheckCircle, Info, Zap } from 'lucide-react'
import React, { useCallback, useEffect, useState } from 'react'
import { emergencyCriticalCleanup, forceMemoryReload } from '../utils/criticalMemoryCleanup'
import { forceImmediateCleanup } from '../utils/emergencyMemoryCleanup'
import { detectMemoryLeaks, getMemoryStats } from '../utils/memoryOptimization'

interface MemoryStats {
  usedMB: number
  limitMB: number
  usagePercentage: number
  usedJSHeapSize: number
  totalJSHeapSize: number
  jsHeapSizeLimit: number
}

interface MemoryMonitorProps {
  bookmarkCount: number
  isVisible?: boolean
  onOptimizationSuggestion?: (suggestion: string) => void
}

export const MemoryMonitor: React.FC<MemoryMonitorProps> = ({
  bookmarkCount,
  isVisible = true,
  onOptimizationSuggestion
}) => {
  const [memoryStats, setMemoryStats] = useState<MemoryStats | null>(null)
  const [isExpanded, setIsExpanded] = useState(false)
  const [history, setHistory] = useState<number[]>([])
  const [lastOptimizationCheck, setLastOptimizationCheck] = useState<number>(0)

  // Update memory stats
  const updateMemoryStats = useCallback(() => {
    const stats = getMemoryStats()
    if (stats) {
      setMemoryStats(stats)
      
      // Keep history of last 20 measurements
      setHistory(prev => {
        const newHistory = [...prev, stats.usagePercentage]
        return newHistory.slice(-20)
      })
      
      // Check for optimization opportunities
      const now = Date.now()
      if (now - lastOptimizationCheck > 30000) { // Check every 30 seconds
        checkOptimizationOpportunities(stats)
        setLastOptimizationCheck(now)
      }
    }
  }, [lastOptimizationCheck])

  // Force garbage collection if available
  const forceGarbageCollection = useCallback(() => {
    if ('gc' in window && typeof (window as any).gc === 'function') {
      console.log('🗑️ Forcing garbage collection...')
      ;(window as any).gc()
      setTimeout(updateMemoryStats, 1000) // Update stats after GC
    } else {
      console.warn('Garbage collection not available in this environment')
    }
  }, [updateMemoryStats])

  // Check for optimization opportunities with immediate action
  const checkOptimizationOpportunities = useCallback((stats: MemoryStats) => {
    const suggestions: string[] = []

    // CRITICAL: Immediate action needed at 65%+
    if (stats.usagePercentage > 65) {
      console.warn(`🚨 CRITICAL: Memory usage at ${stats.usagePercentage}%! Taking immediate action...`)

      // EMERGENCY: At 80%+ trigger aggressive cleanup
      if (stats.usagePercentage > 80) {
        console.error(`🔥 EMERGENCY: Memory at ${stats.usagePercentage}%! Triggering aggressive cleanup!`)

        // Multiple garbage collection cycles
        for (let i = 0; i < 3; i++) {
          if ('gc' in window && typeof (window as any).gc === 'function') {
            (window as any).gc()
          }
          // Force browser to release memory
          const temp = new Array(1000000).fill(null)
          temp.length = 0
        }

        // Clear all possible caches
        if (typeof window !== 'undefined') {
          // Clear all storage
          try {
            localStorage.clear()
            sessionStorage.clear()
          } catch (e) { /* ignore */ }

          // Clear performance data
          if (window.performance?.clearMeasures) window.performance.clearMeasures()
          if (window.performance?.clearMarks) window.performance.clearMarks()
          if (window.performance?.clearResourceTimings) window.performance.clearResourceTimings()

          // Force DOM cleanup
          const hiddenElements = document.querySelectorAll('[style*="display: none"], .hidden')
          hiddenElements.forEach(el => el.remove())
        }

        // Limit history aggressively
        setHistory([stats.usagePercentage])

        console.log('🔥 EMERGENCY cleanup completed')
      }

      // Force immediate garbage collection
      if ('gc' in window && typeof (window as any).gc === 'function') {
        (window as any).gc()
        console.log('🗑️ Emergency garbage collection triggered')
      }

      // Clear browser caches
      if (typeof window !== 'undefined') {
        // Clear performance measurements
        if (window.performance?.clearMeasures) window.performance.clearMeasures()
        if (window.performance?.clearMarks) window.performance.clearMarks()

        // Clear any cached data
        sessionStorage.clear()

        // Limit history to prevent memory accumulation
        setHistory(prev => prev.slice(-5))
      }

      suggestions.push(`CRITICAL: Memory at ${stats.usagePercentage}%! Immediate cleanup performed.`)
    }

    if (stats.usagePercentage > 50) {
      suggestions.push('Memory usage elevated. Virtual scrolling strongly recommended.')
    }

    if (bookmarkCount > 1000 && stats.usagePercentage > 40) {
      suggestions.push('Large dataset detected. Enable virtual scrolling to reduce memory usage.')
    }

    // Enhanced memory leak detection
    if (history.length > 5) {
      const recentGrowth = history.slice(-3).reduce((sum, val) => sum + val, 0) / 3
      const olderAverage = history.slice(-10, -3).reduce((sum, val) => sum + val, 0) / 7

      if (recentGrowth > olderAverage + 15) {
        console.warn(`📈 Rapid memory growth detected: ${recentGrowth.toFixed(1)}% vs ${olderAverage.toFixed(1)}%`)
        suggestions.push('Rapid memory growth detected. Consider reducing visible bookmarks.')

        // Force cleanup on rapid growth
        if (recentGrowth > olderAverage + 25) {
          forceGarbageCollection()
        }
      }
    }

    // Detect memory leaks
    const leakDetection = detectMemoryLeaks()
    if (leakDetection.leaksDetected) {
      suggestions.push(...leakDetection.recommendations)
    }

    if (suggestions.length > 0 && onOptimizationSuggestion) {
      onOptimizationSuggestion(suggestions[0])
    }
  }, [bookmarkCount, history, onOptimizationSuggestion, forceGarbageCollection])

  // Set up monitoring interval with dynamic frequency
  useEffect(() => {
    updateMemoryStats() // Initial update

    // More frequent monitoring during high memory usage
    const getMonitoringInterval = () => {
      if (!memoryStats) return 5000
      if (memoryStats.usagePercentage > 70) return 2000 // Every 2 seconds when critical
      if (memoryStats.usagePercentage > 50) return 3000 // Every 3 seconds when elevated
      return 5000 // Every 5 seconds normally
    }

    // Use IntervalManager for proper interval management
    let intervalId: number | null = null

    if ((window as any).intervalManager) {
      intervalId = (window as any).intervalManager.createInterval(
        updateMemoryStats,
        getMonitoringInterval(),
        'memory-monitor'
      )
    } else {
      // Import IntervalManager if not available globally
      import('../utils/intervalManager').then(({ IntervalManager }) => {
        const manager = IntervalManager.getInstance()
        intervalId = manager.createInterval(
          updateMemoryStats,
          getMonitoringInterval(),
          'memory-monitor'
        )
      })
    }

    return () => {
      if (intervalId !== null) {
        if ((window as any).intervalManager) {
          (window as any).intervalManager.clearInterval(intervalId)
        } else {
          // IntervalManager will handle cleanup automatically
        }
      }
    }
  }, [updateMemoryStats, memoryStats?.usagePercentage])

  // Emergency cleanup handler
  const handleEmergencyCleanup = useCallback(async () => {
    console.log('🚨 Emergency cleanup initiated by user')
    try {
      const result = await forceImmediateCleanup()
      console.log(result)
      if (onOptimizationSuggestion) {
        onOptimizationSuggestion(result)
      }
      setTimeout(updateMemoryStats, 2000) // Update stats after cleanup
    } catch (error) {
      console.error('Emergency cleanup failed:', error)
    }
  }, [updateMemoryStats, onOptimizationSuggestion])

  if (!isVisible || !memoryStats) {
    return null
  }

  const getStatusColor = (percentage: number) => {
    if (percentage < 50) return 'text-white'
    if (percentage < 75) return 'text-yellow-500'
    return 'text-red-500'
  }

  const getStatusIcon = (percentage: number) => {
    if (percentage < 50) return <CheckCircle size={16} className="text-white" />
    if (percentage < 75) return <Info size={16} className="text-yellow-500" />
    return <AlertTriangle size={16} className="text-red-500" />
  }

  return (
    <div className="memory-monitor">
      {/* Compact view */}
      <div 
        className="memory-monitor-compact"
        onClick={() => setIsExpanded(!isExpanded)}
        title={`Memory usage: ${memoryStats.usedMB}MB (${memoryStats.usagePercentage}%)`}
      >
        <Activity size={16} />
        <span className={getStatusColor(memoryStats.usagePercentage)}>
          {memoryStats.usedMB}MB
        </span>
        {getStatusIcon(memoryStats.usagePercentage)}
      </div>

      {/* Expanded view */}
      {isExpanded && (
        <div className="memory-monitor-expanded">
          <div className="memory-monitor-header">
            <h3>Memory Usage</h3>
            <button 
              onClick={() => setIsExpanded(false)}
              className="close-button"
            >
              ×
            </button>
          </div>
          
          <div className="memory-stats">
            <div className="stat-row">
              <span>Used Memory:</span>
              <span className={getStatusColor(memoryStats.usagePercentage)}>
                {memoryStats.usedMB}MB / {memoryStats.limitMB}MB
              </span>
            </div>
            
            <div className="stat-row">
              <span>Usage:</span>
              <span className={getStatusColor(memoryStats.usagePercentage)}>
                {memoryStats.usagePercentage}%
              </span>
            </div>
            
            <div className="stat-row">
              <span>JS Heap:</span>
              <span>
                {Math.round(memoryStats.usedJSHeapSize / (1024 * 1024))}MB / 
                {Math.round(memoryStats.jsHeapSizeLimit / (1024 * 1024))}MB
              </span>
            </div>
            
            <div className="stat-row">
              <span>Bookmarks:</span>
              <span>{bookmarkCount.toLocaleString()}</span>
            </div>
          </div>

          {/* Memory usage bar */}
          <div className="memory-bar">
            <div 
              className={`memory-bar-fill ${
                memoryStats.usagePercentage > 75 ? 'high' : 
                memoryStats.usagePercentage > 50 ? 'medium' : 'low'
              }`}
              style={{ width: `${Math.min(memoryStats.usagePercentage, 100)}%` }}
            />
          </div>

          {/* History chart (simple) */}
          {history.length > 5 && (
            <div className="memory-history">
              <div className="history-label">Usage History</div>
              <div className="history-chart">
                {history.map((value, index) => (
                  <div
                    key={index}
                    className="history-bar"
                    style={{ 
                      height: `${Math.max(value, 5)}%`,
                      backgroundColor: value > 75 ? '#ef4444' : value > 50 ? '#f59e0b' : '#10b981'
                    }}
                  />
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="memory-actions">
            <button
              onClick={forceGarbageCollection}
              className="gc-button"
              title="Force garbage collection (if available)"
            >
              🗑️ Clean Memory
            </button>

            {memoryStats.usagePercentage > 80 && (
              <>
                <button
                  onClick={() => emergencyCriticalCleanup()}
                  className="critical-cleanup-button"
                  title="CRITICAL: Aggressive memory cleanup for emergency situations"
                >
                  <AlertOctagon size={16} />
                  CRITICAL CLEANUP
                </button>
                <button
                  onClick={() => forceMemoryReload()}
                  className="force-reload-button"
                  title="Force page reload to reset memory"
                >
                  🔄 Force Reload
                </button>
              </>
            )}

            {memoryStats.usagePercentage > 60 && memoryStats.usagePercentage <= 80 && (
              <button
                onClick={handleEmergencyCleanup}
                className="emergency-cleanup-button"
                title="Emergency memory cleanup - clears caches and forces optimization"
              >
                <Zap size={16} />
                Emergency Cleanup
              </button>
            )}

            {memoryStats.usagePercentage > 75 && (
              <div className="memory-warning critical">
                🚨 CRITICAL: Memory usage at {memoryStats.usagePercentage}%!
                Use Emergency Cleanup or enable virtual scrolling immediately.
              </div>
            )}

            {memoryStats.usagePercentage > 60 && memoryStats.usagePercentage <= 75 && (
              <div className="memory-warning elevated">
                ⚠️ Elevated memory usage. Consider enabling virtual scrolling or reducing visible bookmarks.
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
