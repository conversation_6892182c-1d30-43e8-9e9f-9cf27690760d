<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Domain Extraction Test - Bookmark Studio</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 20px;
            background: linear-gradient(135deg, #1e293b, #334155);
            border-radius: 12px;
            border: 1px solid #475569;
        }
        
        .header h1 {
            color: #22d3ee;
            margin: 0 0 10px 0;
            font-size: 2rem;
        }
        
        .header p {
            color: #94a3b8;
            margin: 0;
        }
        
        .test-grid {
            display: grid;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .test-item {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            transition: border-color 0.2s;
        }
        
        .test-item.improved {
            border-color: #22c55e;
            box-shadow: 0 0 0 1px rgba(34, 197, 94, 0.2);
        }
        
        .url-original {
            font-size: 12px;
            color: #64748b;
            margin-bottom: 12px;
            word-break: break-all;
            font-family: 'Courier New', monospace;
        }
        
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 16px;
            margin-bottom: 12px;
        }
        
        .method {
            padding: 12px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
        }
        
        .method-simple {
            background: #7f1d1d;
            border: 1px solid #dc2626;
        }
        
        .method-smart {
            background: #14532d;
            border: 1px solid #22c55e;
        }
        
        .method-title {
            font-size: 11px;
            font-weight: bold;
            margin-bottom: 6px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .method-result {
            font-size: 14px;
            font-weight: 500;
        }
        
        .improvement-note {
            font-size: 11px;
            color: #22c55e;
            font-style: italic;
            margin-top: 8px;
        }
        
        .rules-section {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .rules-title {
            color: #22d3ee;
            font-size: 1.2rem;
            margin-bottom: 16px;
            font-weight: 600;
        }
        
        .rules-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .rules-list li {
            padding: 8px 0;
            border-bottom: 1px solid #334155;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .rules-list li:last-child {
            border-bottom: none;
        }
        
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: #1e293b;
            border: 1px solid #334155;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #22d3ee;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            color: #94a3b8;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Domain Extraction Test</h1>
            <p>Testing the improved domain extraction logic for long URLs in Bookmark Studio</p>
        </div>

        <div id="test-results" class="test-grid">
            <!-- Test results will be populated by JavaScript -->
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="total-tests">0</div>
                <div class="stat-label">Total Tests</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="improved-count">0</div>
                <div class="stat-label">Improved Results</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="improvement-rate">0%</div>
                <div class="stat-label">Improvement Rate</div>
            </div>
        </div>

        <div class="rules-section">
            <div class="rules-title">Smart Domain Extraction Rules</div>
            <ul class="rules-list">
                <li>✅ If pathname length > 20 characters: show domain + first path segment</li>
                <li>✅ If first path segment > 15 characters: truncate with ellipsis</li>
                <li>✅ Otherwise: show hostname only (same as simple logic)</li>
                <li>✅ Invalid URLs: truncate at 30 characters with ellipsis</li>
                <li>✅ Always remove 'www.' prefix for cleaner display</li>
            </ul>
        </div>
    </div>

    <script>
        // Test URLs including the problematic one
        const testUrls = [
            'https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/',
            'https://dodi-repacks.site/1480-another-very-long-game-title-with-version-numbers-v2-3456-7-8-multi25/',
            'https://github.com/user/repo',
            'https://very-long-domain-name.com/extremely/long/path/that/goes/on/and/on',
            'https://short.com/a',
            'https://example.com/very-long-path-name-that-exceeds-fifteen-characters-easily',
            'https://subdomain.example.com/short',
            'https://www.google.com/search?q=very+long+search+query+with+many+parameters',
            'invalid-url-test',
            'https://stackoverflow.com/questions/12345/how-to-handle-very-long-urls-in-web-applications',
            'https://docs.microsoft.com/en-us/azure/storage/blobs/storage-blob-container-properties',
            'https://api.example.com/v1/users/12345/bookmarks/67890/collections/abcdef'
        ];

        // Simple domain extraction (original logic)
        function extractSimpleDomain(url) {
            try {
                return new URL(url).hostname.replace('www.', '');
            } catch {
                return url;
            }
        }

        // Smart domain extraction (improved logic)
        function extractSmartDomain(url) {
            try {
                const urlObj = new URL(url);
                const hostname = urlObj.hostname.replace('www.', '');
                
                // For very long URLs, show domain + truncated path
                if (urlObj.pathname && urlObj.pathname.length > 20) {
                    const pathParts = urlObj.pathname.split('/').filter(Boolean);
                    if (pathParts.length > 0) {
                        const firstPath = pathParts[0];
                        if (firstPath.length > 15) {
                            return `${hostname}/${firstPath.substring(0, 15)}...`;
                        }
                        return `${hostname}/${firstPath}`;
                    }
                }
                
                return hostname;
            } catch {
                // Fallback for invalid URLs
                const truncated = url.length > 30 ? url.substring(0, 30) + '...' : url;
                return truncated;
            }
        }

        // Run tests and display results
        function runTests() {
            const resultsContainer = document.getElementById('test-results');
            let improvedCount = 0;
            
            testUrls.forEach((url, index) => {
                const simpleResult = extractSimpleDomain(url);
                const smartResult = extractSmartDomain(url);
                const isImproved = simpleResult !== smartResult;
                
                if (isImproved) improvedCount++;
                
                const testItem = document.createElement('div');
                testItem.className = `test-item ${isImproved ? 'improved' : ''}`;
                
                testItem.innerHTML = `
                    <div class="url-original">Original: ${url}</div>
                    <div class="comparison">
                        <div class="method method-simple">
                            <div class="method-title">❌ Simple Logic</div>
                            <div class="method-result">${simpleResult}</div>
                        </div>
                        <div class="method method-smart">
                            <div class="method-title">✅ Smart Logic</div>
                            <div class="method-result">${smartResult}</div>
                        </div>
                    </div>
                    ${isImproved ? '<div class="improvement-note">✨ Improved: Better context while preventing layout overflow</div>' : ''}
                `;
                
                resultsContainer.appendChild(testItem);
            });
            
            // Update stats
            document.getElementById('total-tests').textContent = testUrls.length;
            document.getElementById('improved-count').textContent = improvedCount;
            document.getElementById('improvement-rate').textContent = 
                Math.round((improvedCount / testUrls.length) * 100) + '%';
        }

        // Run tests when page loads
        document.addEventListener('DOMContentLoaded', runTests);
    </script>
</body>
</html>
