{"timestamp": "2025-07-10T16:36:38.155Z", "auditPhases": {"staticAnalysis": {"completed": true, "score": 100, "issues": [{"test": "Domain Panel Component Existence", "passed": true, "score": 100, "message": "DomainPanel component found with TypeScript interfaces, state management, event handlers", "severity": "normal"}, {"test": "TypeScript Implementation Quality", "passed": true, "score": 100, "message": "Good TypeScript implementation", "severity": "normal"}, {"test": "Domain Logic Implementation", "passed": true, "score": 100, "message": "Domain logic found in 1 files: utilityService.ts", "severity": "normal"}]}, "codeQuality": {"completed": true, "score": 75, "issues": [{"test": "Error Handling Implementation", "passed": true, "score": 75, "message": "Adequate error handling", "severity": "normal"}, {"test": "Performance Considerations", "passed": false, "score": 50, "message": "No performance optimizations detected", "severity": "normal"}, {"test": "Code Organization & Structure", "passed": true, "score": 100, "message": "Project structure includes: components, services, types, utils, hooks", "severity": "normal"}]}, "securityAnalysis": {"completed": true, "score": 133, "issues": [{"test": "Input Validation & Sanitization", "passed": true, "score": 165, "message": "Good security practices", "severity": "normal"}, {"test": "Dependency Security", "passed": true, "score": 100, "message": "Dependencies appear secure", "severity": "normal"}]}, "performanceAnalysis": {"completed": true, "score": 55, "issues": [{"test": "Bundle Size Analysis", "passed": true, "score": 70, "message": "Source code: 166 files, 1566.7KB total, 9.4KB average", "severity": "normal"}, {"test": "Component Complexity", "passed": false, "score": 40, "message": "Component complexity: 297 lines, 14 functions, 8 state hooks", "severity": "warning"}]}, "architectureAnalysis": {"completed": true, "score": 80, "issues": [{"test": "Component Architecture", "passed": true, "score": 80, "message": "Architecture: 63 components, proper naming, good separation", "severity": "normal"}]}}, "totalTests": 12, "passed": 9, "failed": 3, "critical": 1, "warnings": 1, "overallScore": 89, "productionReadiness": "GOOD", "criticalFindings": [], "recommendations": []}