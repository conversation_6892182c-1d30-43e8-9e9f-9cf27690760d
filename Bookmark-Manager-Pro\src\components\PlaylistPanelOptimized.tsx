import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface PlaylistPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const PlaylistPanelOptimized: React.FC<PlaylistPanelProps> = ({ isOpen, onClose, onOpenPanel }) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [playlistName, setPlaylistName] = useState('')
  const [playlistType, setPlaylistType] = useState('sequential')
  const [isCreating, setIsCreating] = useState(false)
  const [creationProgress, setCreationProgress] = useState(0)
  const [selectedBookmarks, setSelectedBookmarks] = useState<string[]>([])
  const [advancedOptionsExpanded, setAdvancedOptionsExpanded] = useState(false)
  const [playlistsExpanded, setPlaylistsExpanded] = useState(false)

  const createPlaylist = () => {
    setIsCreating(true)
    setCreationProgress(0)
    // Playlist creation logic here
    console.log('Creating playlist...')
  }

  const playPlaylist = (playlistId: string) => {
    console.log('Playing playlist:', playlistId)
  }

  return (
    <div className="import-panel">
      {/* Quick Create */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.quickCreate')}</h3>
        <p className="section-description">{t('playlist.quickCreateDescription')}</p>
        
        <div className="form-row">
          <label>{t('playlist.name')}:</label>
          <input 
            type="text" 
            value={playlistName}
            onChange={(e) => setPlaylistName(e.target.value)}
            placeholder={t('playlist.namePlaceholder')}
            className="input-compact" 
          />
        </div>

        <div className="form-row">
          <label>{t('playlist.type')}:</label>
          <select 
            value={playlistType} 
            onChange={(e) => setPlaylistType(e.target.value)}
            className="input-compact"
          >
            <option value="sequential">{t('playlist.sequential')}</option>
            <option value="random">{t('playlist.random')}</option>
            <option value="smart">{t('playlist.smart')}</option>
            <option value="multimedia">{t('playlist.multimedia')}</option>
          </select>
        </div>

        <div className="controls-grid">
          <button 
            className="btn-compact primary" 
            onClick={createPlaylist}
            disabled={isCreating || !playlistName}
          >
            {isCreating ? t('playlist.creating') : t('playlist.create')}
          </button>
          <button className="btn-compact">{t('playlist.autoGenerate')}</button>
        </div>

        {isCreating && (
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${creationProgress}%` }} />
            <span className="progress-text">{creationProgress}%</span>
          </div>
        )}
      </div>

      {/* Playlist Statistics */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.statistics')}</h3>
        
        <div className="controls-grid">
          <div className="status-compact">
            <div className="status-icon info" />
            <span>{selectedBookmarks.length} {t('playlist.selected')}</span>
          </div>
          <div className="status-compact">
            <div className="status-icon success" />
            <span>3 {t('playlist.existing')}</span>
          </div>
        </div>
      </div>

      {/* Quick Templates */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.quickTemplates')}</h3>
        
        <div className="button-grid">
          <button className="btn-compact">{t('playlist.workSites')}</button>
          <button className="btn-compact">{t('playlist.entertainment')}</button>
          <button className="btn-compact">{t('playlist.learning')}</button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact">{t('playlist.news')}</button>
          <button className="btn-compact">{t('playlist.social')}</button>
        </div>
      </div>

      {/* Playlist Controls */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.controls')}</h3>
        
        <div className="form-row">
          <label>{t('playlist.autoPlay')}:</label>
          <select className="input-compact">
            <option value="off">{t('playlist.off')}</option>
            <option value="5s">5 {t('playlist.seconds')}</option>
            <option value="10s">10 {t('playlist.seconds')}</option>
            <option value="30s">30 {t('playlist.seconds')}</option>
            <option value="60s">1 {t('playlist.minute')}</option>
          </select>
        </div>
        
        <div className="form-row">
          <label>
            <input type="checkbox" defaultChecked />
            {t('playlist.openInNewTabs')}
          </label>
        </div>
        
        <div className="form-row">
          <label>
            <input type="checkbox" />
            {t('playlist.shuffleMode')}
          </label>
        </div>
      </div>

      {/* Advanced Options - Collapsible */}
      <div className={`collapsible-section ${advancedOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setAdvancedOptionsExpanded(!advancedOptionsExpanded)}
        >
          <span>{t('playlist.advancedOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('playlist.smartCriteria')}:</label>
            <textarea 
              placeholder={t('playlist.smartCriteriaPlaceholder')} 
              className="input-compact"
              rows={3}
            />
          </div>
          <div className="form-row">
            <label>{t('playlist.tags')}:</label>
            <input 
              type="text" 
              placeholder={t('playlist.tagsPlaceholder')} 
              className="input-compact" 
            />
          </div>
          <div className="form-row">
            <label>{t('playlist.maxItems')}:</label>
            <input type="number" defaultValue="50" className="input-compact" />
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('playlist.includeSubfolders')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('playlist.excludeBroken')}
            </label>
          </div>
          <div className="form-row">
            <label>{t('playlist.sortBy')}:</label>
            <select className="input-compact">
              <option value="name">{t('playlist.name')}</option>
              <option value="date">{t('playlist.dateAdded')}</option>
              <option value="visits">{t('playlist.visitCount')}</option>
              <option value="domain">{t('playlist.domain')}</option>
            </select>
          </div>
        </div>
      </div>

      {/* Existing Playlists - Collapsible */}
      <div className={`collapsible-section ${playlistsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setPlaylistsExpanded(!playlistsExpanded)}
        >
          <span>{t('playlist.existingPlaylists')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="scrollable-content">
            <div className="status-compact">
              <div className="status-icon success" />
              <span>🎵 {t('playlist.workSites')} (12 {t('playlist.items')})</span>
              <button className="btn-compact" onClick={() => playPlaylist('work')}>
                ▶️
              </button>
            </div>
            <div className="status-compact">
              <div className="status-icon info" />
              <span>📚 {t('playlist.learning')} (8 {t('playlist.items')})</span>
              <button className="btn-compact" onClick={() => playPlaylist('learning')}>
                ▶️
              </button>
            </div>
            <div className="status-compact">
              <div className="status-icon warning" />
              <span>🎮 {t('playlist.entertainment')} (25 {t('playlist.items')})</span>
              <button className="btn-compact" onClick={() => playPlaylist('entertainment')}>
                ▶️
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.quickActions')}</h3>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('multimedia')}>
            {t('playlist.multimediaPlaylist')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('export')}>
            {t('playlist.exportPlaylist')}
          </button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('split')}>
            {t('playlist.splitByDomain')}
          </button>
          <button className="btn-compact">{t('playlist.sharePlaylist')}</button>
        </div>
      </div>

      {/* Navigation */}
      <div className="import-section">
        <h3 className="section-title">{t('playlist.relatedTools')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('import')}>
            {t('playlist.importBookmarks')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('content')}>
            {t('playlist.contentAnalysis')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('summary')}>
            {t('playlist.generateSummary')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  )
}