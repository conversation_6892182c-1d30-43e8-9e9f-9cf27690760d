/**
 * MULTIMEDIA PLAYLIST INTEGRATION
 * Seamless integration with existing bookmark interface
 * Dr. <PERSON> - Renowned Web Expert Enhancement
 */

import React, { useState } from 'react'
import { Play, Video, Headphones, FileText, Zap } from 'lucide-react'
import { MultimediaPlaylistPanel } from './MultimediaPlaylistPanel'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import type { Bookmark } from '../../types'

interface MultimediaPlaylistIntegrationProps {
  // For collection-based playlists
  collectionId?: string
  collectionName?: string
  
  // For selected bookmarks
  selectedBookmarks?: Bookmark[]
  
  // For mind map selections
  mindMapSelection?: string[]
  
  // UI context
  trigger?: 'button' | 'menu' | 'auto'
  size?: 'small' | 'medium' | 'large'
  position?: 'inline' | 'floating' | 'sidebar'
}

export const MultimediaPlaylistIntegration: React.FC<MultimediaPlaylistIntegrationProps> = ({
  collectionId,
  collectionName,
  selectedBookmarks,
  mindMapSelection,
  trigger = 'button',
  size = 'medium',
  position = 'inline'
}) => {
  const { bookmarks } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'
  
  const [showPlaylist, setShowPlaylist] = useState(false)
  const [playlistStats, setPlaylistStats] = useState<{
    totalItems: number
    videoCount: number
    audioCount: number
    documentCount: number
    estimatedDuration: number
  } | null>(null)

  // Calculate playlist statistics
  React.useEffect(() => {
    let targetBookmarks: Bookmark[] = []
    
    if (selectedBookmarks && selectedBookmarks.length > 0) {
      targetBookmarks = selectedBookmarks
    } else if (collectionId) {
      targetBookmarks = bookmarks.filter(b => b.collection === collectionId)
    } else if (mindMapSelection && mindMapSelection.length > 0) {
      targetBookmarks = bookmarks.filter(b => mindMapSelection.includes(b.id))
    }

    if (targetBookmarks.length > 0) {
      // Analyze content types (simplified detection)
      const stats = targetBookmarks.reduce(
        (acc, bookmark) => {
          const url = bookmark.url.toLowerCase()
          
          if (url.includes('youtube.com') || url.includes('youtu.be') || url.includes('vimeo.com')) {
            acc.videoCount++
            acc.estimatedDuration += 300 // 5 min average
          } else if (url.includes('spotify.com') || url.includes('soundcloud.com') || url.includes('.mp3')) {
            acc.audioCount++
            acc.estimatedDuration += 240 // 4 min average
          } else {
            acc.documentCount++
            acc.estimatedDuration += 180 // 3 min read average
          }
          
          return acc
        },
        { totalItems: targetBookmarks.length, videoCount: 0, audioCount: 0, documentCount: 0, estimatedDuration: 0 }
      )
      
      setPlaylistStats(stats)
    } else {
      setPlaylistStats(null)
    }
  }, [selectedBookmarks, collectionId, mindMapSelection, bookmarks])

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    }
    return `${minutes}m`
  }

  const getButtonSize = () => {
    switch (size) {
      case 'small': return 'px-3 py-1.5 text-sm'
      case 'large': return 'px-6 py-3 text-lg'
      default: return 'px-4 py-2 text-base'
    }
  }

  const getIconSize = () => {
    switch (size) {
      case 'small': return 'w-4 h-4'
      case 'large': return 'w-6 h-6'
      default: return 'w-5 h-5'
    }
  }

  if (!playlistStats || playlistStats.totalItems === 0) {
    return null
  }

  const PlaylistButton = () => (
    <button
      onClick={() => setShowPlaylist(true)}
      className={`
        ${getButtonSize()}
        rounded-lg font-medium transition-all duration-200
        flex items-center space-x-2
        ${isModernTheme
          ? 'text-white border hover:border-opacity-75'
          : 'border hover:border-opacity-75'
        }
         transition-all duration-200 hover:scale-105
        group
      `}
      style={{
        background: isModernTheme 
          ? 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.2), rgba(var(--accent-color-rgb, 0, 123, 255), 0.2))'
          : 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.1), rgba(var(--accent-color-rgb, 0, 123, 255), 0.1))',
        borderColor: 'var(--accent-color, #007bff)',
        color: isModernTheme ? 'white' : 'var(--accent-color, #007bff)'
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.background = isModernTheme 
          ? 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.3), rgba(var(--accent-color-rgb, 0, 123, 255), 0.3))'
          : 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.2), rgba(var(--accent-color-rgb, 0, 123, 255), 0.2))'
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.background = isModernTheme 
          ? 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.2), rgba(var(--accent-color-rgb, 0, 123, 255), 0.2))'
          : 'linear-gradient(to right, rgba(var(--accent-color-rgb, 0, 123, 255), 0.1), rgba(var(--accent-color-rgb, 0, 123, 255), 0.1))'
      }}
    >
      <div className="flex items-center space-x-1">
        <Play className={`${getIconSize()} group-hover:scale-110 transition-transform`} />
        <Video className={`${getIconSize()} opacity-60`} />
        <Headphones className={`${getIconSize()} opacity-60`} />
      </div>
      
      <div className="flex flex-col items-start">
        <span className="font-semibold">
          🎬 Multimedia Playlist
        </span>
        <span className={`text-xs opacity-75 ${
          size === 'small' ? 'hidden' : ''
        }`}>
          {playlistStats.totalItems} items • {formatDuration(playlistStats.estimatedDuration)}
        </span>
      </div>
      
      <Zap className={`${getIconSize()} opacity-60 group-hover:text-yellow-400 transition-colors`} />
    </button>
  )

  const PlaylistStats = () => (
    <div className={`
      ${size === 'small' ? 'text-xs' : 'text-sm'}
      ${isModernTheme ? 'text-gray-300' : 'text-gray-600'}
      flex items-center space-x-3 mt-2
    `}>
      {playlistStats.videoCount > 0 && (
        <div className="flex items-center space-x-1">
          <Video className="w-3 h-3" />
          <span>{playlistStats.videoCount}</span>
        </div>
      )}
      {playlistStats.audioCount > 0 && (
        <div className="flex items-center space-x-1">
          <Headphones className="w-3 h-3" />
          <span>{playlistStats.audioCount}</span>
        </div>
      )}
      {playlistStats.documentCount > 0 && (
        <div className="flex items-center space-x-1">
          <FileText className="w-3 h-3" />
          <span>{playlistStats.documentCount}</span>
        </div>
      )}
    </div>
  )

  const renderContent = () => {
    switch (position) {
      case 'floating':
        return (
          <div className="fixed bottom-6 right-6 z-40">
            <div className={`
              ${isModernTheme ? 'bg-black/80 backdrop-blur-xl' : 'bg-white shadow-lg'}
              rounded-xl p-4 border
              ${isModernTheme ? 'border-white/20' : 'border-gray-200'}
            `}>
              <PlaylistButton />
              {size !== 'small' && <PlaylistStats />}
            </div>
          </div>
        )
      
      case 'sidebar':
        return (
          <div className={`
            ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
            border rounded-lg p-3 mb-4
          `}>
            <div className="flex items-center justify-between mb-2">
              <h4 className={`font-medium ${
                isModernTheme ? 'text-white' : 'text-gray-900'
              }`}>
                Multimedia Playlist
              </h4>
              <button
                onClick={() => setShowPlaylist(true)}
                className={`p-1 rounded transition-colors ${
                  isModernTheme
                    ? 'hover:bg-opacity-20'
                : 'hover:bg-opacity-10'
              }`}
              style={{
                color: 'var(--accent-color, #007bff)',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                 e.currentTarget.style.backgroundColor = isModernTheme 
                   ? 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.2)' 
                   : 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.1)'
               }}
               onMouseLeave={(e) => {
                 e.currentTarget.style.backgroundColor = 'transparent'
               }}
              >
                <Play className="w-4 h-4" />
              </button>
            </div>
            
            <div className={`text-sm mb-3 ${
              isModernTheme ? 'text-gray-300' : 'text-gray-600'
            }`}>
              {playlistStats.totalItems} items ready for playback
            </div>
            
            <PlaylistStats />
            
            <button
              onClick={() => setShowPlaylist(true)}
              className={`
                w-full mt-3 px-3 py-2 rounded text-sm font-medium transition-colors
                ${isModernTheme
                  ? 'hover:bg-opacity-30'
                : 'hover:bg-opacity-20'
                }
              `}
              style={{
                 backgroundColor: isModernTheme 
                   ? 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.2)' 
                   : 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.1)',
                 color: 'var(--accent-color, #007bff)'
               }}
              onMouseEnter={(e) => {
                 e.currentTarget.style.backgroundColor = isModernTheme 
                   ? 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.3)' 
                   : 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.2)'
               }}
               onMouseLeave={(e) => {
                 e.currentTarget.style.backgroundColor = isModernTheme 
                   ? 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.2)' 
                   : 'rgba(var(--accent-color-rgb, 0, 123, 255), 0.1)'
               }}
              `}
            >
              🏃‍♂️ Start Gym Mode
            </button>
          </div>
        )
      
      default: // inline
        return (
          <div className="mb-4">
            <PlaylistButton />
            {size !== 'small' && <PlaylistStats />}
          </div>
        )
    }
  }

  return (
    <>
      {renderContent()}
      
      <MultimediaPlaylistPanel
        isOpen={showPlaylist}
        onClose={() => setShowPlaylist(false)}
        selectedBookmarks={selectedBookmarks}
        collectionId={collectionId}
        mindMapSelection={mindMapSelection}
      />
    </>
  )
}

// Convenience components for specific use cases
export const CollectionPlaylistButton: React.FC<{
  collectionId: string
  collectionName: string
  size?: 'small' | 'medium' | 'large'
}> = ({ collectionId, collectionName, size = 'medium' }) => (
  <MultimediaPlaylistIntegration
    collectionId={collectionId}
    collectionName={collectionName}
    size={size}
    position="inline"
  />
)

export const SelectedBookmarksPlaylistButton: React.FC<{
  selectedBookmarks: Bookmark[]
  size?: 'small' | 'medium' | 'large'
}> = ({ selectedBookmarks, size = 'medium' }) => (
  <MultimediaPlaylistIntegration
    selectedBookmarks={selectedBookmarks}
    size={size}
    position="inline"
  />
)

export const MindMapPlaylistButton: React.FC<{
  mindMapSelection: string[]
  size?: 'small' | 'medium' | 'large'
}> = ({ mindMapSelection, size = 'medium' }) => (
  <MultimediaPlaylistIntegration
    mindMapSelection={mindMapSelection}
    size={size}
    position="inline"
  />
)

export const FloatingPlaylistButton: React.FC<{
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
}> = ({ selectedBookmarks, collectionId, mindMapSelection }) => (
  <MultimediaPlaylistIntegration
    selectedBookmarks={selectedBookmarks}
    collectionId={collectionId}
    mindMapSelection={mindMapSelection}
    position="floating"
    size="medium"
  />
)

export default MultimediaPlaylistIntegration