import { Alert<PERSON><PERSON>gle, Refresh<PERSON>w, X } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface MemoryWarningBannerProps {
  memoryUsage: number;
  onDismiss?: () => void;
}

export const MemoryWarningBanner: React.FC<MemoryWarningBannerProps> = ({ 
  memoryUsage, 
  onDismiss 
}) => {
  const [isVisible, setIsVisible] = useState(true);
  const [cleanupInProgress, setCleanupInProgress] = useState(false);

  useEffect(() => {
    // Auto-show banner when memory usage is high
    if (memoryUsage > 60) {
      setIsVisible(true);
    }
  }, [memoryUsage]);

  const handleCleanup = async () => {
    setCleanupInProgress(true);
    try {
      const { emergencyMemoryCleanup } = await import('../utils/emergencyMemoryCleanup');
      const result = await emergencyMemoryCleanup();
      console.log('🧹 Manual cleanup completed:', result);
      
      // Show success message
      setTimeout(() => {
        setCleanupInProgress(false);
        if (result.newUsagePercentage < 50) {
          setIsVisible(false);
        }
      }, 2000);
    } catch (error) {
      console.error('❌ Manual cleanup failed:', error);
      setCleanupInProgress(false);
    }
  };

  const handleRefresh = () => {
    window.location.reload();
  };

  const handleDismiss = () => {
    setIsVisible(false);
    onDismiss?.();
  };

  if (!isVisible || memoryUsage < 60) {
    return null;
  }

  const isUrgent = memoryUsage > 70;
  const bannerClass = isUrgent 
    ? 'bg-red-100 border-red-400 text-red-800' 
    : 'bg-yellow-100 border-yellow-400 text-yellow-800';

  return (
    <div className={`fixed top-0 left-0 right-0 z-50 p-4 border-b-2 ${bannerClass}`}>
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <AlertTriangle size={24} className="flex-shrink-0" />
          <div>
            <h3 className="font-semibold">
              {isUrgent ? '🚨 Critical Memory Usage' : '⚠️ High Memory Usage'}
            </h3>
            <p className="text-sm">
              Memory usage: {memoryUsage.toFixed(1)}% 
              {isUrgent 
                ? ' - Immediate action required to prevent crashes'
                : ' - Consider optimizing to improve performance'
              }
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleCleanup}
            disabled={cleanupInProgress}
            className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700 disabled:opacity-50 flex items-center space-x-1"
          >
            {cleanupInProgress ? (
              <>
                <RefreshCw size={14} className="animate-spin" />
                <span>Cleaning...</span>
              </>
            ) : (
              <>
                <RefreshCw size={14} />
                <span>Clean Memory</span>
              </>
            )}
          </button>

          <button
            onClick={handleRefresh}
            className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 flex items-center space-x-1"
          >
            <RefreshCw size={14} />
            <span>Refresh Page</span>
          </button>

          <button
            onClick={handleDismiss}
            className="p-1 hover:bg-black hover:bg-opacity-10 rounded"
          >
            <X size={16} />
          </button>
        </div>
      </div>

      {/* Emergency tips */}
      {isUrgent && (
        <div className="mt-2 text-xs">
          <strong>Emergency Tips:</strong> Close other browser tabs, disable favicons, reduce visible bookmarks, or refresh the page.
        </div>
      )}
    </div>
  );
};

export default MemoryWarningBanner;
