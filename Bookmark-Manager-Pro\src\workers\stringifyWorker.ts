// stringifyWorker.ts - Web Worker for optimizing and stringifying bookmark data

// Define interfaces for the data types
interface Bookmark {
  id: string;
  url: string;
  title: string;
  tags: string[];
  collectionId?: string;
  collection?: string;
  [key: string]: any; // Allow for other properties
}

interface Collection {
  id: string;
  name: string;
  [key: string]: any; // Allow for other properties
}

interface Playlist {
  id: string;
  name: string;
  items: string[];
  [key: string]: any; // Allow for other properties
}

// Define the worker context
const ctx: Worker = self as any;

ctx.addEventListener('message', (event) => {
  const { bookmarks, collections, playlists, isLargeDataset } = event.data;

  let optimizedData;
  if (isLargeDataset) {
    optimizedData = {
      bookmarks: bookmarks.map((b: Bookmark) => ({
        id: b.id,
        url: b.url,
        title: b.title,
        tags: b.tags,
        collectionId: b.collectionId
      })),
      collections: collections.map((c: Collection) => ({
        id: c.id,
        name: c.name
      })),
      playlists: playlists.map((p: Playlist) => ({
        id: p.id,
        name: p.name,
        items: p.items
      }))
    };
  } else {
    optimizedData = {
      bookmarks: bookmarks.map((b: Bookmark) => {
        const { tempProp, ...rest } = b; // Remove temporary properties if any
        return rest;
      }),
      collections,
      playlists
    };
  }

  try {
    const stringified = JSON.stringify(optimizedData);
    ctx.postMessage({ success: true, data: stringified });
  } catch (error: unknown) {
    // Properly handle unknown error type
    const errorMessage = error instanceof Error ? error.message : String(error);
    ctx.postMessage({ success: false, error: errorMessage });
  }
});