import { FC, ReactNode, useCallback, useState } from 'react';
import { Bookmark } from '../types';
import BulkEditModal from './BulkEditModal';
import { FolderIcon, PencilIcon, TagIcon, TrashIcon, XMarkIcon } from './icons/HeroIcons';

interface BulkOperationsProps {
  selectedBookmarks: Bookmark[];
  onUpdateBookmarks: (updates: { id: string; updates: Partial<Bookmark> }[]) => void;
  onDeleteBookmarks: (ids: string[]) => void;
  onClearSelection: () => void;
}

const BulkOperations: FC<BulkOperationsProps> = ({
  selectedBookmarks,
  onUpdateBookmarks,
  onDeleteBookmarks,
  onClearSelection
}) => {
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  const handleBulkEdit = useCallback(() => {
    setIsEditModalOpen(true);
  }, []);

  const handleBulkDelete = useCallback(() => {
    setShowDeleteConfirm(true);
  }, []);

  const confirmDelete = useCallback(() => {
    const ids = selectedBookmarks.map(bookmark => bookmark.id);
    onDeleteBookmarks(ids);
    setShowDeleteConfirm(false);
    onClearSelection();
  }, [selectedBookmarks, onDeleteBookmarks, onClearSelection]);

  const handleEditComplete = useCallback((updates: { id: string; updates: Partial<Bookmark> }[]) => {
    onUpdateBookmarks(updates);
    setIsEditModalOpen(false);
    onClearSelection();
  }, [onUpdateBookmarks, onClearSelection]);

  const handleAddTagsToAll = useCallback((tags: string[]) => {
    const updates = selectedBookmarks.map(bookmark => ({
      id: bookmark.id,
      updates: {
        tags: [...(bookmark.tags || []), ...tags.filter(tag => !(bookmark.tags || []).includes(tag))]
      }
    }));
    onUpdateBookmarks(updates);
  }, [selectedBookmarks, onUpdateBookmarks]);



  const handleMoveToFolder = useCallback((folderPath: string[]) => {
    const updates = selectedBookmarks.map(bookmark => ({
      id: bookmark.id,
      updates: {
        path: folderPath
      }
    }));
    onUpdateBookmarks(updates);
  }, [selectedBookmarks, onUpdateBookmarks]);

  if (selectedBookmarks.length === 0) {
    return null;
  }

  return (
    <>
      <div className="bg-slate-800 border border-slate-700 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <span className="text-sm font-medium text-slate-300">
              {selectedBookmarks.length} bookmark{selectedBookmarks.length !== 1 ? 's' : ''} selected
            </span>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={handleBulkEdit}
                className="inline-flex items-center px-3 py-1.5 border border-slate-600 rounded-md text-sm font-medium text-slate-300 bg-slate-700 hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-sky-500 transition-colors"
                title="Edit selected bookmarks"
              >
                <PencilIcon className="w-4 h-4 mr-1" />
                Edit
              </button>
              
              <button
                onClick={handleBulkDelete}
                className="inline-flex items-center px-3 py-1.5 border border-red-600 rounded-md text-sm font-medium text-red-400 bg-red-900/20 hover:bg-red-900/40 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors"
                title="Delete selected bookmarks"
              >
                <TrashIcon className="w-4 h-4 mr-1" />
                Delete
              </button>
            </div>
          </div>
          
          <button
            onClick={onClearSelection}
            className="text-slate-400 hover:text-white transition-colors"
            title="Clear selection"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>
        
        {/* Quick Actions */}
        <div className="mt-3 pt-3 border-t border-slate-700">
          <div className="text-xs text-slate-400 mb-2">Quick Actions:</div>
          <div className="flex flex-wrap gap-2">
            <QuickTagAction
              icon={<TagIcon className="w-3 h-3" />}
              label="Add 'Important'"
              onClick={() => handleAddTagsToAll(['Important'])}
            />
            <QuickTagAction
              icon={<TagIcon className="w-3 h-3" />}
              label="Add 'Read Later'"
              onClick={() => handleAddTagsToAll(['Read Later'])}
            />
            <QuickTagAction
              icon={<FolderIcon className="w-3 h-3" />}
              label="Move to Archive"
              onClick={() => handleMoveToFolder(['Archive'])}
            />
          </div>
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={() => setShowDeleteConfirm(false)}></div>
            
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div className="inline-block align-bottom bg-slate-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
              <div className="sm:flex sm:items-start">
                <div className="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-900/20 sm:mx-0 sm:h-10 sm:w-10">
                  <TrashIcon className="h-6 w-6 text-red-400" aria-hidden={true} />
                </div>
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 className="text-lg leading-6 font-medium text-white" id="modal-title">
                    Delete Bookmarks
                  </h3>
                  <div className="mt-2">
                    <p className="text-sm text-slate-300">
                      Are you sure you want to delete {selectedBookmarks.length} bookmark{selectedBookmarks.length !== 1 ? 's' : ''}? This action cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
              <div className="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm transition-colors"
                  onClick={confirmDelete}
                >
                  Delete
                </button>
                <button
                  type="button"
                  className="mt-3 w-full inline-flex justify-center rounded-md border border-slate-600 shadow-sm px-4 py-2 bg-slate-700 text-base font-medium text-slate-300 hover:bg-slate-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 sm:mt-0 sm:w-auto sm:text-sm transition-colors"
                  onClick={() => setShowDeleteConfirm(false)}
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bulk Edit Modal */}
      <BulkEditModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        selectedBookmarks={selectedBookmarks}
        onSave={handleEditComplete}
      />
    </>
  );
};

const QuickTagAction: FC<{
  icon: ReactNode;
  label: string;
  onClick: () => void;
}> = ({ icon, label, onClick }) => {
  return (
    <button
      onClick={onClick}
      className="inline-flex items-center px-2 py-1 text-xs bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors"
    >
      {icon}
      <span className="ml-1">{label}</span>
    </button>
  );
};

export default BulkOperations;