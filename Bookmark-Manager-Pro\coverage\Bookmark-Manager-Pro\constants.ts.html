
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Bookmark-Manager-Pro/constants.ts</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../prettify.css" />
    <link rel="stylesheet" href="../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../index.html">All files</a> / <a href="index.html">Bookmark-Manager-Pro</a> constants.ts</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/36</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>0/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/36</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a></td><td class="line-coverage quiet"><span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js"><span class="cstat-no" title="statement not covered" >export const GEMINI_MODEL_NAME = 'gemini-2.5-flash-preview-05-20'; // Updated to current available model<span class="fstat-no" title="function not covered" ><span class="branch-0 cbranch-no" title="branch not covered" ></span></span></span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const PROMPT_SUMMARIZE_BOOKMARK = (title: string, url: string): string =&gt; </span>
<span class="cstat-no" title="statement not covered" >  `Analyze the web page with title "${title}" and URL "${url}". Provide a concise, informative 1-2 sentence summary that captures the main content, purpose, and key value proposition. Focus on what makes this resource unique and useful.`;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const PROMPT_TAG_BOOKMARK = (title: string, url: string): string =&gt;</span>
<span class="cstat-no" title="statement not covered" >  `Analyze the web page with title "${title}" and URL "${url}". Generate 3-5 highly relevant, specific tags that accurately categorize this content. Consider: topic, technology, content type, industry, and use case. Respond ONLY with a JSON array of strings, like ["react", "frontend", "tutorial", "javascript", "web-development"]. Ensure tags are lowercase, hyphenated for multi-word terms, and the response is valid JSON.`;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const PROMPT_SMART_SUGGESTIONS = (bookmarks: any[]): string =&gt;</span>
<span class="cstat-no" title="statement not covered" >  `Analyze these ${bookmarks.length} bookmarks and provide intelligent organization suggestions. Consider: duplicate detection, folder organization by topic/domain, tag standardization, broken link identification, and content categorization. Focus on actionable improvements that enhance discoverability and organization.`;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const PROMPT_BOOKMARK_QA = (question: string, bookmarks: any[]): string =&gt;</span>
<span class="cstat-no" title="statement not covered" >  `Based on the user's question: "${question}" and their ${bookmarks.length} bookmarks, provide a helpful, accurate answer. Reference specific bookmarks when relevant and suggest related resources from their collection. Be concise but comprehensive.`;</span>
&nbsp;
// Example: PROMPT_TAG_BOOKMARK for a page about "React Best Practices" at "example.com/react-bp"
// would be:
// `Suggest 3-5 relevant tags for the web page with title "React Best Practices" and URL "example.com/react-bp".
// Respond ONLY with a JSON array of strings, like ["tag1", "tag2", "tag3"]. Ensure the response is valid JSON.`
&nbsp;
// If the API_KEY is missing, this message will be shown.
<span class="cstat-no" title="statement not covered" >export const API_KEY_ERROR_MESSAGE = "GEMINI_API_KEY environment variable not set. Please configure it to use AI features.";</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export const ITEMS_PER_PAGE = 10; // Number of bookmarks to display per page</span>
<span class="cstat-no" title="statement not covered" >export const MAX_TAGS_PER_BOOKMARK = 7; // Maximum number of tags per bookmark</span>
<span class="cstat-no" title="statement not covered" >export const MAX_SUMMARY_LENGTH = 200; // Maximum length for bookmark summaries</span>
<span class="cstat-no" title="statement not covered" >export const DEBOUNCE_DELAY = 300; // Debounce delay for search input (ms)</span>
<span class="cstat-no" title="statement not covered" >export const AUTO_SAVE_DELAY = 1000; // Auto-save delay for form inputs (ms)</span>
<span class="cstat-no" title="statement not covered" >export const BATCH_PROCESSING_SIZE = 5; // Number of bookmarks to process in parallel</span>
<span class="cstat-no" title="statement not covered" >export const CACHE_DURATION = 5 * 60 * 1000; // Cache duration for AI results (5 minutes)</span>
<span class="cstat-no" title="statement not covered" >export const RETRY_ATTEMPTS = 3; // Number of retry attempts for failed operations</span>
<span class="cstat-no" title="statement not covered" >export const TOAST_DURATION = 4000; // Duration for toast notifications (ms)</span>
&nbsp;
// UI Constants
<span class="cstat-no" title="statement not covered" >export const SIDEBAR_WIDTH = 280; // Sidebar width in pixels</span>
<span class="cstat-no" title="statement not covered" >export const HEADER_HEIGHT = 64; // Header height in pixels</span>
<span class="cstat-no" title="statement not covered" >export const CARD_MIN_HEIGHT = 120; // Minimum height for bookmark cards</span>
&nbsp;
// Search and Filter Constants
<span class="cstat-no" title="statement not covered" >export const MIN_SEARCH_LENGTH = 2; // Minimum characters for search</span>
<span class="cstat-no" title="statement not covered" >export const MAX_RECENT_SEARCHES = 10; // Maximum number of recent searches to store</span>
<span class="cstat-no" title="statement not covered" >export const SEARCH_HIGHLIGHT_CLASS = 'search-highlight'; // CSS class for search highlights</span>
&nbsp;
// Performance Constants
<span class="cstat-no" title="statement not covered" >export const VIRTUAL_SCROLL_THRESHOLD = 100; // Enable virtual scrolling after this many items</span>
<span class="cstat-no" title="statement not covered" >export const IMAGE_LAZY_LOAD_THRESHOLD = 200; // Lazy load images when within this distance (px)</span>
&nbsp;
// Feature Flags
<span class="cstat-no" title="statement not covered" >export const FEATURES = {</span>
<span class="cstat-no" title="statement not covered" >  AI_SUGGESTIONS: true,</span>
<span class="cstat-no" title="statement not covered" >  BULK_OPERATIONS: true,</span>
<span class="cstat-no" title="statement not covered" >  ADVANCED_SEARCH: true,</span>
<span class="cstat-no" title="statement not covered" >  EXPORT_IMPORT: true,</span>
<span class="cstat-no" title="statement not covered" >  KEYBOARD_SHORTCUTS: true,</span>
<span class="cstat-no" title="statement not covered" >  DARK_MODE: true,</span>
<span class="cstat-no" title="statement not covered" >  ANALYTICS: false, // Disabled by default for privacy</span>
<span class="cstat-no" title="statement not covered" >} as const;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T06:55:52.394Z
            </div>
        <script src="../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../sorter.js"></script>
        <script src="../block-navigation.js"></script>
    </body>
</html>
    