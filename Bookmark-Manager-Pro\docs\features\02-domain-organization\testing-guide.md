# Domain Organization - Testing Guide

## Overview
This guide provides comprehensive testing procedures for the Domain Organization feature, focusing on validating intelligent domain-based categorization and platform recognition capabilities.

## Pre-Test Setup

### Test Data Preparation
1. **Platform Diversity**: Create bookmark sets covering all major platform categories
2. **Domain Variations**: Include main domains, subdomains, and country-specific domains
3. **Mixed Content**: Combine well-known platforms with obscure or new domains
4. **Subdomain Testing**: Include docs.*, blog.*, api.*, support.* subdomains
5. **Edge Cases**: Include localhost, IP addresses, and unusual domain formats

### Test Domain Categories
- **Development**: github.com, stackoverflow.com, codepen.io, docs.microsoft.com
- **AI/ML**: openai.com, huggingface.co, replicate.com, anthropic.com
- **Design**: figma.com, dribbble.com, behance.net, adobe.com
- **Learning**: coursera.org, udemy.com, khanacademy.org, pluralsight.com
- **News**: techcrunch.com, arstechnica.com, theverge.com, hackernews.ycombinator.com
- **Social**: twitter.com, linkedin.com, reddit.com, discord.com

## Core Functionality Tests

### 1. Basic Domain Recognition
**Test Objective**: Verify accurate recognition and categorization of major platforms

**Test Steps**:
1. Create bookmark collection with 20 bookmarks from well-known platforms
2. Include 3-4 bookmarks from each major category (Development, AI/ML, Design, etc.)
3. Access Domain Organization panel
4. Click "Organize by Domain"
5. Review categorization results

**Expected Results**:
- GitHub bookmarks grouped under "Development" or "Code Repositories"
- OpenAI/Anthropic bookmarks grouped under "AI & Machine Learning"
- Figma/Adobe bookmarks grouped under "Design & Creative"
- News sites grouped under "News & Information"
- 95%+ accuracy for well-known platforms

**Validation Criteria**:
- Correct platform identification
- Logical category assignment
- Consistent naming conventions
- No misclassified major platforms

### 2. Subdomain Intelligence Testing
**Test Objective**: Confirm intelligent handling of subdomains and related services

**Test Steps**:
1. Create bookmarks for:
   - docs.github.com
   - blog.github.com
   - api.github.com
   - github.com
   - gist.github.com
2. Run domain organization
3. Examine grouping behavior

**Expected Results**:
- All GitHub-related bookmarks grouped together
- Subdomains recognized as part of GitHub ecosystem
- Possible subcategorization (docs, blog, main site)
- Unified category naming

### 3. Unknown Domain Handling
**Test Objective**: Verify graceful handling of unrecognized or new domains

**Test Steps**:
1. Include bookmarks from:
   - New/emerging platforms
   - Personal websites
   - Local development sites
   - Unusual TLDs (.io, .dev, .xyz)
2. Run domain organization
3. Check categorization of unknown domains

**Expected Results**:
- Unknown domains placed in "Other" or "Uncategorized" category
- No processing errors or failures
- Consistent handling across unknown domains
- Option to manually categorize unknown domains

### 4. Corporate Family Grouping
**Test Objective**: Validate grouping of related corporate services

**Test Steps**:
1. Create bookmarks for Google ecosystem:
   - google.com
   - gmail.com
   - drive.google.com
   - docs.google.com
   - youtube.com
2. Test Microsoft ecosystem:
   - microsoft.com
   - office.com
   - outlook.com
   - github.com (Microsoft-owned)
3. Run domain organization

**Expected Results**:
- Related services grouped under corporate families
- Recognition of ownership relationships
- Logical subcategorization within corporate groups
- Consistent treatment of acquired platforms

## Advanced Feature Tests

### 5. Granularity Control Testing
**Test Objective**: Verify different levels of categorization detail

**Test Steps**:
1. Test with "Broad Categories" setting
2. Test with "Detailed Subcategories" setting
3. Compare results for same bookmark set

**Expected Results**:
- Broad: Fewer, larger categories (e.g., "Development")
- Detailed: More specific categories (e.g., "Code Repositories", "Documentation", "Q&A")
- Consistent core categorization across granularity levels
- User control over organization detail level

### 6. Custom Domain Rules Testing
**Test Objective**: Validate custom categorization rule functionality

**Test Steps**:
1. Define custom rule: "internal-tool.company.com" → "Internal Tools"
2. Add bookmarks matching custom rule
3. Run domain organization
4. Verify custom rule application

**Expected Results**:
- Custom rules override default categorization
- Consistent application of custom rules
- No conflicts with built-in categorization
- User-defined categories created as needed

### 7. Preserve Existing Structure Testing
**Test Objective**: Confirm existing organization is respected when enabled

**Test Steps**:
1. Create pre-organized bookmark structure
2. Add new unorganized bookmarks
3. Enable "Preserve Existing Structure"
4. Run domain organization

**Expected Results**:
- Existing folders and organization maintained
- Only new/unorganized bookmarks processed
- No disruption to user's manual organization
- Clear indication of what was changed

## Performance Tests

### 8. Large Dataset Processing
**Test Objective**: Verify efficient processing of large bookmark collections

**Test Steps**:
1. Create/import 2000+ bookmark collection
2. Monitor processing time and memory usage
3. Verify UI responsiveness during processing

**Expected Results**:
- Processing completes within 30 seconds
- Memory usage remains stable
- UI remains responsive
- Progress indicators function correctly

### 9. Real-time Categorization
**Test Objective**: Test instant categorization for new bookmarks

**Test Steps**:
1. Add new bookmark via drag-and-drop or manual entry
2. Verify immediate domain recognition
3. Check automatic category assignment

**Expected Results**:
- Instant domain recognition for known platforms
- Immediate category assignment
- No processing delay for single bookmarks
- Consistent with bulk organization results

## Edge Case Tests

### 10. Malformed URL Handling
**Test Objective**: Verify robust handling of problematic URLs

**Test Steps**:
1. Include bookmarks with:
   - Missing protocols (example.com vs https://example.com)
   - Invalid characters in URLs
   - Extremely long URLs
   - URLs with unusual ports
2. Run domain organization

**Expected Results**:
- Graceful handling of malformed URLs
- Best-effort domain extraction
- No processing failures
- Clear indication of problematic URLs

### 11. International Domain Testing
**Test Objective**: Validate handling of international and non-ASCII domains

**Test Steps**:
1. Include bookmarks with:
   - Country-specific TLDs (.co.uk, .com.au, .de)
   - Internationalized domain names (IDN)
   - Non-Latin character domains
2. Run domain organization

**Expected Results**:
- Correct handling of international domains
- Appropriate geographic categorization when relevant
- No encoding or processing issues
- Consistent categorization logic

## Integration Tests

### 12. Multi-Feature Workflow
**Test Objective**: Test domain organization with other features

**Test Steps**:
1. Run domain organization
2. Follow with Smart AI organization
3. Test health checking on organized bookmarks
4. Generate mind map from organized structure

**Expected Results**:
- Features work together without conflicts
- Domain organization enhances other features
- No data corruption or loss
- Improved performance in subsequent operations

### 13. Export/Import Consistency
**Test Objective**: Verify organized structure survives export/import cycle

**Test Steps**:
1. Organize bookmarks by domain
2. Export organized collection
3. Clear application data
4. Import previously exported collection
5. Verify organization preservation

**Expected Results**:
- Domain-based organization preserved in export
- Successful import of organized structure
- No loss of categorization information
- Consistent folder structure

## User Experience Tests

### 14. Category Naming Validation
**Test Objective**: Confirm category names are clear and professional

**Test Steps**:
1. Review all generated category names
2. Verify naming consistency
3. Check for user-friendly terminology

**Expected Results**:
- Clear, descriptive category names
- Professional terminology
- Consistent naming conventions
- No technical jargon in user-facing names

### 15. Results Communication
**Test Objective**: Validate clear communication of organization results

**Test Steps**:
1. Complete domain organization
2. Review results summary
3. Verify user understanding of changes

**Expected Results**:
- Clear summary of categories created
- Number of bookmarks organized per category
- Easy-to-understand results presentation
- Option to review changes before applying

## Regression Testing

### 16. Consistency Validation
**Test Objective**: Ensure consistent categorization across multiple runs

**Test Steps**:
1. Run domain organization multiple times on same dataset
2. Compare results across runs
3. Verify stability of categorization

**Expected Results**:
- 100% consistency for known domains
- Stable category assignment
- Predictable organization behavior
- No random variations in results

## Performance Benchmarks

### Target Metrics
- **Processing Speed**: 100+ bookmarks per second for known domains
- **Memory Usage**: <100MB overhead for domain classification
- **Accuracy**: 98%+ for major platforms, 85%+ for general domains
- **Response Time**: <1 second for single bookmark categorization
- **Scalability**: Handle 10,000+ bookmarks without performance degradation
