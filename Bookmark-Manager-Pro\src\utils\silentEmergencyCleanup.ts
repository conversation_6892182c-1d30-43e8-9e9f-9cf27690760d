/**
 * SILENT EMERGENCY CLEANUP SERVICE
 * Automatically runs comprehensive cleanup when memory reaches critical levels
 * No user intervention required - runs discreetly in background
 */

class SilentEmergencyCleanupService {
  private isRunning = false
  private lastCleanup = 0
  private readonly COOLDOWN_PERIOD = 60000 // 1 minute
  
  async runSilentEmergencyCleanup(): Promise<boolean> {
    if (this.isRunning) {
      console.log('🛡️ Silent cleanup already running, skipping...')
      return false
    }
    
    const now = Date.now()
    if (now - this.lastCleanup < this.COOLDOWN_PERIOD) {
      console.log('🛡️ Silent cleanup in cooldown period, skipping...')
      return false
    }
    
    this.isRunning = true
    this.lastCleanup = now
    
    try {
      // Only log in development mode
      if (process.env.NODE_ENV === 'development') {
        console.log('🛡️ Automatic memory optimization starting...')
      }

      // Get initial memory usage
      const initialMemory = this.getMemoryUsage()

      // Run all cleanup phases silently
      await this.runCleanupPhases()

      // Get final memory usage
      const finalMemory = this.getMemoryUsage()

      // Only log results in development mode
      if (process.env.NODE_ENV === 'development') {
        if (initialMemory && finalMemory) {
          const reduction = initialMemory - finalMemory
          console.log(`✅ Memory optimization complete: ${initialMemory.toFixed(1)}% → ${finalMemory.toFixed(1)}% (${reduction.toFixed(1)}% freed)`)
        } else {
          console.log('✅ Memory optimization complete')
        }
      }

      return true

    } catch (error) {
      // Only log errors in development mode
      if (process.env.NODE_ENV === 'development') {
        console.error('❌ Memory optimization error:', error)
      }
      return false
    } finally {
      this.isRunning = false
    }
  }
  
  private async runCleanupPhases(): Promise<void> {
    // Phase 1: Stop all activity (silent)
    this.stopAllActivity()
    
    // Phase 2: Clear caches (silent)
    this.clearAllCaches()
    
    // Phase 3: Clear storage (silent)
    this.clearStorage()
    
    // Phase 4: Clear performance data (silent)
    this.clearPerformanceData()
    
    // Phase 5: Clear development tools (silent)
    this.clearDevelopmentTools()
    
    // Phase 6: Remove DOM elements (silent)
    this.removeLargeDOMElements()
    
    // Phase 7: Garbage collection (silent)
    this.performGarbageCollection()
    
    // Phase 8: Clear globals (silent)
    this.clearGlobalVariables()
    
    // Small delay to allow cleanup to complete
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  private stopAllActivity(): void {
    try {
      // Stop all intervals and timeouts
      for (let i = 1; i < 99999; i++) {
        try {
          clearInterval(i)
          clearTimeout(i)
        } catch (e) {
          // Silent ignore
        }
      }
      
      // Stop animation frames
      let rafId = 0
      while (rafId < 10000) {
        try {
          cancelAnimationFrame(rafId)
          rafId++
        } catch (e) {
          break
        }
      }
      
      // Stop interval manager
      if ((window as any).intervalManager) {
        (window as any).intervalManager.emergencyStop()
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private clearAllCaches(): void {
    try {
      // Clear recommendation cache
      if ((window as any).recommendationCache) {
        (window as any).recommendationCache.emergencyCleanup()
      }
      
      // Clear content analyzer cache
      if ((window as any).contentAnalyzer) {
        (window as any).contentAnalyzer.clearCache?.()
      }
      
      // Clear any other caches
      if ((window as any).playlistDemo) {
        delete (window as any).playlistDemo
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private clearStorage(): void {
    try {
      localStorage.clear()
      sessionStorage.clear()
      
      // Clear IndexedDB
      if ('indexedDB' in window) {
        (indexedDB as any).databases?.().then((databases: any[]) => {
          databases.forEach((db: any) => {
            if (db.name) {
              indexedDB.deleteDatabase(db.name)
            }
          })
        }).catch(() => {
          // Silent ignore
        })
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private clearPerformanceData(): void {
    try {
      if (window.performance) {
        if (window.performance.clearMeasures) window.performance.clearMeasures()
        if (window.performance.clearMarks) window.performance.clearMarks()
        if (window.performance.clearResourceTimings) window.performance.clearResourceTimings()
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private clearDevelopmentTools(): void {
    try {
      // Clear React DevTools
      if ((window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__) {
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null;
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null;
        (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberMount = null
      }
      
      // Clear Vite HMR
      if ((window as any).__vite_plugin_react_preamble_installed__) {
        delete (window as any).__vite_plugin_react_preamble_installed__
      }
      if ((window as any).__vite_is_modern_browser) {
        delete (window as any).__vite_is_modern_browser
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private removeLargeDOMElements(): void {
    try {
      // Remove streaming elements
      document.querySelectorAll('[data-streaming="true"], .streaming, .recommendation-card').forEach(el => el.remove())
      
      // Remove memory monitors
      document.querySelectorAll('.memory-monitor, .memory-stats').forEach(el => el.remove())
      
      // Remove large images
      document.querySelectorAll('img[src*="data:"], img[src*="blob:"]').forEach(el => el.remove())
      
      // Remove canvas elements
      document.querySelectorAll('canvas').forEach(el => el.remove())
    } catch (e) {
      // Silent ignore
    }
  }
  
  private performGarbageCollection(): void {
    try {
      // Multiple garbage collection cycles
      for (let i = 0; i < 15; i++) {
        if (typeof (window as any).gc === 'function') {
          (window as any).gc()
        }
        
        // Force memory release patterns
        const temp = new Array(5000000).fill(null)
        temp.length = 0
        
        // Create and destroy objects to trigger GC
        const obj: any = {}
        for (let j = 0; j < 100000; j++) {
          obj[`key${j}`] = null
        }
        Object.keys(obj).forEach(key => delete obj[key])
      }
    } catch (e) {
      // Silent ignore
    }
  }
  
  private clearGlobalVariables(): void {
    try {
      // Clear common global variables that might hold references
      const globalsToClean = [
        'playlistDemo', 'bookmarkData', 'cachedBookmarks', 'searchResults',
        'filteredBookmarks', 'recommendationData', 'summaryCache', 'imageCache',
        'largeDataStructures', 'temporaryData', 'debugData'
      ]
      
      globalsToClean.forEach(global => {
        if ((window as any)[global]) {
          delete (window as any)[global]
        }
      })
    } catch (e) {
      // Silent ignore
    }
  }
  
  private getMemoryUsage(): number | null {
    try {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        return (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100
      }
    } catch (e) {
      // Silent ignore
    }
    return null
  }
  
  // Public method to check if cleanup is needed
  shouldRunCleanup(): boolean {
    const usage = this.getMemoryUsage()
    return usage !== null && usage >= 82 && !this.isRunning
  }
  
  // Public method to get cleanup status
  getStatus(): {
    isRunning: boolean
    lastCleanup: number
    canRunCleanup: boolean
    currentMemoryUsage: number | null
  } {
    return {
      isRunning: this.isRunning,
      lastCleanup: this.lastCleanup,
      canRunCleanup: this.shouldRunCleanup(),
      currentMemoryUsage: this.getMemoryUsage()
    }
  }
}

// Global instance
export const silentEmergencyCleanup = new SilentEmergencyCleanupService()

// Auto-setup for critical memory situations
if (typeof window !== 'undefined') {
  // Make available globally for debugging
  ;(window as any).silentEmergencyCleanup = silentEmergencyCleanup
  
  // Monitor for critical memory usage every 15 seconds
  setInterval(() => {
    if (silentEmergencyCleanup.shouldRunCleanup()) {
      silentEmergencyCleanup.runSilentEmergencyCleanup()
    }
  }, 15000)
}
