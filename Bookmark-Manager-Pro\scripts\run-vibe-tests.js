#!/usr/bin/env node

/**
 * Dr. Elena's Vibe Testing Runner
 * Convenient script to run vibe tests with proper setup and reporting
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 <PERSON><PERSON>\'s Vibe Testing Suite');
console.log('═'.repeat(50));

// Parse command line arguments
const args = process.argv.slice(2);
const testType = args[0] || 'all';
const options = args.slice(1);

// Available test types
const testTypes = {
  all: 'Run all vibe tests',
  star: 'Star interaction confidence tests',
  bulk: 'Bulk operation anxiety prevention tests',
  suggestions: 'AI suggestion timing intelligence tests',
  performance: 'Performance and response time tests',
  emotional: 'Emotional journey analysis tests'
};

// Show help if requested
if (testType === 'help' || testType === '--help' || testType === '-h') {
  console.log('\nAvailable test types:');
  Object.entries(testTypes).forEach(([key, description]) => {
    console.log(`  ${key.padEnd(12)} - ${description}`);
  });
  console.log('\nOptions:');
  console.log('  --headed     - Run tests in headed mode (visible browser)');
  console.log('  --debug      - Run tests in debug mode');
  console.log('  --ui         - Run tests in UI mode');
  console.log('  --report     - Open report after tests complete');
  console.log('\nExamples:');
  console.log('  npm run vibe-test star --headed');
  console.log('  npm run vibe-test all --report');
  console.log('  npm run vibe-test bulk --debug');
  process.exit(0);
}

// Validate test type
if (!testTypes[testType]) {
  console.error(`❌ Unknown test type: ${testType}`);
  console.log('Available types:', Object.keys(testTypes).join(', '));
  process.exit(1);
}

// Build command
let command = 'npx playwright test';

// Add test-specific filters
switch (testType) {
  case 'star':
    command += ' tests/vibe/core/star-interaction-confidence.spec.js';
    break;
  case 'bulk':
    command += ' tests/vibe/core/bulk-operation-anxiety.spec.js';
    break;
  case 'suggestions':
    command += ' tests/vibe/core/suggestion-timing-intelligence.spec.js';
    break;
  case 'performance':
    command += ' tests/vibe --grep "performance|response"';
    break;
  case 'emotional':
    command += ' tests/vibe --grep "emotional|journey"';
    break;
  case 'all':
    command += ' tests/vibe';
    break;
}

// Add project specification for vibe tests
command += ' --project=vibe-desktop';

// Add options
if (options.includes('--headed')) {
  command += ' --headed';
}
if (options.includes('--debug')) {
  command += ' --debug';
}
if (options.includes('--ui')) {
  command += ' --ui';
}

// Ensure test results directory exists
const testResultsDir = path.join(process.cwd(), 'test-results');
if (!fs.existsSync(testResultsDir)) {
  fs.mkdirSync(testResultsDir, { recursive: true });
}

console.log(`\n🎯 Running ${testTypes[testType]}`);
console.log(`📝 Command: ${command}\n`);

try {
  // Run the tests
  execSync(command, { 
    stdio: 'inherit',
    env: { 
      ...process.env,
      VIBE_TEST_MODE: 'true',
      NODE_ENV: 'test'
    }
  });
  
  console.log('\n✅ Vibe tests completed successfully!');
  
  // Show report options
  console.log('\n📊 View Results:');
  console.log('  HTML Report:      npx playwright show-report');
  console.log('  Vibe Metrics:     open test-results/vibe-metrics-report.json');
  console.log('  Emotional Journey: open test-results/emotional-journey-report.html');
  
  // Auto-open report if requested
  if (options.includes('--report')) {
    console.log('\n🚀 Opening test report...');
    execSync('npx playwright show-report', { stdio: 'inherit' });
  }
  
} catch (error) {
  console.error('\n❌ Vibe tests failed!');
  console.error('Check the output above for details.');
  
  // Still show report options for debugging
  console.log('\n🔍 Debug with:');
  console.log('  npx playwright show-report');
  console.log('  Check test-results/ directory for detailed logs');
  
  process.exit(1);
}

// Show vibe score if available
const vibeReportPath = path.join(testResultsDir, 'vibe-metrics-report.json');
if (fs.existsSync(vibeReportPath)) {
  try {
    const vibeReport = JSON.parse(fs.readFileSync(vibeReportPath, 'utf8'));
    if (vibeReport.vibeScore) {
      console.log('\n🎭 VIBE SCORE SUMMARY');
      console.log('─'.repeat(30));
      console.log(`Overall Score: ${vibeReport.vibeScore.score}/100 (${vibeReport.vibeScore.grade})`);
      console.log(`Quality: ${vibeReport.vibeScore.interpretation}`);
      
      if (vibeReport.recommendations && vibeReport.recommendations.length > 0) {
        console.log(`\n💡 Top Recommendations:`);
        vibeReport.recommendations.slice(0, 3).forEach((rec, index) => {
          console.log(`  ${index + 1}. ${rec.priority.toUpperCase()}: ${rec.issue}`);
        });
      }
    }
  } catch (error) {
    // Ignore JSON parsing errors
  }
}