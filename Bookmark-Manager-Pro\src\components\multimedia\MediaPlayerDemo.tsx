import React, { useState } from 'react';
import MediaPlayer from './MediaPlayer';
import { Bookmark } from '../../types';

// Demo data for testing the MediaPlayer
const demoPlaylist: Bookmark[] = [
  {
    id: '1',
    title: 'Sample Audio Track',
    url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
    description: 'A sample audio file for testing',
    tags: ['audio', 'demo'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    category: 'Media',
    isPrivate: false
  },
  {
    id: '2',
    title: 'Sample Video Content',
    url: 'https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4',
    description: 'A sample video file for testing',
    tags: ['video', 'demo'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    category: 'Media',
    isPrivate: false
  },
  {
    id: '3',
    title: 'Another Audio Sample',
    url: 'https://www.soundjay.com/misc/sounds/fail-buzzer-02.wav',
    description: 'Another audio sample for playlist testing',
    tags: ['audio', 'demo', 'sound'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    category: 'Media',
    isPrivate: false
  },
  {
    id: '4',
    title: 'Non-Media Bookmark',
    url: 'https://example.com',
    description: 'This should be filtered out from media playlist',
    tags: ['website'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    category: 'General',
    isPrivate: false
  }
];

interface MediaPlayerDemoProps {
  className?: string;
}

export const MediaPlayerDemo: React.FC<MediaPlayerDemoProps> = ({ className = '' }) => {
  const [playlist, setPlaylist] = useState<Bookmark[]>(demoPlaylist);
  const [autoPlay, setAutoPlay] = useState(false);
  const [loop, setLoop] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 9)]);
  };

  const handlePlaylistEnd = () => {
    addLog('Playlist ended');
  };

  const handleItemChange = (item: Bookmark, index: number) => {
    addLog(`Now playing: ${item.title} (${index + 1}/${playlist.filter(p => 
      p.url.match(/\.(mp3|wav|ogg|aac|flac|m4a|mp4|webm|ogv|avi|mov|mkv)$/i)
    ).length})`);
  };

  const handleError = (error: string, item: Bookmark) => {
    addLog(`Error playing "${item.title}": ${error}`);
  };

  const addCustomMedia = () => {
    const url = prompt('Enter media URL (audio or video):');
    const title = prompt('Enter title:');
    
    if (url && title) {
      const newBookmark: Bookmark = {
        id: Date.now().toString(),
        title,
        url,
        description: 'Custom added media',
        tags: ['custom'],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        category: 'Media',
        isPrivate: false
      };
      
      setPlaylist(prev => [...prev, newBookmark]);
      addLog(`Added custom media: ${title}`);
    }
  };

  const clearPlaylist = () => {
    setPlaylist([]);
    addLog('Playlist cleared');
  };

  const resetPlaylist = () => {
    setPlaylist(demoPlaylist);
    addLog('Playlist reset to demo data');
  };

  return (
    <div className={`multimedia-media-player-demo ${className}`}>
      {/* Demo Controls */}
      <div className="multimedia-section">
        <div className="multimedia-section__header">
          <h3 className="multimedia-section__title">Media Player Demo</h3>
          <p className="multimedia-section__description">
            Test the advanced media player with sample audio and video content.
          </p>
        </div>
        
        <div className="multimedia-demo-controls">
          <div className="multimedia-form-group">
            <label className="multimedia-form-label">
              <input
                type="checkbox"
                checked={autoPlay}
                onChange={(e) => setAutoPlay(e.target.checked)}
                className="multimedia-checkbox"
              />
              Auto Play
            </label>
          </div>
          
          <div className="multimedia-form-group">
            <label className="multimedia-form-label">
              <input
                type="checkbox"
                checked={loop}
                onChange={(e) => setLoop(e.target.checked)}
                className="multimedia-checkbox"
              />
              Loop Playlist
            </label>
          </div>
          
          <div className="multimedia-demo-actions">
            <button 
              onClick={addCustomMedia}
              className="multimedia-btn multimedia-btn--secondary multimedia-btn--sm"
            >
              Add Custom Media
            </button>
            
            <button 
              onClick={clearPlaylist}
              className="multimedia-btn multimedia-btn--ghost multimedia-btn--sm"
            >
              Clear Playlist
            </button>
            
            <button 
              onClick={resetPlaylist}
              className="multimedia-btn multimedia-btn--ghost multimedia-btn--sm"
            >
              Reset Demo
            </button>
          </div>
        </div>
      </div>

      {/* Media Player */}
      <div className="multimedia-section">
        <MediaPlayer
          playlist={playlist}
          autoPlay={autoPlay}
          loop={loop}
          onPlaylistEnd={handlePlaylistEnd}
          onItemChange={handleItemChange}
          onError={handleError}
          className="multimedia-demo-player"
        />
      </div>

      {/* Event Log */}
      <div className="multimedia-section">
        <div className="multimedia-section__header">
          <h4 className="multimedia-section__title">Event Log</h4>
          <button 
            onClick={() => setLogs([])}
            className="multimedia-btn multimedia-btn--ghost multimedia-btn--sm"
          >
            Clear Log
          </button>
        </div>
        
        <div className="multimedia-demo-log">
          {logs.length === 0 ? (
            <p className="multimedia-demo-log__empty">No events yet. Try playing some media!</p>
          ) : (
            <ul className="multimedia-demo-log__list">
              {logs.map((log, index) => (
                <li key={index} className="multimedia-demo-log__item">
                  {log}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      {/* Playlist Info */}
      <div className="multimedia-section">
        <div className="multimedia-section__header">
          <h4 className="multimedia-section__title">Playlist Information</h4>
        </div>
        
        <div className="multimedia-demo-info">
          <div className="multimedia-demo-stat">
            <span className="multimedia-demo-stat__label">Total Bookmarks:</span>
            <span className="multimedia-demo-stat__value">{playlist.length}</span>
          </div>
          
          <div className="multimedia-demo-stat">
            <span className="multimedia-demo-stat__label">Media Items:</span>
            <span className="multimedia-demo-stat__value">
              {playlist.filter(p => 
                p.url.match(/\.(mp3|wav|ogg|aac|flac|m4a|mp4|webm|ogv|avi|mov|mkv)$/i)
              ).length}
            </span>
          </div>
          
          <div className="multimedia-demo-stat">
            <span className="multimedia-demo-stat__label">Audio Files:</span>
            <span className="multimedia-demo-stat__value">
              {playlist.filter(p => 
                p.url.match(/\.(mp3|wav|ogg|aac|flac|m4a)$/i)
              ).length}
            </span>
          </div>
          
          <div className="multimedia-demo-stat">
            <span className="multimedia-demo-stat__label">Video Files:</span>
            <span className="multimedia-demo-stat__value">
              {playlist.filter(p => 
                p.url.match(/\.(mp4|webm|ogv|avi|mov|mkv)$/i)
              ).length}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaPlayerDemo;

// CSS for demo styling (to be added to multimedia-design-system.css)
/*
.multimedia-media-player-demo {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--multimedia-spacing-lg);
  display: flex;
  flex-direction: column;
  gap: var(--multimedia-spacing-lg);
}

.multimedia-demo-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--multimedia-spacing-md);
  align-items: center;
}

.multimedia-demo-actions {
  display: flex;
  gap: var(--multimedia-spacing-sm);
  flex-wrap: wrap;
}

.multimedia-demo-player {
  border: 2px solid var(--multimedia-primary);
}

.multimedia-demo-log {
  background: var(--multimedia-muted);
  border-radius: var(--multimedia-radius-md);
  padding: var(--multimedia-spacing-md);
  max-height: 200px;
  overflow-y: auto;
}

.multimedia-demo-log__empty {
  color: var(--multimedia-text-secondary);
  font-style: italic;
  margin: 0;
}

.multimedia-demo-log__list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.multimedia-demo-log__item {
  font-family: var(--multimedia-font-mono);
  font-size: var(--multimedia-text-sm);
  padding: var(--multimedia-spacing-xs) 0;
  border-bottom: 1px solid var(--multimedia-border);
}

.multimedia-demo-log__item:last-child {
  border-bottom: none;
}

.multimedia-demo-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--multimedia-spacing-md);
}

.multimedia-demo-stat {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--multimedia-spacing-sm);
  background: var(--multimedia-surface);
  border: 1px solid var(--multimedia-border);
  border-radius: var(--multimedia-radius-md);
}

.multimedia-demo-stat__label {
  font-weight: var(--multimedia-font-medium);
  color: var(--multimedia-text-secondary);
}

.multimedia-demo-stat__value {
  font-weight: var(--multimedia-font-semibold);
  color: var(--multimedia-primary);
}
*/