
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Bookmark-Manager-Pro/components/PaginationControls.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> / <a href="index.html">Bookmark-Manager-Pro/components</a> PaginationControls.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/75</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/75</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a>
<a name='L76'></a><a href='#L76'>76</a>
<a name='L77'></a><a href='#L77'>77</a>
<a name='L78'></a><a href='#L78'>78</a>
<a name='L79'></a><a href='#L79'>79</a>
<a name='L80'></a><a href='#L80'>80</a>
<a name='L81'></a><a href='#L81'>81</a>
<a name='L82'></a><a href='#L82'>82</a>
<a name='L83'></a><a href='#L83'>83</a>
<a name='L84'></a><a href='#L84'>84</a>
<a name='L85'></a><a href='#L85'>85</a>
<a name='L86'></a><a href='#L86'>86</a>
<a name='L87'></a><a href='#L87'>87</a>
<a name='L88'></a><a href='#L88'>88</a>
<a name='L89'></a><a href='#L89'>89</a>
<a name='L90'></a><a href='#L90'>90</a>
<a name='L91'></a><a href='#L91'>91</a>
<a name='L92'></a><a href='#L92'>92</a>
<a name='L93'></a><a href='#L93'>93</a>
<a name='L94'></a><a href='#L94'>94</a>
<a name='L95'></a><a href='#L95'>95</a>
<a name='L96'></a><a href='#L96'>96</a>
<a name='L97'></a><a href='#L97'>97</a>
<a name='L98'></a><a href='#L98'>98</a>
<a name='L99'></a><a href='#L99'>99</a>
<a name='L100'></a><a href='#L100'>100</a>
<a name='L101'></a><a href='#L101'>101</a>
<a name='L102'></a><a href='#L102'>102</a>
<a name='L103'></a><a href='#L103'>103</a>
<a name='L104'></a><a href='#L104'>104</a>
<a name='L105'></a><a href='#L105'>105</a>
<a name='L106'></a><a href='#L106'>106</a>
<a name='L107'></a><a href='#L107'>107</a>
<a name='L108'></a><a href='#L108'>108</a>
<a name='L109'></a><a href='#L109'>109</a>
<a name='L110'></a><a href='#L110'>110</a>
<a name='L111'></a><a href='#L111'>111</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">&nbsp;
import React from 'react';
<span class="cstat-no" title="statement not covered" >import { ChevronLeftIcon, ChevronRightIcon } from './icons/HeroIcons'; // Assuming these exist or can be added</span>
&nbsp;
interface PaginationControlsProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) =&gt; void;
}
&nbsp;
<span class="cstat-no" title="statement not covered" >const PageButton: React.FC&lt;{</span>
  page?: number;
  onClick: () =&gt; void;
  isActive?: boolean;
  isDisabled?: boolean;
  children: React.ReactNode;
  ariaLabel?: string;
<span class="cstat-no" title="statement not covered" >}&gt; = ({ page, onClick, isActive, isDisabled, children, ariaLabel }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >  &lt;button</span>
<span class="cstat-no" title="statement not covered" >    onClick={onClick}</span>
<span class="cstat-no" title="statement not covered" >    disabled={isDisabled}</span>
<span class="cstat-no" title="statement not covered" >    aria-label={ariaLabel || (page ? `Go to page ${page}` : undefined)}</span>
<span class="cstat-no" title="statement not covered" >    aria-current={isActive ? 'page' : undefined}</span>
<span class="cstat-no" title="statement not covered" >    className={`min-w-[36px] px-3 py-1.5 text-sm font-medium rounded-md transition-colors</span>
<span class="cstat-no" title="statement not covered" >      ${isDisabled ? 'text-slate-600 cursor-not-allowed' : 'text-slate-300 hover:bg-sky-700'}</span>
<span class="cstat-no" title="statement not covered" >      ${isActive ? 'bg-sky-600 text-white ring-1 ring-sky-500' : 'bg-slate-700'}</span>
    `}
  &gt;
<span class="cstat-no" title="statement not covered" >    {children}</span>
<span class="cstat-no" title="statement not covered" >  &lt;/button&gt;</span>
);
&nbsp;
<span class="cstat-no" title="statement not covered" >const PaginationControls: React.FC&lt;PaginationControlsProps&gt; = ({ currentPage, totalPages, onPageChange }) =&gt; {</span>
<span class="cstat-no" title="statement not covered" >  const pageNumbers = [];</span>
<span class="cstat-no" title="statement not covered" >  const maxPagesToShow = 5; // Max number of page buttons to show (excluding prev/next, first/last)</span>
<span class="cstat-no" title="statement not covered" >  const halfPagesToShow = Math.floor(maxPagesToShow / 2);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  let startPage = Math.max(1, currentPage - halfPagesToShow);</span>
<span class="cstat-no" title="statement not covered" >  let endPage = Math.min(totalPages, currentPage + halfPagesToShow);</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  if (currentPage - halfPagesToShow &lt; 1) {</span>
<span class="cstat-no" title="statement not covered" >    endPage = Math.min(totalPages, maxPagesToShow);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
<span class="cstat-no" title="statement not covered" >  if (currentPage + halfPagesToShow &gt; totalPages) {</span>
<span class="cstat-no" title="statement not covered" >    startPage = Math.max(1, totalPages - maxPagesToShow + 1);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
  
<span class="cstat-no" title="statement not covered" >  if (totalPages &lt;= maxPagesToShow) {</span>
<span class="cstat-no" title="statement not covered" >    startPage = 1;</span>
<span class="cstat-no" title="statement not covered" >    endPage = totalPages;</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  for (let i = startPage; i &lt;= endPage; i++) {</span>
<span class="cstat-no" title="statement not covered" >    pageNumbers.push(i);</span>
<span class="cstat-no" title="statement not covered" >  }</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;nav aria-label="Pagination" className="flex items-center justify-between mt-8 pt-4 border-t border-slate-700"&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="text-sm text-slate-400"&gt;</span>
<span class="cstat-no" title="statement not covered" >        Page &lt;span className="font-semibold text-slate-200"&gt;{currentPage}&lt;/span&gt; of &lt;span className="font-semibold text-slate-200"&gt;{totalPages}&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;div className="flex items-center space-x-2"&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;PageButton</span>
<span class="cstat-no" title="statement not covered" >          onClick={() =&gt; onPageChange(currentPage - 1)}</span>
<span class="cstat-no" title="statement not covered" >          isDisabled={currentPage === 1}</span>
<span class="cstat-no" title="statement not covered" >          ariaLabel="Previous page"</span>
        &gt;
<span class="cstat-no" title="statement not covered" >          &lt;ChevronLeftIcon className="w-5 h-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;span className="sr-only"&gt;Previous&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/PageButton&gt;</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        {startPage &gt; 1 &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >            &lt;PageButton page={1} onClick={() =&gt; onPageChange(1)}&gt;1&lt;/PageButton&gt;</span>
<span class="cstat-no" title="statement not covered" >            {startPage &gt; 2 &amp;&amp; &lt;span className="text-slate-500 px-1"&gt;...&lt;/span&gt;}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/&gt;</span>
        )}
&nbsp;
<span class="cstat-no" title="statement not covered" >        {pageNumbers.map(number =&gt; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;PageButton</span>
<span class="cstat-no" title="statement not covered" >            key={number}</span>
<span class="cstat-no" title="statement not covered" >            page={number}</span>
<span class="cstat-no" title="statement not covered" >            onClick={() =&gt; onPageChange(number)}</span>
<span class="cstat-no" title="statement not covered" >            isActive={currentPage === number}</span>
          &gt;
<span class="cstat-no" title="statement not covered" >            {number}</span>
<span class="cstat-no" title="statement not covered" >          &lt;/PageButton&gt;</span>
<span class="cstat-no" title="statement not covered" >        ))}</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >        {endPage &lt; totalPages &amp;&amp; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;&gt;</span>
<span class="cstat-no" title="statement not covered" >            {endPage &lt; totalPages - 1 &amp;&amp; &lt;span className="text-slate-500 px-1"&gt;...&lt;/span&gt;}</span>
<span class="cstat-no" title="statement not covered" >            &lt;PageButton page={totalPages} onClick={() =&gt; onPageChange(totalPages)}&gt;{totalPages}&lt;/PageButton&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;/&gt;</span>
        )}
&nbsp;
<span class="cstat-no" title="statement not covered" >        &lt;PageButton</span>
<span class="cstat-no" title="statement not covered" >          onClick={() =&gt; onPageChange(currentPage + 1)}</span>
<span class="cstat-no" title="statement not covered" >          isDisabled={currentPage === totalPages}</span>
<span class="cstat-no" title="statement not covered" >          ariaLabel="Next page"</span>
        &gt;
<span class="cstat-no" title="statement not covered" >          &lt;span className="sr-only"&gt;Next&lt;/span&gt;</span>
<span class="cstat-no" title="statement not covered" >          &lt;ChevronRightIcon className="w-5 h-5" /&gt;</span>
<span class="cstat-no" title="statement not covered" >        &lt;/PageButton&gt;</span>
<span class="cstat-no" title="statement not covered" >      &lt;/div&gt;</span>
<span class="cstat-no" title="statement not covered" >    &lt;/nav&gt;</span>
  );
<span class="cstat-no" title="statement not covered" >};</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >export default PaginationControls;</span>
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T06:55:52.394Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    