import React, { useState, useEffect } from 'react';
import { userTierService } from '../services/userTierService';
import { UserTier } from '../types';
import { 
  UserIcon, 
  SparklesIcon, 
  ArrowRightOnRectangleIcon,
  CheckCircleIcon
} from './icons/HeroIcons';

interface UserProfileProps {
  onLoginClick: () => void;
}

const UserProfile: React.FC<UserProfileProps> = ({ onLoginClick }) => {
  const [userTier, setUserTier] = useState<UserTier>(userTierService.getCurrentTier());
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  useEffect(() => {
    // Subscribe to tier changes
    const unsubscribe = userTierService.onTierChange((newTier) => {
      setUserTier(newTier);
    });

    return unsubscribe;
  }, []);

  const handleLogout = () => {
    userTierService.logout();
    setIsDropdownOpen(false);
  };

  const isAuthenticated = userTier.type === 'authenticated';
  const userInfo = userTierService.getUserInfo();
  const processingStats = userTierService.getProcessingConfig();

  const featureList = [
    { key: 'basicSummarization', label: 'Basic Summarization', icon: CheckCircleIcon },
    { key: 'basicTagging', label: 'Basic Tagging', icon: CheckCircleIcon },
    { key: 'aiSummarization', label: 'AI Summarization', icon: SparklesIcon },
    { key: 'aiTagging', label: 'AI Auto-Tagging', icon: SparklesIcon },
    { key: 'advancedSearch', label: 'Advanced Search', icon: SparklesIcon },
    { key: 'cloudSync', label: 'Cloud Sync', icon: SparklesIcon }
  ] as const;

  return (
    <div className="relative">
      {/* User Button */}
      <button
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-slate-700 hover:bg-slate-600 transition-colors border border-slate-600"
      >
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
          isAuthenticated ? 'bg-sky-600' : 'bg-slate-600'
        }`}>
          <UserIcon className="w-5 h-5 text-white" />
        </div>
        <div className="text-left hidden sm:block">
          <div className="text-sm font-medium text-white">
            {isAuthenticated ? 'Premium User' : 'Anonymous'}
          </div>
          <div className="text-xs text-slate-400">
            {isAuthenticated ? userInfo.email : 'Basic features'}
          </div>
        </div>
        {isAuthenticated && (
          <SparklesIcon className="w-4 h-4 text-sky-400" />
        )}
      </button>

      {/* Dropdown */}
      {isDropdownOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsDropdownOpen(false)}
          />
          
          {/* Dropdown Content */}
          <div className="absolute right-0 mt-2 w-80 bg-slate-800 rounded-lg shadow-xl border border-slate-700 z-20">
            {/* Header */}
            <div className="p-4 border-b border-slate-700">
              <div className="flex items-center space-x-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                  isAuthenticated ? 'bg-sky-600' : 'bg-slate-600'
                }`}>
                  <UserIcon className="w-6 h-6 text-white" />
                </div>
                <div>
                  <div className="font-medium text-white">
                    {isAuthenticated ? 'Premium Account' : 'Anonymous User'}
                  </div>
                  <div className="text-sm text-slate-400">
                    {isAuthenticated ? userInfo.email : 'No account required'}
                  </div>
                </div>
              </div>
            </div>

            {/* Features List */}
            <div className="p-4">
              <h3 className="text-sm font-medium text-slate-300 mb-3">Available Features</h3>
              <div className="space-y-2">
                {featureList.map(({ key, label, icon: Icon }) => {
                  const isEnabled = userTier.features[key];
                  return (
                    <div key={key} className="flex items-center space-x-3">
                      <Icon className={`w-4 h-4 ${
                        isEnabled ? 'text-green-400' : 'text-slate-500'
                      }`} />
                      <span className={`text-sm ${
                        isEnabled ? 'text-slate-300' : 'text-slate-500'
                      }`}>
                        {label}
                      </span>
                      {!isEnabled && (
                        <span className="text-xs text-sky-400 ml-auto">Premium</span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Processing Limits */}
            <div className="p-4 border-t border-slate-700">
              <h3 className="text-sm font-medium text-slate-300 mb-2">Processing Limits</h3>
              <div className="text-sm text-slate-400 space-y-1">
                <div>Summary Length: {processingStats.maxSummaryLength} chars</div>
                <div>Max Tags: {processingStats.maxTags}</div>
                <div>AI Processing: {processingStats.enableAI ? 'Enabled' : 'Disabled'}</div>
              </div>
            </div>

            {/* Actions */}
            <div className="p-4 border-t border-slate-700">
              {isAuthenticated ? (
                <button
                  onClick={handleLogout}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors"
                >
                  <ArrowRightOnRectangleIcon className="w-4 h-4" />
                  <span>Sign Out</span>
                </button>
              ) : (
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      onLoginClick();
                      setIsDropdownOpen(false);
                    }}
                    className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-md transition-colors"
                  >
                    <SparklesIcon className="w-4 h-4" />
                    <span>Upgrade to Premium</span>
                  </button>
                  <p className="text-xs text-slate-400 text-center">
                    Get AI-powered features and advanced capabilities
                  </p>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UserProfile;