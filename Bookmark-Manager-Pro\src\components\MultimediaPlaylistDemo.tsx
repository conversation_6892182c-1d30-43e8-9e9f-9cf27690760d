/**
 * MULTIMEDIA PLAYLIST DEMO
 * Demonstration component showcasing all multimedia playlist features
 * Use this to test and demonstrate the complete integration
 */

import React, { useState } from 'react'
import { Play, Video, Headphones, FileText, Zap, Download, Settings } from 'lucide-react'
import { MultimediaPlaylistManager } from './MultimediaPlaylistManager'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import type { Bookmark } from '../types'

export const MultimediaPlaylistDemo: React.FC = () => {
  const { bookmarks } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  const [selectedMode, setSelectedMode] = useState<'floating' | 'sidebar' | 'panel' | 'integration'>('floating')
  const [selectedBookmarks, setSelectedBookmarks] = useState<Bookmark[]>([])
  const [showDemo, setShowDemo] = useState(false)

  // Demo data - create some sample bookmarks for testing
  const demoBookmarks: Bookmark[] = [
    {
      id: 'demo-1',
      title: 'React Tutorial Video',
      url: 'https://youtube.com/watch?v=demo1',
      description: 'Learn React fundamentals',
      tags: ['react', 'tutorial', 'video'],
      collection: 'Learning',
      dateAdded: new Date().toISOString(),
      isFavorite: false
    },
    {
      id: 'demo-2', 
      title: 'JavaScript Podcast Episode',
      url: 'https://podcast.example.com/js-episode',
      description: 'Latest JavaScript trends',
      tags: ['javascript', 'podcast', 'audio'],
      collection: 'Learning',
      dateAdded: new Date().toISOString(),
      isFavorite: true
    },
    {
      id: 'demo-3',
      title: 'Web Development Article',
      url: 'https://blog.example.com/web-dev-guide',
      description: 'Comprehensive web development guide',
      tags: ['web-dev', 'article', 'guide'],
      collection: 'Learning',
      dateAdded: new Date().toISOString(),
      isFavorite: false
    }
  ]

  const handleModeChange = (mode: typeof selectedMode) => {
    setSelectedMode(mode)
    setShowDemo(true)
  }

  const handlePlaylistCreated = (playlist: any) => {
    console.log('🎬 Demo: Playlist created:', playlist.name)
    alert(`✅ Playlist "${playlist.name}" created successfully!\n\nItems: ${playlist.items.length}\nDuration: ${playlist.totalDuration ? Math.floor(playlist.totalDuration / 60) : 'Unknown'} minutes`)
  }

  const handlePlaybackStarted = (sessionId: string) => {
    console.log('▶️ Demo: Playback started:', sessionId)
    alert(`🎵 Playback session started!\n\nSession ID: ${sessionId}`)
  }

  const handleExportCompleted = (result: any) => {
    console.log('📤 Demo: Export completed:', result)
    if (result.success) {
      alert(`📤 Export completed successfully!\n\nFormat: ${result.filename || 'Unknown'}\nStatus: ${result.message || 'Ready for download'}`)
    } else {
      alert(`❌ Export failed: ${result.error}`)
    }
  }

  return (
    <div className={`
      ${isModernTheme ? 'bg-black/90 text-white' : 'bg-white text-gray-900'}
      min-h-screen p-6
    `}>
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className={`text-3xl font-bold mb-4 ${
            isModernTheme ? 'text-white' : 'text-gray-900'
          }`}>
            🎬 Multimedia Playlist Integration Demo
          </h1>
          <p className={`text-lg ${
            isModernTheme ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Test all multimedia playlist features and integration modes
          </p>
        </div>

        {/* Mode Selection */}
        <div className={`
          ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
          border rounded-xl p-6 mb-8
        `}>
          <h2 className={`text-xl font-semibold mb-4 ${
            isModernTheme ? 'text-white' : 'text-gray-900'
          }`}>
            Choose Integration Mode
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {[
              { mode: 'floating' as const, icon: Play, title: 'Floating Widget', desc: 'Bottom-right floating controls' },
              { mode: 'sidebar' as const, icon: Settings, title: 'Sidebar Mode', desc: 'Compact sidebar integration' },
              { mode: 'panel' as const, icon: Video, title: 'Full Panel', desc: 'Complete feature panel' },
              { mode: 'integration' as const, icon: Zap, title: 'Inline Mode', desc: 'Embedded in content' }
            ].map(({ mode, icon: Icon, title, desc }) => (
              <button
                key={mode}
                onClick={() => handleModeChange(mode)}
                className={`
                  p-4 rounded-lg border transition-all text-left
                  ${selectedMode === mode
                    ? isModernTheme
                      ? 'border-white/20 text-white'
                      : 'border-gray-300 text-gray-700'
                    : isModernTheme
                      ? 'bg-white/5 border-white/10 text-gray-300 hover:bg-white/10'
                      : 'bg-white border-gray-200 text-gray-700 hover:bg-gray-50'
                  }
                `}
              >
                <Icon className="w-6 h-6 mb-2" />
                <h3 className="font-semibold">{title}</h3>
                <p className="text-sm opacity-75">{desc}</p>
              </button>
            ))}
          </div>
        </div>

        {/* Demo Controls */}
        <div className={`
          ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
          border rounded-xl p-6 mb-8
        `}>
          <h2 className={`text-xl font-semibold mb-4 ${
            isModernTheme ? 'text-white' : 'text-gray-900'
          }`}>
            Demo Controls
          </h2>
          
          <div className="flex flex-wrap gap-4">
            <button
              onClick={() => setSelectedBookmarks(demoBookmarks)}
              className={`
                px-4 py-2 rounded-lg transition-colors
                ${isModernTheme
                  ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                  : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                }
              `}
            >
              📚 Load Demo Bookmarks ({demoBookmarks.length})
            </button>
            
            <button
              onClick={() => setSelectedBookmarks(bookmarks.slice(0, 5))}
              className={`
                px-4 py-2 rounded-lg transition-colors
                ${isModernTheme
                  ? 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                  : 'bg-green-100 text-green-700 hover:bg-green-200'
                }
              `}
            >
              🔖 Use Real Bookmarks ({Math.min(bookmarks.length, 5)})
            </button>
            
            <button
              onClick={() => setShowDemo(!showDemo)}
              className={`
                px-4 py-2 rounded-lg transition-colors
                ${isModernTheme
                  ? 'text-white hover:opacity-80'
                  : 'text-gray-700 hover:opacity-80'
                }
              `}
            >
              {showDemo ? '🙈 Hide Demo' : '👁️ Show Demo'}
            </button>
          </div>

          {selectedBookmarks.length > 0 && (
            <div className="mt-4">
              <p className={`text-sm ${
                isModernTheme ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Selected: {selectedBookmarks.length} bookmarks
              </p>
              <div className="flex flex-wrap gap-2 mt-2">
                {selectedBookmarks.slice(0, 3).map(bookmark => (
                  <span
                    key={bookmark.id}
                    className={`
                      px-2 py-1 rounded text-xs
                      ${isModernTheme
                        ? 'bg-white/10 text-gray-300'
                        : 'bg-gray-200 text-gray-700'
                      }
                    `}
                  >
                    {bookmark.title}
                  </span>
                ))}
                {selectedBookmarks.length > 3 && (
                  <span className={`
                    px-2 py-1 rounded text-xs
                    ${isModernTheme ? 'text-gray-400' : 'text-gray-500'}
                  `}>
                    +{selectedBookmarks.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Demo Area */}
        {showDemo && (
          <div className={`
            ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
            border rounded-xl p-6 min-h-[400px] relative
          `}>
            <h2 className={`text-xl font-semibold mb-4 ${
              isModernTheme ? 'text-white' : 'text-gray-900'
            }`}>
              Live Demo: {selectedMode.charAt(0).toUpperCase() + selectedMode.slice(1)} Mode
            </h2>
            
            <MultimediaPlaylistManager
              mode={selectedMode}
              selectedBookmarks={selectedBookmarks}
              collectionId="demo-collection"
              size="medium"
              autoShow={selectedMode === 'panel'}
              onPlaylistCreated={handlePlaylistCreated}
              onPlaybackStarted={handlePlaybackStarted}
              onExportCompleted={handleExportCompleted}
            />
          </div>
        )}

        {/* Feature Status */}
        <div className={`
          ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
          border rounded-xl p-6 mt-8
        `}>
          <h2 className={`text-xl font-semibold mb-4 ${
            isModernTheme ? 'text-white' : 'text-gray-900'
          }`}>
            ✅ Implementation Status
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              '🎬 Multimedia Playlist Service',
              '🎵 Text-to-Speech Integration', 
              '📤 Multi-format Export',
              '🤖 AI Enhancement',
              '✨ Smart Suggestions',
              '🎮 Playback Controls',
              '📱 Cross-device Sync',
              '🎯 Multiple Integration Modes',
              '🔧 Complete API Coverage'
            ].map((feature, index) => (
              <div
                key={index}
                className={`
                  p-3 rounded-lg
                  ${isModernTheme
                    ? 'bg-green-500/20 text-green-300'
                    : 'bg-green-100 text-green-700'
                  }
                `}
              >
                {feature}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default MultimediaPlaylistDemo
