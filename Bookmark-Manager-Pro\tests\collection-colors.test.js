import { test, expect } from '@playwright/test'

test.describe('Collection Color System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForSelector('.bookmark-grid-container')
  })

  test('should display unique border colors for different collections', async ({ page }) => {
    // Wait for bookmark cards to load
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      // Get the first few bookmark cards
      const cardCount = Math.min(await bookmarkCards.count(), 5)
      const borderColors = []
      
      for (let i = 0; i < cardCount; i++) {
        const card = bookmarkCards.nth(i)
        const borderColor = await card.evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        borderColors.push(borderColor)
      }
      
      console.log('Border colors found:', borderColors)
      
      // Verify that we have border colors
      expect(borderColors.length).toBeGreaterThan(0)
      
      // Each border color should be a valid CSS color
      borderColors.forEach(color => {
        expect(color).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
      })
    }
  })

  test('should show collection dots on bookmark cards', async ({ page }) => {
    // Look for collection dot indicators
    const collectionDots = page.locator('.collection-dot-indicator')
    
    if (await collectionDots.count() > 0) {
      // Check if dots are visible
      await expect(collectionDots.first()).toBeVisible()
      
      // Check if dots have background colors
      const dotColor = await collectionDots.first().evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      )
      
      expect(dotColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
      console.log('Collection dot color:', dotColor)
    }
  })

  test('should show small collection dots in footer', async ({ page }) => {
    // Look for small collection dots in footer
    const smallDots = page.locator('.collection-dot-small')
    
    if (await smallDots.count() > 0) {
      await expect(smallDots.first()).toBeVisible()
      
      // Check if small dots have background colors
      const dotColor = await smallDots.first().evaluate(el => 
        window.getComputedStyle(el).backgroundColor
      )
      
      expect(dotColor).toMatch(/rgb\(\d+,\s*\d+,\s*\d+\)/)
      console.log('Small collection dot color:', dotColor)
    }
  })

  test('should have consistent colors for same collection', async ({ page }) => {
    // Get all bookmark cards
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 1) {
      const collectionColors = new Map()
      const cardCount = Math.min(await bookmarkCards.count(), 10)
      
      for (let i = 0; i < cardCount; i++) {
        const card = bookmarkCards.nth(i)
        
        // Get collection name from the card
        const collectionElement = card.locator('.collection-redesigned')
        
        if (await collectionElement.count() > 0) {
          const collectionName = await collectionElement.textContent()
          const borderColor = await card.evaluate(el => 
            window.getComputedStyle(el).borderColor
          )
          
          if (collectionColors.has(collectionName)) {
            // Same collection should have same color
            expect(borderColor).toBe(collectionColors.get(collectionName))
          } else {
            collectionColors.set(collectionName, borderColor)
          }
        }
      }
      
      console.log('Collection color mapping:', Object.fromEntries(collectionColors))
    }
  })

  test('should have enhanced border styling', async ({ page }) => {
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const firstCard = bookmarkCards.first()
      
      // Check border width
      const borderWidth = await firstCard.evaluate(el => 
        window.getComputedStyle(el).borderWidth
      )
      
      // Should have 3px border
      expect(borderWidth).toBe('3px')
      
      // Check border style
      const borderStyle = await firstCard.evaluate(el => 
        window.getComputedStyle(el).borderStyle
      )
      
      expect(borderStyle).toBe('solid')
      
      console.log('Border styling:', { borderWidth, borderStyle })
    }
  })

  test('should show hover effects on bookmark cards', async ({ page }) => {
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const firstCard = bookmarkCards.first()
      
      // Get initial transform
      const initialTransform = await firstCard.evaluate(el => 
        window.getComputedStyle(el).transform
      )
      
      // Hover over the card
      await firstCard.hover()
      
      // Wait for transition
      await page.waitForTimeout(300)
      
      // Get transform after hover
      const hoverTransform = await firstCard.evaluate(el => 
        window.getComputedStyle(el).transform
      )
      
      console.log('Transform states:', { initialTransform, hoverTransform })
      
      // Transform should change on hover (indicating hover effect is working)
      // Note: This might not always be different due to timing, but structure should be there
    }
  })

  test('should have collection tooltips on dots', async ({ page }) => {
    const collectionDots = page.locator('.collection-dot-indicator')
    
    if (await collectionDots.count() > 0) {
      const firstDot = collectionDots.first()
      
      // Check if dot has title attribute (tooltip)
      const title = await firstDot.getAttribute('title')
      
      if (title) {
        expect(title).toContain('Collection:')
        console.log('Collection tooltip:', title)
      }
    }
  })

  test('should maintain color consistency across page reloads', async ({ page }) => {
    // Get initial colors
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const initialColors = []
      const cardCount = Math.min(await bookmarkCards.count(), 3)
      
      for (let i = 0; i < cardCount; i++) {
        const borderColor = await bookmarkCards.nth(i).evaluate(el => 
          window.getComputedStyle(el).borderColor
        )
        initialColors.push(borderColor)
      }
      
      // Reload page
      await page.reload()
      await page.waitForSelector('.bookmark-grid-container')
      
      // Get colors after reload
      const reloadedCards = page.locator('.bookmark-card-front-redesigned')
      
      if (await reloadedCards.count() >= cardCount) {
        for (let i = 0; i < cardCount; i++) {
          const borderColor = await reloadedCards.nth(i).evaluate(el => 
            window.getComputedStyle(el).borderColor
          )
          
          // Colors should be consistent after reload
          expect(borderColor).toBe(initialColors[i])
        }
      }
      
      console.log('Color consistency verified across reload')
    }
  })
})
