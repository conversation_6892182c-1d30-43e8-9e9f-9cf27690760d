import React, { forwardRef, ReactNode } from 'react';
import SpinnerIcon from '../icons/SpinnerIcon';

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'ghost' | 'danger' | 'success' | 'warning' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  loading?: boolean;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  fullWidth?: boolean;
  children?: ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>((
  {
    variant = 'primary',
    size = 'md',
    loading = false,
    leftIcon,
    rightIcon,
    fullWidth = false,
    disabled,
    className = '',
    children,
    ...props
  },
  ref
) => {
  const getVariantClasses = () => {
    switch (variant) {
      case 'primary':
        return 'bg-primary-600 hover:bg-primary-700 text-white border-primary-600 hover:border-primary-700 shadow-sm hover:shadow-md';
      case 'secondary':
        return 'bg-surface-100 hover:bg-surface-200 text-surface-700 border-surface-300 hover:border-surface-400';
      case 'ghost':
        return 'bg-transparent hover:bg-surface-100 text-surface-600 hover:text-surface-800 border-transparent';
      case 'danger':
        return 'bg-error-600 hover:bg-error-700 text-white border-error-600 hover:border-error-700';
      case 'success':
        return 'bg-success-600 hover:bg-success-700 text-white border-success-600 hover:border-success-700';
      case 'warning':
        return 'bg-warning-600 hover:bg-warning-700 text-white border-warning-600 hover:border-warning-700';
      case 'info':
        return 'bg-info-600 hover:bg-info-700 text-white border-info-600 hover:border-info-700';
      default:
        return 'bg-primary-600 hover:bg-primary-700 text-white border-primary-600 hover:border-primary-700';
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'xs':
        return 'px-2 py-1 text-xs';
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-4 py-2 text-sm';
      case 'lg':
        return 'px-6 py-3 text-base';
      case 'xl':
        return 'px-8 py-4 text-lg';
      default:
        return 'px-4 py-2 text-sm';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'xs':
        return 'w-3 h-3';
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-4 h-4';
      case 'lg':
        return 'w-5 h-5';
      case 'xl':
        return 'w-6 h-6';
      default:
        return 'w-4 h-4';
    }
  };

  const baseClasses = `
    inline-flex items-center justify-center gap-2
    font-medium border rounded-md
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed disabled:pointer-events-none
    ${fullWidth ? 'w-full' : ''}
    ${loading ? 'cursor-wait' : ''}
  `;

  const variantClasses = getVariantClasses();
  const sizeClasses = getSizeClasses();
  const iconSizeClass = getIconSize();

  return (
    <button
      ref={ref}
      disabled={disabled || loading}
      className={`${baseClasses} ${variantClasses} ${sizeClasses} ${className}`}
      {...props}
    >
      {loading ? (
        <SpinnerIcon className={`${iconSizeClass} animate-spin`} />
      ) : (
        leftIcon && <span className={`${iconSizeClass}`}>{leftIcon}</span>
      )}
      
      <span>{children}</span>
      
      {!loading && rightIcon && (
        <span className={`${iconSizeClass}`}>{rightIcon}</span>
      )}
    </button>
  );
});

Button.displayName = 'Button';

export default Button;