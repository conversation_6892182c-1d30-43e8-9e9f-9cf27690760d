// ***********************************************************
// This example support/component.ts is processed and
// loaded automatically before your component test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Import global styles
import '../../src/index.css';

// Import React Testing Library commands
import '@testing-library/cypress/add-commands';

// Import Cypress Axe for accessibility testing
import 'cypress-axe';

// Example use:
// cy.mount(<MyComponent />)

import { mount } from 'cypress/react18';
import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BookmarkProvider } from '../../src/contexts/BookmarkContext';

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
      mountWithProviders(
        component: React.ReactNode,
        options?: {
          queryClient?: QueryClient;
          bookmarkContextValue?: any;
          routerProps?: any;
        }
      ): Chainable<any>;
      getByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
      findByTestId(testId: string): Chainable<JQuery<HTMLElement>>;
    }
  }
}

// Create a test query client for component tests
const createTestQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  });
};

// Custom mount command with providers
Cypress.Commands.add('mountWithProviders', (component, options = {}) => {
  const {
    queryClient = createTestQueryClient(),
    bookmarkContextValue,
    routerProps = {},
  } = options;

  const wrapped = (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter {...routerProps}>
        <BookmarkProvider value={bookmarkContextValue}>
          {component}
        </BookmarkProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );

  return cy.mount(wrapped);
});

Cypress.Commands.add('mount', mount);

// Component testing utilities
Cypress.Commands.add('getByTestId', (testId: string) => {
  return cy.get(`[data-testid="${testId}"]`);
});

Cypress.Commands.add('findByTestId', (testId: string) => {
  return cy.find(`[data-testid="${testId}"]`);
});