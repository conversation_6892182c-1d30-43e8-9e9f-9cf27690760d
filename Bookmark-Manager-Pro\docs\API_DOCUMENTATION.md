# API Documentation - Bookmark Manager Pro

## Overview
This document provides comprehensive documentation for all services, APIs, and data structures used in the Bookmark Manager Pro application.

⚠️ **API DOCUMENTATION UPDATE** - Updated to include new state management architecture:
- **Context Providers**: Global state management APIs
- **Custom Hooks**: Domain-specific state logic APIs
- **Refactored Components**: Updated component APIs
- **Utility Services**: New shared utility functions

## State Management APIs (New)

### Context Providers

#### BookmarkContext
**File**: `contexts/BookmarkContext.tsx`

Provides global bookmark state management across the application.

**Context Value:**
```typescript
interface BookmarkContextValue {
  bookmarks: Bookmark[];
  filteredBookmarks: Bookmark[];
  selectedBookmarkIds: Set<string>;
  searchTerm: string;
  activeFolder: string;
  sortConfig: SortConfig;
  updateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  deleteBookmarks: (ids: string[]) => void;
  addBookmark: (bookmark: Bookmark) => void;
  setSearchTerm: (term: string) => void;
  setActiveFolder: (folder: string) => void;
  setSortConfig: (config: SortConfig) => void;
  toggleBookmarkSelection: (id: string) => void;
  clearSelection: () => void;
}
```

**Usage:**
```typescript
import { useBookmarkContext } from '../contexts/BookmarkContext';

function MyComponent() {
  const { bookmarks, updateBookmark } = useBookmarkContext();
  // Use bookmark state and actions
}
```

#### UIContext
**File**: `contexts/UIContext.tsx`

Manages UI preferences and display settings.

**Context Value:**
```typescript
interface UIContextValue {
  viewMode: 'list' | 'card' | 'compact';
  density: 'comfortable' | 'compact';
  sidebarCollapsed: boolean;
  theme: 'light' | 'dark' | 'system';
  setViewMode: (mode: 'list' | 'card' | 'compact') => void;
  setDensity: (density: 'comfortable' | 'compact') => void;
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark' | 'system') => void;
}
```

### Custom Hooks

#### useBookmarkData
**File**: `hooks/useBookmarkData.ts`

Manages bookmark data state and filtering logic.

**Returns:**
```typescript
interface UseBookmarkDataReturn {
  bookmarks: Bookmark[];
  filteredBookmarks: Bookmark[];
  searchTerm: string;
  activeFolder: string;
  sortConfig: SortConfig;
  isLoading: boolean;
  error: string | null;
  updateBookmark: (id: string, updates: Partial<Bookmark>) => Promise<void>;
  deleteBookmarks: (ids: string[]) => Promise<void>;
  addBookmark: (bookmark: Bookmark) => Promise<void>;
  setSearchTerm: (term: string) => void;
  setActiveFolder: (folder: string) => void;
  setSortConfig: (config: SortConfig) => void;
}
```

#### useModalManager
**File**: `hooks/useModalManager.ts`

Centralized modal state management.

**Returns:**
```typescript
interface UseModalManagerReturn {
  activeModal: string | null;
  modalData: any;
  openModal: (modalId: string, data?: any) => void;
  closeModal: () => void;
  isModalOpen: (modalId: string) => boolean;
}
```

#### useToastNotifications
**File**: `hooks/useToastNotifications.ts`

Manages toast notification system.

**Returns:**
```typescript
interface UseToastNotificationsReturn {
  toasts: Toast[];
  showToast: (message: string, type: ToastType, duration?: number) => void;
  hideToast: (id: string) => void;
  clearAllToasts: () => void;
}
```

## Core Services

### BookmarkParser Service
**File**: `services/bookmarkParser.ts`

#### `parseBookmarksHTML(htmlContent: string): Bookmark[]`
Parses HTML bookmark files exported from browsers.

**Parameters:**
- `htmlContent` (string): Raw HTML content from bookmark file

**Returns:**
- Array of `Bookmark` objects

**Example:**
```typescript
import { parseBookmarksHTML } from './services/bookmarkParser';

const htmlContent = await file.text();
const bookmarks = parseBookmarksHTML(htmlContent);
```

### Gemini AI Service
**File**: `services/geminiService.ts`

#### `generateSummaryForBookmark(title: string, url: string): Promise<string>`
Generates AI-powered summaries for bookmarks.

**Parameters:**
- `title` (string): Bookmark title
- `url` (string): Bookmark URL

**Returns:**
- Promise resolving to summary string

**Throws:**
- Error if API key is missing or API call fails

#### `generateTagsForBookmark(title: string, url: string): Promise<string[]>`
Generates relevant tags for bookmarks using AI.

**Parameters:**
- `title` (string): Bookmark title
- `url` (string): Bookmark URL

**Returns:**
- Promise resolving to array of tag strings

**Example:**
```typescript
import { generateSummaryForBookmark, generateTagsForBookmark } from './services/geminiService';

try {
  const summary = await generateSummaryForBookmark('React Documentation', 'https://react.dev');
  const tags = await generateTagsForBookmark('React Documentation', 'https://react.dev');
} catch (error) {
  console.error('AI processing failed:', error);
}
```

### YouTube Service
**File**: `services/youtubeService.ts`

#### `isYouTubeUrl(url: string): boolean`
Detects if a URL is a YouTube video URL.

**Parameters:**
- `url` (string): URL to check

**Returns:**
- Boolean indicating if URL is a YouTube video

#### `extractVideoId(url: string): string | null`
Extracts YouTube video ID from URL.

**Parameters:**
- `url` (string): YouTube URL

**Returns:**
- Video ID string or null if invalid

#### `processYouTubeVideo(url: string): Promise<YouTubeProcessingResult>`
Processes YouTube video to extract information, transcript, and generate instructions.

**Parameters:**
- `url` (string): YouTube video URL

**Returns:**
- Promise resolving to `YouTubeProcessingResult` with video info, transcript, and instructions

**Example:**
```typescript
import { youtubeService } from './services/youtubeService';

if (youtubeService.isYouTubeUrl(url)) {
  const result = await youtubeService.processYouTubeVideo(url);
  console.log(result.videoInfo.title);
  console.log(result.transcript.summary);
  console.log(result.transcript.instructions);
}
```

### Error Service
**File**: `services/errorService.ts`

#### `logError(error: Error, context?: ErrorContext, severity?: string): void`
Logs errors with context and severity level.

**Parameters:**
- `error` (Error): Error object to log
- `context` (ErrorContext, optional): Additional context information
- `severity` (string, optional): Error severity level

#### `getErrorLogs(): ErrorLog[]`
Retrieves all logged errors.

**Returns:**
- Array of `ErrorLog` objects

#### `clearErrorLogs(): void`
Clears all logged errors.

#### `setToastHandler(handler: (toast: Omit<Toast, 'id'>) => void): void`
Sets the toast notification handler.

**Parameters:**
- `handler` (function): Toast notification handler function

**Example:**
```typescript
import { errorService } from './services/errorService';

try {
  // Some operation
} catch (error) {
  errorService.logError(error, {
    component: 'BookmarkList',
    action: 'processBookmark'
  }, 'high');
}
```

### User Tier Service
**File**: `services/userTierService.ts`

#### `getCurrentTier(): UserTier`
Returns the current user's tier and feature access.

**Returns:**
- `UserTier` object with type and features

#### `authenticate(token: string): Promise<boolean>`
Authenticates user and upgrades tier.

**Parameters:**
- `token` (string): Authentication token

**Returns:**
- Promise resolving to boolean success status

#### `logout(): void`
Logs out user and reverts to anonymous tier.

#### `onTierChange(callback: (tier: UserTier) => void): () => void`
Subscribes to tier changes.

**Parameters:**
- `callback` (function): Function called when tier changes

**Returns:**
- Unsubscribe function

### Hybrid Processor Service
**File**: `services/hybridProcessor.ts`

#### `processBookmark(bookmark: Bookmark, config: ProcessingConfig): Promise<ProcessingResult>`
Processes bookmarks using hybrid approach (client-side + AI).

**Parameters:**
- `bookmark` (Bookmark): Bookmark to process
- `config` (ProcessingConfig): Processing configuration

**Returns:**
- Promise resolving to `ProcessingResult`

### Client-Side Processor Service
**File**: `services/clientSideProcessor.ts`

#### `generateBasicSummary(title: string, url: string): string`
Generates basic summary using client-side processing.

**Parameters:**
- `title` (string): Bookmark title
- `url` (string): Bookmark URL

**Returns:**
- Basic summary string

#### `generateBasicTags(title: string, url: string): string[]`
Generates basic tags using client-side processing.

**Parameters:**
- `title` (string): Bookmark title
- `url` (string): Bookmark URL

**Returns:**
- Array of basic tag strings

### Advanced Search Service
**File**: `services/advancedSearchService.ts`

#### `searchBookmarks(bookmarks: Bookmark[], filters: SearchFilters): SearchResult`
Performs advanced search with multiple filter criteria.

**Parameters:**
- `bookmarks` (Bookmark[]): Array of bookmarks to search
- `filters` (SearchFilters): Search filter criteria

**Returns:**
- `SearchResult` object with filtered bookmarks and metadata

### Bulk Operations Service
**File**: `services/bulkOperationsService.ts`

#### `performBulkOperation(bookmarks: Bookmark[], operation: BulkOperation): Promise<BulkOperationResult>`
Performs bulk operations on multiple bookmarks.

**Parameters:**
- `bookmarks` (Bookmark[]): Array of bookmarks to operate on
- `operation` (BulkOperation): Operation to perform

**Returns:**
- Promise resolving to `BulkOperationResult`

### Bookmark Q&A Service
**File**: `services/bookmarkQAService.ts`

#### `askQuestion(question: string, bookmarks: Bookmark[]): Promise<string>`
Answers questions about bookmarks using AI.

**Parameters:**
- `question` (string): Question to ask
- `bookmarks` (Bookmark[]): Relevant bookmarks for context

**Returns:**
- Promise resolving to answer string

**Example:**
```typescript
import { userTierService } from './services/userTierService';

// Get current tier
const currentTier = userTierService.getCurrentTier();

// Subscribe to changes
const unsubscribe = userTierService.onTierChange((newTier) => {
  console.log('Tier changed:', newTier);
});

// Cleanup
unsubscribe();
```

### Hybrid Processor Service
**File**: `services/hybridProcessor.ts`

#### `processBookmark(bookmark: Bookmark, config: ProcessingConfig): Promise<ProcessingResult>`
Processes bookmarks using hybrid AI and client-side methods.

**Parameters:**
- `bookmark` (Bookmark): Bookmark to process
- `config` (ProcessingConfig): Processing configuration

**Returns:**
- Promise resolving to `ProcessingResult`

#### `batchProcessBookmarks(bookmarks: Bookmark[], config: ProcessingConfig, onProgress?: (progress: BatchProgress) => void): Promise<ProcessingResult[]>`
Processes multiple bookmarks with progress tracking.

**Parameters:**
- `bookmarks` (Bookmark[]): Array of bookmarks to process
- `config` (ProcessingConfig): Processing configuration
- `onProgress` (function, optional): Progress callback

**Returns:**
- Promise resolving to array of `ProcessingResult`

## Data Types & Interfaces

### Core Types
**File**: `types.ts`

#### `Bookmark`
```typescript
interface Bookmark {
  id: string;                    // Unique identifier
  title: string;                 // Bookmark title
  url: string;                   // Bookmark URL
  addDate?: string;              // UNIX timestamp as string
  lastModified?: string;         // UNIX timestamp as string
  icon?: string;                 // Favicon URL or data URI
  path?: string[];               // Folder path array
  summary?: string;              // AI-generated summary
  tags?: string[];               // Tags array
  summaryError?: string;         // Summary generation error
  tagsError?: string;            // Tag generation error
}
```

#### `BookmarkProcessingState`
```typescript
interface BookmarkProcessingState {
  isSummarizing?: boolean;       // Currently generating summary
  isTagging?: boolean;           // Currently generating tags
  summaryError?: string;         // Summary error message
  tagsError?: string;            // Tag error message
  processingTier?: 'basic' | 'premium'; // Processing tier used
  confidence?: 'low' | 'medium' | 'high'; // Processing confidence
}
```

#### `UserTier`
```typescript
interface UserTier {
  type: 'anonymous' | 'authenticated';
  features: {
    basicSummarization: boolean;
    basicTagging: boolean;
    aiSummarization: boolean;
    aiTagging: boolean;
    cloudSync: boolean;
    advancedSearch: boolean;
  };
}
```

#### `ProcessingConfig`
```typescript
interface ProcessingConfig {
  enableClientSide: boolean;     // Enable client-side processing
  enableAI: boolean;             // Enable AI processing
  fallbackToClientSide: boolean; // Fallback on AI failure
  maxSummaryLength: number;      // Maximum summary length
  maxTags: number;               // Maximum number of tags
}
```

#### `SortConfig`
```typescript
interface SortConfig {
  key: keyof Bookmark | 'default'; // Sort field
  direction: 'asc' | 'desc';        // Sort direction
}
```

### Search & AI Types

#### `WebSource`
```typescript
interface WebSource {
  uri: string;                   // Source URL
  title: string;                 // Source title
}
```

#### `GroundingChunk`
```typescript
interface GroundingChunk {
  web?: WebSource;               // Web source information
}
```

#### `SearchGroundedResult`
```typescript
interface SearchGroundedResult {
  text: string;                  // Search result text
  sources: GroundingChunk[];     // Source references
}
```

## Utility Functions

### Export Utils
**File**: `utils/exportUtils.ts`

#### `exportToCSV(bookmarks: Bookmark[], filename?: string): void`
Exports bookmarks to CSV format.

#### `exportToPDF(bookmarks: Bookmark[], filename?: string): void`
Exports bookmarks to PDF format.

#### `exportBookmarksToHTML(bookmarks: Bookmark[], filename?: string): void`
Exports bookmarks to HTML format.

#### Email Export Functions
- `exportToCSVByEmail(bookmarks: Bookmark[]): void`
- `exportToPDFByEmail(bookmarks: Bookmark[]): void`
- `exportBookmarksToHTMLByEmail(bookmarks: Bookmark[]): void`

**Example:**
```typescript
import { exportToCSV, exportToPDF } from './utils/exportUtils';

// Export selected bookmarks
exportToCSV(selectedBookmarks, 'my-bookmarks.csv');
exportToPDF(selectedBookmarks, 'my-bookmarks.pdf');
```

## Component Props Interfaces

### BookmarkList Props
```typescript
interface BookmarkListProps {
  bookmarks: Bookmark[];
  selectedBookmarkIds: Set<string>;
  processingStates: Record<string, BookmarkProcessingState>;
  onToggleSelect: (id: string) => void;
  onToggleSelectAll: () => void;
  isAllSelected: boolean;
  sortConfig: SortConfig;
  onSort: (key: keyof Bookmark) => void;
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  onAddTag: (bookmarkId: string, tag: string) => void;
  onRemoveTag: (bookmarkId: string, tag: string) => void;
  onTagClick: (tag: string) => void;
}
```

### ActionToolbar Props
```typescript
interface ActionToolbarProps {
  onSummarize: () => void;
  onTag: () => void;
  onExportSelectedCSV: () => void;
  onExportAllCSV: () => void;
  onExportSelectedPDF: () => void;
  onExportAllPDF: () => void;
  onExportSelectedHTML: () => void;
  onExportAllHTML: () => void;
  onExportSelectedCSVByEmail: () => void;
  onExportAllCSVByEmail: () => void;
  onExportSelectedPDFByEmail: () => void;
  onExportAllPDFByEmail: () => void;
  onExportSelectedHTMLByEmail: () => void;
  onExportAllHTMLByEmail: () => void;
  onDeleteSelected: () => void;
  numSelected: number;
  isProcessing: boolean;
  hasAnyBookmarks: boolean;
}
```

### BookmarkImporter Props
```typescript
interface BookmarkImporterProps {
  onFileChange: (event: ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
  onClearImport: () => void;
  hasImportedFile: boolean;
  resetToken: number;
}
```

## Constants
**File**: `constants.ts`

### AI Prompts
- `PROMPT_SUMMARIZE_BOOKMARK(title: string, url: string): string`
- `PROMPT_TAG_BOOKMARK(title: string, url: string): string`

### Configuration
- `API_KEY_ERROR_MESSAGE`: Error message for missing API key
- `ITEMS_PER_PAGE`: Number of items per page (10)
- `GEMINI_MODEL_NAME`: AI model identifier

## Error Handling

### Common Error Types
1. **API Key Missing**: `GEMINI_API_KEY environment variable not set`
2. **Network Errors**: Failed API calls to Gemini service
3. **Parsing Errors**: Invalid HTML bookmark file format
4. **Processing Errors**: AI processing failures

### Error Handling Patterns
```typescript
try {
  const result = await riskyOperation();
  return result;
} catch (error) {
  console.error('Operation failed:', error);
  throw new Error(`Operation failed: ${error.message}`);
}
```

## Environment Variables

### Required
- `GEMINI_API_KEY`: Google Gemini API key for AI features

### Optional
- `VITE_APP_TITLE`: Application title override
- `VITE_API_BASE_URL`: Base URL for future API endpoints

## Performance Considerations

### Optimization Strategies
1. **Memoization**: Use `useMemo` for expensive computations
2. **Virtualization**: Implement virtual scrolling for large lists
3. **Lazy Loading**: Load bookmark processing on demand
4. **Debouncing**: Debounce search and filter operations
5. **Batch Processing**: Process multiple bookmarks efficiently

### Memory Management
- Limit concurrent AI processing requests
- Clean up event listeners and subscriptions
- Use React.memo for component optimization
- Implement proper cleanup in useEffect hooks

## Security Considerations

### API Key Security
- Never expose API keys in client-side code
- Use environment variables for sensitive data
- Implement proper error handling to avoid key leakage

### Data Privacy
- Bookmark data processed locally when possible
- AI processing sends minimal data to external services
- No persistent storage of sensitive user data

### Input Validation
- Validate file uploads before processing
- Sanitize user inputs for search and tags
- Implement proper URL validation for bookmarks

## Testing Guidelines

### Unit Testing
- Test all service functions with various inputs
- Mock external API calls
- Test error handling scenarios
- Verify data transformations

### Component Testing
- Test user interactions and state changes
- Verify prop handling and event callbacks
- Test loading and error states
- Ensure accessibility compliance

### Integration Testing
- Test complete user workflows
- Verify service integrations
- Test file import/export functionality
- Validate AI processing pipelines

## Future API Considerations

### Backend API Design
When implementing cloud sync, consider these endpoints:

```
GET    /api/bookmarks          # Get user bookmarks
POST   /api/bookmarks          # Create bookmark
PUT    /api/bookmarks/:id      # Update bookmark
DELETE /api/bookmarks/:id      # Delete bookmark
POST   /api/bookmarks/sync     # Sync bookmarks
GET    /api/user/profile       # Get user profile
POST   /api/auth/login         # User authentication
POST   /api/auth/logout        # User logout
```

### WebSocket Events
For real-time sync:

```
bookmark:created    # New bookmark added
bookmark:updated    # Bookmark modified
bookmark:deleted    # Bookmark removed
sync:started        # Sync process started
sync:completed      # Sync process finished
```

This API documentation provides a comprehensive reference for developers working with the Bookmark Manager Pro application. Regular updates should be made as new features are added or existing APIs are modified.