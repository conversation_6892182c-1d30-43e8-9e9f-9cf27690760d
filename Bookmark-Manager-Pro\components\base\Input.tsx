import React, { forwardRef, ReactNode } from 'react';

export interface InputProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'size'> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: ReactNode;
  rightIcon?: ReactNode;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  fullWidth?: boolean;
  loading?: boolean;
}

const Input = forwardRef<HTMLInputElement, InputProps>((
  {
    label,
    error,
    helperText,
    leftIcon,
    rightIcon,
    size = 'md',
    variant = 'default',
    fullWidth = false,
    loading = false,
    className = '',
    disabled,
    ...props
  },
  ref
) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-4 py-3 text-base';
      default:
        return 'px-3 py-2 text-sm';
    }
  };

  const getVariantClasses = () => {
    const baseClasses = 'border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500';
    
    switch (variant) {
      case 'filled':
        return `${baseClasses} bg-slate-100 dark:bg-slate-800 border-transparent focus:bg-white dark:focus:bg-slate-700`;
      case 'outlined':
        return `${baseClasses} bg-transparent border-2 border-slate-300 dark:border-slate-600 focus:border-primary-500`;
      default: // default
        return `${baseClasses} bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:border-primary-500`;
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm':
        return 'w-4 h-4';
      case 'md':
        return 'w-5 h-5';
      case 'lg':
        return 'w-6 h-6';
      default:
        return 'w-5 h-5';
    }
  };

  const inputClasses = `
    ${getSizeClasses()}
    ${getVariantClasses()}
    ${fullWidth ? 'w-full' : ''}
    ${leftIcon ? 'pl-10' : ''}
    ${rightIcon ? 'pr-10' : ''}
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
    ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
    text-slate-900 dark:text-slate-100
    placeholder-slate-500 dark:placeholder-slate-400
    ${className}
  `;

  const iconSizeClass = getIconSize();

  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          {label}
        </label>
      )}

      {/* Input Container */}
      <div className="relative">
        {/* Left Icon */}
        {leftIcon && (
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className={`${iconSizeClass} text-slate-400`}>
              {leftIcon}
            </span>
          </div>
        )}

        {/* Input */}
        <input
          ref={ref}
          disabled={disabled || loading}
          className={inputClasses}
          {...props}
        />

        {/* Right Icon */}
        {rightIcon && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <span className={`${iconSizeClass} text-slate-400`}>
              {rightIcon}
            </span>
          </div>
        )}

        {/* Loading Spinner */}
        {loading && (
          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <div className={`${iconSizeClass} animate-spin rounded-full border-2 border-slate-300 border-t-primary-600`} />
          </div>
        )}
      </div>

      {/* Helper Text or Error */}
      {(error || helperText) && (
        <p className={`mt-1 text-xs ${
          error ? 'text-red-600 dark:text-red-400' : 'text-slate-500 dark:text-slate-400'
        }`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Input.displayName = 'Input';

export default Input;