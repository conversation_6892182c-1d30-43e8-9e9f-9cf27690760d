import { FC, useCallback, useEffect, useState } from 'react';
import { InstructionStep, YouTubeVideoInfo } from '../types';
import { ChevronLeftIcon, ChevronRightIcon, DocumentArrowDownIcon, PauseIcon, PlayIcon, PrinterIcon, XMarkIcon } from './icons/HeroIcons';
import ProgressBar from './ProgressBar';

interface InstructionSlideViewerProps {
  instructions: InstructionStep[];
  videoInfo: YouTubeVideoInfo;
  onClose: () => void;
}

interface SlideViewerState {
  currentSlide: number;
  isAutoPlay: boolean;
  autoPlayInterval: number; // seconds
  showNotes: boolean;
}

const InstructionSlideViewer: FC<InstructionSlideViewerProps> = ({
  instructions,
  videoInfo,
  onClose
}) => {
  const [state, setState] = useState<SlideViewerState>({
    currentSlide: 0,
    isAutoPlay: false,
    autoPlayInterval: 5,
    showNotes: false
  });
  const [autoPlayTimer, setAutoPlayTimer] = useState<NodeJS.Timeout | null>(null);

  // Auto-play functionality
  useEffect(() => {
    if (state.isAutoPlay && instructions.length > 1) {
      const timer = setInterval(() => {
        setState(prev => ({
          ...prev,
          currentSlide: (prev.currentSlide + 1) % instructions.length
        }));
      }, state.autoPlayInterval * 1000);
      setAutoPlayTimer(timer);
      return () => clearInterval(timer);
    } else {
      if (autoPlayTimer) {
        clearInterval(autoPlayTimer);
        setAutoPlayTimer(null);
      }
    }
  }, [state.isAutoPlay, state.autoPlayInterval, instructions.length, autoPlayTimer]);

  const goToNextSlide = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentSlide: Math.min(prev.currentSlide + 1, instructions.length - 1)
    }));
  }, [instructions.length]);

  const goToPreviousSlide = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentSlide: Math.max(prev.currentSlide - 1, 0)
    }));
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      switch (e.key) {
        case 'ArrowLeft':
          e.preventDefault();
          goToPreviousSlide();
          break;
        case 'ArrowRight':
        case ' ':
          e.preventDefault();
          goToNextSlide();
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
        case 'Home':
          e.preventDefault();
          setState(prev => ({ ...prev, currentSlide: 0 }));
          break;
        case 'End':
          e.preventDefault();
          setState(prev => ({ ...prev, currentSlide: instructions.length - 1 }));
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [instructions.length, onClose, goToNextSlide, goToPreviousSlide]);

  const goToSlide = useCallback((index: number) => {
    setState(prev => ({
      ...prev,
      currentSlide: Math.max(0, Math.min(index, instructions.length - 1))
    }));
  }, [instructions.length]);

  const toggleAutoPlay = useCallback(() => {
    setState(prev => ({ ...prev, isAutoPlay: !prev.isAutoPlay }));
  }, []);

  const exportSlides = useCallback(() => {
    const slideContent = instructions.map((step, index) => 
      `Slide ${index + 1}: ${step.title}\n${step.description}\n${step.notes ? `Notes: ${step.notes}` : ''}\n\n`
    ).join('');
    
    const content = `${videoInfo.title}\nInstructions\n\n${slideContent}`;
    
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${videoInfo.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_instructions.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [instructions, videoInfo.title]);

  const printSlides = useCallback(() => {
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    const printContent = `
      <!DOCTYPE html>
      <html>
        <head>
          <title>${videoInfo.title} - Instructions</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .slide { page-break-after: always; margin-bottom: 40px; }
            .slide:last-child { page-break-after: auto; }
            .slide-number { color: #666; font-size: 14px; }
            .slide-title { font-size: 24px; font-weight: bold; margin: 10px 0; }
            .slide-description { font-size: 16px; line-height: 1.5; margin-bottom: 20px; }
            .slide-notes { font-size: 14px; color: #666; font-style: italic; }
            .header { text-align: center; margin-bottom: 40px; }
            .video-title { font-size: 28px; font-weight: bold; }
            .video-info { color: #666; margin-top: 10px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="video-title">${videoInfo.title}</div>
            <div class="video-info">Step-by-step Instructions</div>
          </div>
          ${instructions.map((step, index) => `
            <div class="slide">
              <div class="slide-number">Step ${index + 1} of ${instructions.length}</div>
              <div class="slide-title">${step.title}</div>
              <div class="slide-description">${step.description}</div>
              ${step.notes ? `<div class="slide-notes">Notes: ${step.notes}</div>` : ''}
            </div>
          `).join('')}
        </body>
      </html>
    `;
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
  }, [instructions, videoInfo.title]);

  const currentStep = instructions[state.currentSlide];
  const progress = ((state.currentSlide + 1) / instructions.length) * 100;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
      <div className="bg-slate-900 rounded-lg shadow-2xl w-full max-w-4xl h-full max-h-[90vh] flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-slate-700">
          <div className="flex-1">
            <h2 className="text-lg font-semibold text-white truncate">
              {videoInfo.title}
            </h2>
            <div className="text-sm text-slate-400">
              Step {state.currentSlide + 1} of {instructions.length}
            </div>
          </div>
          <div className="flex items-center gap-2">
            {/* Auto-play controls */}
            <button
              onClick={toggleAutoPlay}
              className={`p-2 rounded-md transition-colors ${
                state.isAutoPlay 
                  ? 'bg-sky-600 text-white hover:bg-sky-700' 
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
              title={state.isAutoPlay ? 'Pause auto-play' : 'Start auto-play'}
            >
              {state.isAutoPlay ? (
                <PauseIcon className="w-4 h-4" />
              ) : (
                <PlayIcon className="w-4 h-4" />
              )}
            </button>
            
            {/* Export options */}
            <button
              onClick={exportSlides}
              className="p-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 transition-colors"
              title="Export as text file"
            >
              <DocumentArrowDownIcon className="w-4 h-4" />
            </button>
            
            <button
              onClick={printSlides}
              className="p-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 transition-colors"
              title="Print slides"
            >
              <PrinterIcon className="w-4 h-4" />
            </button>
            
            {/* Close button */}
            <button
              onClick={onClose}
              className="p-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 transition-colors"
              title="Close (Esc)"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Progress bar */}
        <ProgressBar 
          progress={progress} 
          size="sm" 
          color="primary" 
          showPercentage={false}
          animated={false}
          className="bg-slate-800"
        />

        {/* Main content */}
        <div className="flex-1 flex">
          {/* Slide content */}
          <div className="flex-1 p-8 flex flex-col justify-center">
            <div className="max-w-3xl mx-auto">
              <div className="text-sm text-sky-400 mb-4 font-medium">
                Step {state.currentSlide + 1}
              </div>
              
              <h3 className="text-3xl font-bold text-white mb-6 leading-tight">
                {currentStep.title}
              </h3>
              
              <div className="text-lg text-slate-300 leading-relaxed mb-6">
                {currentStep.description}
              </div>
              
              {currentStep.notes && state.showNotes && (
                <div className="bg-slate-800 border border-slate-700 rounded-lg p-4">
                  <div className="text-sm font-medium text-slate-400 mb-2">Notes:</div>
                  <div className="text-sm text-slate-300">
                    {currentStep.notes}
                  </div>
                </div>
              )}
              
              {currentStep.notes && (
                <button
                  onClick={() => setState(prev => ({ ...prev, showNotes: !prev.showNotes }))}
                  className="text-sm text-sky-400 hover:text-sky-300 transition-colors"
                >
                  {state.showNotes ? 'Hide Notes' : 'Show Notes'}
                </button>
              )}
            </div>
          </div>

          {/* Slide navigation sidebar */}
          <div className="w-64 bg-slate-800 border-l border-slate-700 p-4 overflow-y-auto">
            <div className="text-sm font-medium text-slate-400 mb-3">All Steps</div>
            <div className="space-y-2">
              {instructions.map((step, index) => (
                <button
                  key={step.id}
                  onClick={() => goToSlide(index)}
                  className={`w-full text-left p-3 rounded-md transition-colors ${
                    index === state.currentSlide
                      ? 'bg-sky-600 text-white'
                      : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
                  }`}
                >
                  <div className="text-xs opacity-75 mb-1">
                    Step {index + 1}
                  </div>
                  <div className="text-sm font-medium line-clamp-2">
                    {step.title}
                  </div>
                </button>
              ))}
            </div>
          </div>
        </div>

        {/* Footer controls */}
        <div className="flex items-center justify-between p-4 border-t border-slate-700">
          <button
            onClick={goToPreviousSlide}
            disabled={state.currentSlide === 0}
            className="flex items-center px-4 py-2 bg-slate-700 text-slate-300 rounded-md hover:bg-slate-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <ChevronLeftIcon className="w-4 h-4 mr-2" />
            Previous
          </button>

          <div className="flex items-center gap-4">
            {/* Auto-play interval control */}
            {state.isAutoPlay && (
              <div className="flex items-center gap-2 text-sm text-slate-400">
                <span>Interval:</span>
                <select
                  value={state.autoPlayInterval}
                  onChange={(e) => setState(prev => ({ ...prev, autoPlayInterval: parseInt(e.target.value) }))}
                  className="bg-slate-700 text-slate-300 rounded px-2 py-1 text-sm"
                >
                  <option value={3}>3s</option>
                  <option value={5}>5s</option>
                  <option value={10}>10s</option>
                  <option value={15}>15s</option>
                </select>
              </div>
            )}
            
            {/* Keyboard shortcuts hint */}
            <div className="text-xs text-slate-500">
              Use ← → Space keys to navigate
            </div>
          </div>

          <button
            onClick={goToNextSlide}
            disabled={state.currentSlide === instructions.length - 1}
            className="flex items-center px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            Next
            <ChevronRightIcon className="w-4 h-4 ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default InstructionSlideViewer;