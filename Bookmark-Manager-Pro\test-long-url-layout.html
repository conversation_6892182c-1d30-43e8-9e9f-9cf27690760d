<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Long URL Layout Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .bookmark-card {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 12px;
            padding: 16px;
            margin: 16px 0;
            max-width: 300px;
        }
        
        .bookmark-title {
            font-weight: 500;
            margin-bottom: 8px;
            word-break: break-word;
            font-size: 0.875rem;
            line-height: 1.4;
        }
        
        .bookmark-url {
            font-size: 0.75rem;
            color: #888;
            word-break: break-all;
            margin-bottom: 8px;
            opacity: 0.8;
            /* CRITICAL: Prevent layout overflow from long URLs */
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            /* Allow breaking on hyphens and slashes for better wrapping */
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
        }
        
        .bookmark-url-redesigned {
            font-size: 0.875rem;
            color: #888;
            font-weight: 500;
            padding: 4px 8px;
            background-color: #333;
            border-radius: 4px;
            border: 1px solid #555;
            width: fit-content;
            /* CRITICAL: Prevent layout overflow from long URLs */
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            word-break: break-all;
            overflow-wrap: break-word;
            min-width: 0;
        }
        
        .card-style .bookmark-url-redesigned {
            font-size: 0.6rem;
            padding: 1px 4px;
            background: #333;
            border-radius: 2px;
            width: fit-content;
            max-width: 100%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            /* CRITICAL: Enhanced URL handling for very long URLs like dodi-repacks.site */
            word-break: break-all;
            overflow-wrap: break-word;
            /* Ensure container doesn't expand beyond bounds */
            min-width: 0;
            flex-shrink: 1;
        }
        
        .test-section {
            margin: 32px 0;
            padding: 16px;
            background: #333;
            border-radius: 8px;
        }
        
        .test-title {
            color: #4ade80;
            font-weight: 600;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Long URL Layout Test</h1>
        <p>Testing layout fixes for the problematic URL: <code>https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/</code></p>
        
        <div class="test-section">
            <div class="test-title">Test 1: Standard bookmark-url class</div>
            <div class="bookmark-card">
                <div class="bookmark-title">Marvel's Spider-Man: Miles Morales</div>
                <div class="bookmark-url">https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/</div>
                <p>This should truncate with ellipsis and not break the layout.</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 2: bookmark-url-redesigned class</div>
            <div class="bookmark-card">
                <div class="bookmark-title">Marvel's Spider-Man: Miles Morales</div>
                <div class="bookmark-url-redesigned">https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/</div>
                <p>This should display in a styled container with proper truncation.</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 3: Card-style compact URL</div>
            <div class="bookmark-card card-style">
                <div class="bookmark-title">Marvel's Spider-Man: Miles Morales</div>
                <div class="bookmark-url-redesigned">https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/</div>
                <p>This should be very compact and not overflow the card.</p>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 4: Domain extraction simulation</div>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px;">
                <div>
                    <h4 style="color: #ef4444; margin-bottom: 8px;">❌ Before (Original Logic)</h4>
                    <div class="bookmark-card">
                        <div class="bookmark-title">Marvel's Spider-Man: Miles Morales</div>
                        <div class="bookmark-url">dodi-repacks.site</div>
                        <p style="font-size: 0.7rem; color: #888;">Only shows hostname, loses context</p>
                    </div>
                </div>
                <div>
                    <h4 style="color: #22c55e; margin-bottom: 8px;">✅ After (Improved Logic)</h4>
                    <div class="bookmark-card">
                        <div class="bookmark-title">Marvel's Spider-Man: Miles Morales</div>
                        <div class="bookmark-url" id="smart-domain">dodi-repacks.site/1479-marvels-spi...</div>
                        <p style="font-size: 0.7rem; color: #888;">Shows domain + truncated path for context</p>
                    </div>
                </div>
            </div>
            <div style="margin-top: 16px; padding: 12px; background: #444; border-radius: 6px;">
                <strong>Smart Domain Extraction Logic:</strong>
                <ul style="margin: 8px 0; padding-left: 20px; font-size: 0.8rem;">
                    <li>If path > 20 chars: show domain + truncated first path segment</li>
                    <li>If first path > 15 chars: truncate with ellipsis</li>
                    <li>Fallback: show hostname only</li>
                    <li>Invalid URLs: truncate at 30 chars</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <div class="test-title">Test 5: Multiple long URLs in grid</div>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 16px;">
                <div class="bookmark-card">
                    <div class="bookmark-title">Game 1</div>
                    <div class="bookmark-url">https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/</div>
                </div>
                <div class="bookmark-card">
                    <div class="bookmark-title">Game 2</div>
                    <div class="bookmark-url">https://dodi-repacks.site/1480-another-very-long-game-title-with-version-numbers-v2-3456-7-8-multi25/</div>
                </div>
                <div class="bookmark-card">
                    <div class="bookmark-title">Game 3</div>
                    <div class="bookmark-url">https://dodi-repacks.site/1481-yet-another-extremely-long-game-title-that-could-break-layout-v3-9999-0-1-multi30/</div>
                </div>
            </div>
            <p>Grid layout should remain intact with no horizontal overflow.</p>
        </div>
    </div>
    
    <script>
        // Smart domain extraction logic (matches BookmarkCard.tsx)
        function extractSmartDomain(url) {
            try {
                const urlObj = new URL(url);
                const hostname = urlObj.hostname.replace('www.', '');

                // For very long URLs like dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/
                // Show domain + truncated path to prevent layout issues
                if (urlObj.pathname && urlObj.pathname.length > 20) {
                    const pathParts = urlObj.pathname.split('/').filter(Boolean);
                    if (pathParts.length > 0) {
                        const firstPath = pathParts[0];
                        if (firstPath.length > 15) {
                            return `${hostname}/${firstPath.substring(0, 15)}...`;
                        }
                        return `${hostname}/${firstPath}`;
                    }
                }

                return hostname;
            } catch {
                // Fallback for invalid URLs - truncate if too long
                const truncated = url.length > 30 ? url.substring(0, 30) + '...' : url;
                return truncated;
            }
        }

        // Test that no horizontal scrolling is needed
        window.addEventListener('load', () => {
            const body = document.body;
            const hasHorizontalScroll = body.scrollWidth > body.clientWidth;

            if (hasHorizontalScroll) {
                console.warn('❌ Horizontal scroll detected! Layout fix may need adjustment.');
                document.body.style.borderTop = '5px solid red';
            } else {
                console.log('✅ No horizontal scroll detected. Layout fix working correctly.');
                document.body.style.borderTop = '5px solid green';
            }

            // Demonstrate the smart domain extraction
            const testUrl = 'https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/';
            const smartDomainElement = document.getElementById('smart-domain');
            if (smartDomainElement) {
                const extractedDomain = extractSmartDomain(testUrl);
                smartDomainElement.textContent = extractedDomain;
                console.log('🎯 Smart domain extraction result:', extractedDomain);
            }

            // Test with multiple URLs
            const testUrls = [
                'https://dodi-repacks.site/1479-marvels-spider-man-miles-morales-v1-1116-0-0-multi23/',
                'https://github.com/user/repo',
                'https://very-long-domain-name.com/extremely/long/path/that/goes/on/and/on',
                'invalid-url-test',
                'https://short.com/a'
            ];

            console.log('🧪 Domain extraction tests:');
            testUrls.forEach(url => {
                const result = extractSmartDomain(url);
                console.log(`  ${url} → ${result}`);
            });
        });
    </script>
</body>
</html>
