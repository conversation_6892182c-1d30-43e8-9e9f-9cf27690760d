<!doctype html>
<html lang="en">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Created by MiniMax Agent</title>
  <script type="module" crossorigin src="/assets/index-CSh4D5b6.js"></script>
  <link rel="stylesheet" crossorigin href="/assets/index-CY2Co3Dt.css">
</head>

<body>
  <div id="root"></div>

<style>
#minimax-floating-ball {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 10px 12px;
  background: #222222;
  border-radius: 12px;
  display: flex;
  align-items: center;
  color: #F8F8F8;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  z-index: 9999;
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
}

#minimax-floating-ball:hover {
  transform: translateY(-2px);
  background: #383838;
}

.minimax-ball-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.minimax-logo-wave {
  width: 26px;
  height: 22px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='26' height='22' viewBox='0 0 26 22' fill='none'%3E%3Cg clip-path='url(%23clip0_3442_102412)'%3E%3Cpath d='M12.8405 14.6775C12.8405 14.9897 13.0932 15.2424 13.4055 15.2424C13.7178 15.2424 13.9705 14.9897 13.9705 14.6775V2.98254C13.9705 1.88957 13.0809 1 11.9879 1C10.895 1 10.0054 1.88957 10.0054 2.98254V11.566V17.1068C10.0054 17.5773 9.62327 17.9594 9.1528 17.9594C8.68233 17.9594 8.30021 17.5773 8.30021 17.1068V8.04469C8.30021 6.95172 7.41063 6.06215 6.31767 6.06215C5.22471 6.06215 4.33513 6.95172 4.33513 8.04469V11.8855C4.33513 12.3559 3.953 12.7381 3.48254 12.7381C3.01207 12.7381 2.62994 12.3559 2.62994 11.8855V10.4936C2.62994 10.1813 2.37725 9.92861 2.06497 9.92861C1.7527 9.92861 1.5 10.1813 1.5 10.4936V11.8855C1.5 12.9784 2.38957 13.868 3.48254 13.868C4.5755 13.868 5.46508 12.9784 5.46508 11.8855V8.04469C5.46508 7.57422 5.8472 7.19209 6.31767 7.19209C6.78814 7.19209 7.17026 7.57422 7.17026 8.04469V17.1068C7.17026 18.1998 8.05984 19.0894 9.1528 19.0894C10.2458 19.0894 11.1353 18.1998 11.1353 17.1068V2.98254C11.1353 2.51207 11.5175 2.12994 11.9879 2.12994C12.4584 2.12994 12.8405 2.51207 12.8405 2.98254V14.6775Z' fill='%23F8F8F8'/%3E%3Cpath d='M23.3278 6.06215C22.2348 6.06215 21.3452 6.95172 21.3452 8.04469V15.6143C21.3452 16.0847 20.9631 16.4669 20.4926 16.4669C20.0222 16.4669 19.6401 16.0847 19.6401 15.6143V2.98254C19.6401 1.88957 18.7505 1 17.6575 1C16.5645 1 15.675 1.88957 15.675 2.98254V19.0175C15.675 19.4879 15.2928 19.8701 14.8224 19.8701C14.3519 19.8701 13.9698 19.4879 13.9698 19.0175V17.0329C13.9698 16.7206 13.7171 16.4679 13.4048 16.4679C13.0925 16.4679 12.8398 16.7206 12.8398 17.0329V19.0175C12.8398 20.1104 13.7294 21 14.8224 21C15.9153 21 16.8049 20.1104 16.8049 19.0175V2.98254C16.8049 2.51207 17.187 2.12994 17.6575 2.12994C18.128 2.12994 18.5101 2.51207 18.5101 2.98254V15.6143C18.5101 16.7072 19.3997 17.5968 20.4926 17.5968C21.5856 17.5968 22.4752 16.7072 22.4752 15.6143V8.04469C22.4752 7.57422 22.8573 7.19209 23.3278 7.19209C23.7982 7.19209 24.1804 7.57422 24.1804 8.04469V14.6775C24.1804 14.9897 24.4331 15.2424 24.7453 15.2424C25.0576 15.2424 25.3103 14.9897 25.3103 14.6775V8.04469C25.3103 6.95172 24.4207 6.06215 23.3278 6.06215Z' fill='%23F8F8F8'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_3442_102412'%3E%3Crect width='25' height='22' fill='white' transform='translate(0.5)'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
}

.minimax-ball-text {
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

.minimax-close-icon {
  margin-left: 8px;
  font-size: 16px;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.minimax-close-icon:hover {
  opacity: 1;
}
</style>
<div id="minimax-floating-ball">
  <div class="minimax-ball-content">
    <div class="minimax-logo-wave"></div>
    <span class="minimax-ball-text">Created by MiniMax Agent</span>
  </div>
  <div class="minimax-close-icon">×</div>
</div>
<script>
// Initialize floating ball functionality
function initFloatingBall() {
  const ball = document.getElementById('minimax-floating-ball');
  if (!ball) return;

  // Initial animation
  ball.style.opacity = '0';
  ball.style.transform = 'translateY(20px)';

  setTimeout(() => {
    ball.style.opacity = '1';
    ball.style.transform = 'translateY(0)';
  }, 500);

  // Handle logo click
  const ballContent = ball.querySelector('.minimax-ball-content');
  ballContent.addEventListener('click', function (e) {
    e.stopPropagation();
    window.open('https://agent.minimax.io/agent', '_blank');
    ball.style.transform = 'scale(0.95)';
    setTimeout(() => {
      ball.style.transform = 'scale(1)';
    }, 100);
  });

  // Handle close button click
  const closeIcon = ball.querySelector('.minimax-close-icon');
  closeIcon.addEventListener('click', function (e) {
    e.stopPropagation();
    ball.style.opacity = '0';
    ball.style.transform = 'translateY(20px)';

    setTimeout(() => {
      ball.style.display = 'none';
    }, 300);
  });
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', initFloatingBall); 
</script>

</body>

</html>