{"timestamp": "2025-07-10T16:22:45.097Z", "summary": {"total": 18, "passed": 12, "failed": 6, "successRate": 66.7}, "results": [{"name": "Component File Structure Validation", "status": "FAIL", "error": "Component export not found"}, {"name": "Domain Extraction Logic", "status": "PASS", "error": null}, {"name": "Domain Grouping Algorithm", "status": "FAIL", "error": "Ungrouped bookmarks count incorrect"}, {"name": "Subdomain Grouping Feature", "status": "PASS", "error": null}, {"name": "Minimum Bookmarks Per Domain Threshold", "status": "PASS", "error": null}, {"name": "Invalid URL Error Handling", "status": "FAIL", "error": "Invalid URLs should be in ungrouped"}, {"name": "Performance with Large Dataset", "status": "PASS", "error": null}, {"name": "Component Props Interface", "status": "FAIL", "error": "Required prop 'onOrganize' not found in component"}, {"name": "State Management Implementation", "status": "PASS", "error": null}, {"name": "Event Handlers Implementation", "status": "PASS", "error": null}, {"name": "Accessibility Implementation", "status": "PASS", "error": null}, {"name": "CSS Classes and Styling", "status": "PASS", "error": null}, {"name": "TypeScript Type Safety", "status": "PASS", "error": null}, {"name": "Component Export Structure", "status": "FAIL", "error": "Default export not found"}, {"name": "Bookmark Type Integration", "status": "FAIL", "error": "Bookmark property 'url' not referenced"}, {"name": "Organization Preview Generation", "status": "PASS", "error": null}, {"name": "Configuration Options Validation", "status": "PASS", "error": null}, {"name": "Memory Management and Cleanup", "status": "PASS", "error": null}]}