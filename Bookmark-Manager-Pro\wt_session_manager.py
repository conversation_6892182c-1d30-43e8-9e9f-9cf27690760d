import subprocess
import json
import os
import psutil
from datetime import datetime, timedelta
from pathlib import Path
import argparse
import sys

class WindowsTerminalSessionManager:
    def __init__(self, config_path="terminal_rules_enhanced.json"):
        self.config = self.load_config(config_path)
        self.session_storage = Path(self.config["session_management"]["session_storage_path"])
        self.session_storage.mkdir(exist_ok=True)
        self.wt_executable = self.config["windows_terminal"]["executable_path"]
    
    def load_config(self, config_path):
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            # Return default configuration if file doesn't exist
            return {
                "session_management": {
                    "session_storage_path": "./.terminal_sessions",
                    "auto_cleanup_hours": 24,
                    "session_recovery_enabled": True
                },
                "windows_terminal": {
                    "executable_path": "wt.exe",
                    "default_profile": "PowerShell",
                    "session_persistence": True,
                    "max_parallel_sessions": 4,
                    "tab_naming_pattern": "Test-{type}-{timestamp}"
                }
            }
    
    def create_session(self, session_name, test_type=None, persistent=False, command=None):
        """Create a new Windows Terminal session."""
        profile = self.config["windows_terminal"]["default_profile"]
        timestamp = datetime.now().strftime('%H%M%S')
        tab_name = f"Test-{test_type or 'general'}-{timestamp}"
        
        # Default command if none provided
        if not command:
            command = f"cd '{os.getcwd()}'; Write-Host 'Session {session_name} created - Ready for testing'"
        
        # Build Windows Terminal command
        wt_cmd = [
            self.wt_executable,
            "new-tab",
            "--profile", profile,
            "--title", tab_name,
            "powershell.exe", "-NoExit", "-Command",
            command
        ]
        
        try:
            # Execute and track session
            process = subprocess.Popen(wt_cmd)
            session_info = {
                "pid": process.pid,
                "tab_name": tab_name,
                "test_type": test_type,
                "created": datetime.now().isoformat(),
                "persistent": persistent,
                "command": command
            }
            
            self.save_session_info(session_name, session_info)
            print(f"Created session '{session_name}' with PID {process.pid}")
            return process.pid
            
        except FileNotFoundError:
            print(f"Error: Windows Terminal not found at {self.wt_executable}")
            print("Please ensure Windows Terminal is installed and accessible.")
            return None
        except Exception as e:
            print(f"Error creating session: {e}")
            return None
    
    def save_session_info(self, session_name, session_info):
        """Save session information to storage."""
        session_file = self.session_storage / f"{session_name}.json"
        with open(session_file, 'w') as f:
            json.dump(session_info, f, indent=2)
    
    def load_session_info(self, session_name):
        """Load session information from storage."""
        session_file = self.session_storage / f"{session_name}.json"
        if session_file.exists():
            with open(session_file, 'r') as f:
                return json.load(f)
        return None
    
    def run_parallel_sessions(self, test_types, verbose=False):
        """Run multiple test sessions in parallel."""
        max_sessions = self.config["windows_terminal"]["max_parallel_sessions"]
        if len(test_types) > max_sessions:
            print(f"Warning: Limiting to {max_sessions} parallel sessions")
            test_types = test_types[:max_sessions]
        
        sessions = []
        for test_type in test_types:
            session_name = f"{test_type}-{datetime.now().strftime('%H%M%S')}"
            
            # Create command for running tests
            test_command = f"cd '{os.getcwd()}'; .\\run_tests_with_terminal.ps1 -TestType '{test_type}'"
            if verbose:
                test_command += " -Verbose"
            
            pid = self.create_session(session_name, test_type, command=test_command)
            if pid:
                sessions.append((session_name, pid, test_type))
        
        return sessions
    
    def list_sessions(self):
        """List all active sessions."""
        active_sessions = []
        
        for session_file in self.session_storage.glob("*.json"):
            session_name = session_file.stem
            session_info = self.load_session_info(session_name)
            
            if session_info and psutil.pid_exists(session_info["pid"]):
                active_sessions.append({
                    "name": session_name,
                    "pid": session_info["pid"],
                    "type": session_info.get("test_type", "unknown"),
                    "created": session_info["created"],
                    "tab_name": session_info["tab_name"]
                })
        
        return active_sessions
    
    def kill_session(self, session_name):
        """Kill a specific session."""
        session_info = self.load_session_info(session_name)
        if not session_info:
            print(f"Session '{session_name}' not found")
            return False
        
        try:
            if psutil.pid_exists(session_info["pid"]):
                process = psutil.Process(session_info["pid"])
                process.terminate()
                print(f"Terminated session '{session_name}' (PID: {session_info['pid']})")
            
            # Remove session file
            session_file = self.session_storage / f"{session_name}.json"
            if session_file.exists():
                session_file.unlink()
            
            return True
            
        except Exception as e:
            print(f"Error killing session: {e}")
            return False
    
    def attach_to_session(self, session_name):
        """Attach to an existing session (focus the tab)."""
        session_info = self.load_session_info(session_name)
        if not session_info:
            print(f"Session '{session_name}' not found")
            return False
        
        if not psutil.pid_exists(session_info["pid"]):
            print(f"Session '{session_name}' is no longer active")
            return False
        
        print(f"Session '{session_name}' is active in tab '{session_info['tab_name']}'")
        print("Please manually switch to the appropriate Windows Terminal tab.")
        return True
    
    def run_in_session(self, session_name, command):
        """Run a command in an existing session."""
        session_info = self.load_session_info(session_name)
        if not session_info:
            print(f"Session '{session_name}' not found")
            return False
        
        if not psutil.pid_exists(session_info["pid"]):
            print(f"Session '{session_name}' is no longer active")
            return False
        
        # For Windows Terminal, we'll create a new tab with the command
        # since direct command injection into existing tabs is complex
        new_session_name = f"{session_name}-cmd-{datetime.now().strftime('%H%M%S')}"
        return self.create_session(new_session_name, command=command)
    
    def cleanup_stale_sessions(self):
        """Clean up stale session files."""
        max_age_hours = self.config["session_management"]["auto_cleanup_hours"]
        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        for session_file in self.session_storage.glob("*.json"):
            try:
                session_info = self.load_session_info(session_file.stem)
                if session_info:
                    created_time = datetime.fromisoformat(session_info["created"])
                    
                    # Clean up if old and process no longer exists
                    if (created_time < cutoff_time and 
                        not psutil.pid_exists(session_info["pid"])):
                        session_file.unlink()
                        cleaned_count += 1
                        print(f"Cleaned up stale session: {session_file.stem}")
            except Exception as e:
                print(f"Error processing session file {session_file}: {e}")
        
        print(f"Cleaned up {cleaned_count} stale sessions")
        return cleaned_count

def main():
    parser = argparse.ArgumentParser(description="Windows Terminal Session Manager for Testing")
    parser.add_argument("--create-session", help="Create a new session with given name")
    parser.add_argument("--type", help="Test type for the session")
    parser.add_argument("--persistent", action="store_true", help="Make session persistent")
    parser.add_argument("--parallel", action="store_true", help="Run parallel sessions")
    parser.add_argument("--sessions", help="Comma-separated list of session types for parallel execution")
    parser.add_argument("--list-sessions", action="store_true", help="List all active sessions")
    parser.add_argument("--attach", help="Attach to existing session")
    parser.add_argument("--kill-session", help="Kill specific session")
    parser.add_argument("--run-in-session", help="Run command in existing session")
    parser.add_argument("--command", help="Command to run in session")
    parser.add_argument("--cleanup", action="store_true", help="Clean up stale sessions")
    parser.add_argument("--verbose", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    manager = WindowsTerminalSessionManager()
    
    if args.create_session:
        manager.create_session(
            args.create_session, 
            test_type=args.type, 
            persistent=args.persistent,
            command=args.command
        )
    
    elif args.parallel and args.sessions:
        test_types = [t.strip() for t in args.sessions.split(",")]
        sessions = manager.run_parallel_sessions(test_types, verbose=args.verbose)
        print(f"Started {len(sessions)} parallel sessions:")
        for name, pid, test_type in sessions:
            print(f"  - {name} ({test_type}): PID {pid}")
    
    elif args.list_sessions:
        sessions = manager.list_sessions()
        if sessions:
            print("Active sessions:")
            for session in sessions:
                print(f"  - {session['name']} ({session['type']}): PID {session['pid']}, Created: {session['created']}")
        else:
            print("No active sessions found")
    
    elif args.attach:
        manager.attach_to_session(args.attach)
    
    elif args.kill_session:
        manager.kill_session(args.kill_session)
    
    elif args.run_in_session:
        if not args.command:
            print("Error: --command is required when using --run-in-session")
            sys.exit(1)
        manager.run_in_session(args.run_in_session, args.command)
    
    elif args.cleanup:
        manager.cleanup_stale_sessions()
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()