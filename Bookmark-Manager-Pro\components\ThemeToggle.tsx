import React, { useState, useEffect } from 'react';

interface ThemeToggleProps {
  className?: string;
}

const ThemeToggle: React.FC<ThemeToggleProps> = ({ className = '' }) => {
  const [theme, setTheme] = useState<'light' | 'dark' | 'system'>('system');
  const [mounted, setMounted] = useState(false);

  // Handle mounting to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
    const savedTheme = localStorage.getItem('theme') as 'light' | 'dark' | 'system' || 'system';
    setTheme(savedTheme);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (newTheme: 'light' | 'dark' | 'system') => {
    const root = document.documentElement;
    
    if (newTheme === 'system') {
      root.removeAttribute('data-theme');
      // Let CSS media query handle system preference
    } else {
      root.setAttribute('data-theme', newTheme);
    }
  };

  const handleThemeChange = (newTheme: 'light' | 'dark' | 'system') => {
    setTheme(newTheme);
    localStorage.setItem('theme', newTheme);
    applyTheme(newTheme);
  };

  if (!mounted) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-8 h-8 bg-gray-200 rounded animate-pulse"></div>
      </div>
    );
  }

  const getThemeIcon = (themeType: 'light' | 'dark' | 'system') => {
    switch (themeType) {
      case 'light': return '☀️';
      case 'dark': return '🌙';
      case 'system': return '🌓';
      default: return '🌓';
    }
  };

  return (
    <div className={`theme-toggle-wrapper ${className}`}>
      <div className="theme-toggle-container">
        {(['system', 'light', 'dark'] as const).map((themeOption) => (
          <button
            key={themeOption}
            onClick={() => handleThemeChange(themeOption)}
            className={`theme-toggle-button ${
              theme === themeOption ? 'theme-toggle-active' : ''
            }`}
            title={`Switch to ${themeOption} theme`}
            aria-label={`Switch to ${themeOption} theme`}
            aria-pressed={theme === themeOption}
          >
            <span className="theme-toggle-icon">{getThemeIcon(themeOption)}</span>
            <span className="theme-toggle-label">
              {themeOption.charAt(0).toUpperCase() + themeOption.slice(1)}
            </span>
          </button>
        ))}
      </div>
    </div>
  );
};

// Add CSS styles
const styles = `
.theme-toggle-wrapper {
  display: flex;
  align-items: center;
}

.theme-toggle-container {
  display: flex;
  background: var(--background-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: 2px;
  gap: 2px;
  transition: all 0.3s ease;
}

.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--text-secondary);
  font-size: var(--text-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 0;
}

.theme-toggle-button:hover {
  background: var(--surface-elevated);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.theme-toggle-active {
  background: var(--primary-500) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.theme-toggle-icon {
  font-size: 14px;
  line-height: 1;
}

.theme-toggle-label {
  font-size: var(--text-xs);
  font-weight: var(--font-weight-medium);
}

@media (max-width: 640px) {
  .theme-toggle-label {
    display: none;
  }
  
  .theme-toggle-button {
    padding: 8px;
    min-width: 36px;
    justify-content: center;
  }
}
`;

// Inject styles
 if (typeof document !== 'undefined' && !document.getElementById('theme-toggle-styles')) {
   const styleSheet = document.createElement('style');
   styleSheet.id = 'theme-toggle-styles';
   styleSheet.textContent = styles;
   document.head.appendChild(styleSheet);
 }
 
 export default ThemeToggle;