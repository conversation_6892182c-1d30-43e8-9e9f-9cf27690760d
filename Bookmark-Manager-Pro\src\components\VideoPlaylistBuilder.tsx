/**
 * VIDEO PLAYLIST BUILDER
 * Advanced video selection and playlist creation for large video collections
 * Optimized for handling 1000+ videos with smart filtering and batch operations
 */

import React, { useState, useEffect, useMemo } from 'react'
import {
  Search, Filter, Video, Youtube, Play, Plus, Minus, Check, X,
  ChevronDown, ChevronUp, Clock, Calendar, Star, Eye, Tag,
  Shuffle, List, Grid, RotateCcw, Download, Upload
} from 'lucide-react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import type { Bookmark } from '../types'

interface VideoPlaylistBuilderProps {
  isOpen: boolean
  onClose: () => void
  onPlaylistCreated: (playlist: any) => void
}

export const VideoPlaylistBuilder: React.FC<VideoPlaylistBuilderProps> = ({
  isOpen,
  onClose,
  onPlaylistCreated
}) => {
  const { bookmarks } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // Filter to get only video bookmarks
  const videoBookmarks = useMemo(() => {
    return bookmarks.filter(bookmark => {
      const url = bookmark.url.toLowerCase()
      return url.includes('youtube.com') || 
             url.includes('youtu.be') ||
             url.includes('vimeo.com') ||
             url.includes('video') ||
             bookmark.title.toLowerCase().includes('video')
    })
  }, [bookmarks])

  // State management
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedVideos, setSelectedVideos] = useState<Set<string>>(new Set())
  const [sortBy, setSortBy] = useState<'title' | 'date' | 'collection' | 'visits'>('title')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [filterBy, setFilterBy] = useState<'all' | 'youtube' | 'vimeo' | 'other'>('all')
  const [collectionFilter, setCollectionFilter] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list')
  const [playlistName, setPlaylistName] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage] = useState(50) // Manageable chunks for large collections

  // Get unique collections from video bookmarks
  const videoCollections = useMemo(() => {
    const collections = new Set(videoBookmarks.map(v => v.collection).filter(Boolean))
    return Array.from(collections).sort()
  }, [videoBookmarks])

  // Advanced filtering and sorting
  const filteredAndSortedVideos = useMemo(() => {
    let filtered = [...videoBookmarks]

    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(video =>
        video.title.toLowerCase().includes(query) ||
        video.description?.toLowerCase().includes(query) ||
        video.url.toLowerCase().includes(query) ||
        video.tags?.some(tag => tag.toLowerCase().includes(query))
      )
    }

    // Platform filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(video => {
        const url = video.url.toLowerCase()
        switch (filterBy) {
          case 'youtube':
            return url.includes('youtube.com') || url.includes('youtu.be')
          case 'vimeo':
            return url.includes('vimeo.com')
          case 'other':
            return !url.includes('youtube.com') && !url.includes('youtu.be') && !url.includes('vimeo.com')
          default:
            return true
        }
      })
    }

    // Collection filter
    if (collectionFilter !== 'all') {
      filtered = filtered.filter(video => video.collection === collectionFilter)
    }

    // Sorting
    filtered.sort((a, b) => {
      let comparison = 0
      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title)
          break
        case 'date':
          comparison = new Date(a.dateAdded || 0).getTime() - new Date(b.dateAdded || 0).getTime()
          break
        case 'collection':
          comparison = (a.collection || '').localeCompare(b.collection || '')
          break
        case 'visits':
          comparison = (a.visits || 0) - (b.visits || 0)
          break
      }
      return sortOrder === 'desc' ? -comparison : comparison
    })

    return filtered
  }, [videoBookmarks, searchQuery, filterBy, collectionFilter, sortBy, sortOrder])

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedVideos.length / itemsPerPage)
  const paginatedVideos = filteredAndSortedVideos.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  )

  // Selection handlers
  const toggleVideoSelection = (videoId: string) => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev)
      if (newSet.has(videoId)) {
        newSet.delete(videoId)
      } else {
        newSet.add(videoId)
      }
      return newSet
    })
  }

  const selectAllVisible = () => {
    setSelectedVideos(prev => {
      const newSet = new Set(prev)
      paginatedVideos.forEach(video => newSet.add(video.id))
      return newSet
    })
  }

  const clearSelection = () => {
    setSelectedVideos(new Set())
  }

  const selectByCollection = (collection: string) => {
    const collectionVideos = filteredAndSortedVideos.filter(v => v.collection === collection)
    setSelectedVideos(prev => {
      const newSet = new Set(prev)
      collectionVideos.forEach(video => newSet.add(video.id))
      return newSet
    })
  }

  // Playlist creation
  const createPlaylist = () => {
    if (selectedVideos.size === 0) {
      alert('Please select at least one video')
      return
    }

    const selectedVideoBookmarks = videoBookmarks.filter(v => selectedVideos.has(v.id))
    const playlist = {
      id: `video-playlist-${Date.now()}`,
      name: playlistName || `Video Playlist (${selectedVideos.size} videos)`,
      type: 'video',
      items: selectedVideoBookmarks.map(video => ({
        id: video.id,
        title: video.title,
        url: video.url,
        type: 'video' as const,
        thumbnail: video.favicon,
        collection: video.collection
      })),
      createdAt: new Date().toISOString(),
      totalVideos: selectedVideos.size
    }

    onPlaylistCreated(playlist)
    onClose()
  }

  // Platform detection
  const getPlatformIcon = (url: string) => {
    const urlLower = url.toLowerCase()
    if (urlLower.includes('youtube.com') || urlLower.includes('youtu.be')) {
      return '🎥'
    } else if (urlLower.includes('vimeo.com')) {
      return '📹'
    }
    return '🎬'
  }

  const getPlatformCounts = () => {
    const youtube = videoBookmarks.filter(v => 
      v.url.toLowerCase().includes('youtube.com') || v.url.toLowerCase().includes('youtu.be')
    ).length
    const vimeo = videoBookmarks.filter(v => 
      v.url.toLowerCase().includes('vimeo.com')
    ).length
    const other = videoBookmarks.length - youtube - vimeo
    return { youtube, vimeo, other }
  }

  const platformCounts = getPlatformCounts()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm">
      <div className={`
        w-11/12 max-w-6xl h-5/6 
        ${isModernTheme ? 'bg-black/90 border-white/20' : 'bg-white border-gray-200'}
        border rounded-xl shadow-2xl flex flex-col overflow-hidden
      `}>
        
        {/* Header */}
        <div className={`
          flex items-center justify-between p-6 border-b
          ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
        `}>
          <div>
            <h2 className={`text-xl font-semibold ${
              isModernTheme ? 'text-white' : 'text-gray-900'
            }`}>
              🎥 Video Playlist Builder
            </h2>
            <p className={`text-sm mt-1 ${
              isModernTheme ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {videoBookmarks.length} videos available • {selectedVideos.size} selected
            </p>
          </div>
          <button
            onClick={onClose}
            className={`p-2 rounded transition-colors ${
              isModernTheme
                ? 'text-white hover:bg-white/10'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
          >
            <X size={20} />
          </button>
        </div>

        {/* Controls */}
        <div className={`
          p-4 border-b space-y-4
          ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
        `}>
          
          {/* Search and Filters Row */}
          <div className="flex gap-4 items-center flex-wrap">
            {/* Search */}
            <div className="relative flex-1 min-w-64">
              <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search videos by title, description, or tags..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className={`
                  w-full pl-10 pr-4 py-2 rounded-lg border text-sm
                  ${isModernTheme 
                    ? 'bg-white/10 border-white/20 text-white placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900'
                  }
                  focus:outline-none focus:ring-2 focus:ring-purple-500
                `}
              />
            </div>

            {/* Platform Filter */}
            <select
              value={filterBy}
              onChange={(e) => setFilterBy(e.target.value as any)}
              className={`
                px-3 py-2 rounded-lg border text-sm
                ${isModernTheme 
                  ? 'bg-white/10 border-white/20 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
                }
              `}
            >
              <option value="all">All Platforms ({videoBookmarks.length})</option>
              <option value="youtube">YouTube ({platformCounts.youtube})</option>
              <option value="vimeo">Vimeo ({platformCounts.vimeo})</option>
              <option value="other">Other ({platformCounts.other})</option>
            </select>

            {/* Collection Filter */}
            <select
              value={collectionFilter}
              onChange={(e) => setCollectionFilter(e.target.value)}
              className={`
                px-3 py-2 rounded-lg border text-sm
                ${isModernTheme 
                  ? 'bg-white/10 border-white/20 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
                }
              `}
            >
              <option value="all">All Collections</option>
              {videoCollections.map(collection => (
                <option key={collection} value={collection}>
                  {collection} ({videoBookmarks.filter(v => v.collection === collection).length})
                </option>
              ))}
            </select>

            {/* Sort */}
            <select
              value={`${sortBy}-${sortOrder}`}
              onChange={(e) => {
                const [field, order] = e.target.value.split('-')
                setSortBy(field as any)
                setSortOrder(order as any)
              }}
              className={`
                px-3 py-2 rounded-lg border text-sm
                ${isModernTheme 
                  ? 'bg-white/10 border-white/20 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
                }
              `}
            >
              <option value="title-asc">Title A-Z</option>
              <option value="title-desc">Title Z-A</option>
              <option value="date-desc">Newest First</option>
              <option value="date-asc">Oldest First</option>
              <option value="collection-asc">Collection A-Z</option>
              <option value="visits-desc">Most Visited</option>
            </select>
          </div>

          {/* Selection Actions Row */}
          <div className="flex gap-2 items-center flex-wrap">
            <button
              onClick={selectAllVisible}
              className="px-3 py-1 text-white rounded text-sm transition-colors"
                style={{
                  backgroundColor: 'var(--accent-color)',
                  ':hover': { backgroundColor: 'rgba(var(--accent-color-rgb), 0.8)' }
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(var(--accent-color-rgb), 0.8)'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--accent-color)'}
            >
              Select Page ({paginatedVideos.length})
            </button>
            
            <button
              onClick={clearSelection}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Clear All
            </button>

            {videoCollections.slice(0, 3).map(collection => (
              <button
                key={collection}
                onClick={() => selectByCollection(collection)}
                className="px-3 py-1 text-white rounded text-sm transition-colors"
                style={{
                  backgroundColor: 'var(--accent-color)',
                  ':hover': { backgroundColor: 'rgba(var(--accent-color-rgb), 0.8)' }
                }}
                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(var(--accent-color-rgb), 0.8)'}
                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--accent-color)'}
              >
                Select {collection} ({videoBookmarks.filter(v => v.collection === collection).length})
              </button>
            ))}

            <div className={`ml-auto text-sm ${
              isModernTheme ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Showing {paginatedVideos.length} of {filteredAndSortedVideos.length} videos
            </div>
          </div>
        </div>

        {/* Video List */}
        <div className="flex-1 overflow-hidden flex flex-col">
          <div className="flex-1 overflow-y-auto p-4">
            <div className="space-y-2">
              {paginatedVideos.map(video => {
                const isSelected = selectedVideos.has(video.id)
                return (
                  <div
                    key={video.id}
                    className={`
                      flex items-center gap-3 p-3 rounded-lg border cursor-pointer transition-all
                      ${isSelected
                        ? isModernTheme
                          ? 'border'
                          : 'border'
                        : isModernTheme
                          ? 'hover:bg-white/5 border-white/10'
                          : 'hover:bg-gray-50 border-gray-200'
                      }
                    `}
                    style={isSelected ? {
                      backgroundColor: isModernTheme 
                        ? 'rgba(var(--accent-color-rgb), 0.2)' 
                        : 'rgba(var(--accent-color-rgb), 0.1)',
                      borderColor: isModernTheme 
                        ? 'rgba(var(--accent-color-rgb), 0.4)' 
                        : 'rgba(var(--accent-color-rgb), 0.3)'
                    } : undefined
                    `}
                    onClick={() => toggleVideoSelection(video.id)}
                  >
                    {/* Checkbox */}
                    <div className={`
                      w-5 h-5 rounded border-2 flex items-center justify-center
                      ${isSelected
                        ? 'border'
                        : isModernTheme
                          ? 'border-white/30'
                          : 'border-gray-300'
                      }
                    `}
                    style={isSelected ? {
                      backgroundColor: 'var(--accent-color)',
                      borderColor: 'var(--accent-color)'
                    } : undefined
                    `}>
                      {isSelected && <Check size={12} className="text-white" />}
                    </div>

                    {/* Platform Icon */}
                    <span className="text-lg">{getPlatformIcon(video.url)}</span>

                    {/* Video Info */}
                    <div className="flex-1 min-w-0">
                      <div className={`font-medium text-sm truncate ${
                        isModernTheme ? 'text-white' : 'text-gray-900'
                      }`}>
                        {video.title}
                      </div>
                      <div className={`text-xs truncate ${
                        isModernTheme ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {new URL(video.url).hostname} • {video.collection || 'No Collection'}
                      </div>
                    </div>

                    {/* Stats */}
                    <div className={`text-xs ${
                      isModernTheme ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {video.visits ? `${video.visits} views` : 'New'}
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className={`
              flex items-center justify-between p-4 border-t
              ${isModernTheme ? 'border-white/20' : 'border-gray-200'}
            `}>
              <button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
                className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                Previous
              </button>
              
              <span className={`text-sm ${
                isModernTheme ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Page {currentPage} of {totalPages}
              </span>
              
              <button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
                className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors disabled:opacity-50"
              >
                Next
              </button>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className={`
          p-4 border-t
          ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
        `}>
          <div className="flex items-center gap-4">
            <input
              type="text"
              placeholder="Playlist name (optional)"
              value={playlistName}
              onChange={(e) => setPlaylistName(e.target.value)}
              className={`
                flex-1 px-3 py-2 rounded-lg border text-sm
                ${isModernTheme 
                  ? 'bg-white/10 border-white/20 text-white placeholder-gray-400' 
                  : 'bg-white border-gray-300 text-gray-900'
                }
              `}
            />
            
            <button
              onClick={createPlaylist}
              disabled={selectedVideos.size === 0}
              className="px-6 py-2 text-white rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              style={{
                backgroundColor: 'var(--accent-color)',
                ':hover': { backgroundColor: 'rgba(var(--accent-color-rgb), 0.8)' }
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = 'rgba(var(--accent-color-rgb), 0.8)'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'var(--accent-color)'}
            >
              Create Playlist ({selectedVideos.size} videos)
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VideoPlaylistBuilder
