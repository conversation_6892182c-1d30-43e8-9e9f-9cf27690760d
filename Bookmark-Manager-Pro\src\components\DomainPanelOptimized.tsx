import React, { useState } from 'react'
import { CheckCircle, Eye, Globe, Network, Settings, X, Zap } from 'lucide-react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import '../styles/optimized-panels.css'

interface DomainPanelOptimizedProps {
  isOpen: boolean
  onClose: () => void
}

export const DomainPanelOptimized: React.FC<DomainPanelOptimizedProps> = ({ isOpen, onClose }) => {
  const { bookmarks, autoOrganizeBookmarks, previewAutoOrganize } = useBookmarks()
  const themeContext = useModernTheme()
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [platformRecognition, setPlatformRecognition] = useState(true)
  const [subdomainGrouping, setSubdomainGrouping] = useState(false)
  const [minBookmarksPerDomain, setMinBookmarksPerDomain] = useState(3)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [showResults, setShowResults] = useState(false)
  const [activeButton, setActiveButton] = useState<string | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showPreview, setShowPreview] = useState(false)
  const [previewData, setPreviewData] = useState<any>(null)

  // Safe access with multiple fallbacks
  const themeMode = themeContext?.themeMode || 'classic'
  const isModernTheme = themeMode === 'modern'

  if (!isOpen) return null

  const generatePreview = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []
      results.push('🌐 Analyzing domains and website patterns...')
      setOrganizationResults([...results])
      setShowResults(true)

      const preview = await previewAutoOrganize({
        strategy: 'domain',
        preserveExistingFolders,
        useAI: platformRecognition
      })

      results.push(`📊 Analysis complete: ${bookmarks.length} bookmarks analyzed`)
      results.push(`📁 Proposed domain collections: ${preview.foldersToCreate.length}`)

      preview.foldersToCreate.forEach((folder, index) => {
        const bookmarksInFolder = preview.bookmarksToMove.filter(b => b.newFolder === folder).length
        results.push(`   ${index + 1}. "${folder}" (${bookmarksInFolder} bookmarks)`)
      })

      results.push(`🎯 Minimum bookmarks per domain: ${minBookmarksPerDomain}`)
      results.push(`📋 Preview ready - click "Start Domain Organization" to apply changes`)

      setOrganizationResults(results)
      setPreviewData(preview)
      setShowPreview(true)

    } catch (error) {
      console.error('Domain preview failed:', error)
      setOrganizationResults(['❌ Domain preview failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  const handleOrganize = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    setOrganizationResults([])

    try {
      const results: string[] = []

      results.push('🌐 Starting domain-based organization...')
      setOrganizationResults([...results])
      setShowResults(true)
      await new Promise(resolve => setTimeout(resolve, 1000))

      results.push('📊 Analyzing website domains and patterns...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 1200))

      results.push('🏗️ Creating domain-based collections...')
      setOrganizationResults([...results])
      await new Promise(resolve => setTimeout(resolve, 800))

      const organizeResult = await autoOrganizeBookmarks({
        strategy: 'domain',
        preserveExistingFolders,
        useAI: platformRecognition
      })

      if (organizeResult.success) {
        results.push(`✅ Successfully organized ${organizeResult.bookmarksMoved} bookmarks`)
        results.push(`📁 Created ${organizeResult.foldersCreated.length} domain collections`)
        results.push(`🎉 Domain organization complete!`)

        if (preserveExistingFolders) {
          results.push('🔒 Existing folder structure preserved')
        }
      } else {
        results.push('❌ Organization failed. Please try again.')
      }

      setOrganizationResults(results)

      // Auto-close panel after successful completion
      if (organizeResult.success) {
        setTimeout(() => {
          console.log('🌐 DOMAIN PANEL: Auto-closing after successful completion')
          onClose()
        }, 3000) // Close after 3 seconds to let user see the results
      }

    } catch (error) {
      console.error('Domain organization failed:', error)
      setOrganizationResults(['❌ Domain organization failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  return (
    <div className={`optimized-panel ${isModernTheme ? 'modern-enhanced' : ''}`}>
      {/* Header */}
      <div className="panel-header">
        <div className="panel-title">
          <Globe size={18} />
          <span>Expert Domain Organization</span>
        </div>
        <button onClick={onClose} className="close-btn" aria-label="Close">
          <X size={18} />
        </button>
      </div>

      {/* Main Content */}
      <div className="panel-content">
        {/* Quick Actions */}
        <div className="quick-actions">
          <button
            onClick={generatePreview}
            className="action-btn secondary"
            disabled={isOrganizing || bookmarks.length === 0}
          >
            <Eye size={16} />
            Preview
          </button>
          <button
            onClick={() => {
              if (!isOrganizing && bookmarks.length > 0) {
                setActiveButton('organize')
                setTimeout(() => setActiveButton(null), 200)
                handleOrganize()
              }
            }}
            className={`action-btn primary ${activeButton === 'organize' ? 'active' : ''}`}
            disabled={bookmarks.length === 0 || isOrganizing}
          >
            {isOrganizing ? (
              <div className="spinner" />
            ) : (
              <Zap size={16} />
            )}
            {isOrganizing ? 'Organizing...' : 'Start Organization'}
          </button>
        </div>

        {/* Progress */}
        {isOrganizing && (
          <div className="progress-section">
            <div className="progress-bar">
              <div className="progress-fill" style={{ width: `${(organizationResults.length / 6) * 100}%` }} />
            </div>
            <div className="progress-text">
              {organizationResults[organizationResults.length - 1] || 'Starting...'}
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-value">{bookmarks.length}</span>
            <span className="stat-label">Bookmarks</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{minBookmarksPerDomain}</span>
            <span className="stat-label">Min Per Domain</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{platformRecognition ? 'ON' : 'OFF'}</span>
            <span className="stat-label">Platform AI</span>
          </div>
        </div>

        {/* Strategy Overview */}
        <div className="section-compact">
          <h3 className="section-title-compact">
            <Network size={14} />
            Domain Strategy
          </h3>
          <div className="feature-grid">
            <div className="feature-item">
              <Globe size={12} />
              <span>Platform Mapping</span>
            </div>
            <div className="feature-item">
              <CheckCircle size={12} />
              <span>Subdomain Intelligence</span>
            </div>
            <div className="feature-item">
              <Network size={12} />
              <span>Hierarchy Analysis</span>
            </div>
          </div>
        </div>

        {/* Basic Configuration */}
        <div className="section-compact">
          <h3 className="section-title-compact">
            <Settings size={14} />
            Configuration
          </h3>
          <div className="option-list">
            <label className="option-item">
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
              />
              <span>Preserve existing folders</span>
            </label>
            <label className="option-item">
              <input
                type="checkbox"
                checked={platformRecognition}
                onChange={(e) => setPlatformRecognition(e.target.checked)}
              />
              <span>Platform recognition</span>
            </label>
            <label className="option-item">
              <input
                type="checkbox"
                checked={subdomainGrouping}
                onChange={(e) => setSubdomainGrouping(e.target.checked)}
              />
              <span>Subdomain grouping</span>
            </label>
          </div>
        </div>

        {/* Collapsible Advanced Options */}
        <div className="collapsible-section">
          <button
            className="collapsible-header"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Settings size={14} />
            <span>Advanced Options</span>
            <span className={`chevron ${showAdvanced ? 'expanded' : ''}`}>▼</span>
          </button>
          {showAdvanced && (
            <div className="collapsible-content">
              <div className="option-item">
                <div className="range-option">
                  <div className="range-header">
                    <span>Min Bookmarks Per Domain</span>
                    <span className="range-value">{minBookmarksPerDomain}</span>
                  </div>
                  <input
                    type="range"
                    min="1"
                    max="10"
                    step="1"
                    value={minBookmarksPerDomain}
                    onChange={(e) => setMinBookmarksPerDomain(parseInt(e.target.value))}
                    className="range-input"
                  />
                  <small>Minimum bookmarks required to create a domain folder</small>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Collapsible Preview */}
        {showPreview && previewData && (
          <div className="collapsible-section">
            <button
              className="collapsible-header"
              onClick={() => setShowPreview(false)}
            >
              <Eye size={14} />
              <span>Organization Preview</span>
              <span className="chevron expanded">▼</span>
            </button>
            <div className="collapsible-content">
              <div className="preview-content">
                <div className="preview-stats">
                  <span>Folders to create: {previewData.foldersToCreate.length}</span>
                  <span>Bookmarks to move: {previewData.bookmarksToMove.length}</span>
                </div>
                <div className="preview-folders scrollable-content">
                  {previewData.foldersToCreate.slice(0, 5).map((folder: string, index: number) => {
                    const count = previewData.bookmarksToMove.filter((b: any) => b.newFolder === folder).length
                    return (
                      <div key={index} className="preview-folder">
                        <Globe size={12} />
                        <span>{folder}</span>
                        <span className="count">({count})</span>
                      </div>
                    )
                  })}
                  {previewData.foldersToCreate.length > 5 && (
                    <div className="preview-more">...and {previewData.foldersToCreate.length - 5} more</div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {showResults && (
          <div className="results-section">
            <h4 className="results-title">Organization Results</h4>
            <div className="results-content scrollable-content">
              {organizationResults.map((result, index) => (
                <div key={index} className="result-item">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status */}
        <div className="status-section">
          <div className="status-indicator success">
            <Globe size={16} />
            <div className="status-text">
              <span>Domain Intelligence Ready</span>
              <small>{bookmarks.length} bookmarks ready for domain organization</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DomainPanelOptimized