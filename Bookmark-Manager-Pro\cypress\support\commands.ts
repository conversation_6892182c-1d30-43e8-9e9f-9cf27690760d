/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command to interact with bookmark items
Cypress.Commands.add('getBookmarkItem', (title: string) => {
  return cy.get('[data-testid="bookmark-item"]').contains(title).closest('[data-testid="bookmark-item"]');
});

// Custom command to select bookmark by checkbox
Cypress.Commands.add('selectBookmark', (title: string) => {
  cy.getBookmarkItem(title).find('[data-testid="bookmark-checkbox"]').check();
});

// Custom command to delete bookmark
Cypress.Commands.add('deleteBookmark', (title: string) => {
  cy.getBookmarkItem(title).find('[data-testid="delete-bookmark-button"]').click();
  cy.get('[data-testid="confirm-delete-button"]').click();
});

// Custom command to edit bookmark
Cypress.Commands.add('editBookmark', (title: string, newData: { title?: string; url?: string; summary?: string }) => {
  cy.getBookmarkItem(title).find('[data-testid="edit-bookmark-button"]').click();
  
  if (newData.title) {
    cy.get('[data-testid="edit-title-input"]').clear().type(newData.title);
  }
  
  if (newData.url) {
    cy.get('[data-testid="edit-url-input"]').clear().type(newData.url);
  }
  
  if (newData.summary) {
    cy.get('[data-testid="edit-summary-input"]').clear().type(newData.summary);
  }
  
  cy.get('[data-testid="save-edit-button"]').click();
});

// Custom command to generate AI summary
Cypress.Commands.add('generateSummary', (title: string) => {
  cy.getBookmarkItem(title).find('[data-testid="generate-summary-button"]').click();
});

// Custom command to generate AI tags
Cypress.Commands.add('generateTags', (title: string) => {
  cy.getBookmarkItem(title).find('[data-testid="generate-tags-button"]').click();
});

// Custom command to sort bookmarks
Cypress.Commands.add('sortBookmarks', (column: 'title' | 'url' | 'date') => {
  cy.get(`[data-testid="sort-${column}-button"]`).click();
});

// Custom command to filter bookmarks by tag
Cypress.Commands.add('filterByTag', (tag: string) => {
  cy.get('[data-testid="tag-filter"]').click();
  cy.get(`[data-testid="tag-option-${tag}"]`).click();
});

// Custom command to bulk select bookmarks
Cypress.Commands.add('bulkSelectBookmarks', (titles: string[]) => {
  titles.forEach(title => {
    cy.selectBookmark(title);
  });
});

// Custom command to bulk delete selected bookmarks
Cypress.Commands.add('bulkDeleteSelected', () => {
  cy.get('[data-testid="bulk-delete-button"]').click();
  cy.get('[data-testid="confirm-bulk-delete-button"]').click();
});

// Custom command to export bookmarks
Cypress.Commands.add('exportBookmarks', (format: 'json' | 'csv' | 'html') => {
  cy.get('[data-testid="export-button"]').click();
  cy.get(`[data-testid="export-${format}-button"]`).click();
});

// Custom command to import bookmarks
Cypress.Commands.add('importBookmarks', (filePath: string) => {
  cy.get('[data-testid="import-button"]').click();
  cy.get('[data-testid="file-input"]').selectFile(filePath);
  cy.get('[data-testid="confirm-import-button"]').click();
});

// Custom command to check bookmark count
Cypress.Commands.add('checkBookmarkCount', (expectedCount: number) => {
  cy.get('[data-testid="bookmark-item"]').should('have.length', expectedCount);
});

// Custom command to verify bookmark exists
Cypress.Commands.add('verifyBookmarkExists', (title: string, url?: string) => {
  cy.getBookmarkItem(title).should('be.visible');
  
  if (url) {
    cy.getBookmarkItem(title).should('contain', url);
  }
});

// Custom command to verify bookmark does not exist
Cypress.Commands.add('verifyBookmarkNotExists', (title: string) => {
  cy.get('[data-testid="bookmark-item"]').contains(title).should('not.exist');
});

// Custom command to wait for loading to complete
Cypress.Commands.add('waitForLoading', () => {
  cy.get('[data-testid="loading-spinner"]', { timeout: 10000 }).should('not.exist');
});

// Custom command to check for error messages
Cypress.Commands.add('checkErrorMessage', (message: string) => {
  cy.get('[data-testid="error-message"]').should('contain', message);
});

// Custom command to check for success messages
Cypress.Commands.add('checkSuccessMessage', (message: string) => {
  cy.get('[data-testid="success-message"]').should('contain', message);
});

// Custom command to toggle dark mode
Cypress.Commands.add('toggleDarkMode', () => {
  cy.get('[data-testid="dark-mode-toggle"]').click();
});

// Custom command to change view mode
Cypress.Commands.add('changeViewMode', (mode: 'list' | 'grid' | 'card') => {
  cy.get('[data-testid="view-mode-selector"]').click();
  cy.get(`[data-testid="view-mode-${mode}"]`).click();
});

// Custom command to navigate to page
Cypress.Commands.add('navigateToPage', (page: 'home' | 'settings' | 'about') => {
  cy.get(`[data-testid="nav-${page}"]`).click();
});

// Custom command to check responsive design
Cypress.Commands.add('checkResponsive', () => {
  // Test mobile view
  cy.viewport(375, 667);
  cy.get('[data-testid="mobile-menu-button"]').should('be.visible');
  
  // Test tablet view
  cy.viewport(768, 1024);
  cy.get('[data-testid="app-container"]').should('be.visible');
  
  // Test desktop view
  cy.viewport(1280, 720);
  cy.get('[data-testid="app-container"]').should('be.visible');
});

// Extend Cypress chainable interface
declare global {
  namespace Cypress {
    interface Chainable {
      getBookmarkItem(title: string): Chainable<JQuery<HTMLElement>>;
      selectBookmark(title: string): Chainable<void>;
      deleteBookmark(title: string): Chainable<void>;
      editBookmark(title: string, newData: { title?: string; url?: string; summary?: string }): Chainable<void>;
      generateSummary(title: string): Chainable<void>;
      generateTags(title: string): Chainable<void>;
      sortBookmarks(column: 'title' | 'url' | 'date'): Chainable<void>;
      filterByTag(tag: string): Chainable<void>;
      bulkSelectBookmarks(titles: string[]): Chainable<void>;
      bulkDeleteSelected(): Chainable<void>;
      exportBookmarks(format: 'json' | 'csv' | 'html'): Chainable<void>;
      importBookmarks(filePath: string): Chainable<void>;
      checkBookmarkCount(expectedCount: number): Chainable<void>;
      verifyBookmarkExists(title: string, url?: string): Chainable<void>;
      verifyBookmarkNotExists(title: string): Chainable<void>;
      waitForLoading(): Chainable<void>;
      checkErrorMessage(message: string): Chainable<void>;
      checkSuccessMessage(message: string): Chainable<void>;
      toggleDarkMode(): Chainable<void>;
      changeViewMode(mode: 'list' | 'grid' | 'card'): Chainable<void>;
      navigateToPage(page: 'home' | 'settings' | 'about'): Chainable<void>;
      checkResponsive(): Chainable<void>;
    }
  }
}