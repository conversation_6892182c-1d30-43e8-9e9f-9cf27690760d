/**
 * Memory Leak Prevention Utilities
 * Comprehensive tools to prevent and detect memory leaks in React components
 */

export class MemoryLeakPrevention {
  private static timeouts = new Set<NodeJS.Timeout>()
  private static intervals = new Set<NodeJS.Timeout>()
  private static eventListeners = new Map<string, { element: EventTarget; event: string; handler: EventListener }>()

  /**
   * Safe setTimeout that tracks and cleans up automatically
   */
  static safeSetTimeout(callback: () => void, delay: number): NodeJS.Timeout {
    const timeoutId = setTimeout(() => {
      callback()
      this.timeouts.delete(timeoutId)
    }, delay)
    
    this.timeouts.add(timeoutId)
    return timeoutId
  }

  /**
   * Safe setInterval that tracks and cleans up automatically
   */
  static safeSetInterval(callback: () => void, delay: number): NodeJS.Timeout {
    const intervalId = setInterval(callback, delay)
    this.intervals.add(intervalId)
    return intervalId
  }

  /**
   * Safe event listener that tracks and cleans up automatically
   */
  static safeAddEventListener(
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): string {
    const id = `${Date.now()}-${Math.random()}`
    element.addEventListener(event, handler, options)
    this.eventListeners.set(id, { element, event, handler })
    return id
  }

  /**
   * Remove specific event listener
   */
  static removeEventListener(id: string): void {
    const listener = this.eventListeners.get(id)
    if (listener) {
      listener.element.removeEventListener(listener.event, listener.handler)
      this.eventListeners.delete(id)
    }
  }

  /**
   * Clear specific timeout
   */
  static clearSafeTimeout(timeoutId: NodeJS.Timeout): void {
    clearTimeout(timeoutId)
    this.timeouts.delete(timeoutId)
  }

  /**
   * Clear specific interval
   */
  static clearSafeInterval(intervalId: NodeJS.Timeout): void {
    clearInterval(intervalId)
    this.intervals.delete(intervalId)
  }

  /**
   * Clean up all tracked resources
   */
  static cleanupAll(): void {
    // Clear all timeouts
    this.timeouts.forEach(timeoutId => clearTimeout(timeoutId))
    this.timeouts.clear()

    // Clear all intervals
    this.intervals.forEach(intervalId => clearInterval(intervalId))
    this.intervals.clear()

    // Remove all event listeners
    this.eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler)
    })
    this.eventListeners.clear()

    // Stop speech synthesis
    if ('speechSynthesis' in window) {
      speechSynthesis.cancel()
    }

    console.log('🧹 Memory leak prevention: All resources cleaned up')
  }

  /**
   * Get current resource counts for monitoring
   */
  static getResourceCounts(): { timeouts: number; intervals: number; eventListeners: number } {
    return {
      timeouts: this.timeouts.size,
      intervals: this.intervals.size,
      eventListeners: this.eventListeners.size
    }
  }

  /**
   * Media element cleanup utility
   */
  static cleanupMediaElement(element: HTMLVideoElement | HTMLAudioElement | null): void {
    if (!element) return

    try {
      element.pause()
      element.src = ''
      element.load()
      element.removeAttribute('src')
    } catch (error) {
      console.warn('Error cleaning up media element:', error)
    }
  }

  /**
   * React component cleanup hook
   */
  static createCleanupHook() {
    return () => {
      const resources: Array<() => void> = []

      const addCleanup = (cleanup: () => void) => {
        resources.push(cleanup)
      }

      const cleanup = () => {
        resources.forEach(fn => {
          try {
            fn()
          } catch (error) {
            console.warn('Cleanup error:', error)
          }
        })
        resources.length = 0
      }

      return { addCleanup, cleanup }
    }
  }
}

/**
 * React hook for automatic memory leak prevention
 */
export const useMemoryLeakPrevention = () => {
  const { addCleanup, cleanup } = MemoryLeakPrevention.createCleanupHook()

  // Auto cleanup on unmount
  React.useEffect(() => {
    return cleanup
  }, [])

  const safeSetTimeout = (callback: () => void, delay: number) => {
    const timeoutId = MemoryLeakPrevention.safeSetTimeout(callback, delay)
    addCleanup(() => MemoryLeakPrevention.clearSafeTimeout(timeoutId))
    return timeoutId
  }

  const safeSetInterval = (callback: () => void, delay: number) => {
    const intervalId = MemoryLeakPrevention.safeSetInterval(callback, delay)
    addCleanup(() => MemoryLeakPrevention.clearSafeInterval(intervalId))
    return intervalId
  }

  const safeAddEventListener = (
    element: EventTarget,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ) => {
    const id = MemoryLeakPrevention.safeAddEventListener(element, event, handler, options)
    addCleanup(() => MemoryLeakPrevention.removeEventListener(id))
    return id
  }

  return {
    safeSetTimeout,
    safeSetInterval,
    safeAddEventListener,
    addCleanup
  }
}

/**
 * Memory monitoring utility
 */
export const monitorMemoryUsage = () => {
  if (!('memory' in performance)) {
    console.warn('Memory monitoring not available in this browser')
    return null
  }

  const memory = (performance as any).memory
  const usedMB = Math.round(memory.usedJSHeapSize / 1048576)
  const totalMB = Math.round(memory.totalJSHeapSize / 1048576)
  const limitMB = Math.round(memory.jsHeapSizeLimit / 1048576)
  const usagePercentage = Math.round((usedMB / limitMB) * 100)

  const stats = {
    used: usedMB,
    total: totalMB,
    limit: limitMB,
    usagePercentage,
    isHigh: usagePercentage > 80,
    isCritical: usagePercentage > 90
  }

  if (stats.isHigh) {
    console.warn(`🧠 High memory usage detected: ${usagePercentage}% (${usedMB}MB/${limitMB}MB)`)
    
    if (stats.isCritical) {
      console.error('🚨 Critical memory usage! Consider immediate cleanup.')
      MemoryLeakPrevention.cleanupAll()
    }
  }

  return stats
}

// Global cleanup on page unload
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    MemoryLeakPrevention.cleanupAll()
  })
}
