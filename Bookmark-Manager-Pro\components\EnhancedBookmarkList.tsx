import React, { useMemo } from 'react';
import { FixedSizeGrid as Grid } from 'react-window';
import { Bookmark, BookmarkProcessingState } from '../types';
import EmptyState from './EmptyState';
import ModernBookmarkCard from './ModernBookmarkCard';

interface EnhancedBookmarkListProps {
  bookmarks: Bookmark[];
  selectedBookmarks: string[];
  onBookmarkSelect: (id: string) => void;
  onBookmarkUpdate: (id: string, updates: Partial<Bookmark>) => void;
  onBookmarkDelete: (id: string) => void;
  onTagsGenerated: (id: string, tags: string[]) => void;
  processingStates: Record<string, BookmarkProcessingState>;
  density: 'compact' | 'comfortable' | 'spacious';
  viewMode?: 'list' | 'grid';
  onAddBookmark?: () => void;
  onImportBookmarks?: () => void;
  onShowOnboarding?: () => void;
}

interface GridItemProps {
  columnIndex: number;
  rowIndex: number;
  style: React.CSSProperties;
  data: {
    bookmarks: Bookmark[];
    itemsPerRow: number;
    selectedBookmarks: string[];
    onBookmarkSelect: (id: string) => void;
    onBookmarkUpdate: (id: string, updates: Partial<Bookmark>) => void;
    onBookmarkDelete: (id: string) => void;
    onTagsGenerated: (id: string, tags: string[]) => void;
    processingStates: Record<string, BookmarkProcessingState>;
    viewMode: 'list' | 'grid';
  };
}

const GridItem: React.FC<GridItemProps> = ({ columnIndex, rowIndex, style, data }) => {
  const {
    bookmarks,
    itemsPerRow,
    selectedBookmarks,
    onBookmarkSelect,
    onBookmarkUpdate,
    onBookmarkDelete,
    onTagsGenerated: _onTagsGenerated,
    processingStates: _processingStates,
    viewMode
  } = data;

  const index = rowIndex * itemsPerRow + columnIndex;
  const bookmark = bookmarks[index];

  if (!bookmark) {
    return <div style={style} />;
  }

  return (
    <div style={style} className="p-2">
      <ModernBookmarkCard
        bookmark={bookmark}
        viewMode={viewMode}
        isSelected={selectedBookmarks.includes(bookmark.id)}
        onSelect={(id: string, selected: boolean) => {
          if (selected !== selectedBookmarks.includes(id)) {
            onBookmarkSelect(id);
          }
        }}
        onEdit={(bookmark: Bookmark) => {
          // Handle edit - could open a modal or inline edit
          console.log('Edit bookmark:', bookmark);
        }}
        onDelete={onBookmarkDelete}
        onToggleFavorite={(id: string, isFavorite: boolean) => {
          onBookmarkUpdate(id, { isFavorite });
        }}
        onVisit={(_id: string, url: string) => {
          window.open(url, '_blank');
        }}
      />
    </div>
  );
};

const EnhancedBookmarkList: React.FC<EnhancedBookmarkListProps> = ({
  bookmarks,
  selectedBookmarks,
  onBookmarkSelect,
  onBookmarkUpdate,
  onBookmarkDelete,
  onTagsGenerated,
  processingStates,
  density = 'comfortable',
  viewMode = 'grid',
  onAddBookmark,
  onImportBookmarks,
  onShowOnboarding
}) => {
  // Calculate grid dimensions based on density and view mode
  const { itemWidth, itemHeight, itemsPerRow } = useMemo(() => {
    const containerWidth = window.innerWidth - 700; // Account for sidebars

    let width: number;
    let height: number;

    if (viewMode === 'list') {
      // List view: single column, full width
      width = containerWidth - 32; // Account for padding
      switch (density) {
        case 'compact':
          height = 80;
          break;
        case 'spacious':
          height = 120;
          break;
        default: // comfortable
          height = 100;
      }
      return {
        itemWidth: width,
        itemHeight: height,
        itemsPerRow: 1
      };
    } else {
      // Grid view: multiple columns
      switch (density) {
        case 'compact':
          width = 300;
          height = 180;
          break;
        case 'spacious':
          width = 500;
          height = 280;
          break;
        default: // comfortable
          width = 400;
          height = 220;
      }

      const columns = Math.floor(containerWidth / (width + 16)); // 16px for padding

      return {
        itemWidth: width,
        itemHeight: height,
        itemsPerRow: Math.max(1, columns)
      };
    }
  }, [density, viewMode]);

  const rowCount = Math.ceil(bookmarks.length / itemsPerRow);
  const gridHeight = Math.min(window.innerHeight - 300, rowCount * itemHeight);

  const gridData = useMemo(() => ({
    bookmarks,
    itemsPerRow,
    selectedBookmarks,
    onBookmarkSelect,
    onBookmarkUpdate,
    onBookmarkDelete,
    onTagsGenerated,
    processingStates,
    viewMode
  }), [
    bookmarks,
    itemsPerRow,
    selectedBookmarks,
    onBookmarkSelect,
    onBookmarkUpdate,
    onBookmarkDelete,
    onTagsGenerated,
    processingStates,
    viewMode
  ]);

  if (bookmarks.length === 0) {
    return (
      <EmptyState
        onAddBookmark={onAddBookmark || (() => {})}
        onImportBookmarks={onImportBookmarks || (() => {})}
        onShowOnboarding={onShowOnboarding}
      />
    );
  }

  return (
    <div className={`bookmark-grid ${density}`}>
      <Grid
        columnCount={itemsPerRow}
        columnWidth={itemWidth + 16}
        width={itemsPerRow * (itemWidth + 16)}
        height={gridHeight}
        rowCount={rowCount}
        rowHeight={itemHeight + 16}
        itemData={gridData}
        className="bookmark-grid-container"
      >
        {GridItem}
      </Grid>
    </div>
  );
};

export default EnhancedBookmarkList;