describe('Layout Breadth Testing - Page Layout Issues', () => {
  const viewports = [
    { name: 'Mobile', width: 375, height: 667 },
    { name: 'Tablet', width: 768, height: 1024 },
    { name: 'Desktop', width: 1200, height: 800 },
    { name: 'Large Desktop', width: 1920, height: 1080 }
  ];

  beforeEach(() => {
    cy.visit('http://localhost:5173/');
    cy.wait(1000); // Allow page to fully load
  });

  describe('Responsive Layout Testing', () => {
    viewports.forEach(viewport => {
      context(`${viewport.name} (${viewport.width}x${viewport.height})`, () => {
        beforeEach(() => {
          cy.viewport(viewport.width, viewport.height);
        });

        it('should maintain proper header layout', () => {
          cy.get('header, [data-testid="header"], .header').should('be.visible');
          
          // Check header doesn't overflow
          cy.get('header, [data-testid="header"], .header').then($header => {
            if ($header.length > 0) {
              expect($header[0].scrollWidth).to.be.at.most($header[0].clientWidth + 5);
            }
          });

          // Verify navigation elements are accessible
          if (viewport.width >= 768) {
            cy.get('[data-testid="nav-menu"], .nav-menu, nav').should('be.visible');
          } else {
            // Mobile should have hamburger menu or collapsed nav
            cy.get('[data-testid="mobile-menu"], .mobile-menu, [data-testid="hamburger"]')
              .should('exist');
          }
        });

        it('should display bookmark list with proper spacing', () => {
          // Load some test bookmarks first
          cy.fixture('bookmarks.json').then(bookmarks => {
            cy.window().then(win => {
              win.localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
              cy.reload();
            });
          });

          cy.get('[data-testid="bookmark-list"], .bookmark-list').should('be.visible');
          
          // Check bookmark items don't overlap
          cy.get('[data-testid="bookmark-item"], .bookmark-item').then($items => {
            if ($items.length > 1) {
              const firstItem = $items[0].getBoundingClientRect();
              const secondItem = $items[1].getBoundingClientRect();
              expect(firstItem.bottom).to.be.at.most(secondItem.top + 5);
            }
          });

          // Verify horizontal scrolling is not present
          cy.get('body').then($body => {
            expect($body[0].scrollWidth).to.be.at.most($body[0].clientWidth + 5);
          });
        });

        it('should handle long bookmark titles gracefully', () => {
          const longTitleBookmark = {
            id: 'long-title-test',
            title: 'This is an extremely long bookmark title that should be handled gracefully without breaking the layout or causing overflow issues in the user interface',
            url: 'https://example.com/very-long-url-path',
            addDate: Date.now(),
            tags: ['test', 'long-title']
          };

          cy.window().then(win => {
            win.localStorage.setItem('bookmarks', JSON.stringify([longTitleBookmark]));
            cy.reload();
          });

          cy.get('[data-testid="bookmark-item"]').should('be.visible');
          
          // Check title doesn't overflow container
          cy.get('[data-testid="bookmark-title"], .bookmark-title').then($title => {
            if ($title.length > 0) {
              const titleElement = $title[0];
              const container = titleElement.closest('[data-testid="bookmark-item"]') || titleElement.parentElement;
              expect(titleElement.scrollWidth).to.be.at.most(container.clientWidth + 10);
            }
          });
        });

        it('should maintain proper sidebar layout', () => {
          // Check if sidebar exists
          cy.get('[data-testid="sidebar"], .sidebar, aside').then($sidebar => {
            if ($sidebar.length > 0) {
              if (viewport.width >= 1024) {
                // Desktop should show sidebar
                cy.get('[data-testid="sidebar"], .sidebar, aside').should('be.visible');
              } else {
                // Mobile/tablet might hide sidebar or show as overlay
                cy.get('[data-testid="sidebar"], .sidebar, aside')
                  .should($el => {
                    const isVisible = $el.is(':visible');
                    const isOverlay = $el.css('position') === 'fixed' || $el.css('position') === 'absolute';
                    expect(isVisible || isOverlay).to.be.true;
                  });
              }
            }
          });
        });

        it('should handle form layouts properly', () => {
          // Test search form
          cy.get('[data-testid="search-input"], input[type="search"], .search-input').then($search => {
            if ($search.length > 0) {
              cy.get('[data-testid="search-input"], input[type="search"], .search-input')
                .should('be.visible')
                .and('have.css', 'width')
                .and('not.equal', '0px');
            }
          });

          // Test any modal forms
          cy.get('body').then($body => {
            if ($body.find('[data-testid="add-bookmark"], .add-bookmark-btn').length > 0) {
              cy.get('[data-testid="add-bookmark"], .add-bookmark-btn').click();
              
              cy.get('[data-testid="modal"], .modal, [role="dialog"]').should('be.visible');
              
              // Check modal doesn't exceed viewport
              cy.get('[data-testid="modal"], .modal, [role="dialog"]').then($modal => {
                const modalRect = $modal[0].getBoundingClientRect();
                expect(modalRect.width).to.be.at.most(viewport.width);
                expect(modalRect.height).to.be.at.most(viewport.height);
              });

              // Close modal
              cy.get('[data-testid="close-modal"], .close-modal, [aria-label="Close"]').click();
            }
          });
        });
      });
    });
  });

  describe('Cross-Browser Layout Consistency', () => {
    it('should maintain consistent spacing across elements', () => {
      cy.get('[data-testid="bookmark-item"], .bookmark-item').then($items => {
        if ($items.length > 0) {
          const spacings = [];
          for (let i = 0; i < Math.min($items.length - 1, 3); i++) {
            const current = $items[i].getBoundingClientRect();
            const next = $items[i + 1].getBoundingClientRect();
            spacings.push(next.top - current.bottom);
          }
          
          // All spacings should be similar (within 5px tolerance)
          if (spacings.length > 1) {
            const avgSpacing = spacings.reduce((a, b) => a + b) / spacings.length;
            spacings.forEach(spacing => {
              expect(Math.abs(spacing - avgSpacing)).to.be.at.most(5);
            });
          }
        }
      });
    });

    it('should handle CSS Grid/Flexbox layouts properly', () => {
      // Check for proper grid/flex implementations
      cy.get('[data-testid="bookmark-grid"], .bookmark-grid, .grid').then($grid => {
        if ($grid.length > 0) {
          cy.get('[data-testid="bookmark-grid"], .bookmark-grid, .grid')
            .should('have.css', 'display')
            .and('match', /(grid|flex)/);
        }
      });

      // Verify no layout shifts during loading
      cy.get('body').should('not.have.class', 'layout-shift');
    });

    it('should handle text overflow consistently', () => {
      cy.get('[data-testid="bookmark-title"], .bookmark-title').each($title => {
        cy.wrap($title)
          .should('have.css', 'overflow')
          .and('match', /(hidden|ellipsis)/);
      });
    });
  });

  describe('Performance Layout Testing', () => {
    it('should not cause layout thrashing during scroll', () => {
      // Load many bookmarks to test scroll performance
      const manyBookmarks = Array.from({ length: 100 }, (_, i) => ({
        id: `bookmark-${i}`,
        title: `Test Bookmark ${i}`,
        url: `https://example${i}.com`,
        addDate: Date.now() - i * 1000,
        tags: [`tag${i % 5}`]
      }));

      cy.window().then(win => {
        win.localStorage.setItem('bookmarks', JSON.stringify(manyBookmarks));
        cy.reload();
      });

      // Measure scroll performance
      cy.get('[data-testid="bookmark-list"], .bookmark-list, body').then($container => {
        const startTime = performance.now();
        
        cy.wrap($container).scrollTo('bottom', { duration: 1000 });
        
        cy.then(() => {
          const endTime = performance.now();
          const scrollDuration = endTime - startTime;
          
          // Scroll should complete within reasonable time
          expect(scrollDuration).to.be.at.most(2000);
        });
      });
    });

    it('should maintain layout stability during dynamic content loading', () => {
      // Test layout stability when content is added dynamically
      cy.get('[data-testid="bookmark-list"], .bookmark-list').then($list => {
        const initialHeight = $list[0].getBoundingClientRect().height;
        
        // Simulate adding new content
        cy.window().then(win => {
          const newBookmark = {
            id: 'dynamic-bookmark',
            title: 'Dynamically Added Bookmark',
            url: 'https://dynamic.com',
            addDate: Date.now(),
            tags: ['dynamic']
          };
          
          const existing = JSON.parse(win.localStorage.getItem('bookmarks') || '[]');
          existing.push(newBookmark);
          win.localStorage.setItem('bookmarks', JSON.stringify(existing));
          
          // Trigger re-render
          win.dispatchEvent(new Event('storage'));
        });

        cy.wait(500);
        
        // Layout should expand appropriately
        cy.get('[data-testid="bookmark-list"], .bookmark-list').then($updatedList => {
          const newHeight = $updatedList[0].getBoundingClientRect().height;
          expect(newHeight).to.be.at.least(initialHeight);
        });
      });
    });
  });

  describe('Accessibility Layout Testing', () => {
    it('should maintain proper focus indicators', () => {
      cy.get('a, button, input, [tabindex]').each($el => {
        cy.wrap($el).focus();
        
        // Check focus is visible
        cy.wrap($el).should($focused => {
          const styles = window.getComputedStyle($focused[0]);
          const hasOutline = styles.outline !== 'none' && styles.outline !== '0px';
          const hasBoxShadow = styles.boxShadow !== 'none';
          const hasBorder = styles.borderWidth !== '0px';
          
          expect(hasOutline || hasBoxShadow || hasBorder).to.be.true;
        });
      });
    });

    it('should maintain readable text contrast', () => {
      cy.get('body, [data-testid="bookmark-item"], .bookmark-item').should('be.visible');
      
      // Basic contrast check (this would need more sophisticated color analysis in real implementation)
      cy.get('body').should('have.css', 'color').and('not.equal', 'rgba(0, 0, 0, 0)');
      cy.get('body').should('have.css', 'background-color').and('not.equal', 'rgba(0, 0, 0, 0)');
    });

    it('should provide adequate touch targets on mobile', () => {
      cy.viewport(375, 667); // Mobile viewport
      
      cy.get('button, a, [role="button"]').each($el => {
        cy.wrap($el).then($element => {
          const rect = $element[0].getBoundingClientRect();
          const minTouchTarget = 44; // 44px minimum recommended
          
          expect(Math.max(rect.width, rect.height)).to.be.at.least(minTouchTarget - 10); // 10px tolerance
        });
      });
    });
  });

  describe('Edge Case Layout Testing', () => {
    it('should handle empty states gracefully', () => {
      cy.window().then(win => {
        win.localStorage.removeItem('bookmarks');
        cy.reload();
      });

      // Should show empty state without layout issues
      cy.get('[data-testid="empty-state"], .empty-state').should('be.visible');
      
      // Page should still have proper structure
      cy.get('body').should('not.have.css', 'height', '0px');
    });

    it('should handle very long URLs without breaking layout', () => {
      const longUrlBookmark = {
        id: 'long-url-test',
        title: 'Long URL Test',
        url: 'https://example.com/very/long/path/that/goes/on/and/on/and/should/not/break/the/layout/when/displayed/in/the/bookmark/item/component',
        addDate: Date.now(),
        tags: ['test']
      };

      cy.window().then(win => {
        win.localStorage.setItem('bookmarks', JSON.stringify([longUrlBookmark]));
        cy.reload();
      });

      cy.get('[data-testid="bookmark-url"], .bookmark-url').should('be.visible');
      
      // URL should not cause horizontal scroll
      cy.get('body').then($body => {
        expect($body[0].scrollWidth).to.be.at.most($body[0].clientWidth + 5);
      });
    });

    it('should handle rapid viewport changes', () => {
      const viewportSizes = [
        [375, 667],
        [768, 1024],
        [1200, 800],
        [375, 667]
      ];

      viewportSizes.forEach(([width, height]) => {
        cy.viewport(width, height);
        cy.wait(200);
        
        // Layout should remain stable
        cy.get('body').should('be.visible');
        cy.get('[data-testid="bookmark-list"], .bookmark-list, main').should('be.visible');
      });
    });
  });
});