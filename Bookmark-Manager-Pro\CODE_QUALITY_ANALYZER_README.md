# Code Quality Analyzer - Testing-Focused Hook System

A comprehensive React hook system designed for testing code quality analysis and file monitoring functionality. This system provides real-time code analysis, quality suggestions, and automated testing capabilities for maintaining high code standards.

## 🎯 Overview

The Code Quality Analyzer is a testing-focused solution that:
- **Monitors source code files** for changes in real-time
- **Analyzes code quality** using multiple detection algorithms
- **Provides actionable suggestions** for improvements
- **Supports comprehensive testing** with full test coverage
- **Offers visual dashboard** for quality metrics tracking

## 📁 File Structure

```
src/
├── hooks/
│   ├── useCodeQualityAnalyzer.ts      # Main hook implementation
│   └── useCodeQualityAnalyzer.test.ts  # Comprehensive test suite
├── components/
│   ├── CodeQualityDashboard.tsx        # Visual dashboard component
│   └── CodeQualityDashboard.css        # Dashboard styling
└── CODE_QUALITY_ANALYZER_README.md     # This documentation
```

## 🚀 Quick Start

### Basic Usage

```typescript
import { useCodeQualityAnalyzer } from './hooks/useCodeQualityAnalyzer';

function MyComponent() {
  const {
    analysisResults,
    isAnalyzing,
    startWatching,
    stopWatching,
    analyzeFile,
    clearResults
  } = useCodeQualityAnalyzer();

  // Start watching files for changes
  const handleStartWatching = () => {
    startWatching();
  };

  // Analyze a specific file
  const handleAnalyzeFile = async () => {
    const result = await analyzeFile('src/components/MyComponent.tsx');
    console.log('Analysis result:', result);
  };

  return (
    <div>
      <button onClick={handleStartWatching}>Start Watching</button>
      <button onClick={handleAnalyzeFile}>Analyze File</button>
      
      {analysisResults.map(result => (
        <div key={result.file}>
          <h3>{result.file}</h3>
          <p>Issues: {result.suggestions.length}</p>
          <p>Severity: {result.severity}</p>
        </div>
      ))}
    </div>
  );
}
```

### Dashboard Integration

```typescript
import CodeQualityDashboard from './components/CodeQualityDashboard';

function App() {
  return (
    <div>
      <CodeQualityDashboard 
        projectPath="src/**/*.{ts,tsx}"
        autoStart={true}
      />
    </div>
  );
}
```

## 🔧 Configuration Options

### Hook Configuration

```typescript
interface UseCodeQualityAnalyzerOptions {
  watchPaths?: string[];              // Files to watch (default: ['src/**/*.{ts,tsx,js,jsx}'])
  excludePatterns?: string[];         // Patterns to exclude (default: ['node_modules/**', 'dist/**'])
  debounceMs?: number;               // Debounce delay (default: 1000ms)
  enableRealTimeAnalysis?: boolean;  // Enable real-time analysis (default: true)
  analysisTypes?: CodeSuggestion['type'][]; // Types of analysis to perform
}

// Usage with custom options
const analyzer = useCodeQualityAnalyzer({
  watchPaths: ['src/components/**/*.tsx'],
  excludePatterns: ['**/*.test.*', '**/*.spec.*'],
  debounceMs: 500,
  enableRealTimeAnalysis: true,
  analysisTypes: ['code-smell', 'performance', 'best-practice']
});
```

### Analysis Types

The system supports six types of code analysis:

1. **Code Smells** (`code-smell`)
   - Long parameter lists
   - Magic numbers
   - Long lines (>120 characters)

2. **Design Patterns** (`design-pattern`)
   - Factory pattern opportunities
   - Observer pattern suggestions

3. **Best Practices** (`best-practice`)
   - Missing error handling
   - Console statements in production

4. **Performance** (`performance`)
   - Inefficient array operations
   - Missing React memoization

5. **Readability** (`readability`)
   - Line length issues
   - Code formatting problems

6. **Maintainability** (`maintainability`)
   - TypeScript "any" usage
   - Large function detection

## 🧪 Testing Strategy

### Running Tests

```bash
# Run all tests
npm test useCodeQualityAnalyzer

# Run tests with coverage
npm test useCodeQualityAnalyzer -- --coverage

# Run tests in watch mode
npm test useCodeQualityAnalyzer -- --watch
```

### Test Categories

#### 1. Hook Initialization Tests
```typescript
describe('Hook Initialization', () => {
  it('should initialize with default values');
  it('should accept custom options');
});
```

#### 2. Code Analysis Tests
```typescript
describe('Code Analysis Functionality', () => {
  it('should detect code smells - long parameter lists');
  it('should detect magic numbers');
  it('should detect missing error handling');
  it('should detect console statements');
  it('should detect performance issues');
  it('should suggest React.memo for components');
  it('should detect "any" type usage');
  it('should detect long lines');
});
```

#### 3. Results Management Tests
```typescript
describe('Analysis Results Management', () => {
  it('should store analysis results');
  it('should update existing results for the same file');
  it('should clear all results');
  it('should get results for specific file');
});
```

#### 4. Error Handling Tests
```typescript
describe('Error Handling', () => {
  it('should handle non-existent files gracefully');
  it('should handle file read errors gracefully');
  it('should set isAnalyzing state correctly');
});
```

### Test Data Examples

#### Code with Multiple Issues
```typescript
const complexCode = `
import React, { useState, useEffect } from 'react';

export const ComplexComponent = (props: any) => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetch('/api/data').then(response => response.json()).then(setData);
  }, []);
  
  const processItems = (items: any[]) => {
    console.log('Processing items:', items);
    return items.filter(item => item.active).find(item => item.priority > 5);
  };
  
  const handleClick = (param1: string, param2: number, param3: boolean, param4: object, param5: any) => {
    if (param2 > 100) {
      console.warn('High value detected');
    }
  };
  
  return (
    <div onClick={() => handleClick('test', 42, true, {}, null)}>
      {data && processItems(data.items)}
    </div>
  );
};
`;
```

This code will trigger multiple analysis suggestions:
- **Maintainability**: "any" type usage
- **Best Practice**: Console statements, missing error handling
- **Performance**: React.memo suggestion, chained operations
- **Code Smell**: Long parameters, magic number

## 📊 Dashboard Features

### Statistics Overview
- **Files Analyzed**: Total number of analyzed files
- **Total Issues**: Sum of all detected issues
- **Severity Distribution**: High/Medium/Low severity counts

### Filtering Options
- **By Type**: Filter suggestions by analysis type
- **By Severity**: Filter results by severity level

### Real-time Monitoring
- **File Watching**: Automatic analysis on file changes
- **Live Updates**: Real-time dashboard updates
- **Status Indicators**: Visual feedback for system state

### Issue Details
- **Line Numbers**: Exact location of issues
- **Suggestions**: Actionable improvement recommendations
- **Examples**: Code examples for fixes
- **Timestamps**: When analysis was performed

## 🔍 Advanced Usage

### Custom Analysis Rules

```typescript
// Extend the analyzer with custom rules
const customAnalyzer = useCodeQualityAnalyzer({
  analysisTypes: ['performance', 'maintainability'],
  watchPaths: ['src/critical/**/*.ts'],
  debounceMs: 200 // Faster response for critical files
});
```

### Integration with CI/CD

```typescript
// Example CI integration
const runQualityCheck = async () => {
  const analyzer = useCodeQualityAnalyzer({
    enableRealTimeAnalysis: false
  });
  
  const files = await getChangedFiles();
  const results = [];
  
  for (const file of files) {
    const result = await analyzer.analyzeFile(file);
    if (result && result.severity === 'high') {
      results.push(result);
    }
  }
  
  if (results.length > 0) {
    throw new Error(`Quality check failed: ${results.length} high-severity issues found`);
  }
};
```

### Programmatic Analysis

```typescript
// Analyze multiple files programmatically
const analyzeProject = async () => {
  const analyzer = useCodeQualityAnalyzer();
  const files = ['src/App.tsx', 'src/components/Header.tsx'];
  
  const results = await Promise.all(
    files.map(file => analyzer.analyzeFile(file))
  );
  
  const highSeverityFiles = results
    .filter(result => result?.severity === 'high')
    .map(result => result?.file);
    
  console.log('High severity files:', highSeverityFiles);
};
```

## 🎨 Customization

### Custom Styling

```css
/* Override dashboard colors */
.code-quality-dashboard {
  --primary-color: #your-color;
  --success-color: #your-success;
  --warning-color: #your-warning;
  --error-color: #your-error;
}
```

### Custom Analysis Logic

```typescript
// Extend the hook with custom analysis
const useCustomCodeAnalyzer = (options) => {
  const baseAnalyzer = useCodeQualityAnalyzer(options);
  
  const analyzeWithCustomRules = async (filePath: string) => {
    const baseResult = await baseAnalyzer.analyzeFile(filePath);
    
    if (baseResult) {
      // Add custom analysis logic
      const customSuggestions = performCustomAnalysis(baseResult);
      baseResult.suggestions.push(...customSuggestions);
    }
    
    return baseResult;
  };
  
  return {
    ...baseAnalyzer,
    analyzeFile: analyzeWithCustomRules
  };
};
```

## 📈 Performance Considerations

### Optimization Tips

1. **Debounce Settings**: Adjust `debounceMs` based on project size
   - Small projects: 500ms
   - Medium projects: 1000ms (default)
   - Large projects: 2000ms

2. **Watch Patterns**: Use specific patterns to reduce file watching overhead
   ```typescript
   watchPaths: ['src/components/**/*.tsx'] // Specific
   // vs
   watchPaths: ['**/*'] // Too broad
   ```

3. **Analysis Types**: Enable only needed analysis types
   ```typescript
   analysisTypes: ['performance', 'best-practice'] // Focused
   // vs
   analysisTypes: [...] // All types
   ```

### Memory Management

```typescript
// Clear results periodically in long-running applications
const manageMemory = () => {
  const analyzer = useCodeQualityAnalyzer();
  
  useEffect(() => {
    const interval = setInterval(() => {
      // Keep only recent results
      const recentResults = analyzer.analysisResults.filter(
        result => Date.now() - result.timestamp.getTime() < 3600000 // 1 hour
      );
      
      if (recentResults.length < analyzer.analysisResults.length) {
        analyzer.clearResults();
        // Re-add recent results if needed
      }
    }, 300000); // Every 5 minutes
    
    return () => clearInterval(interval);
  }, []);
};
```

## 🐛 Troubleshooting

### Common Issues

#### 1. File Watching Not Working
```typescript
// Check if chokidar is properly installed
npm install chokidar

// Verify file paths are correct
const analyzer = useCodeQualityAnalyzer({
  watchPaths: [path.resolve('src/**/*.tsx')] // Use absolute paths
});
```

#### 2. Analysis Not Triggering
```typescript
// Enable debug logging
const analyzer = useCodeQualityAnalyzer({
  enableRealTimeAnalysis: true,
  debounceMs: 100 // Reduce debounce for testing
});

// Check if files are being excluded
console.log('Excluded patterns:', excludePatterns);
```

#### 3. Performance Issues
```typescript
// Reduce analysis scope
const analyzer = useCodeQualityAnalyzer({
  watchPaths: ['src/components/**/*.tsx'], // More specific
  analysisTypes: ['code-smell'], // Fewer types
  debounceMs: 2000 // Longer debounce
});
```

### Debug Mode

```typescript
// Enable debug logging
const DEBUG = process.env.NODE_ENV === 'development';

const analyzer = useCodeQualityAnalyzer({
  // ... other options
});

// Add debug logging
if (DEBUG) {
  console.log('Analysis results:', analyzer.analysisResults);
  console.log('Is analyzing:', analyzer.isAnalyzing);
}
```

## 🤝 Contributing

### Adding New Analysis Rules

1. **Extend the analysis function**:
```typescript
// In useCodeQualityAnalyzer.ts
const analyzeCodeContent = (content: string, filePath: string): CodeSuggestion[] => {
  // ... existing code
  
  // Add new rule
  if (analysisTypes.includes('your-new-type')) {
    // Your analysis logic
    if (someCondition) {
      suggestions.push({
        type: 'your-new-type',
        line: lineNumber,
        message: 'Your message',
        suggestion: 'Your suggestion'
      });
    }
  }
};
```

2. **Add tests**:
```typescript
// In useCodeQualityAnalyzer.test.ts
it('should detect your new rule', async () => {
  const codeWithIssue = 'your test code';
  mockFs.readFileSync.mockReturnValue(codeWithIssue);
  
  const { result } = renderHook(() => useCodeQualityAnalyzer());
  
  await act(async () => {
    const analysisResult = await result.current.analyzeFile('test.ts');
    expect(analysisResult?.suggestions).toContainEqual(
      expect.objectContaining({
        type: 'your-new-type',
        message: 'Your message'
      })
    );
  });
});
```

3. **Update types**:
```typescript
// Add to CodeSuggestion type
type: 'code-smell' | 'design-pattern' | 'best-practice' | 'performance' | 'readability' | 'maintainability' | 'your-new-type';
```

## 📚 API Reference

### Hook Return Values

```typescript
interface UseCodeQualityAnalyzerReturn {
  analysisResults: CodeAnalysisResult[];     // All analysis results
  isAnalyzing: boolean;                      // Current analysis state
  startWatching: () => void;                 // Start file watching
  stopWatching: () => void;                  // Stop file watching
  analyzeFile: (filePath: string) => Promise<CodeAnalysisResult | null>; // Analyze specific file
  clearResults: () => void;                  // Clear all results
  getResultsForFile: (filePath: string) => CodeAnalysisResult[]; // Get results for file
}
```

### Data Types

```typescript
interface CodeAnalysisResult {
  file: string;                    // File path
  suggestions: CodeSuggestion[];   // List of suggestions
  timestamp: Date;                 // Analysis timestamp
  severity: 'low' | 'medium' | 'high'; // Overall severity
}

interface CodeSuggestion {
  type: 'code-smell' | 'design-pattern' | 'best-practice' | 'performance' | 'readability' | 'maintainability';
  line: number;                    // Line number
  column?: number;                 // Column number (optional)
  message: string;                 // Issue description
  suggestion: string;              // Improvement suggestion
  example?: string;                // Code example (optional)
}
```

## 📄 License

This code quality analyzer system is part of the Bookmark Manager Pro project and follows the same licensing terms.

## 🔗 Related Documentation

- [React Testing Library Documentation](https://testing-library.com/docs/react-testing-library/intro/)
- [Jest Testing Framework](https://jestjs.io/docs/getting-started)
- [Chokidar File Watching](https://github.com/paulmillr/chokidar)
- [TypeScript Best Practices](https://typescript-eslint.io/rules/)

---

**Happy Testing! 🚀**

This testing-focused code quality analyzer provides a robust foundation for maintaining high code standards while ensuring comprehensive test coverage. Use it to catch issues early, improve code quality, and maintain consistent coding practices across your project.