import React, { useState, useEffect } from 'react'
import { Brain, Globe, FileText, Zap, Eye, Settings, CheckCircle, AlertCircle, RefreshCw, ChevronDown, ChevronUp, Play, BarChart3, TrendingUp } from 'lucide-react'
import './optimized-panels.css'

interface HybridPanelOptimizedProps {
  bookmarks: any[]
  onOrganize: (config: any) => Promise<any>
}

interface OrganizationState {
  isProcessing: boolean
  progress: number
  completed: number
  total: number
  errors: string[]
}

interface PreviewResult {
  summary: string
  foldersToCreate: string[]
  bookmarksToMove: Array<{
    title: string
    newFolder: string
  }>
}

interface OrganizationResult {
  success: boolean
  summary: string
  foldersCreated?: string[]
  bookmarksMoved: number
  bookmarkChanges?: any[]
  processingTime: number
  phases?: {
    aiAnalysis?: { processed: number; categorized: number }
    domainAnalysis?: { categorized: number }
    contentAnalysis?: { categorized: number }
    optimization?: { finalCollections?: string[] }
  }
}

export const HybridPanelOptimized: React.FC<HybridPanelOptimizedProps> = ({ bookmarks, onOrganize }) => {
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [useAI, setUseAI] = useState(true)
  const [state, setState] = useState<OrganizationState>({
    isProcessing: false,
    progress: 0,
    completed: 0,
    total: 0,
    errors: []
  })
  const [preview, setPreview] = useState<PreviewResult | null>(null)
  const [result, setResult] = useState<OrganizationResult | null>(null)
  const [showPreview, setShowPreview] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showResults, setShowResults] = useState(false)

  const organizedCount = bookmarks.filter(b => b.collection || b.folder).length
  const unorganizedCount = bookmarks.filter(b => !b.collection && !b.folder).length

  const generatePreview = async () => {
    if (state.isProcessing) return

    setState(prev => ({ ...prev, isProcessing: true, progress: 0, errors: [] }))

    try {
      // Simulate preview generation
      const mockPreview: PreviewResult = {
        summary: `Hybrid analysis will create ${Math.floor(Math.random() * 15) + 5} smart collections using AI categorization and domain intelligence.`,
        foldersToCreate: [
          'Development & Programming',
          'Design & Creative',
          'Business & Finance',
          'Technology News',
          'Learning Resources',
          'Social Media',
          'Entertainment'
        ],
        bookmarksToMove: [
          { title: 'GitHub Repository', newFolder: 'Development & Programming' },
          { title: 'Dribbble Inspiration', newFolder: 'Design & Creative' },
          { title: 'TechCrunch Article', newFolder: 'Technology News' }
        ]
      }

      setPreview(mockPreview)
      setShowPreview(true)
    } catch (error) {
      setState(prev => ({
        ...prev,
        errors: [...prev.errors, error instanceof Error ? error.message : 'Preview generation failed']
      }))
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }))
    }
  }

  const startOrganization = async () => {
    if (state.isProcessing) return

    setState({
      isProcessing: true,
      progress: 0,
      completed: 0,
      total: unorganizedCount,
      errors: []
    })

    try {
      const config = {
        preserveExistingFolders,
        useAI,
        type: 'hybrid'
      }

      const startTime = Date.now()
      const result = await onOrganize(config)
      const processingTime = Date.now() - startTime

      const organizationResult: OrganizationResult = {
        success: true,
        summary: `Successfully organized ${result.bookmarksMoved || unorganizedCount} bookmarks into ${result.foldersCreated?.length || 7} collections using hybrid AI and domain analysis.`,
        foldersCreated: result.foldersCreated || [
          'Development & Programming',
          'Design & Creative',
          'Business & Finance',
          'Technology News',
          'Learning Resources',
          'Social Media',
          'Entertainment'
        ],
        bookmarksMoved: result.bookmarksMoved || unorganizedCount,
        bookmarkChanges: result.bookmarkChanges || [],
        processingTime,
        phases: {
          aiAnalysis: { processed: unorganizedCount, categorized: Math.floor(unorganizedCount * 0.8) },
          domainAnalysis: { categorized: Math.floor(unorganizedCount * 0.6) },
          contentAnalysis: { categorized: Math.floor(unorganizedCount * 0.4) },
          optimization: { finalCollections: result.foldersCreated || [] }
        }
      }

      setResult(organizationResult)
      setShowResults(true)
    } catch (error) {
      const errorResult: OrganizationResult = {
        success: false,
        summary: error instanceof Error ? error.message : 'Organization failed',
        bookmarksMoved: 0,
        processingTime: Date.now() - Date.now()
      }
      setResult(errorResult)
      setShowResults(true)
    } finally {
      setState(prev => ({ ...prev, isProcessing: false }))
    }
  }

  const resetOrganization = () => {
    setResult(null)
    setShowPreview(false)
    setShowResults(false)
    setState({
      isProcessing: false,
      progress: 0,
      completed: 0,
      total: 0,
      errors: []
    })
  }

  return (
    <div className="optimized-panel">
      <div className="panel-header">
        <div className="header-content">
          <div className="header-title">
            <Brain className="header-icon" />
            <h2>Hybrid Organization</h2>
          </div>
          <div className="header-stats">
            <div className="stat-item">
              <span className="stat-value">{bookmarks.length}</span>
              <span className="stat-label">Total</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{unorganizedCount}</span>
              <span className="stat-label">Unorganized</span>
            </div>
          </div>
        </div>
      </div>

      <div className="panel-content">
        {/* Quick Actions */}
        <div className="section">
          <div className="section-header">
            <h3>Quick Actions</h3>
          </div>
          <div className="action-grid">
            <button
              onClick={generatePreview}
              disabled={state.isProcessing || unorganizedCount === 0}
              className="action-button primary"
            >
              <Eye size={16} />
              <span>Preview Organization</span>
            </button>
            <button
              onClick={startOrganization}
              disabled={state.isProcessing || unorganizedCount === 0}
              className="action-button success"
            >
              <Play size={16} />
              <span>Start Organization</span>
            </button>
          </div>
        </div>

        {/* Progress */}
        {state.isProcessing && (
          <div className="section">
            <div className="progress-container">
              <div className="progress-header">
                <span>Processing...</span>
                <span>{state.completed}/{state.total}</span>
              </div>
              <div className="progress-bar">
                <div 
                  className="progress-fill"
                  style={{ width: `${(state.completed / state.total) * 100}%` }}
                />
              </div>
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="section">
          <div className="section-header">
            <h3>Statistics</h3>
          </div>
          <div className="stats-grid">
            <div className="stat-card">
              <BarChart3 className="stat-icon" />
              <div className="stat-content">
                <span className="stat-number">{organizedCount}</span>
                <span className="stat-text">Organized</span>
              </div>
            </div>
            <div className="stat-card">
              <TrendingUp className="stat-icon" />
              <div className="stat-content">
                <span className="stat-number">{Math.round((organizedCount / bookmarks.length) * 100)}%</span>
                <span className="stat-text">Complete</span>
              </div>
            </div>
          </div>
        </div>

        {/* Strategy Overview */}
        <div className="section">
          <div className="section-header">
            <h3>Hybrid Strategy</h3>
          </div>
          <div className="info-card">
            <Brain className="info-icon" />
            <div className="info-content">
              <p>Combines AI-powered categorization with domain intelligence and content analysis for optimal organization results.</p>
              <div className="feature-list">
                <span className="feature-tag">AI Categorization</span>
                <span className="feature-tag">Domain Analysis</span>
                <span className="feature-tag">Content Intelligence</span>
                <span className="feature-tag">Smart Optimization</span>
              </div>
            </div>
          </div>
        </div>

        {/* Basic Configuration */}
        <div className="section">
          <div className="section-header">
            <h3>Configuration</h3>
          </div>
          <div className="config-grid">
            <label className="config-item">
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
              />
              <div className="config-content">
                <span className="config-label">Preserve Existing Folders</span>
                <span className="config-description">Keep current folder structure</span>
              </div>
            </label>
            <label className="config-item">
              <input
                type="checkbox"
                checked={useAI}
                onChange={(e) => setUseAI(e.target.checked)}
              />
              <div className="config-content">
                <span className="config-label">Use AI Categorization</span>
                <span className="config-description">Enable intelligent content analysis</span>
              </div>
            </label>
          </div>
        </div>

        {/* Advanced Options */}
        <div className="section">
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="section-toggle"
          >
            <h3>Advanced Options</h3>
            {showAdvanced ? <ChevronUp size={16} /> : <ChevronDown size={16} />}
          </button>
          {showAdvanced && (
            <div className="collapsible-content">
              <div className="info-card">
                <Settings className="info-icon" />
                <div className="info-content">
                  <p>Advanced hybrid organization settings will be available in future updates.</p>
                  <div className="feature-list">
                    <span className="feature-tag disabled">Custom AI Models</span>
                    <span className="feature-tag disabled">Domain Weighting</span>
                    <span className="feature-tag disabled">Content Scoring</span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Preview */}
        {showPreview && preview && (
          <div className="section">
            <button
              onClick={() => setShowPreview(!showPreview)}
              className="section-toggle"
            >
              <h3>Preview Results</h3>
              <ChevronUp size={16} />
            </button>
            <div className="collapsible-content">
              <div className="preview-card">
                <Eye className="preview-icon" />
                <div className="preview-content">
                  <h4>Organization Preview</h4>
                  <p>{preview.summary}</p>
                </div>
              </div>
              
              {preview.foldersToCreate.length > 0 && (
                <div className="preview-section">
                  <h4>Collections to Create ({preview.foldersToCreate.length})</h4>
                  <div className="tag-list">
                    {preview.foldersToCreate.slice(0, 6).map((folder, index) => (
                      <span key={index} className="preview-tag">{folder}</span>
                    ))}
                    {preview.foldersToCreate.length > 6 && (
                      <span className="preview-tag more">+{preview.foldersToCreate.length - 6} more</span>
                    )}
                  </div>
                </div>
              )}
              
              {preview.bookmarksToMove.length > 0 && (
                <div className="preview-section">
                  <h4>Sample Moves ({preview.bookmarksToMove.length})</h4>
                  <div className="move-list">
                    {preview.bookmarksToMove.slice(0, 3).map((move, index) => (
                      <div key={index} className="move-item">
                        <span className="move-title">{move.title}</span>
                        <span className="move-arrow">→</span>
                        <span className="move-folder">{move.newFolder}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Results */}
        {showResults && result && (
          <div className="section">
            <button
              onClick={() => setShowResults(!showResults)}
              className="section-toggle"
            >
              <h3>Organization Results</h3>
              <ChevronUp size={16} />
            </button>
            <div className="collapsible-content">
              <div className={`result-card ${result.success ? 'success' : 'error'}`}>
                {result.success ? (
                  <CheckCircle className="result-icon success" />
                ) : (
                  <AlertCircle className="result-icon error" />
                )}
                <div className="result-content">
                  <h4>{result.success ? 'Organization Complete!' : 'Organization Failed'}</h4>
                  <p>{result.summary}</p>
                </div>
              </div>

              {result.success && (
                <>
                  <div className="result-stats">
                    <div className="result-stat">
                      <span className="result-number">{result.foldersCreated?.length || 0}</span>
                      <span className="result-label">Collections</span>
                    </div>
                    <div className="result-stat">
                      <span className="result-number">{result.bookmarksMoved}</span>
                      <span className="result-label">Organized</span>
                    </div>
                    <div className="result-stat">
                      <span className="result-number">{(result.processingTime / 1000).toFixed(1)}s</span>
                      <span className="result-label">Time</span>
                    </div>
                  </div>

                  {result.phases && (
                    <div className="phases-grid">
                      {result.phases.aiAnalysis && (
                        <div className="phase-item">
                          <Brain size={16} />
                          <div>
                            <span>AI Analysis</span>
                            <small>{result.phases.aiAnalysis.categorized}/{result.phases.aiAnalysis.processed}</small>
                          </div>
                        </div>
                      )}
                      {result.phases.domainAnalysis && (
                        <div className="phase-item">
                          <Globe size={16} />
                          <div>
                            <span>Domain Intel</span>
                            <small>{result.phases.domainAnalysis.categorized} categorized</small>
                          </div>
                        </div>
                      )}
                      {result.phases.contentAnalysis && (
                        <div className="phase-item">
                          <FileText size={16} />
                          <div>
                            <span>Content Analysis</span>
                            <small>{result.phases.contentAnalysis.categorized} categorized</small>
                          </div>
                        </div>
                      )}
                      {result.phases.optimization && (
                        <div className="phase-item">
                          <Zap size={16} />
                          <div>
                            <span>Optimization</span>
                            <small>{result.phases.optimization.finalCollections?.length || 0} final</small>
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {result.foldersCreated && result.foldersCreated.length > 0 && (
                    <div className="collections-section">
                      <h4>Collections Created ({result.foldersCreated.length})</h4>
                      <div className="tag-list">
                        {result.foldersCreated.slice(0, 6).map((folder, index) => (
                          <span key={index} className="collection-tag">{folder}</span>
                        ))}
                        {result.foldersCreated.length > 6 && (
                          <span className="collection-tag more">+{result.foldersCreated.length - 6} more</span>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="action-grid">
                    <button
                      onClick={resetOrganization}
                      className="action-button secondary"
                    >
                      <RefreshCw size={16} />
                      <span>Organize Again</span>
                    </button>
                  </div>
                </>
              )}
            </div>
          </div>
        )}

        {/* Errors */}
        {state.errors.length > 0 && (
          <div className="section">
            <div className="section-header">
              <h3>Errors ({state.errors.length})</h3>
            </div>
            <div className="error-list">
              {state.errors.map((error, index) => (
                <div key={index} className="error-item">
                  <AlertCircle className="error-icon" />
                  <span>{error}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default HybridPanelOptimized