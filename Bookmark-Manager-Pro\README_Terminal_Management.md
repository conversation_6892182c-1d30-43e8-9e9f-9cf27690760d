# Enhanced Terminal Management System for Trae.ai IDE

A comprehensive Windows Terminal session management solution that provides tmux-like functionality for the Trae.ai IDE environment. This system ensures proper terminal isolation, session persistence, and compliance with IDE execution rules.

## 🚀 Features

### Core Functionality
- **Windows Terminal Integration**: Seamless integration with Windows Terminal for enhanced session management
- **Session Persistence**: Create, manage, and recover terminal sessions across system restarts
- **Parallel Execution**: Run multiple test sessions simultaneously with resource management
- **Compliance Checking**: Validate terminal execution rules and environment setup
- **Session Recovery**: Automatic recovery of sessions after crashes or system restarts
- **IDE Detection**: Intelligent detection of IDE environments to enforce proper terminal usage

### Advanced Features
- **tmux-like Session Management**: Create, attach, detach, and list sessions
- **Automated Cleanup**: Automatic cleanup of stale sessions and recovery snapshots
- **Resource Monitoring**: Monitor system resources during parallel execution
- **Comprehensive Logging**: Detailed logging for debugging and audit purposes
- **Cross-Platform Compatibility**: Designed for Windows with PowerShell and Python integration

## 📋 Prerequisites

### Required Software
- **Windows 10/11** with Windows Terminal installed
- **Python 3.7+** with the following packages:
  - `psutil` - Process and system monitoring
  - `pathlib` - Path manipulation (built-in)
  - `json` - JSON handling (built-in)
- **PowerShell 5.1+** or **PowerShell Core 7+**
- **Windows Terminal** (recommended) or any compatible terminal emulator

### Installation

1. **Install Python Dependencies**:
   ```bash
   pip install psutil
   ```

2. **Verify Windows Terminal Installation**:
   ```bash
   wt.exe --version
   ```

3. **Set Execution Policy** (if needed):
   ```powershell
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

## 🏗️ System Architecture

### Core Components

```
📁 Terminal Management System
├── 🐍 wt_session_manager.py      # Core session management
├── 📜 run_tests_with_terminal.ps1 # PowerShell integration
├── 🔍 terminal_compliance.py     # Compliance validation
├── 🔄 session_recovery.py        # Session recovery system
├── 🖥️ terminal_manager.bat       # User-friendly interface
├── ⚙️ terminal_rules_enhanced.json # Configuration
└── 📚 README_Terminal_Management.md # Documentation
```

### Data Flow

```mermaid
graph TD
    A[User Command] --> B[terminal_manager.bat]
    B --> C[PowerShell Script]
    C --> D[Python Session Manager]
    D --> E[Windows Terminal]
    D --> F[Session Storage]
    F --> G[Recovery System]
    H[Compliance Checker] --> D
```

## 🚀 Quick Start

### 1. Basic Usage

```bash
# Check system compliance
terminal_manager.bat check

# Create a new test session
terminal_manager.bat create my-test-session unit

# List active sessions
terminal_manager.bat list

# Run parallel test sessions
terminal_manager.bat parallel unit,integration
```

### 2. Session Management

```bash
# Create a named session
terminal_manager.bat create frontend-tests integration

# Attach to existing session
terminal_manager.bat attach frontend-tests

# Stop a session
terminal_manager.bat kill frontend-tests

# Clean up stale sessions
terminal_manager.bat cleanup
```

### 3. Advanced Operations

```bash
# Check system status
terminal_manager.bat status

# Run compliance check with detailed report
python terminal_compliance.py --report compliance_report.txt

# Create recovery snapshot
python session_recovery.py --create-snapshot

# Recover from latest snapshot
python session_recovery.py --auto-recover
```

## 📖 Detailed Usage

### Windows Terminal Session Manager

The core Python script provides comprehensive session management:

```python
# Direct Python usage
from wt_session_manager import WindowsTerminalSessionManager

manager = WindowsTerminalSessionManager()

# Create session
manager.create_session("test-session", test_type="unit")

# List sessions
sessions = manager.list_sessions()

# Run parallel sessions
manager.run_parallel_sessions(["unit", "integration"])
```

### PowerShell Integration

The PowerShell script provides IDE integration and enhanced functionality:

```powershell
# Run with specific test type
.\run_tests_with_terminal.ps1 -TestType "integration"

# Run in session mode
.\run_tests_with_terminal.ps1 -SessionMode -SessionName "my-session"

# Run parallel tests
.\run_tests_with_terminal.ps1 -Parallel -TestTypes @("unit", "integration")

# List active sessions
.\run_tests_with_terminal.ps1 -ListSessions
```

### Compliance Checking

Ensure your environment meets all requirements:

```bash
# Basic compliance check
python terminal_compliance.py

# Generate detailed report
python terminal_compliance.py --report compliance_report.txt

# Quiet mode (exit code only)
python terminal_compliance.py --quiet
```

### Session Recovery

Recover sessions after system restarts or crashes:

```bash
# Create snapshot of current sessions
python session_recovery.py --create-snapshot

# List available snapshots
python session_recovery.py --list-snapshots

# Recover from specific snapshot
python session_recovery.py --recover snapshot_20231201_143022.json

# Auto-recovery (non-interactive)
python session_recovery.py --auto-recover --non-interactive
```

## ⚙️ Configuration

### Configuration File: `terminal_rules_enhanced.json`

```json
{
  "rules": {
    "require_new_terminal_for_tests": true,
    "require_environment_path_setup": true,
    "detect_ide_embedded_terminals": true,
    "enable_session_management": true,
    "enable_parallel_execution": true
  },
  "windows_terminal": {
    "executable_path": "wt.exe",
    "default_profile": "PowerShell",
    "session_persistence": true,
    "max_parallel_sessions": 4,
    "tab_naming_pattern": "Test-{type}-{timestamp}"
  },
  "session_management": {
    "session_storage_path": "./.terminal_sessions",
    "auto_cleanup_hours": 24,
    "session_recovery_enabled": true
  }
}
```

### Key Configuration Options

- **`require_new_terminal_for_tests`**: Enforce external terminal usage for tests
- **`detect_ide_embedded_terminals`**: Detect and warn about IDE terminal usage
- **`max_parallel_sessions`**: Limit concurrent session count
- **`session_recovery_enabled`**: Enable automatic session recovery
- **`auto_cleanup_hours`**: Hours before cleaning up stale sessions

## 🔧 Advanced Features

### Parallel Execution

Run multiple test types simultaneously:

```bash
# Run unit and integration tests in parallel
terminal_manager.bat parallel unit,integration,performance

# PowerShell with custom configuration
.\run_tests_with_terminal.ps1 -Parallel -TestTypes @("unit", "integration") -Verbose
```

### Session Persistence

Sessions survive system restarts:

```bash
# Create persistent session
python wt_session_manager.py --create-session "persistent-test" --type "integration" --persistent

# Auto-recover after restart
python session_recovery.py --auto-recover
```

### Resource Monitoring

Monitor system resources during execution:

```json
"parallel_execution": {
  "resource_limits": {
    "max_memory_mb": 2048,
    "max_cpu_percent": 80
  },
  "coordination": {
    "wait_between_starts_ms": 1000,
    "health_check_interval_s": 30
  }
}
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Windows Terminal Not Found
```bash
Error: Windows Terminal not found at wt.exe
```
**Solution**: Install Windows Terminal from Microsoft Store or GitHub releases.

#### 2. Python Package Missing
```bash
Error: Required Python package missing: psutil
```
**Solution**: Install required packages:
```bash
pip install psutil
```

#### 3. PowerShell Execution Policy
```bash
Execution of scripts is disabled on this system
```
**Solution**: Update execution policy:
```powershell
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

#### 4. Session Storage Permissions
```bash
Error: Insufficient permissions for session storage
```
**Solution**: Ensure write permissions to the session storage directory.

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
# PowerShell verbose mode
.\run_tests_with_terminal.ps1 -Verbose

# Python debug logging
python wt_session_manager.py --create-session "debug-test" --verbose
```

### Log Files

Check log files for detailed error information:

- `./logs/terminal_management.log` - General operations
- `./logs/terminal_compliance.log` - Compliance checks
- `./logs/session_recovery.log` - Recovery operations

## 🔒 Security Considerations

### Best Practices

1. **Execution Policy**: Use `RemoteSigned` execution policy for PowerShell scripts
2. **File Permissions**: Restrict access to session storage directories
3. **Process Isolation**: Each session runs in isolated Windows Terminal tabs
4. **Audit Logging**: All operations are logged for security auditing

### Session Security

- Sessions are isolated in separate Windows Terminal tabs
- Process IDs are tracked for proper cleanup
- Session files contain minimal sensitive information
- Automatic cleanup prevents session accumulation

## 📊 Monitoring and Maintenance

### Health Checks

```bash
# System status check
terminal_manager.bat status

# Compliance validation
terminal_manager.bat check

# Session cleanup
terminal_manager.bat cleanup
```

### Automated Maintenance

Set up automated maintenance tasks:

```bash
# Schedule periodic snapshots (every 15 minutes)
python session_recovery.py --schedule-snapshots 15

# Daily cleanup (via Task Scheduler)
terminal_manager.bat cleanup
```

### Performance Monitoring

Monitor system performance during parallel execution:

- Memory usage tracking
- CPU utilization monitoring
- Session health checks
- Resource limit enforcement

## 🤝 Contributing

### Development Setup

1. Clone the repository
2. Install development dependencies
3. Run compliance checks
4. Test with sample sessions

### Testing

```bash
# Test compliance checker
python terminal_compliance.py --config test_config.json

# Test session management
python wt_session_manager.py --create-session "test" --type "unit"

# Test recovery system
python session_recovery.py --create-snapshot
```

## 📝 License

This project is part of the Trae.ai IDE ecosystem and follows the same licensing terms.

## 🆘 Support

For support and questions:

1. Check the troubleshooting section
2. Review log files for error details
3. Run compliance checks to identify issues
4. Consult the Trae.ai IDE documentation

---

**Version**: 2.0  
**Last Updated**: December 2024  
**Compatibility**: Windows 10/11, PowerShell 5.1+, Python 3.7+