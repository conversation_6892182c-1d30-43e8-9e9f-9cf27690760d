<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Recovery Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            border-left: 5px solid #3498db;
        }

        .status-card.critical {
            border-left-color: #e74c3c;
        }

        .status-card.warning {
            border-left-color: #f39c12;
        }

        .status-card.success {
            border-left-color: #27ae60;
        }

        .status-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.3em;
        }

        .memory-usage {
            font-size: 2.5em;
            font-weight: bold;
            margin: 10px 0;
        }

        .memory-usage.critical {
            color: #e74c3c;
        }

        .memory-usage.warning {
            color: #f39c12;
        }

        .memory-usage.safe {
            color: #27ae60;
        }

        .progress-bar {
            width: 100%;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }

        .progress-fill.critical {
            background: #e74c3c;
        }

        .progress-fill.warning {
            background: #f39c12;
        }

        .progress-fill.safe {
            background: #27ae60;
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-warning {
            background: #f39c12;
            color: white;
        }

        .btn-danger {
            background: #e74c3c;
            color: white;
        }

        .btn-success {
            background: #27ae60;
            color: white;
        }

        .log-container {
            background: #2c3e50;
            border-radius: 10px;
            padding: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            color: #ecf0f1;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-entry.info {
            background: rgba(52, 152, 219, 0.2);
        }

        .log-entry.warning {
            background: rgba(243, 156, 18, 0.2);
        }

        .log-entry.error {
            background: rgba(231, 76, 60, 0.2);
        }

        .log-entry.success {
            background: rgba(39, 174, 96, 0.2);
        }

        .timestamp {
            color: #95a5a6;
            font-size: 0.8em;
        }

        .recommendations {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .recommendations h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .recommendation {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #3498db;
        }

        .recommendation.critical {
            border-left-color: #e74c3c;
        }

        .recommendation.warning {
            border-left-color: #f39c12;
        }

        .recommendation.success {
            border-left-color: #27ae60;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.active {
            background: #27ae60;
            animation: pulse 2s infinite;
        }

        .status-indicator.inactive {
            background: #95a5a6;
        }

        .status-indicator.error {
            background: #e74c3c;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .auto-refresh {
            text-align: center;
            margin-top: 20px;
            color: #7f8c8d;
        }

        .emergency-banner {
            background: #e74c3c;
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
            text-align: center;
            font-weight: bold;
            animation: flash 1s infinite;
        }

        @keyframes flash {
            0%, 50% { opacity: 1; }
            25%, 75% { opacity: 0.7; }
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🛡️ Memory Recovery Dashboard</h1>
            <p>Comprehensive Memory Management & Recovery System</p>
        </div>

        <div id="emergencyBanner" class="emergency-banner hidden">
            🚨 CRITICAL MEMORY SITUATION DETECTED - EMERGENCY PROTOCOLS ACTIVE
        </div>

        <div class="status-grid">
            <div class="status-card" id="memoryCard">
                <h3>📊 Memory Usage</h3>
                <div class="memory-usage safe" id="memoryUsage">---%</div>
                <div class="progress-bar">
                    <div class="progress-fill safe" id="memoryProgress"></div>
                </div>
                <p id="memoryStatus">Initializing...</p>
            </div>

            <div class="status-card" id="systemCard">
                <h3>🖥️ System Health</h3>
                <p><span class="status-indicator active" id="systemIndicator"></span><span id="systemHealth">Checking...</span></p>
                <p><strong>Preventive Monitoring:</strong> <span id="preventiveStatus">Initializing...</span></p>
                <p><strong>Emergency Mode:</strong> <span id="emergencyStatus">Inactive</span></p>
            </div>

            <div class="status-card" id="cleanupCard">
                <h3>🧹 Cleanup Systems</h3>
                <p><span class="status-indicator" id="silentIndicator"></span>Silent Emergency: <span id="silentStatus">Checking...</span></p>
                <p><span class="status-indicator" id="protectionIndicator"></span>Memory Protection: <span id="protectionStatus">Checking...</span></p>
                <p><strong>Last Cleanup:</strong> <span id="lastCleanup">Never</span></p>
            </div>

            <div class="status-card" id="recoveryCard">
                <h3>🔄 Recovery Status</h3>
                <p><strong>Recovery Attempts:</strong> <span id="recoveryAttempts">0</span></p>
                <p><strong>Last Recovery:</strong> <span id="lastRecovery">None</span></p>
                <p><strong>Success Rate:</strong> <span id="successRate">100%</span></p>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" onclick="monitorRecovery()">📊 Monitor Recovery</button>
            <button class="btn btn-warning" onclick="checkStatus()">✅ Check Status</button>
            <button class="btn btn-success" onclick="performCleanup()">🧹 Manual Cleanup</button>
            <button class="btn btn-danger" onclick="emergencyRefresh()">🔄 Emergency Refresh</button>
        </div>

        <div class="recommendations" id="recommendationsContainer">
            <h3>💡 Current Recommendations</h3>
            <div id="recommendations">
                <div class="recommendation">
                    <strong>System Status:</strong> Initializing memory recovery system...
                </div>
            </div>
        </div>

        <div class="log-container">
            <div id="logOutput">
                <div class="log-entry info">
                    <span class="timestamp">[${new Date().toLocaleTimeString()}]</span>
                    <span>🛡️ Memory Recovery Dashboard initialized</span>
                </div>
            </div>
        </div>

        <div class="auto-refresh">
            <p>🔄 Auto-refreshing every 3 seconds | Last update: <span id="lastUpdate">--:--:--</span></p>
        </div>
    </div>

    <script src="memory-recovery-implementation.js"></script>
    <script>
        class MemoryRecoveryDashboard {
            constructor() {
                this.updateInterval = null;
                this.logCount = 0;
                this.maxLogs = 50;
                
                this.init();
            }

            init() {
                this.log('🚀 Dashboard initializing...', 'info');
                
                // Wait for memory recovery system to load
                setTimeout(() => {
                    this.startAutoUpdate();
                    this.log('✅ Dashboard ready', 'success');
                }, 1000);
            }

            startAutoUpdate() {
                this.updateDashboard();
                
                this.updateInterval = setInterval(() => {
                    this.updateDashboard();
                }, 3000);
            }

            async updateDashboard() {
                try {
                    await this.updateMemoryStatus();
                    await this.updateSystemStatus();
                    await this.updateCleanupStatus();
                    await this.updateRecoveryStatus();
                    await this.updateRecommendations();
                    
                    document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
                } catch (error) {
                    this.log(`❌ Dashboard update error: ${error.message}`, 'error');
                }
            }

            async updateMemoryStatus() {
                if (!window.memoryRecoverySystem) {
                    return;
                }

                const usage = window.memoryRecoverySystem.getCurrentUsage();
                const usageElement = document.getElementById('memoryUsage');
                const progressElement = document.getElementById('memoryProgress');
                const statusElement = document.getElementById('memoryStatus');
                const cardElement = document.getElementById('memoryCard');
                const emergencyBanner = document.getElementById('emergencyBanner');

                // Update usage display
                usageElement.textContent = `${usage.toFixed(1)}%`;
                progressElement.style.width = `${Math.min(usage, 100)}%`;

                // Update status classes
                const classes = ['safe', 'warning', 'critical'];
                classes.forEach(cls => {
                    usageElement.classList.remove(cls);
                    progressElement.classList.remove(cls);
                    cardElement.classList.remove(cls);
                });

                let statusClass = 'safe';
                let statusText = 'Memory usage is optimal';

                if (usage > 95) {
                    statusClass = 'critical';
                    statusText = 'CRITICAL - Emergency protocols active';
                    emergencyBanner.classList.remove('hidden');
                } else if (usage > 85) {
                    statusClass = 'critical';
                    statusText = 'CRITICAL - Immediate action required';
                    emergencyBanner.classList.remove('hidden');
                } else if (usage > 70) {
                    statusClass = 'warning';
                    statusText = 'WARNING - Monitoring closely';
                    emergencyBanner.classList.add('hidden');
                } else {
                    statusText = 'Memory usage is healthy';
                    emergencyBanner.classList.add('hidden');
                }

                usageElement.classList.add(statusClass);
                progressElement.classList.add(statusClass);
                cardElement.classList.add(statusClass);
                statusElement.textContent = statusText;
            }

            async updateSystemStatus() {
                if (!window.memoryRecoverySystem) {
                    return;
                }

                const state = window.memoryRecoverySystem.getState();
                
                // System health
                const healthElement = document.getElementById('systemHealth');
                const healthIndicator = document.getElementById('systemIndicator');
                
                healthElement.textContent = state.systemHealth || 'Unknown';
                
                const indicatorClasses = ['active', 'inactive', 'error'];
                indicatorClasses.forEach(cls => healthIndicator.classList.remove(cls));
                
                if (state.systemHealth === 'healthy') {
                    healthIndicator.classList.add('active');
                } else if (state.systemHealth === 'error') {
                    healthIndicator.classList.add('error');
                } else {
                    healthIndicator.classList.add('inactive');
                }

                // Preventive monitoring
                document.getElementById('preventiveStatus').textContent = 
                    state.preventiveActive ? 'Active' : 'Inactive';

                // Emergency mode
                document.getElementById('emergencyStatus').textContent = 
                    state.emergencyMode ? 'ACTIVE' : 'Inactive';
            }

            async updateCleanupStatus() {
                if (!window.memoryRecoverySystem) {
                    return;
                }

                try {
                    const status = await window.memoryRecoverySystem.checkStatus();
                    
                    // Silent emergency cleanup
                    const silentIndicator = document.getElementById('silentIndicator');
                    const silentStatus = document.getElementById('silentStatus');
                    
                    if (status.cleanupSystems.silentEmergency?.available) {
                        silentIndicator.classList.add('active');
                        silentStatus.textContent = 'Available';
                    } else {
                        silentIndicator.classList.add('inactive');
                        silentStatus.textContent = 'Not Available';
                    }

                    // Memory protection
                    const protectionIndicator = document.getElementById('protectionIndicator');
                    const protectionStatus = document.getElementById('protectionStatus');
                    
                    if (status.cleanupSystems.memoryProtection?.available) {
                        protectionIndicator.classList.add('active');
                        protectionStatus.textContent = 'Available';
                    } else {
                        protectionIndicator.classList.add('inactive');
                        protectionStatus.textContent = 'Not Available';
                    }

                } catch (error) {
                    this.log(`⚠️ Cleanup status check failed: ${error.message}`, 'warning');
                }
            }

            async updateRecoveryStatus() {
                if (!window.memoryRecoverySystem) {
                    return;
                }

                const state = window.memoryRecoverySystem.getState();
                
                document.getElementById('recoveryAttempts').textContent = state.recoveryAttempts || 0;
                
                const lastRecovery = state.cleanupHistory?.length > 0 
                    ? new Date(state.cleanupHistory[state.cleanupHistory.length - 1].startTime).toLocaleTimeString()
                    : 'None';
                    
                document.getElementById('lastRecovery').textContent = lastRecovery;
                
                // Calculate success rate
                const successfulRecoveries = state.cleanupHistory?.filter(r => 
                    r.effectiveness === 'excellent' || r.effectiveness === 'good'
                ).length || 0;
                
                const totalRecoveries = state.cleanupHistory?.length || 0;
                const successRate = totalRecoveries > 0 
                    ? Math.round((successfulRecoveries / totalRecoveries) * 100)
                    : 100;
                    
                document.getElementById('successRate').textContent = `${successRate}%`;
            }

            async updateRecommendations() {
                if (!window.memoryRecoverySystem) {
                    return;
                }

                try {
                    const report = await window.memoryRecoverySystem.getSystemReport();
                    const recommendations = report.recommendations || [];
                    
                    const container = document.getElementById('recommendations');
                    container.innerHTML = '';
                    
                    if (recommendations.length === 0) {
                        container.innerHTML = '<div class="recommendation"><strong>Status:</strong> All systems operating normally</div>';
                        return;
                    }
                    
                    recommendations.forEach(rec => {
                        const div = document.createElement('div');
                        div.className = `recommendation ${rec.priority}`;
                        div.innerHTML = `<strong>${rec.priority.toUpperCase()}:</strong> ${rec.message}`;
                        container.appendChild(div);
                    });
                    
                } catch (error) {
                    this.log(`⚠️ Recommendations update failed: ${error.message}`, 'warning');
                }
            }

            log(message, type = 'info') {
                const logOutput = document.getElementById('logOutput');
                const timestamp = new Date().toLocaleTimeString();
                
                const logEntry = document.createElement('div');
                logEntry.className = `log-entry ${type}`;
                logEntry.innerHTML = `
                    <span class="timestamp">[${timestamp}]</span>
                    <span>${message}</span>
                `;
                
                logOutput.appendChild(logEntry);
                
                // Limit log entries
                this.logCount++;
                if (this.logCount > this.maxLogs) {
                    logOutput.removeChild(logOutput.firstChild);
                    this.logCount--;
                }
                
                // Auto-scroll to bottom
                logOutput.scrollTop = logOutput.scrollHeight;
            }
        }

        // Global functions for buttons
        async function monitorRecovery() {
            if (!window.memoryRecoverySystem) {
                dashboard.log('❌ Memory recovery system not available', 'error');
                return;
            }

            dashboard.log('📊 Starting memory recovery monitoring...', 'info');
            
            try {
                const result = await window.memoryRecoverySystem.monitorRecovery(30000);
                dashboard.log(`✅ Recovery monitoring completed: ${result.effectiveness}`, 'success');
            } catch (error) {
                dashboard.log(`❌ Recovery monitoring failed: ${error.message}`, 'error');
            }
        }

        async function checkStatus() {
            if (!window.memoryRecoverySystem) {
                dashboard.log('❌ Memory recovery system not available', 'error');
                return;
            }

            dashboard.log('✅ Checking system status...', 'info');
            
            try {
                const status = await window.memoryRecoverySystem.checkStatus();
                dashboard.log('📋 Status check completed - see console for details', 'success');
                console.log('🔍 Detailed Status:', status);
            } catch (error) {
                dashboard.log(`❌ Status check failed: ${error.message}`, 'error');
            }
        }

        async function performCleanup() {
            if (!window.memoryRecoverySystem) {
                dashboard.log('❌ Memory recovery system not available', 'error');
                return;
            }

            dashboard.log('🧹 Performing manual cleanup...', 'info');
            
            try {
                await window.memoryRecoverySystem.emergencyCleanup();
                dashboard.log('✅ Manual cleanup completed', 'success');
            } catch (error) {
                dashboard.log(`❌ Manual cleanup failed: ${error.message}`, 'error');
            }
        }

        function emergencyRefresh() {
            if (!window.memoryRecoverySystem) {
                dashboard.log('❌ Memory recovery system not available', 'error');
                return;
            }

            dashboard.log('🚨 Emergency refresh initiated...', 'warning');
            window.memoryRecoverySystem.forceRefresh();
        }

        // Initialize dashboard
        const dashboard = new MemoryRecoveryDashboard();

        // Handle page visibility
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                dashboard.log('👁️ Dashboard hidden - pausing updates', 'info');
                if (dashboard.updateInterval) {
                    clearInterval(dashboard.updateInterval);
                }
            } else {
                dashboard.log('👁️ Dashboard visible - resuming updates', 'info');
                dashboard.startAutoUpdate();
            }
        });

        console.log('🛡️ Memory Recovery Dashboard loaded');
        console.log('📋 Available functions: monitorRecovery(), checkStatus(), performCleanup(), emergencyRefresh()');
    </script>
</body>
</html>