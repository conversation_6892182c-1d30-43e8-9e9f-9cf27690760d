import React, { useState } from 'react';

interface SimpleFaviconProps {
  url: string;
  title: string;
  favicon?: string;
  className?: string;
}

export const SimpleFavicon: React.FC<SimpleFaviconProps> = ({
  url,
  title,
  favicon,
  className = 'favicon-image'
}) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  // Generate favicon URL
  const faviconUrl = favicon || (() => {
    try {
      const domain = new URL(url).hostname.replace(/^www\./, '');
      return `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    } catch {
      return '';
    }
  })();

  // Fallback letter icon
  const fallbackLetter = title.charAt(0).toUpperCase();

  // Show favicon if available and no error
  if (faviconUrl && !imageError) {
    return (
      <img
        src={faviconUrl}
        alt={`${title} favicon`}
        className={className}
        onError={handleImageError}
        loading="lazy"
        decoding="async"
        style={{
          width: '32px',
          height: '32px',
          objectFit: 'contain'
        }}
      />
    );
  }

  // Fallback to letter icon
  return (
    <div 
      className="favicon-fallback"
      style={{ 
        width: '32px', 
        height: '32px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f3f4f6',
        borderRadius: '4px',
        fontSize: '14px',
        fontWeight: '500',
        color: '#374151',
        border: '1px solid #e5e7eb'
      }}
    >
      {fallbackLetter}
    </div>
  );
};

export default SimpleFavicon;
