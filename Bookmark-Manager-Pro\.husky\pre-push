#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚀 Running pre-push checks..."

# Run TypeScript type checking via ESLint (NO_TSC_RULE compliant)
echo "📝 Running TypeScript checks via ESLint..."
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ TypeScript/ESLint check failed. Please fix all type errors before pushing."
  exit 1
fi

# Run full test suite
echo "🧪 Running full test suite..."
npm run test
if [ $? -ne 0 ]; then
  echo "❌ Test suite failed. Please fix all failing tests before pushing."
  exit 1
fi

# Run build to ensure production readiness
echo "🏗️ Testing production build..."
npm run build
if [ $? -ne 0 ]; then
  echo "❌ Production build failed. Please fix build errors before pushing."
  exit 1
fi

# Type coverage check disabled (NO_TSC_RULE compliant)
echo "📊 Type coverage check disabled - relying on ESLint and IDE integration for type safety..."
# Note: Type coverage is maintained through ESLint TypeScript rules and IDE integration

echo "✅ All pre-push checks passed! Ready to push."