/**
 * TEST BOOKMARK NAVIGATION
 * Quick test to verify bookmark cards are clickable and navigate to URLs
 */

console.log('🧪 Testing Bookmark Navigation...');

// Wait for page to load
setTimeout(() => {
  console.log('🔍 Looking for bookmark cards...');
  
  // Find bookmark cards
  const bookmarkCards = document.querySelectorAll('.bookmark-card-front-redesigned');
  console.log(`📋 Found ${bookmarkCards.length} bookmark cards`);
  
  if (bookmarkCards.length === 0) {
    console.warn('⚠️ No bookmark cards found. Make sure you have bookmarks loaded.');
    return;
  }
  
  // Test first bookmark card
  const firstCard = bookmarkCards[0];
  console.log('🎯 Testing first bookmark card...');
  
  // Check if it has cursor pointer
  const computedStyle = window.getComputedStyle(firstCard);
  const cursor = computedStyle.cursor;
  console.log(`👆 Cursor style: ${cursor}`);
  
  if (cursor === 'pointer') {
    console.log('✅ Bookmark card has pointer cursor - clickable!');
  } else {
    console.warn('⚠️ Bookmark card does not have pointer cursor');
  }
  
  // Check if click handler exists
  const hasClickHandler = firstCard.onclick !== null;
  console.log(`🖱️ Has click handler: ${hasClickHandler}`);
  
  // Add test click listener to verify events
  let clickCount = 0;
  firstCard.addEventListener('click', (e) => {
    clickCount++;
    console.log(`🎯 Bookmark card clicked! (${clickCount} times)`);
    console.log('📍 Click target:', e.target);
    console.log('📍 Current target:', e.currentTarget);
  });
  
  // Simulate a test click after 2 seconds
  setTimeout(() => {
    console.log('🤖 Simulating click on first bookmark card...');
    firstCard.click();
    
    setTimeout(() => {
      if (clickCount > 0) {
        console.log('✅ SUCCESS: Bookmark card click event fired!');
        console.log('🌐 Check if a new tab opened with the bookmark URL');
      } else {
        console.error('❌ FAILED: Bookmark card click event did not fire');
      }
    }, 500);
  }, 2000);
  
  // Test keyboard navigation
  setTimeout(() => {
    console.log('⌨️ Testing keyboard navigation...');
    firstCard.focus();
    
    // Simulate Enter key
    const enterEvent = new KeyboardEvent('keydown', {
      key: 'Enter',
      code: 'Enter',
      keyCode: 13,
      which: 13,
      bubbles: true
    });
    
    firstCard.dispatchEvent(enterEvent);
    console.log('⌨️ Enter key simulated');
  }, 3000);
  
}, 1000);

// Add visual indicator for testing
setTimeout(() => {
  const style = document.createElement('style');
  style.textContent = `
    .bookmark-card-front-redesigned {
      position: relative;
    }
    
    .bookmark-card-front-redesigned::after {
      content: '🖱️ Click to visit';
      position: absolute;
      top: 5px;
      right: 5px;
      background: rgba(59, 130, 246, 0.9);
      color: white;
      padding: 2px 6px;
      border-radius: 4px;
      font-size: 10px;
      font-weight: bold;
      opacity: 0;
      transition: opacity 0.2s ease;
      pointer-events: none;
      z-index: 1000;
    }
    
    .bookmark-card-front-redesigned:hover::after {
      opacity: 1;
    }
  `;
  document.head.appendChild(style);
  console.log('✨ Added visual click indicators to bookmark cards');
}, 1500);

// Monitor for new tabs opening
let originalOpen = window.open;
window.open = function(...args) {
  console.log('🌐 NEW TAB OPENED:', args[0]);
  console.log('✅ Bookmark navigation is working!');
  return originalOpen.apply(this, args);
};

console.log('🎯 Navigation test setup complete. Watch the console for results.');
console.log('💡 You can also manually click on bookmark cards to test navigation.');
