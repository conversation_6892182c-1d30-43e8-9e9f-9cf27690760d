describe('Visual Regression Testing - Layout Consistency', () => {
  const standardViewports = [
    { name: 'mobile', width: 375, height: 667 },
    { name: 'tablet', width: 768, height: 1024 },
    { name: 'desktop', width: 1280, height: 720 },
    { name: 'large-desktop', width: 1920, height: 1080 }
  ];

  beforeEach(() => {
    cy.visit('/');
    cy.loadTestBookmarks();
  });

  describe('Cross-Viewport Visual Consistency', () => {
    standardViewports.forEach(viewport => {
      context(`${viewport.name} viewport`, () => {
        beforeEach(() => {
          cy.viewport(viewport.width, viewport.height);
          cy.wait(1000); // Allow layout to settle
        });

        it('should maintain visual consistency on homepage', () => {
          // Take screenshot for visual comparison
          cy.screenshot(`homepage-${viewport.name}`, {
            capture: 'viewport',
            clip: { x: 0, y: 0, width: viewport.width, height: viewport.height }
          });

          // Check layout doesn't break
          cy.checkElementOverflow('body');
          
          // Verify main content areas are visible
          cy.get('main, [data-testid="main-content"], .main-content')
            .should('be.visible')
            .and('have.css', 'display')
            .and('not.equal', 'none');
        });

        it('should handle bookmark list layout consistently', () => {
          cy.get('[data-testid="bookmark-list"], .bookmark-list')
            .should('be.visible');

          // Screenshot bookmark list
          cy.get('[data-testid="bookmark-list"], .bookmark-list')
            .screenshot(`bookmark-list-${viewport.name}`);

          // Check individual bookmark items
          cy.get('[data-testid="bookmark-item"], .bookmark-item')
            .should('have.length.greaterThan', 0)
            .each($item => {
              cy.wrap($item).should('be.visible');
              
              // Check item doesn't overflow
              cy.wrap($item).then($el => {
                const element = $el[0];
                expect(element.scrollWidth).to.be.at.most(element.clientWidth + 5);
              });
            });
        });

        it('should maintain header layout integrity', () => {
          cy.get('header, [data-testid="header"], .header')
            .should('be.visible')
            .screenshot(`header-${viewport.name}`);

          // Check header elements are properly positioned
          if (viewport.width >= 768) {
            // Desktop/tablet should show full navigation
            cy.get('[data-testid="nav-menu"], .nav-menu, nav ul')
              .should('be.visible');
          } else {
            // Mobile might have hamburger menu
            cy.get('body').then($body => {
              const hasMobileMenu = $body.find('[data-testid="mobile-menu"], .mobile-menu').length > 0;
              const hasHamburger = $body.find('[data-testid="hamburger"], .hamburger').length > 0;
              
              if (hasMobileMenu || hasHamburger) {
                cy.log('Mobile navigation detected');
              }
            });
          }
        });

        it('should handle search and filter components', () => {
          // Test search input if present
          cy.get('body').then($body => {
            if ($body.find('[data-testid="search-input"], input[type="search"]').length > 0) {
              cy.get('[data-testid="search-input"], input[type="search"]')
                .should('be.visible')
                .and('have.css', 'width')
                .and('not.equal', '0px');

              cy.get('[data-testid="search-input"], input[type="search"]')
                .screenshot(`search-input-${viewport.name}`);
            }
          });

          // Test filter components if present
          cy.get('body').then($body => {
            if ($body.find('[data-testid="filter"], .filter').length > 0) {
              cy.get('[data-testid="filter"], .filter')
                .should('be.visible')
                .screenshot(`filters-${viewport.name}`);
            }
          });
        });
      });
    });
  });

  describe('Dynamic Content Visual Testing', () => {
    it('should handle empty state consistently across viewports', () => {
      // Clear bookmarks to test empty state
      cy.window().then(win => {
        win.localStorage.removeItem('bookmarks');
        cy.reload();
      });

      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.wait(500);

        cy.get('[data-testid="empty-state"], .empty-state')
          .should('be.visible')
          .screenshot(`empty-state-${viewport.name}`);

        // Verify empty state doesn't break layout
        cy.checkElementOverflow('body');
      });
    });

    it('should handle loading states visually', () => {
      // Simulate slow loading
      cy.simulateSlowNetwork();
      
      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.visit('/');
        
        // Capture loading state if present
        cy.get('body').then($body => {
          if ($body.find('[data-testid="loading"], .loading, .spinner').length > 0) {
            cy.get('[data-testid="loading"], .loading, .spinner')
              .screenshot(`loading-state-${viewport.name}`);
          }
        });
      });
    });

    it('should handle modal dialogs consistently', () => {
      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        
        // Try to open a modal if add button exists
        cy.get('body').then($body => {
          if ($body.find('[data-testid="add-bookmark"], .add-bookmark').length > 0) {
            cy.get('[data-testid="add-bookmark"], .add-bookmark').click();
            
            cy.get('[data-testid="modal"], .modal, [role="dialog"]')
              .should('be.visible')
              .screenshot(`modal-${viewport.name}`);

            // Check modal fits in viewport
            cy.get('[data-testid="modal"], .modal, [role="dialog"]').then($modal => {
              const modalRect = $modal[0].getBoundingClientRect();
              expect(modalRect.width).to.be.at.most(viewport.width);
              expect(modalRect.height).to.be.at.most(viewport.height);
            });

            // Close modal
            cy.get('[data-testid="close-modal"], .close, [aria-label="Close"]')
              .click();
          }
        });
      });
    });
  });

  describe('Content Overflow Visual Testing', () => {
    it('should handle long bookmark titles gracefully', () => {
      const longTitleBookmarks = [
        {
          id: 'long-1',
          title: '📚 This is an extremely long bookmark title that should be handled gracefully without breaking the layout or causing any visual issues in the user interface design',
          url: 'https://example-very-long-url.com/path/to/resource',
          addDate: Date.now(),
          tags: ['test', 'long-title']
        },
        {
          id: 'long-2',
          title: '🔗 Another very long title with special characters: !@#$%^&*()_+-=[]{}|;:,.<>?',
          url: 'https://example.com',
          addDate: Date.now(),
          tags: ['special-chars']
        }
      ];

      cy.loadTestBookmarks(longTitleBookmarks);
      cy.reload();

      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.wait(500);

        cy.get('[data-testid="bookmark-item"], .bookmark-item')
          .should('have.length', 2)
          .screenshot(`long-titles-${viewport.name}`);

        // Check titles don't overflow
        cy.get('[data-testid="bookmark-title"], .bookmark-title').each($title => {
          cy.checkElementOverflow($title.selector);
        });
      });
    });

    it('should handle many bookmarks without layout issues', () => {
      const manyBookmarks = Array.from({ length: 50 }, (_, i) => ({
        id: `bookmark-${i}`,
        title: `📖 Test Bookmark ${i + 1}`,
        url: `https://example${i}.com`,
        addDate: Date.now() - i * 1000,
        tags: [`tag${i % 5}`, 'test']
      }));

      cy.loadTestBookmarks(manyBookmarks);
      cy.reload();

      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        cy.wait(1000);

        // Screenshot the full list
        cy.get('[data-testid="bookmark-list"], .bookmark-list')
          .screenshot(`many-bookmarks-${viewport.name}`, { capture: 'fullPage' });

        // Check scrolling works properly
        cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
          .scrollTo('bottom', { duration: 1000 });
        
        cy.wait(500);
        
        cy.get('[data-testid="bookmark-list"], .bookmark-list, body')
          .scrollTo('top', { duration: 1000 });
      });
    });
  });

  describe('Interactive Element Visual Testing', () => {
    it('should show proper hover states', () => {
      standardViewports.forEach(viewport => {
        if (viewport.width >= 768) { // Skip hover tests on mobile
          cy.viewport(viewport.width, viewport.height);
          
          // Test bookmark item hover
          cy.get('[data-testid="bookmark-item"], .bookmark-item')
            .first()
            .trigger('mouseover')
            .screenshot(`bookmark-hover-${viewport.name}`);

          // Test button hover states
          cy.get('button, [role="button"]').each($btn => {
            cy.wrap($btn)
              .trigger('mouseover')
              .wait(100); // Allow hover transition
          });
        }
      });
    });

    it('should show proper focus states', () => {
      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        
        // Test focus on interactive elements
        cy.get('a, button, input').each($el => {
          cy.wrap($el)
            .focus()
            .wait(100); // Allow focus transition
        });

        cy.screenshot(`focus-states-${viewport.name}`);
      });
    });

    it('should handle active states properly', () => {
      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        
        // Test active states on buttons
        cy.get('button, [role="button"]').each($btn => {
          cy.wrap($btn)
            .trigger('mousedown')
            .wait(100)
            .trigger('mouseup');
        });
      });
    });
  });

  describe('Layout Consistency Over Time', () => {
    it('should maintain layout after page reload', () => {
      standardViewports.forEach(viewport => {
        cy.viewport(viewport.width, viewport.height);
        
        // Take initial screenshot
        cy.screenshot(`initial-${viewport.name}`);
        
        // Reload and compare
        cy.reload();
        cy.wait(1000);
        
        cy.screenshot(`after-reload-${viewport.name}`);
        
        // Check layout consistency
        cy.checkLayoutConsistency('[data-testid="bookmark-list"], .bookmark-list, main');
      });
    });

    it('should handle browser zoom levels', () => {
      const zoomLevels = [0.75, 1.0, 1.25, 1.5];
      
      zoomLevels.forEach(zoom => {
        cy.visit('/', {
          onBeforeLoad: (win) => {
            // Set zoom level
            (win.document.body.style as any).zoom = zoom;
          }
        });
        
        cy.wait(1000);
        cy.screenshot(`zoom-${zoom}-desktop`);
        
        // Check layout doesn't break at different zoom levels
        cy.checkElementOverflow('body');
      });
    });
  });
});