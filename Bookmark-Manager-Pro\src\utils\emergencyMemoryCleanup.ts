/**
 * Emergency Memory Cleanup Utilities
 * Provides immediate memory optimization when usage becomes critical
 */

import { getMemoryStats } from './memoryOptimization'

interface CleanupResult {
  success: boolean
  memoryFreed: number
  actions: string[]
  newUsagePercentage: number
}

/**
 * Emergency memory cleanup - triggers when memory usage > 62%
 */
export const emergencyMemoryCleanup = async (): Promise<CleanupResult> => {
  const initialStats = getMemoryStats()
  const actions: string[] = []

  console.warn('🚨 EMERGENCY MEMORY CLEANUP INITIATED - 62.3% USAGE DETECTED')
  console.warn('🎯 Target: Reduce memory usage below 400MB')
  
  try {
    // 1. Force garbage collection (most effective)
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc()
      actions.push('Forced garbage collection')
      console.log('✅ Garbage collection completed')
    }
    
    // 2. Clear browser caches and temporary data
    if (typeof window !== 'undefined') {
      // Clear performance measurements
      if (window.performance?.clearMeasures) {
        window.performance.clearMeasures()
        actions.push('Cleared performance measurements')
      }
      
      if (window.performance?.clearMarks) {
        window.performance.clearMarks()
        actions.push('Cleared performance marks')
      }
      
      // Clear session storage (temporary data only)
      try {
        const keysToKeep = ['bookmark-studio-theme-mode', 'bookmark-studio-color-scheme']
        const sessionData: Record<string, string> = {}
        
        // Backup important data
        keysToKeep.forEach(key => {
          const value = sessionStorage.getItem(key)
          if (value) sessionData[key] = value
        })
        
        // Clear all session storage
        sessionStorage.clear()
        
        // Restore important data
        Object.entries(sessionData).forEach(([key, value]) => {
          sessionStorage.setItem(key, value)
        })
        
        actions.push('Cleared session storage (preserved settings)')
      } catch (error) {
        console.warn('Could not clear session storage:', error)
      }
    }
    
    // 3. Clear any accumulated DOM event listeners
    if (typeof window !== 'undefined') {
      // Remove any orphaned event listeners
      const elements = document.querySelectorAll('[data-cleanup-listeners]')
      elements.forEach(el => {
        el.removeAttribute('data-cleanup-listeners')
      })
      actions.push('Cleaned up DOM event listeners')
    }
    
    // 4. Clear any cached data structures
    if (typeof window !== 'undefined') {
      // Clear any jQuery cache if present
      if ((window as any).jQuery?.cache) {
        (window as any).jQuery.cache = {}
        actions.push('Cleared jQuery cache')
      }
      
      // Clear any React DevTools data if present
      if ((window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__?.rendererInterfaces) {
        try {
          (window as any).__REACT_DEVTOOLS_GLOBAL_HOOK__.rendererInterfaces.clear()
          actions.push('Cleared React DevTools cache')
        } catch (error) {
          // Ignore errors
        }
      }
    }
    
    // 5. Force browser to release unused memory
    if (typeof window !== 'undefined') {
      // Create and immediately destroy large objects to trigger cleanup
      for (let i = 0; i < 5; i++) {
        const temp = new Array(1000000).fill(null)
        temp.length = 0
      }
      actions.push('Triggered memory release patterns')
    }
    
    // 6. Wait for cleanup to take effect
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 7. Force another garbage collection
    if ('gc' in window && typeof (window as any).gc === 'function') {
      (window as any).gc()
      actions.push('Final garbage collection')
    }
    
    // 8. Get final memory stats
    const finalStats = getMemoryStats()
    const memoryFreed = initialStats && finalStats ? 
      initialStats.usedMB - finalStats.usedMB : 0
    
    const result: CleanupResult = {
      success: true,
      memoryFreed,
      actions,
      newUsagePercentage: finalStats?.usagePercentage || 0
    }
    
    console.log(`✅ Emergency cleanup completed. Freed ${memoryFreed.toFixed(1)}MB`)
    console.log(`📊 Memory usage: ${initialStats?.usagePercentage}% → ${finalStats?.usagePercentage}%`)
    
    return result
    
  } catch (error) {
    console.error('❌ Emergency cleanup failed:', error)
    
    return {
      success: false,
      memoryFreed: 0,
      actions: ['Cleanup failed'],
      newUsagePercentage: initialStats?.usagePercentage || 0
    }
  }
}

/**
 * Aggressive memory optimization for large datasets
 */
export const aggressiveMemoryOptimization = async (): Promise<void> => {
  console.log('🔧 Starting aggressive memory optimization...')
  
  // 1. Limit DOM nodes
  const allElements = document.querySelectorAll('*')
  if (allElements.length > 15000) {
    console.warn(`⚠️ High DOM node count: ${allElements.length}`)
    
    // Remove any hidden or unnecessary elements
    const hiddenElements = document.querySelectorAll('[style*="display: none"], .hidden')
    hiddenElements.forEach(el => {
      if (!el.hasAttribute('data-keep')) {
        el.remove()
      }
    })
  }
  
  // 2. Optimize images and media
  const images = document.querySelectorAll('img')
  images.forEach(img => {
    // Remove src from images that are not visible
    if ((img as HTMLElement).offsetParent === null && !img.hasAttribute('data-keep')) {
      img.removeAttribute('src')
    }
  })
  
  // 3. Clear any large text content that's not visible
  const textElements = document.querySelectorAll('textarea, pre, code')
  textElements.forEach(el => {
    if ((el as HTMLElement).offsetParent === null && el.textContent && el.textContent.length > 10000) {
      el.textContent = el.textContent.substring(0, 1000) + '...[truncated for memory]'
    }
  })
  
  console.log('✅ Aggressive optimization completed')
}

/**
 * Monitor and auto-trigger emergency cleanup
 */
export const setupEmergencyMonitoring = (): (() => void) => {
  let isMonitoring = true
  let lastCleanup = 0
  
  const monitor = async () => {
    if (!isMonitoring) return
    
    const stats = getMemoryStats()
    if (stats && stats.usagePercentage > 70) {
      const now = Date.now()
      
      // Only trigger cleanup once every 30 seconds
      if (now - lastCleanup > 30000) {
        console.warn(`🚨 Auto-triggering emergency cleanup at ${stats.usagePercentage}%`)
        await emergencyMemoryCleanup()
        lastCleanup = now
      }
    }
    
    // Schedule next check
    setTimeout(monitor, 10000) // Check every 10 seconds
  }
  
  // Start monitoring
  monitor()
  
  // Return cleanup function
  return () => {
    isMonitoring = false
  }
}

/**
 * Get memory optimization recommendations
 */
export const getMemoryOptimizationRecommendations = (): string[] => {
  const stats = getMemoryStats()
  const recommendations: string[] = []
  
  if (!stats) return recommendations
  
  if (stats.usagePercentage > 80) {
    recommendations.push('CRITICAL: Enable virtual scrolling immediately')
    recommendations.push('CRITICAL: Reduce visible bookmarks to <500')
    recommendations.push('CRITICAL: Clear browser cache and restart')
  } else if (stats.usagePercentage > 60) {
    recommendations.push('Enable virtual scrolling for better performance')
    recommendations.push('Consider reducing the number of visible bookmarks')
    recommendations.push('Close other browser tabs to free memory')
  } else if (stats.usagePercentage > 40) {
    recommendations.push('Monitor memory usage during heavy operations')
    recommendations.push('Consider enabling virtual scrolling for large datasets')
  }
  
  // Check bookmark count
  const bookmarkElements = document.querySelectorAll('.bookmark-card, .bookmark-item')
  if (bookmarkElements.length > 1000) {
    recommendations.push(`High bookmark count (${bookmarkElements.length}): Virtual scrolling recommended`)
  }
  
  // Check DOM complexity
  const totalElements = document.querySelectorAll('*').length
  if (totalElements > 10000) {
    recommendations.push(`High DOM complexity (${totalElements} elements): Consider simplifying the interface`)
  }
  
  return recommendations
}

/**
 * Emergency memory status check
 */
export const isMemoryInCriticalState = (): boolean => {
  const stats = getMemoryStats()
  return stats ? stats.usagePercentage > 75 : false
}

/**
 * Force immediate memory cleanup (for emergency button)
 */
export const forceImmediateCleanup = async (): Promise<string> => {
  const result = await emergencyMemoryCleanup()
  
  if (result.success) {
    return `✅ Cleanup successful! Freed ${result.memoryFreed.toFixed(1)}MB. Memory usage: ${result.newUsagePercentage.toFixed(1)}%`
  } else {
    return '❌ Cleanup failed. Try refreshing the page.'
  }
}
