import React, { ReactNode } from 'react';

interface ImprovedLayoutProps {
  header: ReactNode;
  sidebar: React.ReactNode;
  bookmarkList: React.ReactNode;
  aiSection?: React.ReactNode;
  errorSection?: React.ReactNode;
  importSection?: React.ReactNode;
  toolbar?: ReactNode;
}

const ImprovedLayout: React.FC<ImprovedLayoutProps> = ({
  header,
  sidebar,
  bookmarkList,
  importSection,
  aiSection,
  errorSection,
  toolbar
}) => {
  return (
    <div className="improved-layout">
      {/* Header */}
      <header className="layout-header">
        {header}
      </header>

      {/* Main Content Grid */}
      <div className="layout-main">
        {/* Left Sidebar - Navigation & Quick Actions */}
        <aside className="layout-sidebar">
          {sidebar}
        </aside>

        {/* Main Content Area */}
        <main className="flex-1 flex flex-col overflow-hidden">
          {/* Error Section - Displayed above bookmark list if present */}
          {errorSection && (
            <section className="p-0 bg-slate-50 dark:bg-slate-900">
              {errorSection}
            </section>
          )}

          {/* Bookmark List Section */}
          <section className="flex-1 overflow-y-auto p-4 md:p-6 bg-slate-50 dark:bg-slate-900">
            {bookmarkList}
          </section>

          {/* AI/Contextual Section - Pinned to bottom of main content */}
          {aiSection && (
            <section className="h-[200px] md:h-[250px] border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-800 overflow-y-auto p-4">
              {aiSection}
            </section>
          )}
        </main>

        {/* Right Panel - Tools & AI Features */}
        <aside className="layout-tools">
          <div className="tools-container">
            {/* Import Section */}
            <div className="tool-section">
              <h3 className="tool-section-title">Import & Manage</h3>
              <div className="tool-section-content">
                {importSection}
              </div>
            </div>

            {/* AI Features Section */}
            <div className="tool-section">
              <h3 className="tool-section-title">AI Features</h3>
              <div className="tool-section-content">
                {aiSection}
              </div>
            </div>

            {/* Additional Toolbar */}
            {toolbar && (
              <div className="tool-section">
                <h3 className="tool-section-title">Tools</h3>
                <div className="tool-section-content">
                  {toolbar}
                </div>
              </div>
            )}
          </div>
        </aside>
      </div>
    </div>
  );
};

export default ImprovedLayout;