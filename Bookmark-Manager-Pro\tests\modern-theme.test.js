import { test, expect } from '@playwright/test'

test.describe('Modern Theme System Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
    await page.waitForSelector('.bookmark-grid-container')
  })

  test('should show theme toggle in header', async ({ page }) => {
    // Look for the modern theme switch button
    const themeSwitch = page.locator('.quick-modern-theme-switch')
    
    if (await themeSwitch.count() > 0) {
      await expect(themeSwitch).toBeVisible()
      
      // Check if it has the expected text
      const switchText = await themeSwitch.textContent()
      expect(switchText).toMatch(/(Modern|Classic)/)
      
      console.log('Theme switch found with text:', switchText)
    } else {
      console.log('Theme switch not found - may need to check selector')
    }
  })

  test('should apply theme classes to document', async ({ page }) => {
    // Check if theme classes are applied to the document
    const htmlElement = page.locator('html')
    
    // Should have either theme-classic or theme-modern class
    const hasThemeClass = await htmlElement.evaluate(el => {
      return el.classList.contains('theme-classic') || el.classList.contains('theme-modern')
    })
    
    expect(hasThemeClass).toBe(true)
    
    // Should have either light or dark class
    const hasColorSchemeClass = await htmlElement.evaluate(el => {
      return el.classList.contains('light') || el.classList.contains('dark')
    })
    
    expect(hasColorSchemeClass).toBe(true)
    
    console.log('Theme classes verified on document')
  })

  test('should switch themes when toggle is clicked', async ({ page }) => {
    const themeSwitch = page.locator('.quick-modern-theme-switch')
    
    if (await themeSwitch.count() > 0) {
      // Get initial theme
      const initialTheme = await page.locator('html').evaluate(el => {
        return el.classList.contains('theme-modern') ? 'modern' : 'classic'
      })
      
      // Click the theme switch
      await themeSwitch.click()
      
      // Wait for theme change
      await page.waitForTimeout(500)
      
      // Get new theme
      const newTheme = await page.locator('html').evaluate(el => {
        return el.classList.contains('theme-modern') ? 'modern' : 'classic'
      })
      
      // Theme should have changed
      expect(newTheme).not.toBe(initialTheme)
      
      console.log(`Theme switched from ${initialTheme} to ${newTheme}`)
    }
  })

  test('should persist theme preference', async ({ page }) => {
    const themeSwitch = page.locator('.quick-modern-theme-switch')
    
    if (await themeSwitch.count() > 0) {
      // Switch to modern theme
      await themeSwitch.click()
      await page.waitForTimeout(300)
      
      // Get current theme
      const currentTheme = await page.locator('html').evaluate(el => {
        return el.classList.contains('theme-modern') ? 'modern' : 'classic'
      })
      
      // Reload page
      await page.reload()
      await page.waitForSelector('.bookmark-grid-container')
      
      // Check if theme persisted
      const persistedTheme = await page.locator('html').evaluate(el => {
        return el.classList.contains('theme-modern') ? 'modern' : 'classic'
      })
      
      expect(persistedTheme).toBe(currentTheme)
      console.log('Theme persistence verified:', persistedTheme)
    }
  })

  test('should apply modern styling when modern theme is active', async ({ page }) => {
    // Ensure modern theme is active
    const themeSwitch = page.locator('.quick-modern-theme-switch')
    
    if (await themeSwitch.count() > 0) {
      // Check current theme and switch to modern if needed
      const isModern = await page.locator('html').evaluate(el => 
        el.classList.contains('theme-modern')
      )
      
      if (!isModern) {
        await themeSwitch.click()
        await page.waitForTimeout(300)
      }
      
      // Check if modern theme styles are applied
      const hasModernClass = await page.locator('html').evaluate(el => 
        el.classList.contains('theme-modern')
      )
      
      expect(hasModernClass).toBe(true)
      
      // Check if CSS variables are available
      const hasModernVariables = await page.evaluate(() => {
        const styles = getComputedStyle(document.documentElement)
        return styles.getPropertyValue('--primary-bg').trim() !== ''
      })
      
      if (hasModernVariables) {
        console.log('Modern theme CSS variables detected')
      }
    }
  })

  test('should show theme indicator with current status', async ({ page }) => {
    // Look for theme indicator
    const themeIndicator = page.locator('.modern-theme-indicator')
    
    if (await themeIndicator.count() > 0) {
      await expect(themeIndicator).toBeVisible()
      
      const indicatorText = await themeIndicator.textContent()
      expect(indicatorText).toMatch(/(Classic|Modern)/)
      expect(indicatorText).toMatch(/(Light|Dark)/)
      
      console.log('Theme indicator shows:', indicatorText)
    }
  })

  test('should handle color scheme switching', async ({ page }) => {
    // This test would need the full theme panel to be open
    // For now, just verify the structure exists
    
    const htmlElement = page.locator('html')
    
    // Should have a color scheme class
    const hasColorScheme = await htmlElement.evaluate(el => {
      return el.classList.contains('light') || el.classList.contains('dark')
    })
    
    expect(hasColorScheme).toBe(true)
    console.log('Color scheme classes verified')
  })

  test('should maintain bookmark panel styling consistency', async ({ page }) => {
    // Verify that bookmark panels maintain their enhanced styling
    const bookmarkCards = page.locator('.bookmark-card-front-redesigned')
    
    if (await bookmarkCards.count() > 0) {
      const firstCard = bookmarkCards.first()
      
      // Check if card has border styling
      const borderWidth = await firstCard.evaluate(el => 
        window.getComputedStyle(el).borderWidth
      )
      
      // Should have enhanced border (3px)
      expect(borderWidth).toBe('3px')
      
      // Check if collection dots are present
      const collectionDots = page.locator('.collection-dot-indicator')
      if (await collectionDots.count() > 0) {
        await expect(collectionDots.first()).toBeVisible()
        console.log('Collection dots verified with theme system')
      }
    }
  })

  test('should apply responsive design in modern theme', async ({ page }) => {
    // Test responsive behavior
    await page.setViewportSize({ width: 768, height: 1024 })
    
    const isModern = await page.locator('html').evaluate(el => 
      el.classList.contains('theme-modern')
    )
    
    if (isModern) {
      // Check if responsive styles are applied
      // This would depend on specific responsive implementations
      console.log('Responsive design test - modern theme active')
    }
    
    // Reset viewport
    await page.setViewportSize({ width: 1280, height: 720 })
  })

  test('should handle theme transitions smoothly', async ({ page }) => {
    const themeSwitch = page.locator('.quick-modern-theme-switch')
    
    if (await themeSwitch.count() > 0) {
      // Measure transition time
      const startTime = Date.now()
      
      await themeSwitch.click()
      
      // Wait for transition to complete
      await page.waitForTimeout(500)
      
      const endTime = Date.now()
      const transitionTime = endTime - startTime
      
      // Transition should be reasonably fast
      expect(transitionTime).toBeLessThan(1000)
      
      console.log(`Theme transition completed in ${transitionTime}ms`)
    }
  })
})

// Helper function to get current theme
async function getCurrentTheme(page) {
  return await page.locator('html').evaluate(el => {
    if (el.classList.contains('theme-modern')) return 'modern'
    if (el.classList.contains('theme-classic')) return 'classic'
    return 'unknown'
  })
}

// Helper function to get current color scheme
async function getCurrentColorScheme(page) {
  return await page.locator('html').evaluate(el => {
    if (el.classList.contains('dark')) return 'dark'
    if (el.classList.contains('light')) return 'light'
    return 'unknown'
  })
}
