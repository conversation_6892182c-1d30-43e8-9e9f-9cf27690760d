// AI Suggestion Timing Intelligence Tests
// Tests that favorites suggestions appear at natural workflow breaks and feel helpful

import { test, expect } from '@playwright/test';
import { VibeMetrics } from '../utils/vibe-metrics.js';
import { VibeTestData } from '../utils/vibe-test-data.js';

test.describe('AI Suggestion Timing Intelligence - Vibe Testing', () => {
  
  test.beforeEach(async ({ page }) => {
    // Setup suggestion test data with user patterns
    await VibeTestData.createSuggestionTestData(page);
    await page.goto('/bookmarks');
  });
  
  test('Favorites suggestions appear at natural workflow breaks', async ({ page }) => {
    // Setup user activity pattern for suggestion testing
    await VibeTestData.simulateUserActivity(page, {
      bookmarksAdded: 10,
      timeSpent: 300000, // 5 minutes
      activityType: 'research'
    });
    
    // Suggestions should NOT appear during active interaction
    await page.locator('[data-testid="bookmark-item"]').first().hover();
    await expect(page.locator('[data-testid="suggestion-popup"]')).not.toBeVisible();
    
    // Continue active interaction - no interruptions
    await page.locator('[data-testid="bookmark-star"]').first().click();
    await expect(page.locator('[data-testid="suggestion-popup"]')).not.toBeVisible();
    
    // Simulate natural pause in activity (user stops interacting)
    await page.waitForTimeout(3000);
    
    // Now suggestions can appear at natural break
    await expect(page.locator('[data-testid="suggestion-notification"]')).toBeVisible({ timeout: 5000 });
    
    // Suggestions should be dismissible without penalty
    await page.locator('[data-testid="dismiss-suggestion"]').click();
    await expect(page.locator('[data-testid="suggestion-notification"]')).not.toBeVisible();
    
    // User should be able to continue workflow immediately
    const continuationMetrics = await VibeMetrics.measureResponseTime(page, async () => {
      await page.locator('[data-testid="bookmark-star"]').nth(1).click();
    }, 50);
    
    expect(continuationMetrics.quality).toBe('excellent');
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        session: {
          type: 'suggestion-timing',
          duration: 8000,
          interruptions: 0
        },
        emotionalStates: [
          { emotion: 'focused', timestamp: 0, trigger: 'active-interaction' },
          { emotion: 'uninterrupted', timestamp: 2000, trigger: 'no-suggestions-during-flow' },
          { emotion: 'receptive', timestamp: 5000, trigger: 'natural-pause' },
          { emotion: 'in-control', timestamp: 6000, trigger: 'dismissible-suggestion' }
        ],
        satisfactionPoints: [
          { category: 'respect', trigger: 'no-interruption', intensity: 9 },
          { category: 'timing', trigger: 'natural-break', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Favorites suggestions feel intelligent, not creepy', async ({ page }) => {
    // Create realistic usage pattern
    await VibeTestData.createUsagePattern(page, {
      favoriteTopics: ['javascript', 'react', 'testing'],
      accessFrequency: 'high',
      timeOfDay: 'morning'
    });
    
    // Wait for suggestions to generate based on pattern
    await page.waitForTimeout(2000);
    
    // Check if suggestions appear
    if (await page.locator('[data-testid="suggestion-item"]').isVisible()) {
      const suggestions = page.locator('[data-testid="suggestion-item"]');
      const suggestionTexts = await suggestions.allTextContents();
      
      // Measure suggestion quality
      const suggestionQuality = await VibeMetrics.measureSuggestionQuality(page, 
        suggestionTexts.map(text => ({
          text,
          relevanceScore: this.calculateRelevanceScore(text, ['javascript', 'react', 'testing']),
          timing: 'natural-pause',
          dismissible: true,
          intrusive: false,
          hasAction: true,
          actionClear: true
        }))
      );
      
      // At least 70% of suggestions should match user interests
      expect(suggestionQuality.rates.relevance).toBeGreaterThan(0.7);
      expect(suggestionQuality.quality).toMatch(/excellent|good/);
      
      // Test suggestion interaction doesn't feel pushy
      await suggestions.first().hover();
      
      // Should show more info without being aggressive
      const suggestionDetails = await page.locator('[data-testid="suggestion-details"]').isVisible();
      
      // Test accepting a suggestion feels natural
      if (await page.locator('[data-testid="accept-suggestion"]').isVisible()) {
        await page.locator('[data-testid="accept-suggestion"]').first().click();
        
        // Should provide smooth transition to suggested action
        await expect(page.locator('[data-testid="suggestion-applied"]')).toBeVisible({ timeout: 2000 });
      }
      
      await test.info().attach('vibe-metrics', {
        body: JSON.stringify({
          suggestionQuality,
          testType: 'suggestion-intelligence'
        }),
        contentType: 'application/json'
      });
    }
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        emotionalStates: [
          { emotion: 'curious', timestamp: 0, trigger: 'suggestion-appeared' },
          { emotion: 'impressed', timestamp: 1000, trigger: 'relevant-suggestions' },
          { emotion: 'trusting', timestamp: 2000, trigger: 'non-pushy-interaction' }
        ],
        satisfactionPoints: [
          { category: 'intelligence', trigger: 'relevant-suggestions', intensity: 8 },
          { category: 'respect', trigger: 'non-intrusive', intensity: 9 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  calculateRelevanceScore(text, userTopics) {
    const lowerText = text.toLowerCase();
    const matches = userTopics.filter(topic => lowerText.includes(topic.toLowerCase()));
    return matches.length / userTopics.length;
  }
  
  test('Suggestion frequency adapts to user receptiveness', async ({ page }) => {
    // Test suggestion learning and adaptation
    
    // Phase 1: User dismisses suggestions frequently
    for (let i = 0; i < 3; i++) {
      await page.waitForTimeout(2000);
      
      if (await page.locator('[data-testid="suggestion-notification"]').isVisible()) {
        await page.locator('[data-testid="dismiss-suggestion"]').click();
      }
    }
    
    // System should learn and reduce suggestion frequency
    await page.waitForTimeout(5000);
    
    // Should show fewer or no suggestions after dismissals
    const suggestionsAfterDismissals = await page.locator('[data-testid="suggestion-notification"]').isVisible();
    
    // Phase 2: User accepts suggestions
    if (suggestionsAfterDismissals) {
      await page.locator('[data-testid="accept-suggestion"]').first().click();
    }
    
    // System should learn and potentially increase helpful suggestions
    await page.waitForTimeout(3000);
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        session: {
          type: 'suggestion-learning',
          duration: 15000,
          adaptations: 2
        },
        emotionalStates: [
          { emotion: 'annoyed', timestamp: 0, trigger: 'frequent-suggestions' },
          { emotion: 'relieved', timestamp: 5000, trigger: 'reduced-frequency' },
          { emotion: 'appreciative', timestamp: 10000, trigger: 'learned-preferences' }
        ],
        satisfactionPoints: [
          { category: 'adaptation', trigger: 'learned-behavior', intensity: 9 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Suggestions provide clear value proposition', async ({ page }) => {
    // Wait for suggestions to appear
    await page.waitForTimeout(3000);
    
    if (await page.locator('[data-testid="suggestion-item"]').isVisible()) {
      const suggestion = page.locator('[data-testid="suggestion-item"]').first();
      
      // Suggestion should clearly explain its value
      const suggestionMetrics = await suggestion.evaluate((el) => {
        const text = el.textContent || '';
        
        return {
          hasActionVerb: /star|favorite|organize|group|tag/.test(text.toLowerCase()),
          explainsBenefit: /save time|improve|organize|find faster/.test(text.toLowerCase()),
          showsContext: text.length > 20, // Not just "Star this"
          hasPreview: el.querySelector('[data-testid="suggestion-preview"]') !== null,
          allowsCustomization: el.querySelector('[data-testid="customize-suggestion"]') !== null
        };
      });
      
      expect(suggestionMetrics.hasActionVerb).toBe(true);
      expect(suggestionMetrics.showsContext).toBe(true);
      
      // Test suggestion preview if available
      if (suggestionMetrics.hasPreview) {
        await suggestion.hover();
        await expect(page.locator('[data-testid="suggestion-preview"]')).toBeVisible();
        
        // Preview should show what will happen
        const previewText = await page.locator('[data-testid="suggestion-preview"]').textContent();
        expect(previewText.length).toBeGreaterThan(10);
      }
      
      await test.info().attach('vibe-metrics', {
        body: JSON.stringify({
          suggestionMetrics,
          testType: 'suggestion-value'
        }),
        contentType: 'application/json'
      });
    }
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        satisfactionPoints: [
          { category: 'clarity', trigger: 'clear-value-proposition', intensity: 8 },
          { category: 'transparency', trigger: 'suggestion-preview', intensity: 7 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Suggestion dismissal feels respectful, not punitive', async ({ page }) => {
    // Wait for suggestion to appear
    await page.waitForTimeout(3000);
    
    if (await page.locator('[data-testid="suggestion-notification"]').isVisible()) {
      // Test different dismissal methods
      
      // Method 1: Explicit dismiss button
      if (await page.locator('[data-testid="dismiss-suggestion"]').isVisible()) {
        await page.locator('[data-testid="dismiss-suggestion"]').click();
        
        // Should disappear smoothly without negative feedback
        await expect(page.locator('[data-testid="suggestion-notification"]')).not.toBeVisible();
        
        // Should not show error or negative messaging
        await expect(page.locator('[data-testid="suggestion-error"]')).not.toBeVisible();
      }
      
      // Method 2: Click outside (if supported)
      if (await page.locator('[data-testid="suggestion-notification"]').isVisible()) {
        await page.click('body', { position: { x: 100, y: 100 } });
        
        // Should respect user's implicit dismissal
        await page.waitForTimeout(1000);
      }
      
      // Method 3: ESC key
      await page.keyboard.press('Escape');
      
      // Should close any remaining suggestions
      await expect(page.locator('[data-testid="suggestion-notification"]')).not.toBeVisible();
      
      // User should be able to continue workflow immediately
      const postDismissalMetrics = await VibeMetrics.measureResponseTime(page, async () => {
        await page.locator('[data-testid="bookmark-star"]').first().click();
      }, 50);
      
      expect(postDismissalMetrics.quality).toBe('excellent');
      
      await test.info().attach('emotional-journey', {
        body: JSON.stringify({
          emotionalStates: [
            { emotion: 'neutral', timestamp: 0, trigger: 'suggestion-appeared' },
            { emotion: 'dismissive', timestamp: 1000, trigger: 'not-interested' },
            { emotion: 'respected', timestamp: 1500, trigger: 'easy-dismissal' },
            { emotion: 'unimpeded', timestamp: 2000, trigger: 'continued-workflow' }
          ],
          satisfactionPoints: [
            { category: 'respect', trigger: 'respectful-dismissal', intensity: 9 },
            { category: 'control', trigger: 'multiple-dismiss-methods', intensity: 8 }
          ]
        }),
        contentType: 'application/json'
      });
    }
  });
  
  test('Suggestions adapt to user context and activity', async ({ page }) => {
    // Test contextual suggestion intelligence
    
    // Context 1: Research mode (rapid bookmarking)
    await VibeTestData.simulateUserActivity(page, {
      activityType: 'research',
      bookmarksAdded: 15,
      timeSpent: 600000, // 10 minutes
      rapidStarring: true
    });
    
    await page.waitForTimeout(2000);
    
    // Should suggest organization-focused actions
    if (await page.locator('[data-testid="suggestion-item"]').isVisible()) {
      const researchSuggestions = await page.locator('[data-testid="suggestion-item"]').allTextContents();
      const organizationSuggestions = researchSuggestions.filter(text => 
        /organize|group|folder|category/.test(text.toLowerCase())
      );
      
      expect(organizationSuggestions.length).toBeGreaterThan(0);
    }
    
    // Context 2: Cleanup mode (reviewing old bookmarks)
    await VibeTestData.simulateUserActivity(page, {
      activityType: 'cleanup',
      bookmarksAdded: 2,
      timeSpent: 300000, // 5 minutes
      bulkOperations: true
    });
    
    await page.waitForTimeout(2000);
    
    // Should suggest cleanup-focused actions
    if (await page.locator('[data-testid="suggestion-item"]').isVisible()) {
      const cleanupSuggestions = await page.locator('[data-testid="suggestion-item"]').allTextContents();
      const maintenanceSuggestions = cleanupSuggestions.filter(text => 
        /remove|delete|clean|archive/.test(text.toLowerCase())
      );
      
      // Context-appropriate suggestions should appear
      expect(maintenanceSuggestions.length).toBeGreaterThanOrEqual(0);
    }
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        session: {
          type: 'contextual-suggestions',
          contexts: ['research', 'cleanup'],
          duration: 20000
        },
        emotionalStates: [
          { emotion: 'focused', timestamp: 0, trigger: 'research-mode' },
          { emotion: 'helped', timestamp: 5000, trigger: 'relevant-organization-suggestions' },
          { emotion: 'efficient', timestamp: 15000, trigger: 'cleanup-suggestions' }
        ],
        satisfactionPoints: [
          { category: 'intelligence', trigger: 'context-awareness', intensity: 9 },
          { category: 'relevance', trigger: 'activity-based-suggestions', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Suggestion acceptance leads to satisfying outcomes', async ({ page }) => {
    // Wait for suggestions
    await page.waitForTimeout(3000);
    
    if (await page.locator('[data-testid="suggestion-item"]').isVisible()) {
      const suggestion = page.locator('[data-testid="suggestion-item"]').first();
      const suggestionText = await suggestion.textContent();
      
      // Accept the suggestion
      await page.locator('[data-testid="accept-suggestion"]').first().click();
      
      // Should provide immediate feedback about what's happening
      await expect(page.locator('[data-testid="suggestion-processing"]')).toBeVisible({ timeout: 1000 });
      
      // Should complete the suggested action
      await expect(page.locator('[data-testid="suggestion-applied"]')).toBeVisible({ timeout: 5000 });
      
      // Should show clear results of the action
      const resultMessage = await page.locator('[data-testid="suggestion-result"]').textContent();
      expect(resultMessage.length).toBeGreaterThan(0);
      
      // User should be able to see the impact immediately
      if (suggestionText?.includes('star') || suggestionText?.includes('favorite')) {
        // Check that bookmarks were actually starred
        const starredCount = await page.locator('[data-testid="bookmark-star"].starred').count();
        expect(starredCount).toBeGreaterThan(0);
      }
      
      // Should offer undo if appropriate
      if (await page.locator('[data-testid="undo-suggestion"]').isVisible()) {
        // Test undo functionality
        await page.locator('[data-testid="undo-suggestion"]').click();
        
        // Should reverse the action smoothly
        await expect(page.locator('[data-testid="suggestion-undone"]')).toBeVisible({ timeout: 2000 });
      }
      
      await test.info().attach('emotional-journey', {
        body: JSON.stringify({
          emotionalStates: [
            { emotion: 'curious', timestamp: 0, trigger: 'suggestion-appeared' },
            { emotion: 'trusting', timestamp: 1000, trigger: 'accepted-suggestion' },
            { emotion: 'satisfied', timestamp: 3000, trigger: 'clear-results' },
            { emotion: 'confident', timestamp: 4000, trigger: 'undo-available' }
          ],
          satisfactionPoints: [
            { category: 'effectiveness', trigger: 'suggestion-worked', intensity: 9 },
            { category: 'transparency', trigger: 'clear-results', intensity: 8 },
            { category: 'safety', trigger: 'undo-option', intensity: 8 }
          ],
          delightMoments: [
            { category: 'ai-assistance', trigger: 'helpful-suggestion', intensity: 8 }
          ]
        }),
        contentType: 'application/json'
      });
    }
  });
  
});