describe('Bookmark Management', () => {
  beforeEach(() => {
    // Clear any existing data
    cy.clearBookmarks();
    
    // Visit the application
    cy.visit('/');
    
    // Wait for the app to be ready
    cy.waitForApp();
  });

  describe('Adding Bookmarks', () => {
    it('should add a new bookmark successfully', () => {
      const testUrl = 'https://example.com';
      const testTitle = 'Example Website';
      
      // Add a bookmark
      cy.addBookmark(testUrl, testTitle);
      
      // Verify the bookmark was added
      cy.verifyBookmarkExists(testTitle, testUrl);
      cy.checkBookmarkCount(1);
    });

    it('should handle invalid URLs gracefully', () => {
      const invalidUrl = 'not-a-valid-url';
      
      // Try to add an invalid URL
      cy.get('[data-testid="add-bookmark-button"]').click();
      cy.get('[data-testid="bookmark-url-input"]').type(invalidUrl);
      cy.get('[data-testid="save-bookmark-button"]').click();
      
      // Should show an error message
      cy.checkErrorMessage('Please enter a valid URL');
    });

    it('should auto-generate title from URL', () => {
      const testUrl = 'https://github.com';
      
      // Add bookmark without specifying title
      cy.addBookmark(testUrl);
      
      // Should auto-generate title
      cy.verifyBookmarkExists('GitHub');
    });
  });

  describe('Bookmark List Operations', () => {
    beforeEach(() => {
      // Seed some test data
      const testBookmarks = [
        {
          id: '1',
          title: 'React Documentation',
          url: 'https://react.dev',
          dateAdded: new Date().toISOString(),
          tags: ['react', 'documentation'],
          summary: 'Official React documentation'
        },
        {
          id: '2',
          title: 'TypeScript Handbook',
          url: 'https://www.typescriptlang.org/docs/',
          dateAdded: new Date().toISOString(),
          tags: ['typescript', 'documentation'],
          summary: 'TypeScript language documentation'
        },
        {
          id: '3',
          title: 'Jest Testing Framework',
          url: 'https://jestjs.io',
          dateAdded: new Date().toISOString(),
          tags: ['jest', 'testing'],
          summary: 'JavaScript testing framework'
        }
      ];
      
      cy.seedBookmarks(testBookmarks);
      cy.reload();
      cy.waitForApp();
    });

    it('should display all bookmarks', () => {
      cy.checkBookmarkCount(3);
      cy.verifyBookmarkExists('React Documentation');
      cy.verifyBookmarkExists('TypeScript Handbook');
      cy.verifyBookmarkExists('Jest Testing Framework');
    });

    it('should search bookmarks', () => {
      // Search for React
      cy.searchBookmarks('React');
      cy.checkBookmarkCount(1);
      cy.verifyBookmarkExists('React Documentation');
      
      // Clear search
      cy.get('[data-testid="clear-search-button"]').click();
      cy.checkBookmarkCount(3);
    });

    it('should sort bookmarks by title', () => {
      cy.sortBookmarks('title');
      
      // Verify order (alphabetical)
      cy.get('[data-testid="bookmark-item"]').first().should('contain', 'Jest Testing Framework');
      cy.get('[data-testid="bookmark-item"]').last().should('contain', 'TypeScript Handbook');
    });

    it('should sort bookmarks by date', () => {
      cy.sortBookmarks('date');
      
      // Should be sorted by date added (newest first by default)
      cy.get('[data-testid="bookmark-item"]').should('have.length', 3);
    });

    it('should filter bookmarks by tag', () => {
      cy.filterByTag('documentation');
      cy.checkBookmarkCount(2);
      cy.verifyBookmarkExists('React Documentation');
      cy.verifyBookmarkExists('TypeScript Handbook');
    });
  });

  describe('Bookmark Actions', () => {
    beforeEach(() => {
      // Add a test bookmark
      cy.addBookmark('https://example.com', 'Test Bookmark');
    });

    it('should delete a bookmark', () => {
      cy.deleteBookmark('Test Bookmark');
      cy.verifyBookmarkNotExists('Test Bookmark');
      cy.checkBookmarkCount(0);
    });

    it('should edit a bookmark', () => {
      const newData = {
        title: 'Updated Test Bookmark',
        url: 'https://updated-example.com',
        summary: 'Updated summary'
      };
      
      cy.editBookmark('Test Bookmark', newData);
      cy.verifyBookmarkExists('Updated Test Bookmark', 'https://updated-example.com');
    });

    it('should generate AI summary', () => {
      cy.generateSummary('Test Bookmark');
      
      // Wait for AI processing
      cy.waitForLoading();
      
      // Should show generated summary
      cy.getBookmarkItem('Test Bookmark').should('contain', 'AI-generated summary');
    });

    it('should generate AI tags', () => {
      cy.generateTags('Test Bookmark');
      
      // Wait for AI processing
      cy.waitForLoading();
      
      // Should show generated tags
      cy.getBookmarkItem('Test Bookmark').find('[data-testid="bookmark-tags"]').should('not.be.empty');
    });
  });

  describe('Bulk Operations', () => {
    beforeEach(() => {
      // Add multiple bookmarks
      cy.addBookmark('https://example1.com', 'Bookmark 1');
      cy.addBookmark('https://example2.com', 'Bookmark 2');
      cy.addBookmark('https://example3.com', 'Bookmark 3');
    });

    it('should select multiple bookmarks', () => {
      cy.bulkSelectBookmarks(['Bookmark 1', 'Bookmark 2']);
      
      // Should show bulk actions
      cy.get('[data-testid="bulk-actions-bar"]').should('be.visible');
      cy.get('[data-testid="selected-count"]').should('contain', '2');
    });

    it('should bulk delete bookmarks', () => {
      cy.bulkSelectBookmarks(['Bookmark 1', 'Bookmark 2']);
      cy.bulkDeleteSelected();
      
      cy.checkBookmarkCount(1);
      cy.verifyBookmarkExists('Bookmark 3');
      cy.verifyBookmarkNotExists('Bookmark 1');
      cy.verifyBookmarkNotExists('Bookmark 2');
    });

    it('should select all bookmarks', () => {
      cy.get('[data-testid="select-all-checkbox"]').check();
      
      // All bookmarks should be selected
      cy.get('[data-testid="selected-count"]').should('contain', '3');
    });
  });

  describe('Accessibility', () => {
    it('should be accessible', () => {
      // Check accessibility of the main page
      cy.checkA11y();
    });

    it('should be keyboard navigable', () => {
      // Test keyboard navigation
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'add-bookmark-button');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'search-input');
    });

    it('should support screen readers', () => {
      // Check for proper ARIA labels
      cy.get('[data-testid="bookmark-item"]').first().should('have.attr', 'role', 'listitem');
      cy.get('[data-testid="bookmark-checkbox"]').should('have.attr', 'aria-label');
    });
  });

  describe('Responsive Design', () => {
    it('should work on different screen sizes', () => {
      cy.checkResponsive();
    });

    it('should show mobile menu on small screens', () => {
      cy.viewport(375, 667);
      cy.get('[data-testid="mobile-menu-button"]').should('be.visible');
      cy.get('[data-testid="mobile-menu-button"]').click();
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
    });
  });

  describe('Data Persistence', () => {
    it('should persist bookmarks across page reloads', () => {
      cy.addBookmark('https://persistent.com', 'Persistent Bookmark');
      
      // Reload the page
      cy.reload();
      cy.waitForApp();
      
      // Bookmark should still exist
      cy.verifyBookmarkExists('Persistent Bookmark');
    });

    it('should handle offline scenarios', () => {
      // Simulate offline
      cy.window().then((win) => {
        cy.stub(win.navigator, 'onLine').value(false);
      });
      
      // Try to add a bookmark while offline
      cy.addBookmark('https://offline.com', 'Offline Bookmark');
      
      // Should show offline message or queue the action
      cy.get('[data-testid="offline-indicator"]').should('be.visible');
    });
  });
});