/**
 * MODERN RECOMMENDATION SYSTEM INTERFACES
 * Following industry best practices for scalable recommendation engines
 */

import type { Bookmark, Playlist } from '../../../types'

// Re-export core types for convenience
export type { Bookmark, Playlist }

// Core recommendation interfaces
export interface RecommendationEngine {
  generateCandidates(userId: string, context: RecommendationContext): Promise<Candidate[]>
  rankCandidates(candidates: Candidate[], userProfile: UserProfile): Promise<RankedRecommendation[]>
  streamRecommendations(options: StreamingOptions): RecommendationStream
  recordFeedback(recommendationId: string, feedback: FeedbackType): Promise<void>
}

export interface CandidateGenerator {
  generateContentBased(bookmarks: Bookmark[]): Promise<Candidate[]>
  generateCollaborative(userProfile: UserProfile): Promise<Candidate[]>
  generateTemporal(timeContext: TemporalContext): Promise<Candidate[]>
  generateHybrid(context: RecommendationContext): Promise<Candidate[]>
}

export interface RankingService {
  rank(candidates: Candidate[], features: FeatureVector): Promise<RankedRecommendation[]>
  explainRanking(recommendation: RankedRecommendation): Promise<Explanation>
  updateRankingModel(feedback: FeedbackData[]): Promise<void>
}

export interface ContentAnalyzer {
  analyzeContentSimilarity(bookmarks: Bookmark[]): Promise<SimilarityMatrix>
  extractTopics(content: string): Promise<Topic[]>
  generateEmbeddings(texts: string[]): Promise<EmbeddingVector[]>
  calculateSemanticSimilarity(text1: string, text2: string): Promise<number>
}

export interface CollaborativeFilteringService {
  generateUserBasedRecommendations(userId: string): Promise<Recommendation[]>
  generateItemBasedRecommendations(bookmarkIds: string[]): Promise<Recommendation[]>
  calculateUserSimilarities(userId: string): Promise<UserSimilarity[]>
  updateUserProfile(userId: string, interactions: Interaction[]): Promise<void>
}

// Data structures
export interface RecommendationContext {
  bookmarks: Bookmark[]
  existingPlaylists: Playlist[]
  userProfile: UserProfile
  timeRange?: DateRange
  preferences?: UserPreferences
}

export interface Candidate {
  id: string
  type: 'playlist' | 'collection' | 'tag-group'
  name: string
  description: string
  bookmarkIds: string[]
  confidence: number
  source: 'content' | 'collaborative' | 'temporal' | 'hybrid'
  features: FeatureVector
}

export interface RankedRecommendation extends Candidate {
  rank: number
  score: number
  explanation: Explanation
  diversity: number
  novelty: number
}

export interface UserProfile {
  userId: string
  preferences: UserPreferences
  interactions: Interaction[]
  embeddings: EmbeddingVector
  clusters: string[]
  lastUpdated: Date
}

export interface UserPreferences {
  favoriteTopics: string[]
  preferredContentTypes: string[]
  timePatterns: TemporalPattern[]
  diversityPreference: number // 0-1, higher = more diverse recommendations
  noveltyPreference: number // 0-1, higher = more novel recommendations
}

export interface Interaction {
  userId: string
  bookmarkId: string
  playlistId?: string
  action: 'view' | 'save' | 'share' | 'like' | 'dislike' | 'create_playlist'
  timestamp: Date
  duration?: number
  context?: string
}

export interface SimilarityMatrix {
  items: string[]
  matrix: number[][]
  algorithm: 'cosine' | 'jaccard' | 'euclidean' | 'semantic'
}

export interface Topic {
  id: string
  name: string
  keywords: string[]
  weight: number
  coherence: number
}

export interface EmbeddingVector {
  id: string
  vector: number[]
  dimension: number
  model: string
}

export interface FeatureVector {
  contentFeatures: number[]
  collaborativeFeatures: number[]
  temporalFeatures: number[]
  contextualFeatures: number[]
}

export interface Explanation {
  primary: string
  secondary: string[]
  confidence: number
  factors: ExplanationFactor[]
}

export interface ExplanationFactor {
  type: 'content_similarity' | 'user_behavior' | 'temporal_pattern' | 'popularity'
  weight: number
  description: string
  evidence: string[]
}

export interface StreamingOptions {
  userId: string
  context: RecommendationContext
  maxRecommendations?: number
  onRecommendation: (recommendation: RankedRecommendation) => void
  onExplanation: (explanation: Explanation) => void
  onError: (error: Error) => void
}

export interface RecommendationStream {
  close(): void
  pause(): void
  resume(): void
  updateContext(context: Partial<RecommendationContext>): void
}

export interface UserSimilarity {
  userId: string
  similarity: number
  commonInterests: string[]
  interactionOverlap: number
}

export interface TemporalContext {
  timeRange: DateRange
  patterns: TemporalPattern[]
  seasonality: SeasonalityData
}

export interface TemporalPattern {
  type: 'daily' | 'weekly' | 'monthly' | 'seasonal'
  peak: string
  confidence: number
  bookmarkIds: string[]
}

export interface SeasonalityData {
  trends: TrendData[]
  cycles: CycleData[]
  anomalies: AnomalyData[]
}

export interface DateRange {
  start: Date
  end: Date
}

export interface TrendData {
  period: string
  direction: 'increasing' | 'decreasing' | 'stable'
  strength: number
}

export interface CycleData {
  period: number
  amplitude: number
  phase: number
}

export interface AnomalyData {
  timestamp: Date
  severity: number
  description: string
}

export interface FeedbackData {
  recommendationId: string
  userId: string
  feedback: FeedbackType
  timestamp: Date
  context?: string
}

export type FeedbackType = 'like' | 'dislike' | 'not_interested' | 'irrelevant' | 'created_playlist'

export interface Recommendation {
  id: string
  name: string
  description: string
  bookmarkIds: string[]
  confidence: number
  reasoning: string
  category: string
}

// Performance and caching interfaces
export interface RecommendationCache {
  getCachedSimilarities(itemId: string): Promise<SimilarityScore[]>
  cacheUserProfile(userId: string, profile: UserProfile): Promise<void>
  getCachedRecommendations(userId: string, context: string): Promise<RankedRecommendation[]>
  invalidateUserCache(userId: string): Promise<void>
}

export interface SimilarityScore {
  itemId: string
  score: number
  lastUpdated: Date
}

// Model interfaces
export interface MLModel {
  predict(features: FeatureVector): Promise<number>
  train(trainingData: TrainingData[]): Promise<void>
  evaluate(testData: TrainingData[]): Promise<ModelMetrics>
  save(path: string): Promise<void>
  load(path: string): Promise<void>
}

export interface TrainingData {
  features: FeatureVector
  label: number
  weight?: number
}

export interface ModelMetrics {
  accuracy: number
  precision: number
  recall: number
  f1Score: number
  auc: number
}
