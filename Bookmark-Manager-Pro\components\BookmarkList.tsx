
import * as React from 'react';
import { useCallback, useMemo } from 'react';
import { Bookmark, BookmarkProcessingState, SortConfig } from '../types';
import BookmarkItem from './BookmarkItem';
import EmptyState from './EmptyState';
import { ChevronDownIcon, ChevronUpIcon } from './icons/HeroIcons';

interface BookmarkListProps {
  bookmarks: Bookmark[];
  selectedBookmarks?: Set<string>;
  processingStates: Record<string, BookmarkProcessingState>;
  onSelectBookmark?: (id: string, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
  sortConfig: SortConfig;
  onSort: (key: keyof Bookmark) => void;
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  onDeleteBookmark: (id: string) => void;
  onGenerateSummary: (id: string) => void;
  onGenerateTags: (id: string) => void;
  onProcessYouTube?: (bookmark: Bookmark) => void;

  densityMode?: 'compact' | 'comfortable' | 'spacious';
  searchResult?: {
    query?: string;
    filters?: Record<string, unknown>;
  };
  onClearSearch?: () => void;
  onAddBookmark?: () => void;
  onImportBookmarks?: () => void;
  onShowOnboarding?: () => void;
}

interface SortableHeaderProps {
  label: string;
  sortKey: keyof Bookmark;
  currentSortConfig: SortConfig;
  onSort: (key: keyof Bookmark) => void;
  className?: string;
  colSpan?: number;
}

const SortableHeader: React.FC<SortableHeaderProps> = React.memo(({
  label,
  sortKey,
  currentSortConfig,
  onSort,
  className = ""
}) => {
  const isActive = currentSortConfig.key === sortKey;
  const directionIcon = isActive ? (currentSortConfig.direction === 'asc' ? <ChevronUpIcon className="w-4 h-4 ml-1" /> : <ChevronDownIcon className="w-4 h-4 ml-1" />) : null;
  const isCenter = className.includes('text-center');

  return (
    <div
        className={`text-sm font-semibold text-slate-300 cursor-pointer hover:text-sky-300 transition-colors ${className}`}
        onClick={useCallback(() => onSort(sortKey), [onSort, sortKey])}
        role="button"
        tabIndex={0}
        onKeyDown={useCallback((e: React.KeyboardEvent) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onSort(sortKey);
          }
        }, [onSort, sortKey])}
        aria-sort={isActive ? (currentSortConfig.direction === 'asc' ? 'ascending' : 'descending') : 'none'}
    >
      <div className={`flex items-center ${isCenter ? 'justify-center' : ''}`}>
        {label}
        {directionIcon}
      </div>
    </div>
  );
});

const BookmarkList: React.FC<BookmarkListProps> = React.memo(({
  bookmarks,
  selectedBookmarks = new Set(),
  processingStates,
  onSelectBookmark,
  onSelectAll,
  sortConfig,
  onSort,
  onUpdateBookmark,
  onDeleteBookmark,
  onGenerateSummary,
  onGenerateTags,
  onProcessYouTube,

  searchResult,
  onClearSearch,
  onAddBookmark,
  onImportBookmarks,
  onShowOnboarding,
  densityMode = 'comfortable'
}) => {
  const selectionState = useMemo(() => {
    const isAllSelected = selectedBookmarks.size === bookmarks.length && bookmarks.length > 0;
    return { isAllSelected };
  }, [selectedBookmarks.size, bookmarks.length]);

  const handleSelectAllChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSelectAll) {
      onSelectAll(e.target.checked);
    }
  }, [onSelectAll]);
  return (
    <div className={`bookmark-list-container ${densityMode}`}>
      {/* Search Result Banner */}
      {searchResult && (
        <div className="search-result-banner">
          <div className="search-result-content">
            <div>
              <h3 className="search-result-title">Search Results</h3>
              <p className="search-result-description">
                {searchResult.query && `Search results for "${searchResult.query}"`}
              </p>
            </div>
            {onClearSearch && (
              <button
                onClick={onClearSearch}
                className="search-result-clear"
              >
                Clear Search
              </button>
            )}
          </div>
        </div>
      )}

      {bookmarks.length === 0 ? (
        <EmptyState
          onAddBookmark={onAddBookmark || (() => {})}
          onImportBookmarks={onImportBookmarks || (() => {})}
          onShowOnboarding={onShowOnboarding}
          hasSearchQuery={!!searchResult}
          searchQuery={searchResult?.query}
          onClearSearch={onClearSearch}
        />
      ) : (
        <div className="mt-6 flow-root">
          <div className="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg bg-slate-700/30">
            {/* CSS Grid Container */}
            <div
              className={`bookmark-grid-container grid items-start gap-x-6 w-full bg-slate-900 rounded-xl overflow-hidden ${
                onSelectBookmark ? 'grid-cols-[auto_1fr_2fr_auto_auto]' : 'grid-cols-[1fr_2fr_auto_auto]'
              }`}
            >
              {/* Header Row */}
              {onSelectBookmark && (
                <div className="grid-header-cell px-4 py-5 bg-slate-800 border-b border-slate-600/50 text-xs font-medium text-slate-400 uppercase tracking-wider flex items-center justify-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-slate-500 text-sky-500 focus:ring-sky-400 accent-sky-500"
                    checked={selectionState.isAllSelected}
                    onChange={handleSelectAllChange}
                    aria-label="Select all bookmarks"
                  />
                </div>
              )}
              <div className="grid-header-cell px-4 py-5 bg-slate-800 border-b border-slate-600/50 text-xs font-medium text-slate-400 uppercase tracking-wider">
                <SortableHeader
                  label="Title"
                  sortKey="title"
                  currentSortConfig={sortConfig}
                  onSort={onSort}
                  className="text-left"
                />
              </div>
              <div className="grid-header-cell px-4 py-5 bg-slate-800 border-b border-slate-600/50 text-xs font-medium text-slate-400 uppercase tracking-wider">
                <SortableHeader
                  label="URL & Details"
                  sortKey="url"
                  currentSortConfig={sortConfig}
                  onSort={onSort}
                  className="text-left"
                />
              </div>
              <div className="grid-header-cell px-4 py-5 bg-slate-800 border-b border-slate-600/50 text-xs font-medium text-slate-400 uppercase tracking-wider text-center">
                <SortableHeader
                  label="Date Added"
                  sortKey="addDate"
                  currentSortConfig={sortConfig}
                  onSort={onSort}
                  className="text-center"
                />
              </div>
              <div className="grid-header-cell px-4 py-5 bg-slate-800 border-b border-slate-600/50 text-xs font-medium text-slate-400 uppercase tracking-wider text-center">
                Actions
              </div>

              {/* Bookmark Rows */}
              {bookmarks.map((bookmark) => (
                <BookmarkItem
                  key={bookmark.id}
                  bookmark={bookmark}
                  isSelected={selectedBookmarks.has(bookmark.id)}
                  processingState={processingStates[bookmark.id] || {}}
                  onToggleSelect={onSelectBookmark ? (id) => onSelectBookmark(id, !selectedBookmarks.has(id)) : undefined}
                  onUpdateBookmark={onUpdateBookmark}
                  onDeleteBookmark={onDeleteBookmark}
                  onGenerateSummary={onGenerateSummary}
                  onGenerateTags={onGenerateTags}
                  onProcessYouTube={onProcessYouTube}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
      </div>
      )}

      {/* CSS styles are handled by Tailwind classes and grid layout */}
    </div>
  );
});

BookmarkList.displayName = 'BookmarkList';
SortableHeader.displayName = 'SortableHeader';

export default BookmarkList;
