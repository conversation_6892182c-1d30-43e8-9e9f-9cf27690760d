import React from 'react'

interface LocalizationButtonProps {
  locale: 'en-US' | 'en-GB'
  onToggle: () => void
}

// Design Option 1: Elegant Flag Switcher (Current Implementation)
export const LocalizationButtonElegant: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    gap: '6px',
    position: 'relative'
  }}>
    <div style={{
      fontSize: '18px',
      lineHeight: 1,
      filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))',
      transition: 'all var(--transition-fast)'
    }}>
      {locale === 'en-US' ? '🇺🇸' : '🇬🇧'}
    </div>
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      lineHeight: 1
    }}>
      <span style={{
        fontSize: '11px',
        fontWeight: '600',
        color: 'var(--text-primary)',
        letterSpacing: '0.5px'
      }}>
        {locale === 'en-US' ? 'US' : 'UK'}
      </span>
      <span style={{
        fontSize: '9px',
        color: 'var(--text-muted)',
        textTransform: 'uppercase',
        letterSpacing: '0.3px'
      }}>
        EN
      </span>
    </div>
    <div style={{
      position: 'absolute',
      top: '-2px',
      right: '-2px',
      width: '6px',
      height: '6px',
      backgroundColor: 'var(--accent-color)',
      borderRadius: '50%',
      opacity: 0.8,
      animation: 'pulse 2s infinite'
    }} />
  </div>
)

// Design Option 2: Minimal Badge Style
export const LocalizationButtonMinimal: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <button
    onClick={onToggle}
    className="localization-option-minimal"
    title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
    aria-label={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
  >
    <span style={{ fontSize: '14px' }}>{locale === 'en-US' ? '🇺🇸' : '🇬🇧'}</span>
    <span>{locale === 'en-US' ? 'US' : 'UK'}</span>
  </button>
)

// Design Option 3: Gradient Badge with Shimmer
export const LocalizationButtonGradient: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <button
    onClick={onToggle}
    className="localization-option-badge"
    title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
    aria-label={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
  >
    <span style={{ fontSize: '16px' }}>{locale === 'en-US' ? '🇺🇸' : '🇬🇧'}</span>
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      lineHeight: 1
    }}>
      <span style={{
        fontSize: '11px',
        fontWeight: '600',
        letterSpacing: '0.5px'
      }}>
        {locale === 'en-US' ? 'US' : 'UK'}
      </span>
      <span style={{
        fontSize: '9px',
        opacity: 0.7,
        textTransform: 'uppercase'
      }}>
        English
      </span>
    </div>
  </button>
)

// Design Option 4: Compact Icon with Badge
export const LocalizationButtonCompact: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <button
    onClick={onToggle}
    className="localization-option-compact"
    data-locale={locale === 'en-US' ? 'US' : 'UK'}
    title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
    aria-label={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
  >
    {locale === 'en-US' ? '🇺🇸' : '🇬🇧'}
  </button>
)

// Design Option 5: Modern Toggle Switch
export const LocalizationButtonToggle: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <div style={{
    display: 'flex',
    alignItems: 'center',
    gap: '8px',
    padding: '6px 12px',
    background: 'var(--tertiary-bg)',
    border: '1px solid var(--border-color)',
    borderRadius: 'var(--radius-full)',
    height: '44px',
    cursor: 'pointer',
    transition: 'all var(--transition-fast)'
  }} onClick={onToggle}>
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '4px',
      padding: '4px 8px',
      background: locale === 'en-US' ? 'var(--accent-color)' : 'transparent',
      borderRadius: 'var(--radius-sm)',
      color: locale === 'en-US' ? 'white' : 'var(--text-secondary)',
      fontSize: '11px',
      fontWeight: '600',
      transition: 'all var(--transition-fast)'
    }}>
      <span>🇺🇸</span>
      <span>US</span>
    </div>
    <div style={{
      display: 'flex',
      alignItems: 'center',
      gap: '4px',
      padding: '4px 8px',
      background: locale === 'en-GB' ? 'var(--accent-color)' : 'transparent',
      borderRadius: 'var(--radius-sm)',
      color: locale === 'en-GB' ? 'white' : 'var(--text-secondary)',
      fontSize: '11px',
      fontWeight: '600',
      transition: 'all var(--transition-fast)'
    }}>
      <span>🇬🇧</span>
      <span>UK</span>
    </div>
  </div>
)

// Design Option 6: Sleek Professional
export const LocalizationButtonProfessional: React.FC<LocalizationButtonProps> = ({ locale, onToggle }) => (
  <button
    onClick={onToggle}
    title={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
    aria-label={`Switch to ${locale === 'en-US' ? 'British' : 'American'} English`}
    style={{
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      padding: '8px 16px',
      background: 'linear-gradient(135deg, var(--secondary-bg), var(--tertiary-bg))',
      border: '1px solid var(--border-color)',
      borderRadius: 'var(--radius-lg)',
      color: 'var(--text-secondary)',
      cursor: 'pointer',
      fontSize: '13px',
      fontWeight: '500',
      height: '44px',
      transition: 'all var(--transition-fast)',
      position: 'relative',
      overflow: 'hidden'
    }}
    onMouseEnter={(e) => {
      e.currentTarget.style.background = 'var(--accent-color)'
      e.currentTarget.style.color = 'white'
      e.currentTarget.style.borderColor = 'var(--accent-color)'
      e.currentTarget.style.transform = 'translateY(-1px)'
      e.currentTarget.style.boxShadow = '0 4px 12px rgba(74, 158, 255, 0.3)'
    }}
    onMouseLeave={(e) => {
      e.currentTarget.style.background = 'linear-gradient(135deg, var(--secondary-bg), var(--tertiary-bg))'
      e.currentTarget.style.color = 'var(--text-secondary)'
      e.currentTarget.style.borderColor = 'var(--border-color)'
      e.currentTarget.style.transform = 'translateY(0)'
      e.currentTarget.style.boxShadow = 'none'
    }}
  >
    <div style={{
      width: '24px',
      height: '24px',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      fontSize: '14px',
      background: 'var(--primary-bg)',
      border: '1px solid var(--border-color)'
    }}>
      {locale === 'en-US' ? '🇺🇸' : '🇬🇧'}
    </div>
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'flex-start',
      lineHeight: 1
    }}>
      <span style={{
        fontSize: '12px',
        fontWeight: '600',
        letterSpacing: '0.3px'
      }}>
        {locale === 'en-US' ? 'American' : 'British'}
      </span>
      <span style={{
        fontSize: '10px',
        opacity: 0.8,
        textTransform: 'uppercase',
        letterSpacing: '0.5px'
      }}>
        English
      </span>
    </div>
  </button>
)

// Export all designs for easy switching
export const LocalizationButtonDesigns = {
  elegant: LocalizationButtonElegant,
  minimal: LocalizationButtonMinimal,
  gradient: LocalizationButtonGradient,
  compact: LocalizationButtonCompact,
  toggle: LocalizationButtonToggle,
  professional: LocalizationButtonProfessional
}

// Default export - current implementation
export default LocalizationButtonElegant
