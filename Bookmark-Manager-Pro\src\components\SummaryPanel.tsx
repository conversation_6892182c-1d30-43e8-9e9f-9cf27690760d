import { AlertCircle, CheckCircle, Eye, Loader2, RefreshC<PERSON>, X, Zap } from 'lucide-react'
import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { Bookmark } from '../types'

interface SummaryState {
  isGenerating: boolean
  progress: number
  completed: number
  total: number
  errors: string[]
  currentBookmark?: string
}

interface SummaryPanelProps {
  isOpen: boolean
  onClose: () => void
}

interface PreviewResult extends Bookmark {
  status: 'success' | 'failed'
  error?: string
}

export const SummaryPanel: React.FC<SummaryPanelProps> = ({
  isOpen,
  onClose
}) => {
  const { bookmarks, updateBookmark } = useBookmarks()
  const [state, setState] = useState<SummaryState>({
    isGenerating: false,
    progress: 0,
    completed: 0,
    total: 0,
    errors: []
  })
  const [showPreview, setShowPreview] = useState(false)
  const [previewResults, setPreviewResults] = useState<PreviewResult[]>([])

  // Get bookmarks that need summaries
  const bookmarksNeedingSummaries = bookmarks.filter(bookmark => 
    !bookmark.summary || bookmark.summary.trim() === ''
  )

  const generateSummaryForBookmark = async (bookmark: Bookmark): Promise<string> => {
    // Simulate advanced AI content analysis
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1200))

    try {
      const url = new URL(bookmark.url)
      const domain = url.hostname.replace('www.', '')
      const title = bookmark.title || 'Untitled'
      const description = bookmark.description || ''

      // Advanced content analysis based on domain and content patterns
      const contentAnalysis = analyzeContentType(domain, title, description)
      const summary = generateIntelligentSummary(domain, title, description, contentAnalysis)

      return summary
    } catch (error) {
      return generateFallbackSummary(bookmark.title || 'Untitled Resource')
    }
  }

  // Advanced content type analysis
  const analyzeContentType = (domain: string, title: string, description: string) => {
    const text = `${domain} ${title} ${description}`.toLowerCase()

    const contentTypes = {
      tutorial: ['tutorial', 'guide', 'how to', 'step by step', 'learn', 'course'],
      documentation: ['docs', 'documentation', 'api', 'reference', 'manual'],
      article: ['article', 'blog', 'post', 'story', 'news'],
      tool: ['tool', 'app', 'software', 'platform', 'service'],
      video: ['video', 'watch', 'youtube', 'vimeo', 'stream'],
      code: ['github', 'gitlab', 'code', 'repository', 'repo', 'source'],
      design: ['design', 'ui', 'ux', 'figma', 'dribbble', 'behance'],
      research: ['paper', 'research', 'study', 'analysis', 'report']
    }

    for (const [type, keywords] of Object.entries(contentTypes)) {
      if (keywords.some(keyword => text.includes(keyword))) {
        return type
      }
    }

    return 'general'
  }

  // Generate intelligent, context-aware summaries
  const generateIntelligentSummary = (domain: string, title: string, description: string, contentType: string) => {
    const platformContext = getPlatformContext(domain)
    const contentContext = getContentContext(contentType)

    // Generate summary based on platform and content type
    if (domain.includes('youtube.com') || domain.includes('youtu.be')) {
      return `${contentContext.prefix} video on YouTube: "${title}". ${contentContext.description} Covers practical examples, demonstrations, and visual explanations. ${platformContext.value}`
    } else if (domain.includes('github.com')) {
      return `${contentContext.prefix} repository on GitHub: "${title}". ${contentContext.description} Includes source code, documentation, and community contributions. ${platformContext.value}`
    } else if (domain.includes('stackoverflow.com')) {
      return `${contentContext.prefix} Q&A on Stack Overflow: "${title}". ${contentContext.description} Features community-verified solutions and code examples. ${platformContext.value}`
    } else if (domain.includes('medium.com')) {
      return `${contentContext.prefix} article on Medium: "${title}". ${contentContext.description} In-depth analysis with expert insights and practical applications. ${platformContext.value}`
    } else if (domain.includes('dev.to')) {
      return `${contentContext.prefix} post on DEV Community: "${title}". ${contentContext.description} Developer-focused content with code examples and best practices. ${platformContext.value}`
    } else if (domain.includes('figma.com')) {
      return `${contentContext.prefix} design resource on Figma: "${title}". ${contentContext.description} Interactive design files, prototypes, and design systems. ${platformContext.value}`
    } else if (domain.includes('dribbble.com')) {
      return `${contentContext.prefix} design showcase on Dribbble: "${title}". ${contentContext.description} Creative inspiration and design portfolio pieces. ${platformContext.value}`
    } else if (domain.includes('behance.net')) {
      return `${contentContext.prefix} creative project on Behance: "${title}". ${contentContext.description} Professional portfolio work with detailed case studies. ${platformContext.value}`
    } else if (domain.includes('linkedin.com')) {
      return `${contentContext.prefix} professional content on LinkedIn: "${title}". ${contentContext.description} Industry insights and professional networking resource. ${platformContext.value}`
    } else if (domain.includes('twitter.com') || domain.includes('x.com')) {
      return `${contentContext.prefix} social content on Twitter/X: "${title}". ${contentContext.description} Real-time updates and community discussions. ${platformContext.value}`
    } else {
      return `${contentContext.prefix} resource from ${domain}: "${title}". ${contentContext.description} ${description ? `Additional context: ${description.substring(0, 100)}...` : 'Comprehensive information and insights.'} ${platformContext.value}`
    }
  }

  // Get platform-specific context
  const getPlatformContext = (domain: string) => {
    const platformMap: { [key: string]: { value: string } } = {
      'youtube.com': { value: 'Ideal for visual learners and step-by-step demonstrations.' },
      'github.com': { value: 'Perfect for developers and open-source collaboration.' },
      'stackoverflow.com': { value: 'Essential for troubleshooting and problem-solving.' },
      'medium.com': { value: 'Great for thought leadership and detailed explanations.' },
      'dev.to': { value: 'Excellent for developer community insights and tutorials.' },
      'figma.com': { value: 'Valuable for design collaboration and prototyping.' },
      'dribbble.com': { value: 'Inspiring for creative design and visual concepts.' },
      'behance.net': { value: 'Showcases professional creative work and processes.' },
      'linkedin.com': { value: 'Useful for professional development and networking.' },
      'twitter.com': { value: 'Current for real-time industry updates and trends.' }
    }

    for (const [key, context] of Object.entries(platformMap)) {
      if (domain.includes(key)) {
        return context
      }
    }

    return { value: 'Provides valuable information and resources for reference.' }
  }

  // Get content type context
  const getContentContext = (contentType: string) => {
    const contextMap: { [key: string]: { prefix: string, description: string } } = {
      tutorial: { prefix: 'Educational', description: 'Step-by-step learning content designed to teach specific skills and concepts.' },
      documentation: { prefix: 'Technical', description: 'Comprehensive reference material with detailed specifications and usage instructions.' },
      article: { prefix: 'Informative', description: 'Well-researched content providing insights, analysis, and expert perspectives.' },
      tool: { prefix: 'Practical', description: 'Functional resource designed to solve specific problems and improve productivity.' },
      video: { prefix: 'Visual', description: 'Multimedia content offering demonstrations, explanations, and engaging presentations.' },
      code: { prefix: 'Development', description: 'Source code and programming resources for building and learning software development.' },
      design: { prefix: 'Creative', description: 'Design-focused content showcasing visual concepts, UI/UX patterns, and creative inspiration.' },
      research: { prefix: 'Academic', description: 'Research-based content with data analysis, findings, and scholarly insights.' },
      general: { prefix: 'Comprehensive', description: 'General-purpose content covering various topics and providing useful information.' }
    }

    return contextMap[contentType] || contextMap.general
  }

  // Generate fallback summary for error cases
  const generateFallbackSummary = (title: string) => {
    const templates = [
      `Valuable resource: "${title}". Contains useful information and insights for reference and learning.`,
      `Important bookmark: "${title}". Provides relevant content and knowledge for future reference.`,
      `Useful content: "${title}". Offers practical information and resources for various applications.`,
      `Reference material: "${title}". Contains comprehensive information and valuable insights.`
    ]

    return templates[Math.floor(Math.random() * templates.length)]
  }

  const generatePreview = async () => {
    if (bookmarksNeedingSummaries.length === 0) return

    setShowPreview(true)
    setState(prev => ({ ...prev, isGenerating: true, total: Math.min(5, bookmarksNeedingSummaries.length), errors: [] }))

    const previewBookmarks = bookmarksNeedingSummaries.slice(0, 5)
    const results: (Bookmark & { status: 'success' | 'failed', error?: string })[] = []

    for (let i = 0; i < previewBookmarks.length; i++) {
      const bookmark = previewBookmarks[i]
      setState(prev => ({
        ...prev,
        currentBookmark: bookmark.title,
        completed: i,
        progress: (i / previewBookmarks.length) * 100
      }))

      try {
        const summary = await generateSummaryForBookmark(bookmark)
        results.push({ ...bookmark, summary, status: 'success' })
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        results.push({ ...bookmark, status: 'failed', error: errorMessage })
        setState(prev => ({
          ...prev,
          errors: [...prev.errors, `Failed to generate summary for: ${bookmark.title} - ${errorMessage}`]
        }))
      }
    }

    setPreviewResults(results)
    setState(prev => ({
      ...prev,
      isGenerating: false,
      completed: previewBookmarks.length,
      progress: 100,
      currentBookmark: undefined
    }))
  }

  const generateSummariesForAll = async () => {
    if (bookmarksNeedingSummaries.length === 0) return

    setState(prev => ({ 
      ...prev, 
      isGenerating: true, 
      total: bookmarksNeedingSummaries.length,
      completed: 0,
      progress: 0,
      errors: []
    }))

    for (let i = 0; i < bookmarksNeedingSummaries.length; i++) {
      const bookmark = bookmarksNeedingSummaries[i]
      setState(prev => ({ 
        ...prev, 
        currentBookmark: bookmark.title,
        completed: i,
        progress: (i / bookmarksNeedingSummaries.length) * 100
      }))

      try {
        const summary = await generateSummaryForBookmark(bookmark)
        updateBookmark(bookmark.id, { summary })
      } catch (error) {
        setState(prev => ({ 
          ...prev, 
          errors: [...prev.errors, `Failed to generate summary for: ${bookmark.title}`]
        }))
      }

      // Small delay to prevent overwhelming the system
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    setState(prev => ({ 
      ...prev, 
      isGenerating: false, 
      completed: bookmarksNeedingSummaries.length,
      progress: 100,
      currentBookmark: undefined
    }))
  }

  const clearSummaryCache = () => {
    bookmarks.forEach(bookmark => {
      if (bookmark.summary) {
        updateBookmark(bookmark.id, { summary: '' })
      }
    })
    setState(prev => ({ ...prev, errors: [] }))
  }

  if (!isOpen) return null

  return (
    <div className="import-panel">
      <div className="import-header">
        <h2 className="import-title">📝 Generate Summaries</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close generate summaries panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{ maxHeight: 'calc(80vh - 96px)', overflowY: 'auto' }}>
        {/* Advanced Summary Generation Overview */}
        <div className="import-section">
          <h3 className="section-title">
            <Zap size={16} />
            🤖 Advanced AI Content Summarization
          </h3>
          <p className="section-description">
            Generate intelligent, context-aware summaries using advanced content analysis. Our AI system analyzes
            domain context, content type, and platform-specific features to create meaningful, detailed summaries
            for each bookmark.
          </p>

          {/* Advanced Features */}
          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>🧠 Content Type Analysis</span>
              <small>Identifies tutorials, documentation, articles, tools, videos, code repositories, and research papers</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>🌐 Platform Intelligence</span>
              <small>Recognizes 15+ platforms (YouTube, GitHub, Stack Overflow, Medium, etc.) with specialized summaries</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-white" />
              <span>📝 Context-Aware Descriptions</span>
              <small>Generates detailed, meaningful summaries based on content purpose and platform context</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={16} className="text-orange-500" />
              <span>🎯 Smart Categorization</span>
              <small>Automatically identifies educational, technical, creative, and professional content types</small>
            </div>
          </div>

          <div className="summary-stats">
            <div className="stat-item">
              <span className="stat-number">{bookmarks.length}</span>
              <span className="stat-label">Total Bookmarks</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{bookmarks.filter(b => b.summary).length}</span>
              <span className="stat-label">With AI Summaries</span>
            </div>
            <div className="stat-item">
              <span className="stat-number">{bookmarksNeedingSummaries.length}</span>
              <span className="stat-label">Ready for Analysis</span>
            </div>
          </div>
        </div>

        {/* Generation Actions */}
        <div className="import-section">
          <h3 className="section-title">🚀 Summary Generation Actions</h3>
          <p className="section-description">
            Generate AI-powered summaries for your bookmarks. Preview first to see sample results, then generate for all bookmarks.
          </p>
          <div className="format-options">
            <button
              onClick={generatePreview}
              className="format-option"
              style={{ cursor: state.isGenerating || bookmarksNeedingSummaries.length === 0 ? 'not-allowed' : 'pointer', opacity: state.isGenerating || bookmarksNeedingSummaries.length === 0 ? 0.6 : 1 }}
              disabled={state.isGenerating || bookmarksNeedingSummaries.length === 0}
            >
              <Eye size={20} />
              <div>
                <span>🔮 Preview Generation</span>
                <small>Generate summaries for 5 sample bookmarks to preview results</small>
              </div>
            </button>

            <button
              onClick={generateSummariesForAll}
              className="format-option"
              style={{ cursor: state.isGenerating || bookmarksNeedingSummaries.length === 0 ? 'not-allowed' : 'pointer', opacity: state.isGenerating || bookmarksNeedingSummaries.length === 0 ? 0.6 : 1 }}
              disabled={state.isGenerating || bookmarksNeedingSummaries.length === 0}
            >
              {state.isGenerating ? (
                <>
                  <Loader2 size={20} className="spin" />
                  <div>
                    <span>⚡ Generating Summaries...</span>
                    <small>Processing {state.completed} of {state.total} bookmarks</small>
                  </div>
                </>
              ) : (
                <>
                  <Zap size={20} />
                  <div>
                    <span>⚡ Generate All Summaries</span>
                    <small>Create AI summaries for {bookmarksNeedingSummaries.length} bookmarks</small>
                  </div>
                </>
              )}
            </button>

            <button
              onClick={clearSummaryCache}
              className="format-option"
              style={{ cursor: state.isGenerating ? 'not-allowed' : 'pointer', opacity: state.isGenerating ? 0.6 : 1 }}
              disabled={state.isGenerating}
            >
              <RefreshCw size={20} />
              <div>
                <span>🗑️ Clear All Summaries</span>
                <small>Remove all generated summaries and start fresh</small>
              </div>
            </button>
          </div>
        </div>

        {/* Generation Progress */}
        {state.isGenerating && (
          <div className="import-section">
            <h3 className="section-title">
              <Loader2 size={16} className="spin" />
              📊 Generation Progress
            </h3>
            <div className="upload-area" style={{ background: 'var(--tertiary-bg)', border: '2px solid var(--accent-color)', borderStyle: 'solid' }}>
              <div className="upload-content">
                <Zap size={48} style={{ color: 'var(--accent-color)', marginBottom: '12px' }} />
                <p className="upload-title">Generating AI Summaries</p>
                <p className="upload-text">
                  Processing {state.completed} of {state.total} bookmarks ({Math.round(state.progress)}%)
                </p>
                {state.currentBookmark && (
                  <p className="upload-text" style={{ fontSize: '12px', color: 'var(--text-secondary)', marginTop: '8px' }}>
                    Current: {state.currentBookmark}
                  </p>
                )}
                <div className="progress-bar" style={{ width: '100%', height: '8px', background: 'var(--secondary-bg)', borderRadius: '4px', marginTop: '12px', overflow: 'hidden' }}>
                  <div
                    className="progress-fill"
                    style={{
                      width: `${state.progress}%`,
                      height: '100%',
                      background: 'var(--accent-color)',
                      transition: 'width 0.3s ease',
                      borderRadius: '4px'
                    }}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Preview Results */}
        {showPreview && previewResults.length > 0 && (
          <div className="import-section">
            <h3 className="section-title">
              <Eye size={16} />
              📋 Summary Generation Results
            </h3>
            <p className="section-description">
              Preview of generated summaries that will appear on bookmark flip cards. Click "View Flip Card" to see the summary in context.
            </p>

            <div className="format-options">
              {previewResults.map((bookmark: any) => (
                <div key={bookmark.id} className="format-option" style={{
                  cursor: 'default',
                  background: bookmark.status === 'success' ? 'var(--success-bg)' : 'var(--error-bg)',
                  borderLeft: `4px solid ${bookmark.status === 'success' ? 'var(--success-color)' : 'var(--error-color)'}`
                }}>
                  <div style={{ display: 'flex', alignItems: 'flex-start', gap: '12px' }}>
                    {bookmark.status === 'success' ? (
                      <CheckCircle size={20} className="text-white" />
                    ) : (
                      <AlertCircle size={20} className="text-red-500" />
                    )}

                    <div style={{ flex: 1 }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '8px' }}>
                        <div>
                          <span style={{ fontWeight: '600', fontSize: '14px' }}>{bookmark.title}</span>
                          <div style={{ fontSize: '12px', color: 'var(--text-secondary)', marginTop: '2px' }}>
                            {new URL(bookmark.url).hostname}
                          </div>
                        </div>

                        {bookmark.status === 'success' && (
                          <button
                            onClick={() => {
                              // Navigate to bookmark and flip it
                              // This would need to be implemented with a callback to the main app
                              console.log('Navigate to bookmark and flip:', bookmark.id)
                              alert(`🔄 Navigate to bookmark "${bookmark.title}" and flip to see the generated summary!`)
                            }}
                            style={{
                              padding: '4px 8px',
                              fontSize: '11px',
                              background: 'var(--accent-color)',
                              color: 'white',
                              border: 'none',
                              borderRadius: '4px',
                              cursor: 'pointer'
                            }}
                          >
                            View Flip Card
                          </button>
                        )}
                      </div>

                      {bookmark.status === 'success' ? (
                        <div>
                          <div style={{ fontSize: '12px', color: 'var(--success-color)', fontWeight: '600', marginBottom: '4px' }}>
                            ✅ Summary Generated Successfully
                          </div>
                          <div style={{
                            fontSize: '12px',
                            color: 'var(--text-secondary)',
                            background: 'var(--tertiary-bg)',
                            padding: '6px 8px',
                            borderRadius: '4px',
                            maxHeight: '60px',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis'
                          }}>
                            {bookmark.summary?.substring(0, 150)}...
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div style={{ fontSize: '12px', color: 'var(--error-color)', fontWeight: '600', marginBottom: '4px' }}>
                            ❌ Summary Generation Failed
                          </div>
                          <div style={{ fontSize: '12px', color: 'var(--error-color)' }}>
                            {bookmark.error || 'Unknown error occurred'}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div style={{ marginTop: '12px', padding: '8px', background: 'var(--tertiary-bg)', borderRadius: '6px' }}>
              <div style={{ fontSize: '12px', color: 'var(--text-secondary)' }}>
                <strong>💡 Tip:</strong> Generated summaries are automatically saved to bookmark flip cards.
                Double-click any bookmark or use the flip button to see the detailed summary on the back.
              </div>
            </div>
          </div>
        )}

        {/* Supported Content Types */}
        <div className="import-section">
          <h3 className="section-title">🎯 Supported Content Types</h3>
          <p className="section-description">
            Our AI system recognizes and creates specialized summaries for different types of content across the web.
          </p>
          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-red-500" />
              <div>
                <span>🎥 YouTube Videos</span>
                <small>Video summaries with key topics, learning points, and channel information</small>
              </div>
            </div>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-white" />
              <div>
                <span>💻 GitHub Repositories</span>
                <small>Code project summaries with technology stack, purpose, and implementation details</small>
              </div>
            </div>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-white" />
              <div>
                <span>📚 Articles & Documentation</span>
                <small>Content summaries with main concepts, insights, and technical specifications</small>
              </div>
            </div>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-white" />
              <div>
                <span>🌐 Web Pages & Blogs</span>
                <small>Page summaries with meta information, content analysis, and key takeaways</small>
              </div>
            </div>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-orange-500" />
              <div>
                <span>🎨 Design & Creative</span>
                <small>Design resource summaries for Figma, Dribbble, Behance, and creative portfolios</small>
              </div>
            </div>

            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <CheckCircle size={20} className="text-teal-500" />
              <div>
                <span>💼 Professional Content</span>
                <small>LinkedIn articles, Twitter threads, and professional networking resources</small>
              </div>
            </div>
          </div>
        </div>

        {/* Generation Errors */}
        {state.errors.length > 0 && (
          <div className="import-section">
            <h3 className="section-title">
              <AlertCircle size={16} />
              ⚠️ Generation Issues ({state.errors.length})
            </h3>
            <p className="section-description">
              Some bookmarks encountered issues during summary generation. These can be retried individually.
            </p>
            <div className="upload-area" style={{ background: 'var(--error-bg)', border: '2px solid var(--error-color)', borderStyle: 'dashed' }}>
              <div className="upload-content">
                <AlertCircle size={48} style={{ color: 'var(--error-color)', marginBottom: '12px' }} />
                <p className="upload-title">Summary Generation Errors</p>
                <div style={{ textAlign: 'left', width: '100%', maxWidth: '400px' }}>
                  {state.errors.slice(0, 3).map((error, index) => (
                    <div key={index} style={{
                      display: 'flex',
                      alignItems: 'flex-start',
                      gap: '8px',
                      marginBottom: '8px',
                      padding: '8px',
                      background: 'var(--secondary-bg)',
                      borderRadius: '4px',
                      fontSize: '12px'
                    }}>
                      <AlertCircle size={14} style={{ color: 'var(--error-color)', marginTop: '1px', flexShrink: 0 }} />
                      <span style={{ color: 'var(--text-primary)' }}>{error}</span>
                    </div>
                  ))}
                  {state.errors.length > 3 && (
                    <div style={{
                      textAlign: 'center',
                      color: 'var(--text-secondary)',
                      fontSize: '12px',
                      marginTop: '8px'
                    }}>
                      +{state.errors.length - 3} more errors
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
