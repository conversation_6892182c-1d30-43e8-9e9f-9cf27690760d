import React, { useState, useEffect, useRef, useCallback } from 'react';
import MediaControls from './MediaControls';
import { Bookmark } from '../../types';

interface MediaPlayerProps {
  playlist: Bookmark[];
  autoPlay?: boolean;
  loop?: boolean;
  className?: string;
  onPlaylistEnd?: () => void;
  onItemChange?: (item: Bookmark, index: number) => void;
  onError?: (error: string, item: Bookmark) => void;
}

interface MediaState {
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  isLoading: boolean;
  volume: number;
  isMuted: boolean;
  playbackRate: number;
  error: string | null;
}

const SUPPORTED_AUDIO_TYPES = [
  'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/ogg', 'audio/aac', 'audio/flac'
];

const SUPPORTED_VIDEO_TYPES = [
  'video/mp4', 'video/webm', 'video/ogg', 'video/avi', 'video/mov'
];

const getMediaType = (url: string): 'audio' | 'video' | 'unknown' => {
  const extension = url.split('.').pop()?.toLowerCase();
  
  const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac', 'm4a'];
  const videoExtensions = ['mp4', 'webm', 'ogv', 'avi', 'mov', 'mkv'];
  
  if (extension && audioExtensions.includes(extension)) return 'audio';
  if (extension && videoExtensions.includes(extension)) return 'video';
  
  return 'unknown';
};

const isMediaUrl = (url: string): boolean => {
  const mediaType = getMediaType(url);
  return mediaType === 'audio' || mediaType === 'video';
};

export const MediaPlayer: React.FC<MediaPlayerProps> = ({
  playlist,
  autoPlay = false,
  loop = false,
  className = '',
  onPlaylistEnd,
  onItemChange,
  onError
}) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [mediaState, setMediaState] = useState<MediaState>({
    currentTime: 0,
    duration: 0,
    isPlaying: false,
    isLoading: false,
    volume: 75,
    isMuted: false,
    playbackRate: 1,
    error: null
  });
  
  const audioRef = useRef<HTMLAudioElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  // Filter playlist to only include media items
  const mediaPlaylist = playlist.filter(item => isMediaUrl(item.url));
  const currentItem = mediaPlaylist[currentIndex];
  const currentMediaType = currentItem ? getMediaType(currentItem.url) : 'unknown';
  
  // Get current media element
  const getCurrentMediaElement = useCallback(() => {
    return currentMediaType === 'video' ? videoRef.current : audioRef.current;
  }, [currentMediaType]);
  
  // Update media state from media element
  const updateMediaState = useCallback(() => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    setMediaState(prev => ({
      ...prev,
      currentTime: media.currentTime,
      duration: media.duration || 0,
      isLoading: media.readyState < 3,
      error: null
    }));
  }, [getCurrentMediaElement]);
  
  // Media event handlers
  const handleLoadedMetadata = useCallback(() => {
    updateMediaState();
  }, [updateMediaState]);
  
  const handleTimeUpdate = useCallback(() => {
    updateMediaState();
  }, [updateMediaState]);
  
  const handleEnded = useCallback(() => {
    if (currentIndex < mediaPlaylist.length - 1) {
      // Move to next item
      setCurrentIndex(prev => prev + 1);
    } else if (loop) {
      // Loop back to beginning
      setCurrentIndex(0);
    } else {
      // Playlist ended
      setMediaState(prev => ({ ...prev, isPlaying: false }));
      onPlaylistEnd?.();
    }
  }, [currentIndex, mediaPlaylist.length, loop, onPlaylistEnd]);
  
  const handleError = useCallback((event: Event) => {
    const media = event.target as HTMLMediaElement;
    const errorMessage = `Failed to load media: ${media.error?.message || 'Unknown error'}`;
    
    setMediaState(prev => ({
      ...prev,
      error: errorMessage,
      isPlaying: false,
      isLoading: false
    }));
    
    onError?.(errorMessage, currentItem);
  }, [currentItem, onError]);
  
  const handleCanPlay = useCallback(() => {
    setMediaState(prev => ({ ...prev, isLoading: false }));
    
    if (autoPlay && mediaState.isPlaying) {
      const media = getCurrentMediaElement();
      media?.play().catch(console.error);
    }
  }, [autoPlay, mediaState.isPlaying, getCurrentMediaElement]);
  
  // Setup media element event listeners
  useEffect(() => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    media.addEventListener('loadedmetadata', handleLoadedMetadata);
    media.addEventListener('timeupdate', handleTimeUpdate);
    media.addEventListener('ended', handleEnded);
    media.addEventListener('error', handleError);
    media.addEventListener('canplay', handleCanPlay);
    
    return () => {
      media.removeEventListener('loadedmetadata', handleLoadedMetadata);
      media.removeEventListener('timeupdate', handleTimeUpdate);
      media.removeEventListener('ended', handleEnded);
      media.removeEventListener('error', handleError);
      media.removeEventListener('canplay', handleCanPlay);
    };
  }, [getCurrentMediaElement, handleLoadedMetadata, handleTimeUpdate, handleEnded, handleError, handleCanPlay]);
  
  // Update media source when current item changes
  useEffect(() => {
    const media = getCurrentMediaElement();
    if (!media || !currentItem) return;
    
    setMediaState(prev => ({ ...prev, isLoading: true, error: null }));
    media.src = currentItem.url;
    media.volume = mediaState.volume / 100;
    media.muted = mediaState.isMuted;
    
    onItemChange?.(currentItem, currentIndex);
  }, [currentItem, currentIndex, getCurrentMediaElement, mediaState.volume, mediaState.isMuted, onItemChange]);
  
  // Control functions
  const handlePlayPause = useCallback(async () => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    try {
      if (mediaState.isPlaying) {
        media.pause();
        setMediaState(prev => ({ ...prev, isPlaying: false }));
      } else {
        await media.play();
        setMediaState(prev => ({ ...prev, isPlaying: true }));
      }
    } catch (error) {
      console.error('Playback error:', error);
      setMediaState(prev => ({ 
        ...prev, 
        error: 'Playback failed. Please try again.',
        isPlaying: false 
      }));
    }
  }, [getCurrentMediaElement, mediaState.isPlaying]);
  
  const handleSeek = useCallback((time: number) => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    media.currentTime = time;
    setMediaState(prev => ({ ...prev, currentTime: time }));
  }, [getCurrentMediaElement]);
  
  const handlePrevious = useCallback(() => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  }, [currentIndex]);
  
  const handleNext = useCallback(() => {
    if (currentIndex < mediaPlaylist.length - 1) {
      setCurrentIndex(prev => prev + 1);
    }
  }, [currentIndex, mediaPlaylist.length]);
  
  const handleVolumeChange = useCallback((volume: number) => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    media.volume = volume / 100;
    setMediaState(prev => ({ 
      ...prev, 
      volume, 
      isMuted: volume === 0 
    }));
  }, [getCurrentMediaElement]);
  
  const handleMute = useCallback(() => {
    const media = getCurrentMediaElement();
    if (!media) return;
    
    const newMuted = !mediaState.isMuted;
    media.muted = newMuted;
    setMediaState(prev => ({ ...prev, isMuted: newMuted }));
  }, [getCurrentMediaElement, mediaState.isMuted]);
  
  const handleFullscreen = useCallback(async () => {
    if (currentMediaType !== 'video' || !videoRef.current) return;
    
    try {
      if (!isFullscreen) {
        await videoRef.current.requestFullscreen();
        setIsFullscreen(true);
      } else {
        await document.exitFullscreen();
        setIsFullscreen(false);
      }
    } catch (error) {
      console.error('Fullscreen error:', error);
    }
  }, [currentMediaType, isFullscreen]);
  
  // Handle fullscreen change events
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };
    
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
  }, []);
  
  // No media items in playlist
  if (mediaPlaylist.length === 0) {
    return (
      <div className={`multimedia-media-player multimedia-media-player--empty ${className}`}>
        <div className="multimedia-media-player__empty-state">
          <div className="multimedia-media-player__empty-icon">🎵</div>
          <h3 className="multimedia-media-player__empty-title">No Media Found</h3>
          <p className="multimedia-media-player__empty-description">
            Add audio or video bookmarks to your playlist to start playing media.
          </p>
        </div>
      </div>
    );
  }
  
  return (
    <div className={`multimedia-media-player ${className}`}>
      {/* Media Elements */}
      <audio
        ref={audioRef}
        preload="metadata"
        style={{ display: 'none' }}
      />
      
      {currentMediaType === 'video' && (
        <div className="multimedia-media-player__video-container">
          <video
            ref={videoRef}
            className="multimedia-media-player__video"
            preload="metadata"
            controls={false}
            onClick={handlePlayPause}
          />
          
          {/* Video Overlay */}
          <div className="multimedia-media-player__video-overlay">
            {mediaState.isLoading && (
              <div className="multimedia-media-player__loading">
                <div className="multimedia-spinner"></div>
                <span>Loading...</span>
              </div>
            )}
            
            {mediaState.error && (
              <div className="multimedia-media-player__error">
                <span>⚠️ {mediaState.error}</span>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Audio Visualization (for audio files) */}
      {currentMediaType === 'audio' && (
        <div className="multimedia-media-player__audio-visual">
          <div className="multimedia-media-player__album-art">
            <div className="multimedia-media-player__audio-icon">🎵</div>
          </div>
          
          {mediaState.isLoading && (
            <div className="multimedia-media-player__loading">
              <div className="multimedia-spinner"></div>
              <span>Loading audio...</span>
            </div>
          )}
          
          {mediaState.error && (
            <div className="multimedia-media-player__error">
              <span>⚠️ {mediaState.error}</span>
            </div>
          )}
        </div>
      )}
      
      {/* Media Controls */}
      <MediaControls
        playlist={mediaPlaylist}
        currentItem={currentItem}
        currentTime={mediaState.currentTime}
        duration={mediaState.duration}
        isPlaying={mediaState.isPlaying}
        volume={mediaState.volume}
        isMuted={mediaState.isMuted}
        onSeek={handleSeek}
        onPlayPause={handlePlayPause}
        onPrevious={handlePrevious}
        onNext={handleNext}
        onVolumeChange={handleVolumeChange}
        onMute={handleMute}
        onFullscreen={currentMediaType === 'video' ? handleFullscreen : undefined}
        className="multimedia-media-player__controls"
      />
      
      {/* Playlist Info */}
      <div className="multimedia-media-player__playlist-info">
        <span className="multimedia-media-player__playlist-count">
          {mediaPlaylist.length} media {mediaPlaylist.length === 1 ? 'item' : 'items'}
        </span>
        {loop && (
          <span className="multimedia-media-player__loop-indicator">
            🔁 Loop enabled
          </span>
        )}
      </div>
    </div>
  );
};

export default MediaPlayer;