import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface SummaryGenerationPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const SummaryGenerationPanelOptimized: React.FC<SummaryGenerationPanelProps> = ({ isOpen, onClose, onOpenPanel }) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [summaryType, setSummaryType] = useState('overview')
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [generatedSummary, setGeneratedSummary] = useState('')
  const [advancedOptionsExpanded, setAdvancedOptionsExpanded] = useState(false)
  const [templatesExpanded, setTemplatesExpanded] = useState(false)

  const generateSummary = () => {
    setIsGenerating(true)
    setGenerationProgress(0)
    // Summary generation logic here
    console.log('Generating summary...')
  }

  const exportSummary = () => {
    console.log('Exporting summary...')
  }

  return (
    <div className="import-panel">
      {/* Quick Generate */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.quickGenerate')}</h3>
        <p className="section-description">{t('summary.quickGenerateDescription')}</p>
        
        <div className="form-row">
          <label>{t('summary.type')}:</label>
          <select 
            value={summaryType} 
            onChange={(e) => setSummaryType(e.target.value)}
            className="input-compact"
          >
            <option value="overview">{t('summary.overview')}</option>
            <option value="detailed">{t('summary.detailed')}</option>
            <option value="statistics">{t('summary.statistics')}</option>
            <option value="health">{t('summary.health')}</option>
            <option value="domains">{t('summary.domains')}</option>
          </select>
        </div>

        <div className="controls-grid">
          <button 
            className="btn-compact primary" 
            onClick={generateSummary}
            disabled={isGenerating}
          >
            {isGenerating ? t('summary.generating') : t('summary.generate')}
          </button>
          <button className="btn-compact" onClick={exportSummary}>
            {t('summary.export')}
          </button>
        </div>

        {isGenerating && (
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${generationProgress}%` }} />
            <span className="progress-text">{generationProgress}%</span>
          </div>
        )}
      </div>

      {/* Summary Statistics */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.statistics')}</h3>
        
        <div className="controls-grid">
          <div className="status-compact">
            <div className="status-icon info" />
            <span>{bookmarks.length} {t('summary.totalBookmarks')}</span>
          </div>
          <div className="status-compact">
            <div className="status-icon success" />
            <span>15 {t('summary.folders')}</span>
          </div>
        </div>
        
        <div className="controls-grid">
          <div className="status-compact">
            <div className="status-icon warning" />
            <span>42 {t('summary.domains')}</span>
          </div>
          <div className="status-compact">
            <div className="status-icon info" />
            <span>8 {t('summary.tags')}</span>
          </div>
        </div>
      </div>

      {/* Quick Templates */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.quickTemplates')}</h3>
        
        <div className="button-grid">
          <button className="btn-compact">{t('summary.weeklyReport')}</button>
          <button className="btn-compact">{t('summary.healthReport')}</button>
          <button className="btn-compact">{t('summary.domainAnalysis')}</button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact">{t('summary.usageStats')}</button>
          <button className="btn-compact">{t('summary.customReport')}</button>
        </div>
      </div>

      {/* Summary Options */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.options')}</h3>
        
        <div className="form-row">
          <label>{t('summary.format')}:</label>
          <select className="input-compact">
            <option value="markdown">{t('summary.markdown')}</option>
            <option value="html">{t('summary.html')}</option>
            <option value="pdf">{t('summary.pdf')}</option>
            <option value="text">{t('summary.plainText')}</option>
          </select>
        </div>
        
        <div className="form-row">
          <label>
            <input type="checkbox" defaultChecked />
            {t('summary.includeCharts')}
          </label>
        </div>
        
        <div className="form-row">
          <label>
            <input type="checkbox" defaultChecked />
            {t('summary.includeMetadata')}
          </label>
        </div>
      </div>

      {/* Advanced Options - Collapsible */}
      <div className={`collapsible-section ${advancedOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setAdvancedOptionsExpanded(!advancedOptionsExpanded)}
        >
          <span>{t('summary.advancedOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('summary.dateRange')}:</label>
            <div className="controls-grid">
              <input type="date" className="input-compact" />
              <input type="date" className="input-compact" />
            </div>
          </div>
          <div className="form-row">
            <label>{t('summary.includeFields')}:</label>
            <div className="form-row">
              <label>
                <input type="checkbox" defaultChecked />
                {t('summary.urls')}
              </label>
            </div>
            <div className="form-row">
              <label>
                <input type="checkbox" defaultChecked />
                {t('summary.descriptions')}
              </label>
            </div>
            <div className="form-row">
              <label>
                <input type="checkbox" />
                {t('summary.favicons')}
              </label>
            </div>
          </div>
          <div className="form-row">
            <label>{t('summary.groupBy')}:</label>
            <select className="input-compact">
              <option value="folder">{t('summary.folder')}</option>
              <option value="domain">{t('summary.domain')}</option>
              <option value="date">{t('summary.date')}</option>
              <option value="tags">{t('summary.tags')}</option>
            </select>
          </div>
          <div className="form-row">
            <label>{t('summary.maxItems')}:</label>
            <input type="number" defaultValue="1000" className="input-compact" />
          </div>
        </div>
      </div>

      {/* Custom Templates - Collapsible */}
      <div className={`collapsible-section ${templatesExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setTemplatesExpanded(!templatesExpanded)}
        >
          <span>{t('summary.customTemplates')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('summary.templateName')}:</label>
            <input type="text" placeholder={t('summary.templateNamePlaceholder')} className="input-compact" />
          </div>
          <div className="form-row">
            <label>{t('summary.template')}:</label>
            <textarea 
              placeholder={t('summary.templatePlaceholder')} 
              className="input-compact"
              rows={4}
            />
          </div>
          <div className="controls-grid">
            <button className="btn-compact">{t('summary.saveTemplate')}</button>
            <button className="btn-compact">{t('summary.loadTemplate')}</button>
          </div>
        </div>
      </div>

      {/* Generated Summary Preview */}
      {generatedSummary && (
        <div className="collapsible-section expanded">
          <button className="collapsible-header">
            <span>{t('summary.preview')}</span>
            <span className="collapsible-icon">▼</span>
          </button>
          <div className="collapsible-content">
            <div className="scrollable-content">
              <div className="summary-preview">
                <h4>{t('summary.bookmarkSummary')}</h4>
                <p>{t('summary.generatedOn')}: {new Date().toLocaleDateString()}</p>
                <p>{t('summary.totalBookmarks')}: {bookmarks.length}</p>
                <p>{t('summary.topDomains')}: google.com, github.com, stackoverflow.com</p>
                <p>{t('summary.healthStatus')}: {t('summary.good')}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.quickActions')}</h3>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('health')}>
            {t('summary.healthCheck')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('domain')}>
            {t('summary.domainAnalysis')}
          </button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('export')}>
            {t('summary.exportData')}
          </button>
          <button className="btn-compact">{t('summary.scheduleSummary')}</button>
        </div>
      </div>

      {/* Navigation */}
      <div className="import-section">
        <h3 className="section-title">{t('summary.relatedTools')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('content')}>
            {t('summary.contentAnalysis')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('multimedia')}>
            {t('summary.multimediaAnalysis')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('hybrid')}>
            {t('summary.hybridAnalysis')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  )
}