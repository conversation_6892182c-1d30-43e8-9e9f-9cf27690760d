{"typescript.preferences.includePackageJsonAutoImports": "on", "typescript.suggest.autoImports": true, "typescript.validate.enable": true, "typescript.format.enable": true, "typescript.preferences.quoteStyle": "single", "typescript.preferences.semicolons": "insert", "typescript.reportStyleChecksAsWarnings": true, "typescript.check.npmIsInstalled": true, "typescript.tsc.autoDetect": "off", "typescript.preferences.noSemicolons": false, "typescript.updateImportsOnFileMove.enabled": "always", "typescript.suggest.completeFunctionCalls": true, "typescript.suggest.includeAutomaticOptionalChainCompletions": true, "typescript.inlayHints.enumMemberValues.enabled": true, "typescript.inlayHints.functionLikeReturnTypes.enabled": true, "typescript.inlayHints.parameterNames.enabled": "all", "typescript.inlayHints.parameterTypes.enabled": true, "typescript.inlayHints.propertyDeclarationTypes.enabled": true, "typescript.inlayHints.variableTypes.enabled": true, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "explicit"}, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact"], "eslint.format.enable": true, "files.associations": {"*.tsx": "typescriptreact", "*.ts": "typescript"}, "emmet.includeLanguages": {"typescriptreact": "html"}, "typescript.preferences.importModuleSpecifier": "relative", "typescript.workspaceSymbols.scope": "allOpenProjects", "problems.decorations.enabled": true, "editor.quickSuggestions": {"other": true, "comments": false, "strings": true}, "[typescriptreact]": {"editor.defaultFormatter": "vscode.typescript-language-features"}}