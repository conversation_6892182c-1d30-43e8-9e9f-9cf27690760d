<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Memory Relief Dashboard - 63.2% Growth Fix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
            transition: transform 0.2s;
        }
        
        .status-card:hover {
            transform: translateY(-2px);
        }
        
        .status-card.critical {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        
        .status-card.warning {
            border-left-color: #ffc107;
            background: #fffbf0;
        }
        
        .status-card.success {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        
        .status-value {
            font-size: 2.5em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .status-label {
            color: #666;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 15px 30px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #28a745, #1e7e34);
            color: white;
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #545b62);
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .log-container {
            background: #1a1a1a;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .log-output {
            color: #00ff00;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.4;
            white-space: pre-wrap;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 20px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .alert {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        
        .alert-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }
        
        .alert-warning {
            background: #fff3cd;
            color: #856404;
            border-left: 4px solid #ffc107;
        }
        
        .alert-danger {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        
        .pulsing {
            animation: pulse 1s infinite;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🚨 Memory Relief Dashboard</h1>
            <p>Emergency Response for 63.2% Memory Growth</p>
        </div>
        
        <div class="content">
            <div id="alertContainer"></div>
            
            <div class="status-grid">
                <div class="status-card" id="memoryCard">
                    <div class="status-value" id="memoryValue">--</div>
                    <div class="status-label">Memory Usage</div>
                </div>
                <div class="status-card" id="usedCard">
                    <div class="status-value" id="usedValue">-- MB</div>
                    <div class="status-label">Used Memory</div>
                </div>
                <div class="status-card" id="totalCard">
                    <div class="status-value" id="totalValue">-- MB</div>
                    <div class="status-label">Total Allocated</div>
                </div>
                <div class="status-card" id="limitCard">
                    <div class="status-value" id="limitValue">-- MB</div>
                    <div class="status-label">Memory Limit</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div class="controls">
                <button class="btn btn-primary" onclick="checkMemoryStatus()">📊 Check Status</button>
                <button class="btn btn-danger" onclick="executeEmergencyRelief()" id="reliefBtn">🚨 Emergency Relief</button>
                <button class="btn btn-success" onclick="runQuickCleanup()">🧹 Quick Cleanup</button>
                <button class="btn btn-secondary" onclick="clearLogs()">🗑️ Clear Logs</button>
            </div>
            
            <div class="log-container">
                <div class="log-output" id="logOutput">Ready for memory relief operations...
</div>
            </div>
        </div>
    </div>

    <script src="immediate-memory-relief-63percent.js"></script>
    <script>
        let isRunning = false;
        let logElement = document.getElementById('logOutput');
        let alertContainer = document.getElementById('alertContainer');
        
        // Override console.log to capture output
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            appendLog(args.join(' '));
        };
        
        function appendLog(message) {
            logElement.textContent += new Date().toLocaleTimeString() + ' - ' + message + '\n';
            logElement.scrollTop = logElement.scrollHeight;
        }
        
        function showAlert(message, type = 'info') {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alertContainer.appendChild(alert);
            
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.parentNode.removeChild(alert);
                }
            }, 5000);
        }
        
        function updateMemoryDisplay() {
            if (!('memory' in performance)) {
                showAlert('Memory API not available in this browser', 'warning');
                return;
            }
            
            const memory = performance.memory;
            const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            const limit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
            const percentage = Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100);
            
            document.getElementById('memoryValue').textContent = percentage + '%';
            document.getElementById('usedValue').textContent = used + ' MB';
            document.getElementById('totalValue').textContent = total + ' MB';
            document.getElementById('limitValue').textContent = limit + ' MB';
            
            // Update progress bar
            document.getElementById('progressFill').style.width = percentage + '%';
            
            // Update card styles based on memory usage
            const memoryCard = document.getElementById('memoryCard');
            memoryCard.className = 'status-card';
            
            if (percentage >= 80) {
                memoryCard.classList.add('critical');
                showAlert(`CRITICAL: ${percentage}% memory usage detected!`, 'danger');
            } else if (percentage >= 60) {
                memoryCard.classList.add('warning');
                if (percentage >= 63) {
                    showAlert(`HIGH: ${percentage}% memory usage - Emergency relief recommended`, 'warning');
                }
            } else {
                memoryCard.classList.add('success');
            }
        }
        
        function checkMemoryStatus() {
            appendLog('📊 Checking memory status...');
            updateMemoryDisplay();
            appendLog('✅ Memory status updated');
        }
        
        async function executeEmergencyRelief() {
            if (isRunning) {
                showAlert('Emergency relief already in progress', 'warning');
                return;
            }
            
            isRunning = true;
            const reliefBtn = document.getElementById('reliefBtn');
            reliefBtn.disabled = true;
            reliefBtn.classList.add('pulsing');
            reliefBtn.textContent = '🔄 Running Relief...';
            
            try {
                appendLog('🚨 Starting emergency memory relief...');
                showAlert('Emergency memory relief started', 'info');
                
                const result = await ImmediateMemoryRelief.execute();
                
                appendLog(`✅ Emergency relief completed in ${result.duration}ms`);
                appendLog(`📉 Memory reduced by ${result.reduction.toFixed(1)}%`);
                
                if (result.reduction > 0) {
                    showAlert(`Success! Memory reduced by ${result.reduction.toFixed(1)}%`, 'success');
                } else {
                    showAlert('Relief completed but no significant reduction achieved', 'warning');
                }
                
                updateMemoryDisplay();
                
            } catch (error) {
                appendLog(`❌ Emergency relief failed: ${error.message}`);
                showAlert(`Emergency relief failed: ${error.message}`, 'danger');
            } finally {
                isRunning = false;
                reliefBtn.disabled = false;
                reliefBtn.classList.remove('pulsing');
                reliefBtn.textContent = '🚨 Emergency Relief';
            }
        }
        
        async function runQuickCleanup() {
            appendLog('🧹 Running quick cleanup...');
            
            try {
                // Quick cleanup without full relief
                const relief = new ImmediateMemoryRelief();
                await relief.clearAllTimersAndIntervals();
                await relief.forceGarbageCollection();
                
                appendLog('✅ Quick cleanup completed');
                showAlert('Quick cleanup completed', 'success');
                updateMemoryDisplay();
                
            } catch (error) {
                appendLog(`❌ Quick cleanup failed: ${error.message}`);
                showAlert(`Quick cleanup failed: ${error.message}`, 'danger');
            }
        }
        
        function clearLogs() {
            logElement.textContent = 'Logs cleared...\n';
            alertContainer.innerHTML = '';
        }
        
        // Auto-refresh memory stats every 3 seconds
        setInterval(updateMemoryDisplay, 3000);
        
        // Initial load
        updateMemoryDisplay();
        
        // Auto-trigger relief if memory is critically high
        setTimeout(() => {
            if ('memory' in performance) {
                const currentUsage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
                if (currentUsage >= 63.2) {
                    appendLog(`🚨 Auto-detected ${currentUsage.toFixed(1)}% memory usage - Triggering emergency relief...`);
                    executeEmergencyRelief();
                }
            }
        }, 2000);
        
        appendLog('🎯 Memory Relief Dashboard initialized');
        appendLog('📊 Monitoring memory usage every 3 seconds');
        appendLog('🚨 Emergency relief will auto-trigger at 63.2%+ usage');
    </script>
</body>
</html>