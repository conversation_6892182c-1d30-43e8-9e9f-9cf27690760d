/**
 * IMMEDIATE MEMORY RELIEF - 67.8% USAGE
 * Run this script immediately in browser console
 */

console.log('🚨 IMMEDIATE MEMORY RELIEF - 67.8% USAGE');
console.log('==========================================');

// 1. AGGRESSIVE GARBAGE COLLECTION
console.log('🗑️ Phase 1: Aggressive garbage collection...');
for (let i = 0; i < 10; i++) {
  if (typeof gc !== 'undefined') {
    gc();
  }
  
  // Force memory release patterns
  const temp = new Array(3000000).fill(null);
  temp.length = 0;
}

// 2. CLEAR RECOMMENDATION SYSTEM CACHES
console.log('🧹 Phase 2: Clearing recommendation caches...');
if (window.recommendationCache) {
  window.recommendationCache.emergencyCleanup();
  console.log('✅ Recommendation caches cleared');
} else {
  console.log('ℹ️ No recommendation cache found');
}

// 3. C<PERSON>AR PLAYLIST DEMO DATA
console.log('🧹 Phase 3: Clearing demo data...');
if (window.playlistDemo) {
  delete window.playlistDemo;
  console.log('✅ Playlist demo data cleared');
}

// 4. CLEAR ALL STORAGE
console.log('💾 Phase 4: Clearing storage...');
try {
  localStorage.clear();
  sessionStorage.clear();
  console.log('✅ Storage cleared');
} catch (e) {
  console.warn('⚠️ Storage clear failed:', e);
}

// 5. CLEAR PERFORMANCE DATA
console.log('📊 Phase 5: Clearing performance data...');
if (window.performance) {
  if (window.performance.clearMeasures) window.performance.clearMeasures();
  if (window.performance.clearMarks) window.performance.clearMarks();
  if (window.performance.clearResourceTimings) window.performance.clearResourceTimings();
  console.log('✅ Performance data cleared');
}

// 6. CLEAR REACT DEVTOOLS
console.log('⚛️ Phase 6: Clearing React DevTools...');
if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberRoot = null;
  window.__REACT_DEVTOOLS_GLOBAL_HOOK__.onCommitFiberUnmount = null;
  console.log('✅ React DevTools cleared');
}

// 7. CLEAR VITE HMR DATA
console.log('⚡ Phase 7: Clearing Vite HMR data...');
if (window.__vite_plugin_react_preamble_installed__) {
  delete window.__vite_plugin_react_preamble_installed__;
  console.log('✅ Vite HMR data cleared');
}

// 8. REMOVE STREAMING ELEMENTS
console.log('📡 Phase 8: Removing streaming elements...');
const streamingElements = document.querySelectorAll('[data-streaming="true"], .streaming, .recommendation-card');
streamingElements.forEach(el => el.remove());
console.log(`✅ Removed ${streamingElements.length} streaming elements`);

// 9. CLEAR LARGE OBJECTS
console.log('🔄 Phase 9: Clearing large objects...');
for (let i = 0; i < 20; i++) {
  const largeArray = new Array(2000000).fill(null);
  largeArray.length = 0;
  
  const largeObject = {};
  for (let j = 0; j < 100000; j++) {
    largeObject[`key${j}`] = null;
  }
  Object.keys(largeObject).forEach(key => delete largeObject[key]);
}

// 10. FINAL GARBAGE COLLECTION BURST
console.log('🗑️ Phase 10: Final cleanup burst...');
for (let i = 0; i < 5; i++) {
  if (typeof gc !== 'undefined') {
    gc();
  }
}

// 11. CHECK MEMORY AFTER CLEANUP
setTimeout(() => {
  if (performance.memory) {
    const memory = performance.memory;
    const usageAfter = (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100;
    
    console.log('📊 MEMORY RELIEF RESULTS:');
    console.log('========================');
    console.log(`Memory usage after cleanup: ${usageAfter.toFixed(1)}%`);
    
    if (usageAfter < 50) {
      console.log('✅ EXCELLENT: Memory relief successful!');
      console.log('💡 Memory usage is now in safe range');
    } else if (usageAfter < 60) {
      console.log('✅ GOOD: Memory improved significantly');
      console.log('💡 Continue monitoring and avoid large operations');
    } else if (usageAfter < 70) {
      console.log('⚠️ MODERATE: Some improvement achieved');
      console.log('💡 Consider refreshing the page if memory continues to grow');
    } else {
      console.log('🚨 CRITICAL: Memory still high after cleanup');
      console.log('💡 IMMEDIATE ACTION: Refresh the page or restart browser');
      console.log('🔄 Run: window.location.reload() to force refresh');
    }
    
    // Provide recommendations
    console.log('\n🎯 RECOMMENDATIONS:');
    if (usageAfter > 60) {
      console.log('- Reduce the number of visible bookmarks');
      console.log('- Enable virtual scrolling if available');
      console.log('- Close other browser tabs');
      console.log('- Avoid generating large recommendation sets');
    }
    console.log('- Monitor memory usage regularly');
    console.log('- Use the enhanced memory monitor for ongoing protection');
    
  } else {
    console.log('ℹ️ Memory API not available - cleanup completed');
  }
}, 2000);

// 12. SHOW SUCCESS NOTIFICATION
const notification = document.createElement('div');
notification.style.cssText = `
  position: fixed;
  top: 20px;
  right: 20px;
  background: linear-gradient(135deg, #059669, #10b981);
  color: white;
  padding: 16px;
  border-radius: 8px;
  font-weight: bold;
  z-index: 10000;
  box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
  max-width: 300px;
  font-family: system-ui, -apple-system, sans-serif;
`;

notification.innerHTML = `
  ✅ Memory Relief Complete!<br>
  <small style="font-weight: normal; margin-top: 8px; display: block;">
    67.8% → Check console for results. Click to dismiss.
  </small>
`;

notification.onclick = () => notification.remove();
document.body.appendChild(notification);

setTimeout(() => {
  if (notification.parentNode) {
    notification.remove();
  }
}, 8000);

console.log('🎉 IMMEDIATE MEMORY RELIEF COMPLETED');
console.log('Check results above and monitor memory usage going forward.');
