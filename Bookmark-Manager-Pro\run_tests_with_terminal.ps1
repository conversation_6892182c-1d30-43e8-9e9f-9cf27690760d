param(
    [Parameter(Mandatory=$false)]
    [string]$TestType = "unit",
    
    [Parameter(Mandatory=$false)]
    [switch]$Parallel,
    
    [Parameter(Mandatory=$false)]
    [string[]]$TestTypes = @("unit", "integration"),
    
    [Parameter(Mandatory=$false)]
    [switch]$Verbose,
    
    [Parameter(Mandatory=$false)]
    [switch]$SessionMode,
    
    [Parameter(Mandatory=$false)]
    [string]$SessionName,
    
    [Parameter(Mandatory=$false)]
    [switch]$ListSessions,
    
    [Parameter(Mandatory=$false)]
    [string]$AttachSession,
    
    [Parameter(Mandatory=$false)]
    [string]$KillSession,
    
    [Parameter(Mandatory=$false)]
    [switch]$CleanupSessions
)

# Enhanced Terminal Management Script for Trae.ai IDE
# Provides comprehensive testing with Windows Terminal session management

# Configuration
$ConfigPath = "./terminal_rules_enhanced.json"
$SessionManagerScript = "./wt_session_manager.py"
$LogPath = "./logs/terminal_management.log"

# Ensure logs directory exists
if (!(Test-Path "./logs")) {
    New-Item -ItemType Directory -Path "./logs" -Force | Out-Null
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN" { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        default { Write-Host $logEntry }
    }
    
    # Write to log file
    Add-Content -Path $LogPath -Value $logEntry
}

function Test-Configuration {
    if (!(Test-Path $ConfigPath)) {
        Write-Log "Configuration file not found: $ConfigPath" "ERROR"
        return $false
    }
    
    if (!(Test-Path $SessionManagerScript)) {
        Write-Log "Session manager script not found: $SessionManagerScript" "ERROR"
        return $false
    }
    
    return $true
}

function Test-IDEEnvironment {
    Write-Log "Checking for IDE environment..."
    
    # Check for IDE environment variables
    $ideEnvVars = @(
        "VSCODE_PID",
        "PYCHARM_HOSTED", 
        "JUPYTER_SERVER_ROOT",
        "SPYDER_ARGS",
        "THEIA_WORKSPACE_ROOT"
    )
    
    foreach ($envVar in $ideEnvVars) {
        if (Get-ChildItem Env: | Where-Object { $_.Name -eq $envVar }) {
            Write-Log "Detected IDE environment: $envVar" "WARN"
            return $true
        }
    }
    
    # Check for IDE processes
    $ideProcesses = @("code", "pycharm64", "jupyter", "spyder")
    foreach ($process in $ideProcesses) {
        if (Get-Process -Name $process -ErrorAction SilentlyContinue) {
            Write-Log "Detected IDE process: $process" "WARN"
            return $true
        }
    }
    
    return $false
}

function Test-WindowsTerminal {
    try {
        $wtVersion = & wt.exe --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Windows Terminal detected: $wtVersion" "SUCCESS"
            return $true
        }
    } catch {
        Write-Log "Windows Terminal not found or not accessible" "ERROR"
        return $false
    }
    
    return $false
}

function Invoke-SessionManager {
    param(
        [string[]]$Arguments
    )
    
    try {
        $result = & python $SessionManagerScript @Arguments 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Session manager executed successfully"
            if ($result) {
                Write-Host $result
            }
            return $true
        } else {
            Write-Log "Session manager failed with exit code: $LASTEXITCODE" "ERROR"
            if ($result) {
                Write-Log "Error output: $result" "ERROR"
            }
            return $false
        }
    } catch {
        Write-Log "Failed to execute session manager: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Start-TestSession {
    param(
        [string]$Type,
        [string]$Name = $null
    )
    
    if (!$Name) {
        $timestamp = Get-Date -Format "HHmmss"
        $Name = "$Type-test-$timestamp"
    }
    
    Write-Log "Creating test session: $Name (Type: $Type)"
    
    $args = @(
        "--create-session", $Name,
        "--type", $Type
    )
    
    if ($Verbose) {
        $args += "--verbose"
    }
    
    return Invoke-SessionManager -Arguments $args
}

function Start-ParallelTestSessions {
    param(
        [string[]]$Types
    )
    
    Write-Log "Starting parallel test sessions for types: $($Types -join ', ')"
    
    $args = @(
        "--parallel",
        "--sessions", ($Types -join ",")
    )
    
    if ($Verbose) {
        $args += "--verbose"
    }
    
    return Invoke-SessionManager -Arguments $args
}

function Get-ActiveSessions {
    Write-Log "Retrieving active sessions..."
    return Invoke-SessionManager -Arguments @("--list-sessions")
}

function Connect-ToSession {
    param([string]$Name)
    
    Write-Log "Attaching to session: $Name"
    return Invoke-SessionManager -Arguments @("--attach", $Name)
}

function Stop-TestSession {
    param([string]$Name)
    
    Write-Log "Stopping session: $Name"
    return Invoke-SessionManager -Arguments @("--kill-session", $Name)
}

function Clear-StaleSessions {
    Write-Log "Cleaning up stale sessions..."
    return Invoke-SessionManager -Arguments @("--cleanup")
}

function Test-PythonEnvironment {
    try {
        $pythonVersion = & python --version 2>&1
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Python detected: $pythonVersion" "SUCCESS"
            
            # Check for required packages
            $requiredPackages = @("psutil")
            foreach ($package in $requiredPackages) {
                $packageCheck = & python -c "import $package; print('$package: OK')" 2>&1
                if ($LASTEXITCODE -eq 0) {
                    Write-Log "Required package available: $package" "SUCCESS"
                } else {
                    Write-Log "Missing required package: $package" "ERROR"
                    Write-Log "Install with: pip install $package" "INFO"
                    return $false
                }
            }
            return $true
        }
    } catch {
        Write-Log "Python not found or not accessible" "ERROR"
        return $false
    }
    
    return $false
}

# Main execution logic
Write-Log "=== Enhanced Terminal Management Script Started ===" "SUCCESS"

# Validate environment
if (!(Test-Configuration)) {
    Write-Log "Configuration validation failed" "ERROR"
    exit 1
}

if (!(Test-PythonEnvironment)) {
    Write-Log "Python environment validation failed" "ERROR"
    exit 1
}

if (!(Test-WindowsTerminal)) {
    Write-Log "Windows Terminal validation failed" "ERROR"
    exit 1
}

# Check for IDE environment
if (Test-IDEEnvironment) {
    Write-Log "IDE environment detected - using external Windows Terminal for testing" "WARN"
}

# Handle different operation modes
if ($ListSessions) {
    Get-ActiveSessions
    exit 0
}

if ($AttachSession) {
    Connect-ToSession -Name $AttachSession
    exit 0
}

if ($KillSession) {
    Stop-TestSession -Name $KillSession
    exit 0
}

if ($CleanupSessions) {
    Clear-StaleSessions
    exit 0
}

# Session management mode
if ($SessionMode) {
    if ($SessionName) {
        Start-TestSession -Type $TestType -Name $SessionName
    } else {
        Start-TestSession -Type $TestType
    }
    exit 0
}

# Parallel execution mode
if ($Parallel) {
    Start-ParallelTestSessions -Types $TestTypes
    exit 0
}

# Default: Single test session
Start-TestSession -Type $TestType

Write-Log "=== Enhanced Terminal Management Script Completed ===" "SUCCESS"