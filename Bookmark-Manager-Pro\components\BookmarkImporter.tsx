
import React, { ChangeEvent } from 'react';
import { ArrowPathIcon } from './icons/HeroIcons';
import SpinnerIcon from './icons/SpinnerIcon';
import FileUpload from '../src/components/FileUpload';

interface BookmarkImporterProps {
  onFileChange: (event: ChangeEvent<HTMLInputElement>) => void;
  isLoading: boolean;
  onClearImport: () => void;
  hasImportedFile: boolean;
  resetToken: number;
  onFileUpload?: (file: File) => Promise<void>;
}

const BookmarkImporter: React.FC<BookmarkImporterProps> = ({ 
  onFileChange, 
  isLoading, 
  onClearImport, 
  hasImportedFile,
  resetToken,
  onFileUpload
}) => {
  const handleFileSelect = (file: File) => {
    // Create a proper synthetic event
    const fileList = {
      0: file,
      length: 1,
      item: (index: number) => index === 0 ? file : null,
      [Symbol.iterator]: function* () { yield file; }
    } as FileList;
    
    const syntheticEvent = {
      target: { files: fileList },
      currentTarget: { files: fileList },
      nativeEvent: new Event('change'),
      bubbles: false,
      cancelable: false,
      defaultPrevented: false,
      eventPhase: 0,
      isTrusted: false,
      preventDefault: () => {},
      isDefaultPrevented: () => false,
      stopPropagation: () => {},
      isPropagationStopped: () => false,
      persist: () => {},
      timeStamp: Date.now(),
      type: 'change'
    } as ChangeEvent<HTMLInputElement>;
    onFileChange(syntheticEvent);
  };

  return (
    <div className="bookmark-importer-modern bg-gradient-to-br from-surface-50 to-surface-100 dark:from-dark-surface-secondary dark:to-dark-surface-tertiary rounded-2xl shadow-lg border border-surface-200 dark:border-dark-border-primary overflow-hidden">
      {/* Header Section with Enhanced Design */}
      <div className="relative p-8 bg-gradient-to-r from-primary-500 to-primary-600 text-white">
        <div className="absolute inset-0 bg-black/10" />
        <div className="relative">
          <div className="flex items-center space-x-3 mb-3">
            <div className="p-2 bg-white/20 rounded-xl backdrop-blur-sm">
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 7h6m0 10v-3M9 17v-3m3-2h.01" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl font-bold">
                Import Your Bookmarks
              </h2>
              <p className="text-primary-100 text-sm">
                Seamlessly migrate your bookmark collection
              </p>
            </div>
          </div>
          
          {/* Feature Highlights */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <div className="flex items-center space-x-2 text-sm text-primary-100">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Multiple formats supported</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-primary-100">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Intelligent parsing</span>
            </div>
            <div className="flex items-center space-x-2 text-sm text-primary-100">
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span>Secure processing</span>
            </div>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="p-8">
        <FileUpload
          key={resetToken}
          onFileSelect={handleFileSelect}
          onFileUpload={onFileUpload}
          acceptedTypes={['.html', '.json', '.xml']}
          maxSizeInMB={10}
          multiple={false}
          className="mb-6"
          disabled={isLoading}
        />

        {/* Success State with Enhanced Design */}
        {hasImportedFile && !isLoading && (
          <div className="mt-6 p-6 bg-gradient-to-r from-success-50 to-success-100 dark:from-success-900/20 dark:to-success-800/20 border border-success-200 dark:border-success-800 rounded-2xl">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="p-2 bg-success-500 rounded-xl">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-success-900 dark:text-success-300">
                    Import Successful! 🎉
                  </h3>
                  <p className="text-sm text-success-700 dark:text-success-400">
                    Your bookmarks have been successfully imported and are ready to use.
                  </p>
                </div>
              </div>
              <button
                onClick={onClearImport}
                className="
                  px-4 py-2 bg-success-600 hover:bg-success-700 text-white
                  rounded-xl font-medium transition-all duration-200
                  hover:scale-105 hover:shadow-lg
                  focus:outline-none focus:ring-2 focus:ring-success-500 focus:ring-offset-2
                  flex items-center space-x-2
                "
              >
                <ArrowPathIcon className="w-4 h-4" />
                <span>Import Another</span>
              </button>
            </div>
          </div>
        )}

        {/* Loading State with Enhanced Design */}
        {isLoading && (
          <div className="mt-6 p-6 bg-gradient-to-r from-primary-50 to-primary-100 dark:from-primary-900/20 dark:to-primary-800/20 border border-primary-200 dark:border-primary-800 rounded-2xl">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <SpinnerIcon className="w-8 h-8 text-primary-600" />
                <div className="absolute inset-0 rounded-full bg-primary-500/20 animate-ping" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-primary-900 dark:text-primary-300">
                  Processing Your Bookmarks...
                </h3>
                <p className="text-sm text-primary-700 dark:text-primary-400">
                  We're analyzing and importing your bookmark file. This may take a moment.
                </p>
              </div>
            </div>
            
            {/* Progress Indicator */}
            <div className="mt-4">
              <div className="w-full bg-primary-200 dark:bg-primary-800 rounded-full h-2">
                <div className="bg-gradient-to-r from-primary-500 to-primary-600 h-2 rounded-full animate-pulse w-3/5" />
              </div>
            </div>
          </div>
        )}

        {/* Help Section */}
        <div className="mt-8 p-6 bg-surface-100 dark:bg-dark-surface-quaternary rounded-2xl border border-surface-200 dark:border-dark-border-secondary">
          <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3 flex items-center space-x-2">
            <svg className="w-4 h-4 text-primary-500" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <span>Supported File Formats</span>
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
            <div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300">
              <div className="w-2 h-2 bg-primary-500 rounded-full" />
              <span><strong>HTML:</strong> Browser exports</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300">
              <div className="w-2 h-2 bg-primary-500 rounded-full" />
              <span><strong>JSON:</strong> Structured data</span>
            </div>
            <div className="flex items-center space-x-2 text-gray-700 dark:text-gray-300">
              <div className="w-2 h-2 bg-primary-500 rounded-full" />
              <span><strong>XML:</strong> OPML format</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookmarkImporter;
