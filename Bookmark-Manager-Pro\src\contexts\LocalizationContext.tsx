import React, { createContext, useContext, useEffect, useState } from 'react'

export type LocaleType = 'en-US' | 'en-GB'

interface LocalizationContextType {
  locale: LocaleType
  setLocale: (locale: LocaleType) => void
  t: (key: string) => string
}

const LocalizationContext = createContext<LocalizationContextType | undefined>(undefined)

// Localization dictionary
const translations = {
  'en-US': {
    // General UI
    'search.placeholder': 'Search bookmarks',
    'filter.all': 'All',
    'filter.favorites': 'Favorites',
    'filter.recent': 'Recent',
    'filter.visited': 'Visited',
    
    // Bookmark actions
    'bookmark.favorite': 'Favorite',
    'bookmark.favorited': 'favorited',
    'bookmark.organize': 'Organize',
    'bookmark.color': 'Color',
    'bookmark.collection': 'Collection',
    
    // ListView
    'listview.title': 'Title',
    'listview.url': 'URL',
    'listview.collection': 'Collection',
    'listview.dateAdded': 'Date Added',
    'listview.actions': 'Actions',
    'listview.sortBy': 'Sort by:',
    'listview.bookmarks': 'bookmarks',
    'listview.bookmark': 'bookmark',
    
    // Performance
    'performance.protection': 'Performance Protection',
    'performance.emergency': 'EMERGENCY MODE',
    'performance.showing': 'Showing first',
    'performance.of': 'of',
    'performance.limited': 'Limited for performance',
    
    // Import/Export
    'import.title': 'Import Bookmarks',
    'export.title': 'Export Bookmarks',
    'import.dragDrop': 'Drag & Drop',
    'import.processing': 'Processing your bookmarks...',
    'import.success': 'Import completed successfully!',
    'import.error': 'Import failed',
    'export.processing': 'Exporting bookmarks...',
    'export.success': 'Export completed successfully!',
    'export.ready': 'Ready to export your bookmarks',

    // Health Check
    'health.title': 'Health Check',
    'health.controls': 'Health Check Controls',
    'health.description': 'Scan for duplicates and broken links',
    'health.checkAll': 'Check All',
    'health.checking': 'Checking...',
    'health.results': 'Bookmark Results',
    'health.duplicates': 'Duplicates Found',
    'health.broken': 'Broken Links',

    // Organization
    'organize.title': 'Auto-Organize Bookmarks',
    'organize.hybrid': 'Hybrid Organization',
    'organize.smartAI': 'Smart AI Organization',
    'organize.domain': 'Domain Organization',
    'organize.content': 'Content Organization',
    'organize.processing': 'Organizing...',
    'organize.success': 'Organization completed!',
    'organize.preserveFolders': 'Preserve existing folders',

    // Multimedia
    'multimedia.title': 'Multimedia Playlists',
    'multimedia.create': 'Create Playlist',
    'multimedia.autoAdvance': 'Auto-advance to next video',
    'multimedia.smartAdvance': 'Smart auto-advance (5s countdown after video ends)',
    'multimedia.videoFinished': 'Video Finished',
    'multimedia.nextVideo': 'Next Video',
    'multimedia.previousVideo': 'Previous Video',

    // Summary Generator
    'summary.title': 'Generate Summaries',
    'summary.description': 'Generate AI-powered summaries for your bookmarks',
    'summary.generating': 'Generating summaries...',
    'summary.success': 'Summaries generated successfully!',

    // Themes
    'theme.classic': 'Classic',
    'theme.modern': 'Modern',
    'theme.dark': 'Dark',
    'theme.light': 'Light'
  },
  'en-GB': {
    // General UI
    'search.placeholder': 'Search bookmarks',
    'filter.all': 'All',
    'filter.favorites': 'Favourites', // British spelling
    'filter.recent': 'Recent',
    'filter.visited': 'Visited',
    
    // Bookmark actions
    'bookmark.favorite': 'Favourite', // British spelling
    'bookmark.favorited': 'favourited', // British spelling
    'bookmark.organize': 'Organise', // British spelling
    'bookmark.color': 'Colour', // British spelling
    'bookmark.collection': 'Collection',
    
    // ListView
    'listview.title': 'Title',
    'listview.url': 'URL',
    'listview.collection': 'Collection',
    'listview.dateAdded': 'Date Added',
    'listview.actions': 'Actions',
    'listview.sortBy': 'Sort by:',
    'listview.bookmarks': 'bookmarks',
    'listview.bookmark': 'bookmark',
    
    // Performance
    'performance.protection': 'Performance Protection',
    'performance.emergency': 'EMERGENCY MODE',
    'performance.showing': 'Showing first',
    'performance.of': 'of',
    'performance.limited': 'Limited for performance',
    
    // Import/Export
    'import.title': 'Import Bookmarks',
    'export.title': 'Export Bookmarks',
    'import.dragDrop': 'Drag & Drop',
    'import.processing': 'Processing your bookmarks...',
    'import.success': 'Import completed successfully!',
    'import.error': 'Import failed',
    'export.processing': 'Exporting bookmarks...',
    'export.success': 'Export completed successfully!',
    'export.ready': 'Ready to export your bookmarks',

    // Health Check
    'health.title': 'Health Check',
    'health.controls': 'Health Check Controls',
    'health.description': 'Scan for duplicates and broken links',
    'health.checkAll': 'Check All',
    'health.checking': 'Checking...',
    'health.results': 'Bookmark Results',
    'health.duplicates': 'Duplicates Found',
    'health.broken': 'Broken Links',

    // Organization (British spellings)
    'organize.title': 'Auto-Organise Bookmarks', // British spelling
    'organize.hybrid': 'Hybrid Organisation', // British spelling
    'organize.smartAI': 'Smart AI Organisation', // British spelling
    'organize.domain': 'Domain Organisation', // British spelling
    'organize.content': 'Content Organisation', // British spelling
    'organize.processing': 'Organising...', // British spelling
    'organize.success': 'Organisation completed!', // British spelling
    'organize.preserveFolders': 'Preserve existing folders',

    // Multimedia
    'multimedia.title': 'Multimedia Playlists',
    'multimedia.create': 'Create Playlist',
    'multimedia.autoAdvance': 'Auto-advance to next video',
    'multimedia.smartAdvance': 'Smart auto-advance (5s countdown after video ends)',
    'multimedia.videoFinished': 'Video Finished',
    'multimedia.nextVideo': 'Next Video',
    'multimedia.previousVideo': 'Previous Video',

    // Summary Generator
    'summary.title': 'Generate Summaries',
    'summary.description': 'Generate AI-powered summaries for your bookmarks',
    'summary.generating': 'Generating summaries...',
    'summary.success': 'Summaries generated successfully!',

    // Themes
    'theme.classic': 'Classic',
    'theme.modern': 'Modern',
    'theme.dark': 'Dark',
    'theme.light': 'Light'
  }
}

export const LocalizationProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [locale, setLocaleState] = useState<LocaleType>('en-US') // Default to American English

  console.log('🌍 LocalizationProvider initialized with locale:', locale)

  // Load saved locale from localStorage
  useEffect(() => {
    const savedLocale = localStorage.getItem('bookmark-manager-locale') as LocaleType
    if (savedLocale && (savedLocale === 'en-US' || savedLocale === 'en-GB')) {
      setLocaleState(savedLocale)
    }
  }, [])

  // Save locale to localStorage when changed
  const setLocale = (newLocale: LocaleType) => {
    console.log(`🌍 LocalizationContext: Setting locale from ${locale} to ${newLocale}`)
    setLocaleState(newLocale)
    localStorage.setItem('bookmark-manager-locale', newLocale)
    console.log(`🌍 Locale changed to: ${newLocale}`)
  }

  // Translation function
  const t = (key: string): string => {
    const translation = (translations[locale] as any)?.[key] || key
    console.log(`🌍 Translation: ${key} (${locale}) -> ${translation}`)
    return translation
  }

  return (
    <LocalizationContext.Provider value={{ locale, setLocale, t }}>
      {children}
    </LocalizationContext.Provider>
  )
}

export const useLocalization = () => {
  const context = useContext(LocalizationContext)
  if (context === undefined) {
    throw new Error('useLocalization must be used within a LocalizationProvider')
  }
  return context
}
