import React from 'react';

export type DensityMode = 'compact' | 'comfortable' | 'spacious';

interface DensityControlProps {
  currentDensity: DensityMode;
  onDensityChange: (density: DensityMode) => void;
  className?: string;
}

const DensityControl: React.FC<DensityControlProps> = ({
  currentDensity,
  onDensityChange,
  className = ''
}) => {
  const densityOptions: Array<{
    value: DensityMode;
    label: string;
    icon: string;
    description: string;
  }> = [
    {
      value: 'compact',
      label: 'Compact',
      icon: '▦',
      description: 'More bookmarks per screen'
    },
    {
      value: 'comfortable',
      label: 'Comfortable',
      icon: '▣',
      description: 'Balanced view'
    },
    {
      value: 'spacious',
      label: 'Spacious',
      icon: '▢',
      description: 'More breathing room'
    }
  ];

  return (
    <div className={`density-control ${className}`}>
      <div className="density-control-label">
        <span className="text-sm font-medium text-foreground">View Density</span>
      </div>
      <div className="density-control-options">
        {densityOptions.map((option) => (
          <button
            key={option.value}
            onClick={() => onDensityChange(option.value)}
            className={`density-option ${
              currentDensity === option.value ? 'active' : ''
            }`}
            title={option.description}
          >
            <span className="density-option-icon">{option.icon}</span>
            <span className="density-option-label">{option.label}</span>
          </button>
        ))}
      </div>
    </div>
  );
};

export default DensityControl;