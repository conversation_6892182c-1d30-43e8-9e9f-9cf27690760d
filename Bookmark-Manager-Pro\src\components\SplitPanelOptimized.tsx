import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface SplitPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const SplitPanelOptimized: React.FC<SplitPanelProps> = ({ isOpen, onClose, onOpenPanel }) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [splitMethod, setSplitMethod] = useState('folder')
  const [isSplitting, setIsSplitting] = useState(false)
  const [splitProgress, setSplitProgress] = useState(0)
  const [splitResults, setSplitResults] = useState<any>(null)
  const [advancedOptionsExpanded, setAdvancedOptionsExpanded] = useState(false)
  const [previewExpanded, setPreviewExpanded] = useState(false)

  const startSplit = () => {
    setIsSplitting(true)
    setSplitProgress(0)
    // Split logic here
    console.log('Starting split...')
  }

  const previewSplit = () => {
    console.log('Previewing split...')
    setPreviewExpanded(true)
  }

  return (
    <div className="import-panel">
      {/* Quick Split */}
      <div className="import-section">
        <h3 className="section-title">{t('split.quickSplit')}</h3>
        <p className="section-description">{t('split.quickSplitDescription')}</p>
        
        <div className="form-row">
          <label>{t('split.method')}:</label>
          <select 
            value={splitMethod} 
            onChange={(e) => setSplitMethod(e.target.value)}
            className="input-compact"
          >
            <option value="folder">{t('split.byFolder')}</option>
            <option value="domain">{t('split.byDomain')}</option>
            <option value="date">{t('split.byDate')}</option>
            <option value="tags">{t('split.byTags')}</option>
            <option value="size">{t('split.bySize')}</option>
          </select>
        </div>

        <div className="controls-grid">
          <button 
            className="btn-compact primary" 
            onClick={startSplit}
            disabled={isSplitting}
          >
            {isSplitting ? t('split.splitting') : t('split.startSplit')}
          </button>
          <button className="btn-compact" onClick={previewSplit}>
            {t('split.preview')}
          </button>
        </div>

        {isSplitting && (
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${splitProgress}%` }} />
            <span className="progress-text">{splitProgress}%</span>
          </div>
        )}
      </div>

      {/* Split Statistics */}
      <div className="import-section">
        <h3 className="section-title">{t('split.statistics')}</h3>
        
        <div className="controls-grid">
          <div className="status-compact">
            <div className="status-icon info" />
            <span>{bookmarks.length} {t('split.totalBookmarks')}</span>
          </div>
          <div className="status-compact">
            <div className="status-icon success" />
            <span>{splitResults?.groups || 0} {t('split.groups')}</span>
          </div>
        </div>
      </div>

      {/* Quick Split Options */}
      <div className="import-section">
        <h3 className="section-title">{t('split.quickOptions')}</h3>
        
        <div className="button-grid">
          <button className="btn-compact">{t('split.byDomain')}</button>
          <button className="btn-compact">{t('split.byFolder')}</button>
          <button className="btn-compact">{t('split.byDate')}</button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact">{t('split.bySize')}</button>
          <button className="btn-compact">{t('split.byTags')}</button>
        </div>
      </div>

      {/* Split Configuration */}
      <div className="import-section">
        <h3 className="section-title">{t('split.configuration')}</h3>
        
        <div className="form-row">
          <label>{t('split.maxPerGroup')}:</label>
          <input type="number" defaultValue="100" className="input-compact" />
        </div>
        
        <div className="form-row">
          <label>{t('split.outputFormat')}:</label>
          <select className="input-compact">
            <option value="folders">{t('split.separateFolders')}</option>
            <option value="files">{t('split.separateFiles')}</option>
            <option value="both">{t('split.both')}</option>
          </select>
        </div>
        
        <div className="form-row">
          <label>
            <input type="checkbox" defaultChecked />
            {t('split.preserveStructure')}
          </label>
        </div>
      </div>

      {/* Advanced Options - Collapsible */}
      <div className={`collapsible-section ${advancedOptionsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setAdvancedOptionsExpanded(!advancedOptionsExpanded)}
        >
          <span>{t('split.advancedOptions')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>{t('split.customCriteria')}:</label>
            <textarea 
              placeholder={t('split.customCriteriaPlaceholder')} 
              className="input-compact"
              rows={3}
            />
          </div>
          <div className="form-row">
            <label>{t('split.excludePatterns')}:</label>
            <input 
              type="text" 
              placeholder={t('split.excludePatternsPlaceholder')} 
              className="input-compact" 
            />
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('split.includeMetadata')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('split.createIndex')}
            </label>
          </div>
          <div className="form-row">
            <label>{t('split.namingPattern')}:</label>
            <input 
              type="text" 
              defaultValue="{method}_{index}" 
              className="input-compact" 
            />
          </div>
        </div>
      </div>

      {/* Split Preview - Collapsible */}
      <div className={`collapsible-section ${previewExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setPreviewExpanded(!previewExpanded)}
        >
          <span>{t('split.preview')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="scrollable-content">
            <div className="status-compact">
              <div className="status-icon info" />
              <span>{t('split.group')} 1: google.com (45 {t('split.bookmarks')})</span>
            </div>
            <div className="status-compact">
              <div className="status-icon info" />
              <span>{t('split.group')} 2: github.com (32 {t('split.bookmarks')})</span>
            </div>
            <div className="status-compact">
              <div className="status-icon info" />
              <span>{t('split.group')} 3: stackoverflow.com (28 {t('split.bookmarks')})</span>
            </div>
            <div className="status-compact">
              <div className="status-icon info" />
              <span>{t('split.group')} 4: {t('split.others')} (15 {t('split.bookmarks')})</span>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('split.quickActions')}</h3>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('export')}>
            {t('split.exportGroups')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('health')}>
            {t('split.healthCheck')}
          </button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('domain')}>
            {t('split.domainAnalysis')}
          </button>
          <button className="btn-compact">{t('split.undoSplit')}</button>
        </div>
      </div>

      {/* Navigation */}
      <div className="import-section">
        <h3 className="section-title">{t('split.relatedTools')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('import')}>
            {t('split.importBookmarks')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('playlist')}>
            {t('split.createPlaylist')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('summary')}>
            {t('split.generateSummary')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  )
}