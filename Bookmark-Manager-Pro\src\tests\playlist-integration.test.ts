/**
 * PLAYLIST INTEGRATION TESTS
 * End-to-end testing of playlist functionality
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { smartPlaylistService } from '../services/smartPlaylistService'
import type { Bookmark, Playlist } from '../../types'

// Extended mock data for integration testing
const createMockBookmarks = (count: number): Bookmark[] => {
  const domains = ['github.com', 'stackoverflow.com', 'medium.com', 'dev.to', 'css-tricks.com']
  const collections = ['Development', 'Design', 'Learning', 'Tools', 'Inspiration']
  const tags = ['javascript', 'react', 'css', 'html', 'typescript', 'node', 'frontend', 'backend']
  
  return Array.from({ length: count }, (_, i) => ({
    id: `bookmark-${i + 1}`,
    title: `Bookmark ${i + 1}`,
    url: `https://${domains[i % domains.length]}/article-${i + 1}`,
    description: `Description for bookmark ${i + 1}`,
    tags: [tags[i % tags.length], tags[(i + 1) % tags.length]],
    collection: collections[i % collections.length],
    dateAdded: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
    favicon: `https://${domains[i % domains.length]}/favicon.ico`,
    isPrivate: i % 5 === 0 // Every 5th bookmark is private
  }))
}

const createMockPlaylists = (count: number): Playlist[] => {
  const colors = ['#3b82f6', '#ec4899', '#10b981', '#f59e0b', '#8b5cf6']
  
  return Array.from({ length: count }, (_, i) => ({
    id: `playlist-${i + 1}`,
    name: `Playlist ${i + 1}`,
    description: `Description for playlist ${i + 1}`,
    color: colors[i % colors.length],
    bookmarkIds: [`bookmark-${i + 1}`, `bookmark-${i + 2}`],
    dateCreated: new Date(Date.now() - (i * 24 * 60 * 60 * 1000)).toISOString(),
    dateUpdated: new Date(Date.now() - (i * 12 * 60 * 60 * 1000)).toISOString()
  }))
}

describe('Playlist Integration Tests', () => {
  let mockBookmarks: Bookmark[]
  let mockPlaylists: Playlist[]

  beforeEach(() => {
    mockBookmarks = createMockBookmarks(50)
    mockPlaylists = createMockPlaylists(5)
  })

  describe('Smart Playlist Generation', () => {
    it('should generate content-based suggestions', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { 
          maxSuggestions: 10,
          minConfidence: 0.6,
          includeTemporalAnalysis: false,
          includeBehavioralAnalysis: false,
          includeSemanticAnalysis: true
        }
      )

      const contentBasedSuggestions = suggestions.filter(s => s.category === 'content-based')
      expect(contentBasedSuggestions.length).toBeGreaterThan(0)
      
      contentBasedSuggestions.forEach(suggestion => {
        expect(suggestion.bookmarkIds.length).toBeGreaterThan(0)
        expect(suggestion.reasoning).toContain('content')
      })
    })

    it('should generate temporal-based suggestions', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { 
          maxSuggestions: 10,
          minConfidence: 0.5,
          includeTemporalAnalysis: true,
          includeBehavioralAnalysis: false,
          includeSemanticAnalysis: false
        }
      )

      const temporalSuggestions = suggestions.filter(s => s.category === 'temporal')
      expect(temporalSuggestions.length).toBeGreaterThan(0)
      
      temporalSuggestions.forEach(suggestion => {
        expect(suggestion.reasoning).toMatch(/recent|time|date|period/i)
      })
    })

    it('should handle large datasets efficiently', async () => {
      const largeBookmarkSet = createMockBookmarks(1000)
      const largePlaylists = createMockPlaylists(20)
      
      const startTime = Date.now()
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        largeBookmarkSet,
        largePlaylists,
        { maxSuggestions: 5 }
      )
      const endTime = Date.now()
      
      // Should complete within reasonable time (5 seconds)
      expect(endTime - startTime).toBeLessThan(5000)
      expect(suggestions.length).toBeLessThanOrEqual(5)
    })

    it('should avoid duplicate suggestions', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { maxSuggestions: 20 }
      )

      const suggestionNames = suggestions.map(s => s.name)
      const uniqueNames = [...new Set(suggestionNames)]
      
      expect(suggestionNames.length).toBe(uniqueNames.length)
    })

    it('should respect existing playlists', async () => {
      const suggestions = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists
      )

      const existingPlaylistNames = mockPlaylists.map(p => p.name.toLowerCase())
      
      suggestions.forEach(suggestion => {
        expect(existingPlaylistNames).not.toContain(suggestion.name.toLowerCase())
      })
    })
  })

  describe('Playlist Analytics', () => {
    it('should provide comprehensive analytics', async () => {
      const playlist = mockPlaylists[0]
      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        mockBookmarks
      )

      // Verify all analytics properties
      expect(analytics.totalBookmarks).toBeGreaterThan(0)
      expect(Object.keys(analytics.contentTypes).length).toBeGreaterThan(0)
      expect(Object.keys(analytics.domains).length).toBeGreaterThan(0)
      expect(Object.keys(analytics.tags).length).toBeGreaterThan(0)
      expect(analytics.temporalPatterns.length).toBeGreaterThan(0)
      expect(analytics.topDomains.length).toBeGreaterThan(0)
      expect(Array.isArray(analytics.recommendations)).toBe(true)
    })

    it('should calculate engagement scores correctly', async () => {
      const playlist = mockPlaylists[0]
      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        mockBookmarks
      )

      expect(analytics.engagementScore).toBeGreaterThanOrEqual(0)
      expect(analytics.engagementScore).toBeLessThanOrEqual(1)
    })

    it('should detect duplicates and broken links', async () => {
      // Create bookmarks with duplicates
      const duplicateBookmarks = [
        ...mockBookmarks,
        {
          ...mockBookmarks[0],
          id: 'duplicate-1',
          title: 'Duplicate Bookmark'
        }
      ]

      const playlist = {
        ...mockPlaylists[0],
        bookmarkIds: [mockBookmarks[0].id, 'duplicate-1']
      }

      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        duplicateBookmarks
      )

      expect(analytics.duplicateCount).toBeGreaterThan(0)
    })

    it('should provide actionable recommendations', async () => {
      const playlist = mockPlaylists[0]
      const analytics = await smartPlaylistService.generatePlaylistAnalytics(
        playlist,
        mockBookmarks
      )

      expect(analytics.recommendations.length).toBeGreaterThan(0)
      
      analytics.recommendations.forEach(recommendation => {
        expect(typeof recommendation).toBe('string')
        expect(recommendation.length).toBeGreaterThan(10)
      })
    })
  })

  describe('Auto-Creation Features', () => {
    it('should auto-create high-confidence playlists', async () => {
      const autoPlaylists = await smartPlaylistService.autoCreatePlaylists(
        mockBookmarks,
        mockPlaylists,
        { autoCreateThreshold: 0.9 }
      )

      autoPlaylists.forEach(playlist => {
        expect(playlist.confidence).toBeGreaterThanOrEqual(0.9)
        expect(playlist.bookmarkIds.length).toBeGreaterThan(0)
        expect(playlist.name).toBeTruthy()
        expect(playlist.description).toBeTruthy()
      })
    })

    it('should handle edge cases gracefully', async () => {
      // Test with empty bookmarks
      const emptyResult = await smartPlaylistService.generateSmartSuggestions([], [])
      expect(emptyResult).toEqual([])

      // Test with single bookmark
      const singleBookmark = [mockBookmarks[0]]
      const singleResult = await smartPlaylistService.generateSmartSuggestions(
        singleBookmark,
        []
      )
      expect(Array.isArray(singleResult)).toBe(true)
    })

    it('should maintain consistency across multiple calls', async () => {
      const result1 = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { maxSuggestions: 5, minConfidence: 0.8 }
      )

      const result2 = await smartPlaylistService.generateSmartSuggestions(
        mockBookmarks,
        mockPlaylists,
        { maxSuggestions: 5, minConfidence: 0.8 }
      )

      // Results should be consistent (same input = same output)
      expect(result1.length).toBe(result2.length)
      
      // At least some suggestions should be the same
      const names1 = result1.map(r => r.name).sort()
      const names2 = result2.map(r => r.name).sort()
      
      const intersection = names1.filter(name => names2.includes(name))
      expect(intersection.length).toBeGreaterThan(0)
    })
  })

  describe('Performance and Scalability', () => {
    it('should handle memory efficiently with large datasets', async () => {
      const largeDataset = createMockBookmarks(2000)
      const largePlaylists = createMockPlaylists(50)

      // Monitor memory usage (if available)
      const initialMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      await smartPlaylistService.generateSmartSuggestions(
        largeDataset,
        largePlaylists,
        { maxSuggestions: 10 }
      )

      const finalMemory = (performance as any).memory?.usedJSHeapSize || 0
      
      // Memory increase should be reasonable (less than 50MB)
      if (initialMemory && finalMemory) {
        const memoryIncrease = finalMemory - initialMemory
        expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024) // 50MB
      }
    })

    it('should process suggestions in reasonable time', async () => {
      const mediumDataset = createMockBookmarks(500)
      const mediumPlaylists = createMockPlaylists(10)

      const startTime = performance.now()
      
      await smartPlaylistService.generateSmartSuggestions(
        mediumDataset,
        mediumPlaylists,
        { maxSuggestions: 10 }
      )

      const endTime = performance.now()
      const processingTime = endTime - startTime

      // Should complete within 2 seconds for medium dataset
      expect(processingTime).toBeLessThan(2000)
    })
  })
})
