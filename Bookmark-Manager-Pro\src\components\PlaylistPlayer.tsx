/**
 * PLAYLIST PLAYER COMPONENT
 * Dedicated player for multimedia playlists with intuitive controls
 * Supports video, audio, documents with TTS, and web content
 */

import React, { useState, useRef, useEffect } from 'react'
import {
  Play, Pause, SkipForward, SkipBack, Volume2, VolumeX, Settings,
  Shuffle, Repeat, RotateCcw, Mic, MicOff, ExternalLink,
  Clock, List, X, Maximize2, Minimize2
} from 'lucide-react'
import { useModernTheme } from '../contexts/ModernThemeContext'
import type { Bookmark } from '../types'

interface PlaylistItem {
  id: string
  title: string
  url: string
  type: 'video' | 'audio' | 'document' | 'web'
  duration?: number
  thumbnail?: string
}

interface PlaylistPlayerProps {
  playlist: {
    id: string
    name: string
    items: PlaylistItem[]
  }
  isOpen: boolean
  onClose: () => void
  autoStart?: boolean
  gymMode?: boolean
}

export const PlaylistPlayer: React.FC<PlaylistPlayerProps> = ({
  playlist,
  isOpen,
  onClose,
  autoStart = false,
  gymMode = false
}) => {
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // Player state
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [volume, setVolume] = useState(80)
  const [isMuted, setIsMuted] = useState(false)
  const [shuffle, setShuffle] = useState(false)
  const [repeat, setRepeat] = useState<'none' | 'one' | 'all'>('none')
  const [ttsEnabled, setTtsEnabled] = useState(gymMode)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [progress, setProgress] = useState(0)
  const [duration, setDuration] = useState(0)

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const currentItem = playlist.items[currentIndex]

  // Auto-start if requested
  useEffect(() => {
    if (autoStart && currentItem) {
      playItem(currentItem)
    }
  }, [autoStart, currentItem])

  const playItem = async (item: PlaylistItem) => {
    setIsPlaying(true)

    try {
      switch (item.type) {
        case 'video':
          await playVideo(item)
          break
        case 'audio':
          await playAudio(item)
          break
        case 'document':
        case 'web':
          if (ttsEnabled) {
            await playTextToSpeech(item)
          } else {
            window.open(item.url, '_blank')
            setIsPlaying(false)
          }
          break
      }
    } catch (error) {
      console.error('Failed to play item:', error)
      setIsPlaying(false)
    }
  }

  const playVideo = async (item: PlaylistItem) => {
    if (videoRef.current) {
      videoRef.current.src = item.url
      videoRef.current.volume = isMuted ? 0 : volume / 100
      await videoRef.current.play()
    }
  }

  const playAudio = async (item: PlaylistItem) => {
    if (audioRef.current) {
      audioRef.current.src = item.url
      audioRef.current.volume = isMuted ? 0 : volume / 100
      await audioRef.current.play()
    }
  }

  const playTextToSpeech = async (item: PlaylistItem) => {
    if (!('speechSynthesis' in window)) {
      alert('Text-to-speech not supported in this browser')
      return
    }

    const text = `Now playing: ${item.title}. Opening ${item.url}`
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.volume = isMuted ? 0 : volume / 100
    utterance.onend = () => {
      window.open(item.url, '_blank')
      if (repeat !== 'one') {
        setTimeout(() => playNext(), 2000) // Auto-advance after 2 seconds
      }
    }
    speechSynthesis.speak(utterance)
  }

  const togglePlayPause = () => {
    if (isPlaying) {
      pause()
    } else {
      if (currentItem) {
        playItem(currentItem)
      }
    }
  }

  const pause = () => {
    setIsPlaying(false)
    if (videoRef.current) videoRef.current.pause()
    if (audioRef.current) audioRef.current.pause()
    speechSynthesis.cancel()
  }

  const playNext = () => {
    let nextIndex = currentIndex + 1

    if (shuffle) {
      nextIndex = Math.floor(Math.random() * playlist.items.length)
    } else if (nextIndex >= playlist.items.length) {
      if (repeat === 'all') {
        nextIndex = 0
      } else {
        setIsPlaying(false)
        return
      }
    }

    setCurrentIndex(nextIndex)
    if (isPlaying) {
      playItem(playlist.items[nextIndex])
    }
  }

  const playPrevious = () => {
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : playlist.items.length - 1
    setCurrentIndex(prevIndex)
    if (isPlaying) {
      playItem(playlist.items[prevIndex])
    }
  }

  const toggleMute = () => {
    setIsMuted(!isMuted)
    if (videoRef.current) videoRef.current.volume = isMuted ? volume / 100 : 0
    if (audioRef.current) audioRef.current.volume = isMuted ? volume / 100 : 0
  }

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume)
    if (!isMuted) {
      if (videoRef.current) videoRef.current.volume = newVolume / 100
      if (audioRef.current) audioRef.current.volume = newVolume / 100
    }
  }

  const getItemIcon = (type: string) => {
    switch (type) {
      case 'video': return '🎥'
      case 'audio': return '🎵'
      case 'document': return '📄'
      default: return '🌐'
    }
  }

  if (!isOpen) return null

  return (
    <div className={`
      fixed inset-0 z-50 flex items-center justify-center
      ${isModernTheme ? 'bg-black/80' : 'bg-black/50'}
      backdrop-blur-sm
    `}>
      <div className={`
        ${isFullscreen ? 'w-full h-full' : 'w-11/12 max-w-4xl h-5/6'}
        ${isModernTheme ? 'bg-black/90 border-white/20' : 'bg-white border-gray-200'}
        border rounded-xl shadow-2xl flex flex-col overflow-hidden
      `}>
        
        {/* Header */}
        <div className={`
          flex items-center justify-between p-4 border-b
          ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
        `}>
          <div>
            <h2 className={`text-lg font-semibold ${
              isModernTheme ? 'text-white' : 'text-gray-900'
            }`}>
              🎬 {playlist.name}
            </h2>
            <p className={`text-sm ${
              isModernTheme ? 'text-gray-400' : 'text-gray-600'
            }`}>
              {currentIndex + 1} of {playlist.items.length} • {currentItem?.title}
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setIsFullscreen(!isFullscreen)}
              className={`p-2 rounded transition-colors ${
                isModernTheme
                  ? 'text-white hover:bg-white/10'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
            >
              {isFullscreen ? <Minimize2 size={20} /> : <Maximize2 size={20} />}
            </button>
            
            <button
              onClick={onClose}
              className={`p-2 rounded transition-colors ${
                isModernTheme
                  ? 'text-white hover:bg-white/10'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
              title="Close player"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-1 overflow-hidden">
          
          {/* Playlist Sidebar */}
          <div className={`
            w-1/3 border-r overflow-y-auto
            ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
          `}>
            <div className="p-4">
              <h3 className={`font-semibold mb-3 ${
                isModernTheme ? 'text-white' : 'text-gray-900'
              }`}>
                Playlist Items
              </h3>
              
              <div className="space-y-2">
                {playlist.items.map((item, index) => (
                  <div
                    key={item.id}
                    onClick={() => {
                      setCurrentIndex(index)
                      if (isPlaying) playItem(item)
                    }}
                    className={`
                      p-3 rounded-lg cursor-pointer transition-all
                      ${index === currentIndex
                        ? isModernTheme
                          ? 'border-white/20'
                          : 'border-gray-300'
                        : isModernTheme
                          ? 'hover:bg-white/10 border-transparent'
                          : 'hover:bg-gray-100 border-transparent'
                      }
                      border
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{getItemIcon(item.type)}</span>
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium text-sm truncate ${
                          isModernTheme ? 'text-white' : 'text-gray-900'
                        }`}>
                          {item.title}
                        </div>
                        <div className={`text-xs truncate ${
                          isModernTheme ? 'text-gray-400' : 'text-gray-500'
                        }`}>
                          {new URL(item.url).hostname}
                        </div>
                      </div>
                      <div className={`text-xs ${
                        isModernTheme ? 'text-gray-400' : 'text-gray-500'
                      }`}>
                        {index + 1}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Player Area */}
          <div className="flex-1 flex flex-col">
            
            {/* Media Display */}
            <div className="flex-1 flex items-center justify-center p-8">
              {currentItem ? (
                <div className="w-full max-w-2xl text-center">
                  
                  {currentItem.type === 'video' && (
                    <video
                      ref={videoRef}
                      className="w-full rounded-lg shadow-lg mb-4"
                      controls={!gymMode}
                      poster={currentItem.thumbnail}
                      onEnded={playNext}
                    />
                  )}
                  
                  {currentItem.type === 'audio' && (
                    <div className="mb-4">
                      <div className={`
                        w-32 h-32 mx-auto mb-4 rounded-full flex items-center justify-center text-4xl
                        ${isModernTheme ? 'bg-white/10' : 'bg-gray-100'}
                      `}>
                        🎵
                      </div>
                      <audio
                        ref={audioRef}
                        className="w-full"
                        controls
                        onEnded={playNext}
                      />
                    </div>
                  )}
                  
                  {(currentItem.type === 'document' || currentItem.type === 'web') && (
                    <div className="text-center">
                      <div className={`
                        w-32 h-32 mx-auto mb-4 rounded-lg flex items-center justify-center text-4xl
                        ${isModernTheme ? 'bg-white/10' : 'bg-gray-100'}
                      `}>
                        {getItemIcon(currentItem.type)}
                      </div>
                      <p className={`text-sm mb-4 ${
                        isModernTheme ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {ttsEnabled ? 'Text-to-speech enabled' : 'Click play to open in new tab'}
                      </p>
                    </div>
                  )}
                  
                  <h3 className={`text-xl font-semibold mb-2 ${
                    isModernTheme ? 'text-white' : 'text-gray-900'
                  }`}>
                    {currentItem.title}
                  </h3>
                  
                  <p className={`text-sm ${
                    isModernTheme ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {new URL(currentItem.url).hostname}
                  </p>
                </div>
              ) : (
                <div className="text-center">
                  <div className={`text-6xl mb-4 ${
                    isModernTheme ? 'text-white/20' : 'text-gray-300'
                  }`}>
                    🎬
                  </div>
                  <p className={`${
                    isModernTheme ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Select an item to start playing
                  </p>
                </div>
              )}
            </div>

            {/* Controls */}
            <div className={`
              p-6 border-t
              ${isModernTheme ? 'border-white/20 bg-white/5' : 'border-gray-200 bg-gray-50'}
            `}>
              
              {/* Main Controls */}
              <div className="flex items-center justify-center gap-4 mb-4">
                <button
                  onClick={playPrevious}
                  className={`p-3 rounded-full transition-colors ${
                    isModernTheme
                      ? 'text-white hover:bg-white/10'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  title="Previous"
                >
                  <SkipBack size={24} />
                </button>
                
                <button
                  onClick={togglePlayPause}
                  className={`
                    p-4 rounded-full transition-colors
                    ${isPlaying
                      ? 'bg-red-500 text-white hover:bg-red-600'
                      : 'text-white hover:opacity-80'
                    }
                  `}
                  title={isPlaying ? 'Pause' : 'Play'}
                >
                  {isPlaying ? <Pause size={28} /> : <Play size={28} />}
                </button>
                
                <button
                  onClick={playNext}
                  className={`p-3 rounded-full transition-colors ${
                    isModernTheme
                      ? 'text-white hover:bg-white/10'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  title="Next"
                >
                  <SkipForward size={24} />
                </button>
              </div>

              {/* Secondary Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setShuffle(!shuffle)}
                    className={`p-2 rounded transition-colors ${
                      shuffle
                        ? 'text-white'
                        : isModernTheme
                          ? 'text-white hover:bg-white/10'
                          : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Shuffle"
                  >
                    <Shuffle size={16} />
                  </button>
                  
                  <button
                    onClick={() => setRepeat(repeat === 'none' ? 'all' : repeat === 'all' ? 'one' : 'none')}
                    className={`p-2 rounded transition-colors ${
                      repeat !== 'none'
                        ? 'text-white'
                        : isModernTheme
                          ? 'text-white hover:bg-white/10'
                          : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title={`Repeat: ${repeat}`}
                  >
                    <Repeat size={16} />
                  </button>
                  
                  <button
                    onClick={() => setTtsEnabled(!ttsEnabled)}
                    className={`p-2 rounded transition-colors ${
                      ttsEnabled
                        ? 'bg-green-500 text-white'
                        : isModernTheme
                          ? 'text-white hover:bg-white/10'
                          : 'text-gray-600 hover:bg-gray-100'
                    }`}
                    title="Text-to-Speech"
                  >
                    {ttsEnabled ? <Mic size={16} /> : <MicOff size={16} />}
                  </button>
                </div>

                {/* Volume Control */}
                <div className="flex items-center gap-2">
                  <button
                    onClick={toggleMute}
                    className={`p-2 rounded transition-colors ${
                      isModernTheme
                        ? 'text-white hover:bg-white/10'
                        : 'text-gray-600 hover:bg-gray-100'
                    }`}
                  >
                    {isMuted ? <VolumeX size={16} /> : <Volume2 size={16} />}
                  </button>
                  
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={volume}
                    onChange={(e) => handleVolumeChange(Number(e.target.value))}
                    className="w-20"
                  />
                  
                  <span className={`text-xs w-8 ${
                    isModernTheme ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {volume}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default PlaylistPlayer
