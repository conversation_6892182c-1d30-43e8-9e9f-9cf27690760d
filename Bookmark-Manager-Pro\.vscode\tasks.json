{"version": "2.0.0", "tasks": [{"label": "Vite: Development Server", "type": "shell", "command": "npm", "args": ["run", "dev"], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": [], "isBackground": true}, {"label": "Vite: Build Production", "type": "shell", "command": "npm", "args": ["run", "build"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "ESLint: Check Code Quality", "type": "shell", "command": "npm", "args": ["run", "lint"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Vite: Build Check", "type": "shell", "command": "npm", "args": ["run", "build:check"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Test: Run All", "type": "shell", "command": "npm", "args": ["run", "test"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Test: Coverage", "type": "shell", "command": "npm", "args": ["run", "test:coverage"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}]}