# Test Strategy - Bookmark Manager Pro

## Overview

This document outlines the comprehensive testing strategy for Bookmark Manager Pro, covering all aspects of quality assurance from unit tests to end-to-end testing scenarios.

⚠️ **TESTING STRATEGY UPDATE** - Based on architecture refactoring, this strategy has been updated to address:
- **State Management Testing**: New context providers and custom hooks
- **Component Architecture**: Refactored components and reusable patterns
- **Performance Testing**: Large state management optimization
- **Integration Testing**: Context provider interactions

## Table of Contents

- [Testing Philosophy](#testing-philosophy)
- [Test Pyramid](#test-pyramid)
- [Testing Tools & Framework](#testing-tools--framework)
- [Unit Testing Strategy](#unit-testing-strategy)
- [Integration Testing Strategy](#integration-testing-strategy)
- [End-to-End Testing Strategy](#end-to-end-testing-strategy)
- [Performance Testing](#performance-testing)
- [Accessibility Testing](#accessibility-testing)
- [Security Testing](#security-testing)
- [Test Data Management](#test-data-management)
- [CI/CD Integration](#cicd-integration)
- [Test Coverage Goals](#test-coverage-goals)
- [Testing Checklist](#testing-checklist)

## Testing Philosophy

### Core Principles

1. **Test-Driven Development (TDD)**: Write tests before implementation
2. **Behavior-Driven Development (BDD)**: Focus on user behavior and requirements
3. **Shift-Left Testing**: Catch issues early in the development cycle
4. **Risk-Based Testing**: Prioritize testing based on business impact
5. **Continuous Testing**: Integrate testing into CI/CD pipeline

### Quality Gates

- **Code Coverage**: Minimum 80% for critical paths (85% for refactored components)
- **Performance**: Core Web Vitals within acceptable ranges
- **State Management**: 100% coverage for custom hooks and context providers
- **Component Reusability**: 90% coverage for base components
- **Accessibility**: WCAG 2.1 AA compliance
- **Security**: No high/critical vulnerabilities
- **Cross-Browser**: Support for modern browsers
- **Refactoring Safety**: All existing functionality preserved during migration

## Test Pyramid

```
        ┌─────────────────┐
        │   E2E Tests     │  ← 10% (Slow, Expensive)
        │   (Cypress)     │
        └─────────────────┘
      ┌───────────────────────┐
      │  Integration Tests    │  ← 20% (Medium Speed)
      │ (React Testing Lib)   │
      └───────────────────────┘
    ┌─────────────────────────────┐
    │      Unit Tests             │  ← 70% (Fast, Cheap)
    │   (Jest + Testing Lib)      │
    └─────────────────────────────┘
```

### Test Distribution

- **70% Unit Tests**: Fast, isolated, focused on individual functions/components
  - **Custom Hooks**: 15% - State management logic
  - **Components**: 35% - UI rendering and behavior
  - **Services**: 20% - Business logic
- **20% Integration Tests**: Component interactions and service integrations
  - **Context Integration**: 8% - Provider and consumer interactions
  - **Component Integration**: 7% - Component composition
  - **Service Integration**: 5% - API and external service mocking
- **10% E2E Tests**: Complete user workflows and critical paths
  - **Critical User Flows**: 6% - Core bookmark management
  - **State Persistence**: 2% - Context state across navigation
  - **Performance**: 2% - Large dataset handling

## Testing Tools & Framework

### Core Testing Stack

| Tool | Purpose | Justification |
|------|---------|---------------|
| **Jest** | Unit Testing Framework | Industry standard, great TypeScript support |
| **React Testing Library** | Component Testing | Encourages best practices, accessibility-focused |
| **Cypress** | E2E Testing | Excellent developer experience, time-travel debugging |
| **MSW** | API Mocking | Service worker-based mocking, realistic network behavior |
| **@testing-library/jest-dom** | Custom Matchers | Enhanced assertions for DOM testing |

### Additional Tools

- **Storybook**: Component documentation and visual testing
- **Chromatic**: Visual regression testing
- **Lighthouse CI**: Performance and accessibility auditing
- **axe-core**: Accessibility testing
- **Bundlephobia**: Bundle size analysis

### Setup Configuration

#### Jest Configuration (`jest.config.js`)

```javascript
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  moduleNameMapping: {
    '\\.(css|less|scss|sass)$': 'identity-obj-proxy',
  },
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
    '!src/**/*.stories.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  testMatch: [
    '<rootDir>/src/**/__tests__/**/*.{ts,tsx}',
    '<rootDir>/src/**/*.{test,spec}.{ts,tsx}',
  ],
};
```

#### Test Setup (`src/test/setup.ts`)

```typescript
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// Establish API mocking before all tests
beforeAll(() => server.listen());

// Reset any request handlers that we may add during the tests
afterEach(() => server.resetHandlers());

// Clean up after the tests are finished
afterAll(() => server.close());

// Mock environment variables
process.env.VITE_GEMINI_API_KEY = 'test-api-key';

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(),
    removeListener: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});
```

## Unit Testing Strategy

### Component Testing

#### Testing Approach

1. **Render Testing**: Verify component renders without crashing
2. **Props Testing**: Test different prop combinations
3. **Event Testing**: Test user interactions
4. **State Testing**: Test internal state changes
5. **Accessibility Testing**: Test ARIA attributes and keyboard navigation

#### Example: BookmarkItem Component Test

```typescript
// components/__tests__/BookmarkItem.test.tsx
import { render, screen, fireEvent } from '@testing-library/react';
import { BookmarkItem } from '../BookmarkItem';
import { Bookmark } from '../../types';

const mockBookmark: Bookmark = {
  id: '1',
  title: 'Test Bookmark',
  url: 'https://example.com',
  folder: 'Test Folder',
  tags: ['test', 'example'],
  dateAdded: '2024-01-01',
};

describe('BookmarkItem', () => {
  const mockProps = {
    bookmark: mockBookmark,
    isSelected: false,
    onSelect: jest.fn(),
    onUpdate: jest.fn(),
    onDelete: jest.fn(),
    showSummary: true,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders bookmark information correctly', () => {
    render(<BookmarkItem {...mockProps} />);
    
    expect(screen.getByText('Test Bookmark')).toBeInTheDocument();
    expect(screen.getByText('https://example.com')).toBeInTheDocument();
    expect(screen.getByText('Test Folder')).toBeInTheDocument();
    expect(screen.getByText('test')).toBeInTheDocument();
    expect(screen.getByText('example')).toBeInTheDocument();
  });

  it('calls onSelect when checkbox is clicked', () => {
    render(<BookmarkItem {...mockProps} />);
    
    const checkbox = screen.getByRole('checkbox');
    fireEvent.click(checkbox);
    
    expect(mockProps.onSelect).toHaveBeenCalledWith('1');
  });

  it('shows selected state correctly', () => {
    render(<BookmarkItem {...mockProps} isSelected={true} />);
    
    const checkbox = screen.getByRole('checkbox');
    expect(checkbox).toBeChecked();
  });

  it('handles tag editing', async () => {
    render(<BookmarkItem {...mockProps} />);
    
    const editButton = screen.getByLabelText('Edit tags');
    fireEvent.click(editButton);
    
    const tagInput = screen.getByDisplayValue('test, example');
    fireEvent.change(tagInput, { target: { value: 'test, example, new' } });
    fireEvent.blur(tagInput);
    
    expect(mockProps.onUpdate).toHaveBeenCalledWith('1', {
      tags: ['test', 'example', 'new'],
    });
  });

  it('is accessible', () => {
    render(<BookmarkItem {...mockProps} />);
    
    expect(screen.getByRole('checkbox')).toHaveAccessibleName();
    expect(screen.getByRole('link')).toHaveAccessibleName();
  });
});
```

### Service Testing

#### Example: GeminiService Test

```typescript
// services/__tests__/geminiService.test.ts
import { GeminiService } from '../geminiService';
import { GoogleGenerativeAI } from '@google/generative-ai';

// Mock the Google Generative AI
jest.mock('@google/generative-ai');

const mockGoogleGenerativeAI = GoogleGenerativeAI as jest.MockedClass<typeof GoogleGenerativeAI>;

describe('GeminiService', () => {
  let geminiService: GeminiService;
  let mockModel: any;

  beforeEach(() => {
    mockModel = {
      generateContent: jest.fn(),
    };

    mockGoogleGenerativeAI.prototype.getGenerativeModel = jest.fn().mockReturnValue(mockModel);
    
    // Set up environment variable
    process.env.VITE_GEMINI_API_KEY = 'test-api-key';
    
    geminiService = new GeminiService();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('generateSummaryForBookmark', () => {
    it('generates summary successfully', async () => {
      const mockResponse = {
        response: {
          text: () => 'Generated summary for the bookmark',
        },
      };
      
      mockModel.generateContent.mockResolvedValue(mockResponse);
      
      const result = await geminiService.generateSummaryForBookmark(
        'Test Title',
        'https://example.com'
      );
      
      expect(result).toBe('Generated summary for the bookmark');
      expect(mockModel.generateContent).toHaveBeenCalledWith(
        expect.stringContaining('Test Title')
      );
    });

    it('handles API errors gracefully', async () => {
      mockModel.generateContent.mockRejectedValue(new Error('API Error'));
      
      await expect(
        geminiService.generateSummaryForBookmark('Test Title', 'https://example.com')
      ).rejects.toThrow('Failed to generate summary');
    });

    it('handles rate limiting', async () => {
      const rateLimitError = new Error('Rate limit exceeded');
      rateLimitError.name = 'GoogleGenerativeAIError';
      
      mockModel.generateContent.mockRejectedValue(rateLimitError);
      
      await expect(
        geminiService.generateSummaryForBookmark('Test Title', 'https://example.com')
      ).rejects.toThrow('Rate limit exceeded');
    });
  });
});
```

### Utility Function Testing

#### Example: Export Utils Test

```typescript
// utils/__tests__/exportUtils.test.ts
import { exportToHTML, exportToJSON, exportToCSV } from '../exportUtils';
import { Bookmark } from '../../types';

const mockBookmarks: Bookmark[] = [
  {
    id: '1',
    title: 'Test Bookmark 1',
    url: 'https://example1.com',
    folder: 'Folder 1',
    tags: ['tag1', 'tag2'],
    dateAdded: '2024-01-01',
  },
  {
    id: '2',
    title: 'Test Bookmark 2',
    url: 'https://example2.com',
    folder: 'Folder 2',
    tags: ['tag3'],
    dateAdded: '2024-01-02',
  },
];

describe('exportUtils', () => {
  describe('exportToHTML', () => {
    it('generates valid HTML structure', () => {
      const html = exportToHTML(mockBookmarks);
      
      expect(html).toContain('<!DOCTYPE NETSCAPE-Bookmark-file-1>');
      expect(html).toContain('<TITLE>Bookmarks</TITLE>');
      expect(html).toContain('Test Bookmark 1');
      expect(html).toContain('https://example1.com');
    });

    it('handles empty bookmark list', () => {
      const html = exportToHTML([]);
      
      expect(html).toContain('<!DOCTYPE NETSCAPE-Bookmark-file-1>');
      expect(html).not.toContain('<A HREF=');
    });

    it('escapes HTML characters in titles', () => {
      const bookmarksWithSpecialChars: Bookmark[] = [
        {
          id: '1',
          title: 'Title with <script> & "quotes"',
          url: 'https://example.com',
          folder: 'Test',
          tags: [],
          dateAdded: '2024-01-01',
        },
      ];
      
      const html = exportToHTML(bookmarksWithSpecialChars);
      
      expect(html).toContain('&lt;script&gt;');
      expect(html).toContain('&amp;');
      expect(html).toContain('&quot;');
    });
  });

  describe('exportToJSON', () => {
    it('generates valid JSON', () => {
      const json = exportToJSON(mockBookmarks);
      const parsed = JSON.parse(json);
      
      expect(parsed).toHaveLength(2);
      expect(parsed[0]).toEqual(mockBookmarks[0]);
    });
  });

  describe('exportToCSV', () => {
    it('generates valid CSV with headers', () => {
      const csv = exportToCSV(mockBookmarks);
      const lines = csv.split('\n');
      
      expect(lines[0]).toBe('Title,URL,Folder,Tags,Date Added');
      expect(lines[1]).toContain('Test Bookmark 1');
      expect(lines[1]).toContain('https://example1.com');
    });

    it('handles commas in data', () => {
      const bookmarksWithCommas: Bookmark[] = [
        {
          id: '1',
          title: 'Title, with commas',
          url: 'https://example.com',
          folder: 'Folder, with commas',
          tags: ['tag1', 'tag2'],
          dateAdded: '2024-01-01',
        },
      ];
      
      const csv = exportToCSV(bookmarksWithCommas);
      
      expect(csv).toContain('"Title, with commas"');
      expect(csv).toContain('"Folder, with commas"');
    });
  });
});
```

## Integration Testing Strategy

### Component Integration Tests

#### Testing Component Interactions

```typescript
// components/__tests__/BookmarkList.integration.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BookmarkList } from '../BookmarkList';
import { Bookmark } from '../../types';

const mockBookmarks: Bookmark[] = [
  // ... mock data
];

describe('BookmarkList Integration', () => {
  it('handles bookmark selection and bulk operations', async () => {
    const mockOnDelete = jest.fn();
    const mockOnUpdate = jest.fn();
    
    render(
      <BookmarkList
        bookmarks={mockBookmarks}
        selectedBookmarks={new Set()}
        onSelectionChange={jest.fn()}
        onUpdate={mockOnUpdate}
        onDelete={mockOnDelete}
        showSummary={true}
      />
    );
    
    // Select multiple bookmarks
    const checkboxes = screen.getAllByRole('checkbox');
    fireEvent.click(checkboxes[0]);
    fireEvent.click(checkboxes[1]);
    
    // Verify bulk actions are enabled
    const deleteButton = screen.getByText('Delete Selected');
    expect(deleteButton).not.toBeDisabled();
    
    // Perform bulk delete
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(mockOnDelete).toHaveBeenCalledWith(['1', '2']);
    });
  });
});
```

### Service Integration Tests

#### Testing with MSW (Mock Service Worker)

```typescript
// test/mocks/handlers.ts
import { rest } from 'msw';

export const handlers = [
  rest.post('https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent', (req, res, ctx) => {
    return res(
      ctx.json({
        candidates: [{
          content: {
            parts: [{
              text: 'Generated summary for the bookmark'
            }]
          }
        }]
      })
    );
  }),
];
```

```typescript
// test/mocks/server.ts
import { setupServer } from 'msw/node';
import { handlers } from './handlers';

export const server = setupServer(...handlers);
```

## End-to-End Testing Strategy

### Cypress Configuration

#### `cypress.config.ts`

```typescript
import { defineConfig } from 'cypress';

export default defineConfig({
  e2e: {
    baseUrl: 'http://localhost:5173',
    setupNodeEvents(on, config) {
      // implement node event listeners here
    },
    env: {
      VITE_GEMINI_API_KEY: 'test-api-key',
    },
    viewportWidth: 1280,
    viewportHeight: 720,
    video: true,
    screenshotOnRunFailure: true,
  },
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },
});
```

### Critical User Journeys

#### 1. Bookmark Import and Management

```typescript
// cypress/e2e/bookmark-management.cy.ts
describe('Bookmark Management', () => {
  beforeEach(() => {
    cy.visit('/');
  });

  it('imports bookmarks from HTML file', () => {
    // Upload bookmark file
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/bookmarks.html');
    
    // Verify bookmarks are loaded
    cy.get('[data-testid="bookmark-item"]').should('have.length.greaterThan', 0);
    
    // Verify bookmark details
    cy.get('[data-testid="bookmark-item"]').first().within(() => {
      cy.get('[data-testid="bookmark-title"]').should('be.visible');
      cy.get('[data-testid="bookmark-url"]').should('be.visible');
    });
  });

  it('filters bookmarks by search term', () => {
    // Import bookmarks first
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/bookmarks.html');
    
    // Search for specific bookmark
    cy.get('[data-testid="search-input"]').type('GitHub');
    
    // Verify filtered results
    cy.get('[data-testid="bookmark-item"]').should('contain.text', 'GitHub');
    cy.get('[data-testid="bookmark-item"]').should('not.contain.text', 'Google');
  });

  it('performs bulk operations on selected bookmarks', () => {
    // Import bookmarks
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/bookmarks.html');
    
    // Select multiple bookmarks
    cy.get('[data-testid="bookmark-checkbox"]').first().check();
    cy.get('[data-testid="bookmark-checkbox"]').eq(1).check();
    
    // Verify bulk actions are enabled
    cy.get('[data-testid="bulk-delete-btn"]').should('not.be.disabled');
    
    // Perform bulk delete
    cy.get('[data-testid="bulk-delete-btn"]').click();
    cy.get('[data-testid="confirm-delete-btn"]').click();
    
    // Verify bookmarks are deleted
    cy.get('[data-testid="bookmark-item"]').should('have.length.lessThan', 10);
  });
});
```

#### 2. AI Features Integration

```typescript
// cypress/e2e/ai-features.cy.ts
describe('AI Features', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/bookmarks.html');
  });

  it('generates summaries for bookmarks', () => {
    // Mock Gemini API response
    cy.intercept('POST', '**/generateContent', {
      candidates: [{
        content: {
          parts: [{ text: 'AI-generated summary' }]
        }
      }]
    }).as('generateSummary');
    
    // Trigger summary generation
    cy.get('[data-testid="generate-summaries-btn"]').click();
    
    // Wait for API call
    cy.wait('@generateSummary');
    
    // Verify summary appears
    cy.get('[data-testid="bookmark-summary"]').should('contain.text', 'AI-generated summary');
  });

  it('handles API errors gracefully', () => {
    // Mock API error
    cy.intercept('POST', '**/generateContent', {
      statusCode: 429,
      body: { error: 'Rate limit exceeded' }
    }).as('apiError');
    
    // Trigger summary generation
    cy.get('[data-testid="generate-summaries-btn"]').click();
    
    // Wait for API call
    cy.wait('@apiError');
    
    // Verify error message
    cy.get('[data-testid="error-toast"]').should('contain.text', 'Rate limit exceeded');
  });
});
```

### Cross-Browser Testing

```typescript
// cypress/e2e/cross-browser.cy.ts
describe('Cross-Browser Compatibility', () => {
  const browsers = ['chrome', 'firefox', 'edge'];
  
  browsers.forEach(browser => {
    it(`works correctly in ${browser}`, () => {
      cy.visit('/');
      
      // Test core functionality
      cy.get('[data-testid="file-input"]').should('be.visible');
      cy.get('[data-testid="search-input"]').should('be.visible');
      
      // Test responsive design
      cy.viewport('iphone-x');
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
      
      cy.viewport('macbook-15');
      cy.get('[data-testid="desktop-nav"]').should('be.visible');
    });
  });
});
```

## Performance Testing

### Performance Test Suite

#### Lighthouse CI Configuration

```javascript
// lighthouserc.js
module.exports = {
  ci: {
    collect: {
      url: ['http://localhost:5173'],
      numberOfRuns: 3,
    },
    assert: {
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.8 }],
        'categories:seo': ['warn', { minScore: 0.8 }],
      },
    },
    upload: {
      target: 'temporary-public-storage',
    },
  },
};
```

#### Performance Testing with Cypress

```typescript
// cypress/e2e/performance.cy.ts
describe('Performance Tests', () => {
  it('loads large bookmark files efficiently', () => {
    cy.visit('/');
    
    // Start performance measurement
    cy.window().then((win) => {
      win.performance.mark('start-import');
    });
    
    // Import large file
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/large-bookmarks.html');
    
    // Wait for import to complete
    cy.get('[data-testid="bookmark-item"]').should('have.length', 1000);
    
    // End performance measurement
    cy.window().then((win) => {
      win.performance.mark('end-import');
      win.performance.measure('import-duration', 'start-import', 'end-import');
      
      const measure = win.performance.getEntriesByName('import-duration')[0];
      expect(measure.duration).to.be.lessThan(5000); // Less than 5 seconds
    });
  });

  it('maintains smooth scrolling with many bookmarks', () => {
    cy.visit('/');
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/large-bookmarks.html');
    
    // Test scroll performance
    cy.get('[data-testid="bookmark-list"]').scrollTo('bottom', { duration: 2000 });
    cy.get('[data-testid="bookmark-list"]').scrollTo('top', { duration: 2000 });
    
    // Verify no layout shifts
    cy.get('[data-testid="bookmark-item"]').first().should('be.visible');
  });
});
```

## Accessibility Testing

### Automated Accessibility Testing

```typescript
// cypress/e2e/accessibility.cy.ts
import 'cypress-axe';

describe('Accessibility Tests', () => {
  beforeEach(() => {
    cy.visit('/');
    cy.injectAxe();
  });

  it('has no accessibility violations on main page', () => {
    cy.checkA11y();
  });

  it('supports keyboard navigation', () => {
    cy.get('[data-testid="file-input"]').focus();
    cy.get('[data-testid="file-input"]').should('be.focused');
    
    // Tab through interactive elements
    cy.tab();
    cy.get('[data-testid="search-input"]').should('be.focused');
    
    cy.tab();
    cy.get('[data-testid="filter-dropdown"]').should('be.focused');
  });

  it('provides proper ARIA labels', () => {
    cy.get('[data-testid="file-input"]').should('have.attr', 'aria-label');
    cy.get('[data-testid="search-input"]').should('have.attr', 'aria-label');
    cy.get('[data-testid="bookmark-checkbox"]').should('have.attr', 'aria-label');
  });

  it('supports screen readers', () => {
    // Test with screen reader announcements
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/bookmarks.html');
    
    cy.get('[role="status"]').should('contain.text', 'bookmarks loaded');
  });
});
```

### Manual Accessibility Testing Checklist

- [ ] **Keyboard Navigation**: All interactive elements accessible via keyboard
- [ ] **Screen Reader**: Content properly announced by screen readers
- [ ] **Color Contrast**: Meets WCAG 2.1 AA standards (4.5:1 ratio)
- [ ] **Focus Management**: Clear focus indicators and logical tab order
- [ ] **ARIA Labels**: Proper labeling for complex UI components
- [ ] **Semantic HTML**: Use of proper HTML elements and landmarks

## Security Testing

### Security Test Cases

```typescript
// cypress/e2e/security.cy.ts
describe('Security Tests', () => {
  it('sanitizes user input to prevent XSS', () => {
    cy.visit('/');
    
    // Attempt XSS injection in search
    const xssPayload = '<script>alert("XSS")</script>';
    cy.get('[data-testid="search-input"]').type(xssPayload);
    
    // Verify script is not executed
    cy.window().then((win) => {
      expect(win.document.body.innerHTML).not.to.contain('<script>');
    });
  });

  it('validates file uploads', () => {
    cy.visit('/');
    
    // Attempt to upload non-HTML file
    cy.get('[data-testid="file-input"]').selectFile('cypress/fixtures/malicious.txt');
    
    // Verify error message
    cy.get('[data-testid="error-toast"]').should('contain.text', 'Invalid file type');
  });

  it('protects API keys', () => {
    cy.visit('/');
    
    // Verify API key is not exposed in client-side code
    cy.window().then((win) => {
      const scripts = Array.from(win.document.scripts);
      scripts.forEach(script => {
        expect(script.innerHTML).not.to.contain('AIza'); // Gemini API key prefix
      });
    });
  });
});
```

## Test Data Management

### Test Fixtures

#### Bookmark Test Data

```typescript
// cypress/fixtures/bookmarks.json
{
  "bookmarks": [
    {
      "id": "1",
      "title": "GitHub",
      "url": "https://github.com",
      "folder": "Development",
      "tags": ["code", "git"],
      "dateAdded": "2024-01-01"
    },
    {
      "id": "2",
      "title": "Google",
      "url": "https://google.com",
      "folder": "Search",
      "tags": ["search", "web"],
      "dateAdded": "2024-01-02"
    }
  ]
}
```

#### HTML Bookmark File

```html
<!-- cypress/fixtures/bookmarks.html -->
<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks Menu</H1>
<DL><p>
    <DT><H3>Development</H3>
    <DL><p>
        <DT><A HREF="https://github.com" ADD_DATE="1704067200">GitHub</A>
        <DT><A HREF="https://stackoverflow.com" ADD_DATE="1704067200">Stack Overflow</A>
    </DL><p>
    <DT><H3>Search</H3>
    <DL><p>
        <DT><A HREF="https://google.com" ADD_DATE="1704067200">Google</A>
    </DL><p>
</DL><p>
```

### Test Data Factories

```typescript
// test/factories/bookmarkFactory.ts
import { Bookmark } from '../../types';

export const createMockBookmark = (overrides: Partial<Bookmark> = {}): Bookmark => {
  return {
    id: Math.random().toString(36).substr(2, 9),
    title: 'Test Bookmark',
    url: 'https://example.com',
    folder: 'Test Folder',
    tags: ['test'],
    dateAdded: new Date().toISOString(),
    ...overrides,
  };
};

export const createMockBookmarks = (count: number): Bookmark[] => {
  return Array.from({ length: count }, (_, index) => 
    createMockBookmark({
      id: `bookmark-${index}`,
      title: `Test Bookmark ${index + 1}`,
      url: `https://example${index + 1}.com`,
    })
  );
};
```

## CI/CD Integration

### GitHub Actions Workflow

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  unit-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit tests
      run: npm run test:unit -- --coverage
    
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Run Cypress tests
      uses: cypress-io/github-action@v5
      with:
        start: npm run preview
        wait-on: 'http://localhost:4173'
        browser: chrome
      env:
        VITE_GEMINI_API_KEY: ${{ secrets.VITE_GEMINI_API_KEY }}

  lighthouse:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Build application
      run: npm run build
    
    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}
```

### Package.json Scripts

```json
{
  "scripts": {
    "test": "jest",
    "test:unit": "jest --testPathPattern=__tests__",
    "test:integration": "jest --testPathPattern=integration",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage",
    "test:e2e": "cypress run",
    "test:e2e:open": "cypress open",
    "test:a11y": "cypress run --spec 'cypress/e2e/accessibility.cy.ts'",
    "test:performance": "lighthouse http://localhost:5173 --output=html --output-path=./lighthouse-report.html",
    "test:all": "npm run test:unit && npm run test:e2e"
  }
}
```

## Test Coverage Goals

### Coverage Targets

| Component Type | Coverage Target | Priority |
|----------------|-----------------|----------|
| **Critical Business Logic** | 95%+ | High |
| **UI Components** | 85%+ | High |
| **Service Layer** | 90%+ | High |
| **Utility Functions** | 95%+ | Medium |
| **Type Definitions** | N/A | Low |

### Coverage Reporting

```javascript
// jest.config.js coverage configuration
module.exports = {
  collectCoverageFrom: [
    'src/**/*.{ts,tsx}',
    '!src/**/*.d.ts',
    '!src/test/**',
    '!src/**/*.stories.tsx',
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
    './src/services/': {
      branches: 90,
      functions: 90,
      lines: 90,
      statements: 90,
    },
  },
  coverageReporters: ['text', 'lcov', 'html'],
};
```

## Testing Checklist

### Pre-Development

- [ ] **Test Plan**: Create test plan for new features
- [ ] **Test Cases**: Write test cases before implementation
- [ ] **Mock Data**: Prepare test fixtures and mock data
- [ ] **Environment**: Set up testing environment

### During Development

- [ ] **Unit Tests**: Write unit tests for new components/functions
- [ ] **Integration Tests**: Test component interactions
- [ ] **Manual Testing**: Verify functionality manually
- [ ] **Code Coverage**: Maintain coverage targets

### Pre-Commit

- [ ] **All Tests Pass**: Run full test suite
- [ ] **Coverage Check**: Verify coverage thresholds
- [ ] **Linting**: Fix all linting errors
- [ ] **Type Check**: Resolve TypeScript errors

### Pre-Release

- [ ] **E2E Tests**: Run complete E2E test suite
- [ ] **Performance Tests**: Verify performance benchmarks
- [ ] **Accessibility Tests**: Check accessibility compliance
- [ ] **Cross-Browser**: Test on multiple browsers
- [ ] **Security Tests**: Run security test suite

### Post-Release

- [ ] **Monitoring**: Set up error monitoring
- [ ] **User Feedback**: Collect and analyze user feedback
- [ ] **Performance Monitoring**: Track real-world performance
- [ ] **Test Maintenance**: Update tests based on issues

---

**Last Updated**: December 2024
**Version**: 1.0.0
**Review Cycle**: Monthly