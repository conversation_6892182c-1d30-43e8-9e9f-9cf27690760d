import React, { useEffect, useState } from 'react';
import { useCodeQualityAnalyzer } from '../hooks/useCodeQualityAnalyzer';
import './CodeQualityDashboard.css';

interface CodeQualityDashboardProps {
  projectPath?: string;
  autoStart?: boolean;
}

const CodeQualityDashboard: React.FC<CodeQualityDashboardProps> = ({
  projectPath = 'src/**/*.{ts,tsx}',
  autoStart = false
}) => {
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [isWatching, setIsWatching] = useState(false);
  const [filterType, setFilterType] = useState<string>('all');
  const [severityFilter, setSeverityFilter] = useState<string>('all');

  const {
    analysisResults,
    isAnalyzing,
    startWatching,
    stopWatching,
    analyzeFile,
    clearResults,
    getResultsForFile
  } = useCodeQualityAnalyzer({
    watchPaths: [projectPath],
    enableRealTimeAnalysis: true,
    debounceMs: 1000
  });

  useEffect(() => {
    if (autoStart) {
      handleStartWatching();
    }
  }, [autoStart]);

  const handleStartWatching = () => {
    startWatching();
    setIsWatching(true);
  };

  const handleStopWatching = () => {
    stopWatching();
    setIsWatching(false);
  };

  const handleAnalyzeFile = async () => {
    if (selectedFile.trim()) {
      await analyzeFile(selectedFile.trim());
      // Log existing results for the file after analysis
      const existingResults = getResultsForFile(selectedFile.trim());
      console.log(`Analysis complete for ${selectedFile.trim()}. Found ${existingResults.length} previous analysis results.`);
    }
  };

  const handleClearResults = () => {
    clearResults();
    setSelectedFile('');
  };

  // Filter results based on selected criteria
  const filteredResults = analysisResults.filter(result => {
    const typeMatch = filterType === 'all' || 
      result.suggestions.some(s => s.type === filterType);
    const severityMatch = severityFilter === 'all' || 
      result.severity === severityFilter;
    return typeMatch && severityMatch;
  });

  // Get statistics
  const totalIssues = analysisResults.reduce((sum, result) => sum + result.suggestions.length, 0);
  const fileCount = analysisResults.length;
  const severityCounts = analysisResults.reduce((acc, result) => {
    acc[result.severity] = (acc[result.severity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const typeCounts = analysisResults.reduce((acc, result) => {
    result.suggestions.forEach(suggestion => {
      acc[suggestion.type] = (acc[suggestion.type] || 0) + 1;
    });
    return acc;
  }, {} as Record<string, number>);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return '#ef4444';
      case 'medium': return '#f59e0b';
      case 'low': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'code-smell': return '🦨';
      case 'design-pattern': return '🏗️';
      case 'best-practice': return '✅';
      case 'performance': return '⚡';
      case 'readability': return '👁️';
      case 'maintainability': return '🔧';
      default: return '📝';
    }
  };

  return (
    <div className="code-quality-dashboard">
      <div className="dashboard-header">
        <h2>Code Quality Analyzer</h2>
        <div className="dashboard-controls">
          <div className="file-input-group">
            <input
              type="text"
              value={selectedFile}
              onChange={(e) => setSelectedFile(e.target.value)}
              placeholder="Enter file path to analyze..."
              className="file-input"
            />
            <button 
              onClick={handleAnalyzeFile}
              disabled={!selectedFile.trim() || isAnalyzing}
              className="analyze-btn"
            >
              {isAnalyzing ? 'Analyzing...' : 'Analyze File'}
            </button>
          </div>
          
          <div className="watch-controls">
            <button
              onClick={isWatching ? handleStopWatching : handleStartWatching}
              className={`watch-btn ${isWatching ? 'watching' : ''}`}
            >
              {isWatching ? '⏹️ Stop Watching' : '▶️ Start Watching'}
            </button>
            <button onClick={handleClearResults} className="clear-btn">
              🗑️ Clear Results
            </button>
          </div>
        </div>
      </div>

      <div className="dashboard-stats">
        <div className="stat-card">
          <div className="stat-value">{fileCount}</div>
          <div className="stat-label">Files Analyzed</div>
        </div>
        <div className="stat-card">
          <div className="stat-value">{totalIssues}</div>
          <div className="stat-label">Total Issues</div>
        </div>
        <div className="stat-card">
          <div className="stat-value" style={{ color: getSeverityColor('high') }}>
            {severityCounts.high || 0}
          </div>
          <div className="stat-label">High Severity</div>
        </div>
        <div className="stat-card">
          <div className="stat-value" style={{ color: getSeverityColor('medium') }}>
            {severityCounts.medium || 0}
          </div>
          <div className="stat-label">Medium Severity</div>
        </div>
        <div className="stat-card">
          <div className="stat-value" style={{ color: getSeverityColor('low') }}>
            {severityCounts.low || 0}
          </div>
          <div className="stat-label">Low Severity</div>
        </div>
      </div>

      <div className="dashboard-filters">
        <div className="filter-group">
          <label>Filter by Type:</label>
          <select 
            value={filterType} 
            onChange={(e) => setFilterType(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Types</option>
            <option value="code-smell">Code Smells</option>
            <option value="design-pattern">Design Patterns</option>
            <option value="best-practice">Best Practices</option>
            <option value="performance">Performance</option>
            <option value="readability">Readability</option>
            <option value="maintainability">Maintainability</option>
          </select>
        </div>
        
        <div className="filter-group">
          <label>Filter by Severity:</label>
          <select 
            value={severityFilter} 
            onChange={(e) => setSeverityFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Severities</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
        </div>
      </div>

      <div className="dashboard-content">
        {filteredResults.length === 0 ? (
          <div className="empty-state">
            <div className="empty-icon">📊</div>
            <h3>No Analysis Results</h3>
            <p>
              {analysisResults.length === 0 
                ? "Start by analyzing a file or enabling file watching to see code quality insights."
                : "No results match your current filters. Try adjusting the filter criteria."
              }
            </p>
          </div>
        ) : (
          <div className="results-list">
            {filteredResults.map((result, index) => (
              <div key={`${result.file}-${index}`} className="result-card">
                <div className="result-header">
                  <div className="file-info">
                    <h3 className="file-name">{result.file}</h3>
                    <div className="file-meta">
                      <span className="timestamp">
                        {result.timestamp.toLocaleString()}
                      </span>
                      <span 
                        className="severity-badge"
                        style={{ backgroundColor: getSeverityColor(result.severity) }}
                      >
                        {result.severity.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  <div className="issue-count">
                    {result.suggestions.length} issue{result.suggestions.length !== 1 ? 's' : ''}
                  </div>
                </div>
                
                <div className="suggestions-list">
                  {result.suggestions
                    .filter(suggestion => 
                      filterType === 'all' || suggestion.type === filterType
                    )
                    .map((suggestion, suggestionIndex) => (
                    <div key={suggestionIndex} className="suggestion-item">
                      <div className="suggestion-header">
                        <span className="suggestion-type">
                          {getTypeIcon(suggestion.type)} {suggestion.type}
                        </span>
                        <span className="suggestion-location">
                          Line {suggestion.line}
                          {suggestion.column && `, Column ${suggestion.column}`}
                        </span>
                      </div>
                      <div className="suggestion-message">
                        {suggestion.message}
                      </div>
                      <div className="suggestion-fix">
                        <strong>Suggestion:</strong> {suggestion.suggestion}
                      </div>
                      {suggestion.example && (
                        <div className="suggestion-example">
                          <strong>Example:</strong>
                          <code>{suggestion.example}</code>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {Object.keys(typeCounts).length > 0 && (
        <div className="dashboard-summary">
          <h3>Issue Type Distribution</h3>
          <div className="type-distribution">
            {Object.entries(typeCounts).map(([type, count]) => (
              <div key={type} className="type-stat">
                <span className="type-icon">{getTypeIcon(type)}</span>
                <span className="type-name">{type}</span>
                <span className="type-count">{count}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="dashboard-footer">
        <div className="status-indicator">
          <span className={`status-dot ${isWatching ? 'active' : 'inactive'}`}></span>
          <span className="status-text">
            {isWatching ? 'Watching for file changes' : 'File watching disabled'}
          </span>
        </div>
        
        {isAnalyzing && (
          <div className="analyzing-indicator">
            <span className="spinner"></span>
            <span>Analyzing code...</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default CodeQualityDashboard;