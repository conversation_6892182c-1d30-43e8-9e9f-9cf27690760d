{"metadata": {"testDataVersion": "1.0.0", "createdBy": "<PERSON>. <PERSON> - Test Expert", "purpose": "Comprehensive test data for Domain Organization feature validation", "lastUpdated": "2024-12-19", "totalBookmarks": 85, "categories": 8, "testScenarios": ["Basic domain recognition", "Subdomain intelligence", "Platform ecosystem mapping", "Corporate family grouping", "Edge cases and unusual domains", "International domain handling", "Performance testing datasets", "Integration test scenarios"]}, "testCategories": {"development": {"description": "Development tools, repositories, and coding platforms", "expectedDomainGroups": ["GitHub Ecosystem", "Microsoft Dev Tools", "Code Sharing", "Documentation"], "bookmarks": [{"id": "dev-001", "title": "GitHub - Main Repository", "url": "https://github.com/user/awesome-project", "description": "Main project repository", "expectedCategory": "GitHub Ecosystem", "testScenario": "basic-recognition"}, {"id": "dev-002", "title": "GitHub Documentation", "url": "https://docs.github.com/en/get-started", "description": "GitHub official documentation", "expectedCategory": "GitHub Ecosystem", "testScenario": "subdomain-grouping"}, {"id": "dev-003", "title": "GitHub Gist - Code Snippet", "url": "https://gist.github.com/username/abc123def456", "description": "Useful code snippet", "expectedCategory": "GitHub Ecosystem", "testScenario": "subdomain-grouping"}, {"id": "dev-004", "title": "GitHub API Reference", "url": "https://api.github.com/repos/user/repo", "description": "API endpoint documentation", "expectedCategory": "GitHub Ecosystem", "testScenario": "subdomain-grouping"}, {"id": "dev-005", "title": "Stack Overflow - React Question", "url": "https://stackoverflow.com/questions/12345/react-hooks-best-practices", "description": "React hooks best practices discussion", "expectedCategory": "Development", "testScenario": "basic-recognition"}, {"id": "dev-006", "title": "CodePen - CSS Animation", "url": "https://codepen.io/username/pen/amazing-animation", "description": "Beautiful CSS animation demo", "expectedCategory": "Code Sharing", "testScenario": "basic-recognition"}, {"id": "dev-007", "title": "Microsoft Docs - TypeScript", "url": "https://docs.microsoft.com/en-us/typescript/", "description": "TypeScript official documentation", "expectedCategory": "Microsoft Dev Tools", "testScenario": "corporate-family"}, {"id": "dev-008", "title": "VS Code Extensions", "url": "https://marketplace.visualstudio.com/vscode", "description": "Visual Studio Code extensions marketplace", "expectedCategory": "Microsoft Dev Tools", "testScenario": "corporate-family"}]}, "aiMachineLearning": {"description": "AI/ML platforms, models, and research tools", "expectedDomainGroups": ["OpenAI Ecosystem", "Hugging Face", "AI Research", "ML Platforms"], "bookmarks": [{"id": "ai-001", "title": "OpenAI Platform", "url": "https://platform.openai.com/docs/api-reference", "description": "OpenAI API documentation", "expectedCategory": "OpenAI Ecosystem", "testScenario": "basic-recognition"}, {"id": "ai-002", "title": "ChatGPT Interface", "url": "https://chat.openai.com/", "description": "ChatGPT web interface", "expectedCategory": "OpenAI Ecosystem", "testScenario": "subdomain-grouping"}, {"id": "ai-003", "title": "Hugging Face Models", "url": "https://huggingface.co/models/bert-base-uncased", "description": "BERT model on Hugging Face", "expectedCategory": "Hugging Face", "testScenario": "basic-recognition"}, {"id": "ai-004", "title": "Hugging Face Datasets", "url": "https://huggingface.co/datasets/squad", "description": "SQuAD dataset for training", "expectedCategory": "Hugging Face", "testScenario": "platform-ecosystem"}, {"id": "ai-005", "title": "Anthropic <PERSON>", "url": "https://www.anthropic.com/claude", "description": "<PERSON> assistant", "expectedCategory": "AI Research", "testScenario": "basic-recognition"}, {"id": "ai-006", "title": "Replicate AI Models", "url": "https://replicate.com/stability-ai/stable-diffusion", "description": "Stable Diffusion on Replicate", "expectedCategory": "ML Platforms", "testScenario": "basic-recognition"}]}, "design": {"description": "Design tools, inspiration, and creative platforms", "expectedDomainGroups": ["Adobe Ecosystem", "Design Inspiration", "Prototyping Tools"], "bookmarks": [{"id": "design-001", "title": "Figma Design System", "url": "https://www.figma.com/file/abc123/design-system", "description": "Company design system in Figma", "expectedCategory": "Prototyping Tools", "testScenario": "basic-recognition"}, {"id": "design-002", "title": "Dribbble Shot - UI Design", "url": "https://dribbble.com/shots/12345678-mobile-app-ui", "description": "Beautiful mobile app UI design", "expectedCategory": "Design Inspiration", "testScenario": "basic-recognition"}, {"id": "design-003", "title": "<PERSON><PERSON><PERSON>", "url": "https://www.behance.net/gallery/123456/brand-identity", "description": "Brand identity project showcase", "expectedCategory": "Design Inspiration", "testScenario": "basic-recognition"}, {"id": "design-004", "title": "Adobe Creative Cloud", "url": "https://creativecloud.adobe.com/apps/all", "description": "Adobe Creative Cloud apps", "expectedCategory": "Adobe Ecosystem", "testScenario": "corporate-family"}, {"id": "design-005", "title": "Adobe Fonts", "url": "https://fonts.adobe.com/fonts/source-sans", "description": "Source Sans Pro font family", "expectedCategory": "Adobe Ecosystem", "testScenario": "corporate-family"}]}, "learning": {"description": "Educational platforms, courses, and learning resources", "expectedDomainGroups": ["Online Courses", "Educational Content", "Skill Development"], "bookmarks": [{"id": "learn-001", "title": "Coursera - Machine Learning Course", "url": "https://www.coursera.org/learn/machine-learning", "description": "<PERSON>'s Machine Learning course", "expectedCategory": "Online Courses", "testScenario": "basic-recognition"}, {"id": "learn-002", "title": "Udemy - React Development", "url": "https://www.udemy.com/course/react-the-complete-guide", "description": "Complete React development course", "expectedCategory": "Online Courses", "testScenario": "basic-recognition"}, {"id": "learn-003", "title": "Khan Academy - Algorithms", "url": "https://www.khanacademy.org/computing/computer-science/algorithms", "description": "Computer science algorithms course", "expectedCategory": "Educational Content", "testScenario": "basic-recognition"}, {"id": "learn-004", "title": "Pluralsight - Cloud Computing", "url": "https://www.pluralsight.com/paths/cloud-computing", "description": "Cloud computing learning path", "expectedCategory": "Skill Development", "testScenario": "basic-recognition"}]}, "news": {"description": "Tech news, industry updates, and information sources", "expectedDomainGroups": ["Tech News", "Developer News", "Industry Analysis"], "bookmarks": [{"id": "news-001", "title": "TechCrunch - AI Startup Funding", "url": "https://techcrunch.com/2024/12/19/ai-startup-raises-100m", "description": "Latest AI startup funding news", "expectedCategory": "Tech News", "testScenario": "basic-recognition"}, {"id": "news-002", "title": "Hacker News - Discussion", "url": "https://news.ycombinator.com/item?id=12345678", "description": "Interesting tech discussion", "expectedCategory": "Developer News", "testScenario": "basic-recognition"}, {"id": "news-003", "title": "The Verge - Tech Review", "url": "https://www.theverge.com/2024/12/19/new-smartphone-review", "description": "Latest smartphone review", "expectedCategory": "Tech News", "testScenario": "basic-recognition"}, {"id": "news-004", "title": "Ars Technica - Deep Dive", "url": "https://arstechnica.com/science/2024/12/quantum-computing-breakthrough", "description": "Quantum computing breakthrough analysis", "expectedCategory": "Industry Analysis", "testScenario": "basic-recognition"}]}, "social": {"description": "Social platforms, communication tools, and community sites", "expectedDomainGroups": ["Professional Networks", "Social Media", "Communication", "Communities"], "bookmarks": [{"id": "social-001", "title": "LinkedIn Profile", "url": "https://www.linkedin.com/in/username", "description": "Professional LinkedIn profile", "expectedCategory": "Professional Networks", "testScenario": "basic-recognition"}, {"id": "social-002", "title": "Twitter Thread", "url": "https://twitter.com/username/status/1234567890", "description": "Interesting tech thread", "expectedCategory": "Social Media", "testScenario": "basic-recognition"}, {"id": "social-003", "title": "Discord Server", "url": "https://discord.gg/developers", "description": "Developer community Discord", "expectedCategory": "Communication", "testScenario": "basic-recognition"}, {"id": "social-004", "title": "Reddit - Programming", "url": "https://www.reddit.com/r/programming/comments/abc123", "description": "Programming discussion on Reddit", "expectedCategory": "Communities", "testScenario": "basic-recognition"}]}, "edgeCases": {"description": "Edge cases, unusual domains, and challenging scenarios", "expectedDomainGroups": ["Local Development", "IP Addresses", "International Domains", "Unusual TLDs"], "bookmarks": [{"id": "edge-001", "title": "Local Development Server", "url": "http://localhost:3000/dashboard", "description": "Local React development server", "expectedCategory": "Local Development", "testScenario": "edge-case-localhost"}, {"id": "edge-002", "title": "Local API Server", "url": "http://127.0.0.1:8080/api/users", "description": "Local API endpoint", "expectedCategory": "Local Development", "testScenario": "edge-case-localhost"}, {"id": "edge-003", "title": "Network Device", "url": "http://***********/admin", "description": "Router admin interface", "expectedCategory": "IP Addresses", "testScenario": "edge-case-ip"}, {"id": "edge-004", "title": "Chinese Domain", "url": "https://example.中国/page", "description": "International domain with Chinese characters", "expectedCategory": "International Domains", "testScenario": "edge-case-international"}, {"id": "edge-005", "title": "Arabic Domain", "url": "https://موقع.السعودية/content", "description": "Arabic international domain", "expectedCategory": "International Domains", "testScenario": "edge-case-international"}, {"id": "edge-006", "title": "Dev TLD Site", "url": "https://awesome-project.dev/docs", "description": "Site using .dev TLD", "expectedCategory": "Unusual TLDs", "testScenario": "edge-case-tld"}, {"id": "edge-007", "title": "IO Domain", "url": "https://startup.io/product", "description": "Startup using .io domain", "expectedCategory": "Unusual TLDs", "testScenario": "edge-case-tld"}, {"id": "edge-008", "title": "XYZ Domain", "url": "https://creative.xyz/portfolio", "description": "Creative site using .xyz domain", "expectedCategory": "Unusual TLDs", "testScenario": "edge-case-tld"}]}, "performanceTesting": {"description": "Large dataset for performance and stress testing", "expectedDomainGroups": ["Bulk Processing Test"], "bookmarks": [{"id": "perf-001", "title": "Performance Test Bookmark 1", "url": "https://github.com/user/repo-1", "description": "Performance testing bookmark", "expectedCategory": "GitHub Ecosystem", "testScenario": "performance-bulk"}, {"id": "perf-002", "title": "Performance Test Bookmark 2", "url": "https://stackoverflow.com/questions/1", "description": "Performance testing bookmark", "expectedCategory": "Development", "testScenario": "performance-bulk"}, {"id": "perf-003", "title": "Performance Test Bookmark 3", "url": "https://www.figma.com/file/test-1", "description": "Performance testing bookmark", "expectedCategory": "Prototyping Tools", "testScenario": "performance-bulk"}, {"id": "perf-004", "title": "Performance Test Bookmark 4", "url": "https://platform.openai.com/test-1", "description": "Performance testing bookmark", "expectedCategory": "OpenAI Ecosystem", "testScenario": "performance-bulk"}, {"id": "perf-005", "title": "Performance Test Bookmark 5", "url": "https://www.coursera.org/test-1", "description": "Performance testing bookmark", "expectedCategory": "Online Courses", "testScenario": "performance-bulk"}]}}, "testScenarios": {"basicRecognition": {"description": "Test basic domain recognition for major platforms", "expectedResults": {"accuracy": ">= 98%", "processingTime": "< 1 second per bookmark", "categoriesCreated": "6-8 main categories"}}, "subdomainGrouping": {"description": "Test intelligent subdomain grouping under parent domains", "expectedResults": {"githubEcosystem": "4 bookmarks grouped together", "openaiEcosystem": "2 bookmarks grouped together", "accuracy": ">= 95%"}}, "corporateFamily": {"description": "Test recognition of corporate service families", "expectedResults": {"microsoftEcosystem": "2 bookmarks grouped", "adobeEcosystem": "2 bookmarks grouped", "accuracy": ">= 90%"}}, "edgeCases": {"description": "Test handling of unusual domains and edge cases", "expectedResults": {"localhostHandling": "Graceful handling without errors", "internationalDomains": "Proper Unicode support", "unusualTlds": "Recognition of modern TLDs"}}, "performanceBulk": {"description": "Test performance with larger datasets", "expectedResults": {"processingSpeed": ">= 100 bookmarks per second", "memoryUsage": "< 100MB overhead", "responseTime": "< 5 seconds for 100 bookmarks"}}}, "validationCriteria": {"domainRecognition": {"majorPlatforms": {"github.com": "GitHub Ecosystem", "stackoverflow.com": "Development", "figma.com": "Prototyping Tools", "openai.com": "OpenAI Ecosystem", "coursera.org": "Online Courses"}, "subdomainGrouping": {"docs.github.com": "Should group with github.com", "api.github.com": "Should group with github.com", "gist.github.com": "Should group with github.com"}, "edgeCaseHandling": {"localhost": "Should not cause errors", "ipAddresses": "Should be categorized appropriately", "internationalDomains": "Should handle Unicode correctly"}}, "performanceMetrics": {"processingSpeed": "100+ bookmarks/second for known domains", "memoryUsage": "<100MB overhead for domain classification", "accuracy": "98%+ for major platforms, 85%+ for general domains", "responseTime": "<1 second for single bookmark categorization"}, "userExperience": {"progressFeedback": "Real-time progress updates during processing", "errorHandling": "Graceful error messages and recovery", "resultsCommunication": "Clear summary of organization results", "configurationOptions": "Intuitive configuration controls"}}, "testInstructions": {"setup": ["1. Clear existing bookmarks or create backup", "2. Import test data using bookmark import functionality", "3. Verify all test bookmarks are loaded correctly", "4. Access Domain Organization panel from sidebar"], "basicTesting": ["1. Run domain organization with default settings", "2. Verify major platforms are recognized correctly", "3. Check subdomain grouping behavior", "4. Validate corporate family grouping"], "advancedTesting": ["1. Test with 'Preserve Existing Folders' enabled/disabled", "2. Test with 'Platform Recognition' enabled/disabled", "3. Test with 'Subdomain Grouping' enabled/disabled", "4. Test different 'Min Bookmarks per Domain' values"], "edgeCaseTesting": ["1. Verify localhost URLs don't cause errors", "2. Test international domain handling", "3. Validate unusual TLD recognition", "4. Check IP address handling"], "performanceTesting": ["1. Load performance test dataset (100+ bookmarks)", "2. Measure processing time and memory usage", "3. Verify accuracy with large datasets", "4. Test responsiveness during processing"]}}