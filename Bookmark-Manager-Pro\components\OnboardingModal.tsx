import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, XMarkIcon } from './icons/HeroIcons';

interface OnboardingModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAddBookmark: () => void;
  onImportBookmarks: () => void;
}

interface OnboardingStep {
  title: string;
  description: string;
  icon: string;
  content: React.ReactNode;
}

const OnboardingModal: React.FC<OnboardingModalProps> = ({
  isOpen,
  onClose,
  onAddBookmark,
  onImportBookmarks
}) => {
  const [currentStep, setCurrentStep] = useState(0);

  const steps: OnboardingStep[] = [
    {
      title: "Welcome to Bookmark Manager Pro",
      description: "Your intelligent bookmark organization starts here",
      icon: "🚀",
      content: (
        <div className="text-center">
          <p className="text-lg mb-4">
            Organize, search, and manage your bookmarks with AI-powered features.
          </p>
          <div className="grid grid-cols-3 gap-4 mt-6">
            <div className="text-center">
              <div className="text-3xl mb-2">🤖</div>
              <h4 className="font-semibold">AI Organization</h4>
              <p className="text-sm text-gray-600">Smart categorization and tagging</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">🔍</div>
              <h4 className="font-semibold">Powerful Search</h4>
              <p className="text-sm text-gray-600">Find anything instantly</p>
            </div>
            <div className="text-center">
              <div className="text-3xl mb-2">📊</div>
              <h4 className="font-semibold">Analytics</h4>
              <p className="text-sm text-gray-600">Track your browsing patterns</p>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "Import Your Bookmarks",
      description: "Bring your existing bookmarks from any browser",
      icon: "📥",
      content: (
        <div>
          <p className="text-lg mb-6">
            We support importing from Chrome, Firefox, Safari, Edge, and more.
          </p>
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-blue-900 mb-2">How to export from your browser:</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• <strong>Chrome:</strong> Settings → Bookmarks → Bookmark manager → Export bookmarks</li>
              <li>• <strong>Firefox:</strong> Library → Bookmarks → Show All Bookmarks → Export</li>
              <li>• <strong>Safari:</strong> File → Export Bookmarks</li>
            </ul>
          </div>
          <button
            onClick={() => {
              onImportBookmarks();
              onClose();
            }}
            className="btn btn-primary w-full"
          >
            Import Bookmarks Now
          </button>
        </div>
      )
    },
    {
      title: "Keyboard Shortcuts",
      description: "Work faster with these handy shortcuts",
      icon: "⌨️",
      content: (
        <div>
          <div className="grid grid-cols-1 gap-4">
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Open Command Palette</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-sm">Ctrl + K</kbd>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Add New Bookmark</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-sm">Ctrl + N</kbd>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Search Bookmarks</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-sm">Ctrl + F</kbd>
            </div>
            <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
              <span>Toggle Sidebar</span>
              <kbd className="px-2 py-1 bg-gray-200 rounded text-sm">Ctrl + B</kbd>
            </div>
          </div>
        </div>
      )
    },
    {
      title: "You're All Set!",
      description: "Start building your digital library",
      icon: "🎉",
      content: (
        <div className="text-center">
          <p className="text-lg mb-6">
            You're ready to start organizing your bookmarks like a pro!
          </p>
          <div className="space-y-3">
            <button
              onClick={() => {
                onAddBookmark();
                onClose();
              }}
              className="btn btn-primary w-full"
            >
              Add Your First Bookmark
            </button>
            <button
              onClick={() => {
                onImportBookmarks();
                onClose();
              }}
              className="btn btn-secondary w-full"
            >
              Import Existing Bookmarks
            </button>
          </div>
          <p className="text-sm text-gray-600 mt-4">
            You can always access this tour again from the help menu.
          </p>
        </div>
      )
    }
  ];

  const nextStep = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  if (!isOpen) return null;

  const currentStepData = steps[currentStep];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div className="flex items-center space-x-3">
            <span className="text-3xl">{currentStepData.icon}</span>
            <div>
              <h2 className="text-xl font-semibold">{currentStepData.title}</h2>
              <p className="text-gray-600">{currentStepData.description}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStepData.content}
        </div>

        {/* Footer */}
        <div className="flex justify-between items-center p-6 border-t bg-gray-50">
          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => goToStep(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-blue-600'
                    : index < currentStep
                    ? 'bg-blue-300'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <div className="flex space-x-3">
            <button
              onClick={prevStep}
              disabled={currentStep === 0}
              className="btn btn-secondary disabled:opacity-50"
            >
              <ChevronLeftIcon className="w-4 h-4 mr-1" />
              Previous
            </button>
            {currentStep === steps.length - 1 ? (
              <button
                onClick={onClose}
                className="btn btn-primary"
              >
                Get Started
              </button>
            ) : (
              <button
                onClick={nextStep}
                className="btn btn-primary"
              >
                Next
                <ChevronRightIcon className="w-4 h-4 ml-1" />
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default OnboardingModal;
