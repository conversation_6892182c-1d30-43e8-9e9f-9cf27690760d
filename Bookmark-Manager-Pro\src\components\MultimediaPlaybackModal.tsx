import {
  Ch<PERSON>ron<PERSON>eft,
  Maximize2,
  <PERSON>mize2,
  Pause,
  Play,
  SkipB<PERSON>,
  Ski<PERSON>For<PERSON>,
  Volume2,
  VolumeX,
  X
} from 'lucide-react'
import React, { useCallback, useRef, useState } from 'react'
import { useModernTheme } from '../contexts/ModernThemeContext'
import { cn } from '../lib/utils'
import '../styles/optimized-panels.css'

// Utility function to detect media type from URL
const detectMediaType = (url: string): 'video' | 'audio' | 'document' | 'article' => {
  const urlLower = url.toLowerCase()

  // Video extensions and patterns
  if (urlLower.match(/\.(mp4|webm|ogg|avi|mov|wmv|flv|mkv)(\?.*)?$/i) ||
    urlLower.includes('youtube.com/watch') ||
    urlLower.includes('youtu.be/') ||
    urlLower.includes('vimeo.com/') ||
    urlLower.includes('twitch.tv/')) {
    return 'video'
  }

  // Audio extensions and patterns
  if (urlLower.match(/\.(mp3|wav|ogg|aac|flac|m4a|wma)(\?.*)?$/i) ||
    urlLower.includes('spotify.com/') ||
    urlLower.includes('soundcloud.com/')) {
    return 'audio'
  }

  // Document extensions
  if (urlLower.match(/\.(pdf|doc|docx|ppt|pptx|xls|xlsx)(\?.*)?$/i)) {
    return 'document'
  }

  // Default to article for web pages
  return 'article'
}

// Utility function to check if URL is directly playable
const isDirectMediaUrl = (url: string): boolean => {
  const urlLower = url.toLowerCase()
  return urlLower.match(/\.(mp4|webm|ogg|avi|mov|mp3|wav|aac|flac|m4a)(\?.*)?$/i) !== null
}

interface MultimediaPlaylistItem {
  id: string
  title: string
  url: string
  type: 'video' | 'audio' | 'document' | 'article'
  duration?: number
  thumbnail?: string
}

interface MultimediaPlaylist {
  id: string
  name: string
  items: MultimediaPlaylistItem[]
  playbackSettings: {
    shuffle: boolean
    repeat: 'none' | 'one' | 'all'
    autoplay: boolean
  }
}

interface PlaybackState {
  isPlaying: boolean
  currentTime: number
  duration: number
  volume: number
  isMuted: boolean
  playbackRate: number
  isLoading: boolean
  error: string | null
}

interface MultimediaPlaybackModalProps {
  isOpen: boolean
  onClose: () => void
  playlist: MultimediaPlaylist
  initialItemIndex?: number
}

export const MultimediaPlaybackModal: React.FC<MultimediaPlaybackModalProps> = ({
  isOpen,
  onClose,
  playlist,
  initialItemIndex = 0
}) => {
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  const modalRef = useRef<HTMLDivElement>(null)
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)

  const [currentItemIndex, setCurrentItemIndex] = useState(initialItemIndex)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [showPlaylist, setShowPlaylist] = useState(true)

  const [playbackState, setPlaybackState] = useState<PlaybackState>({
    isPlaying: false,
    currentTime: 0,
    duration: 0,
    volume: 100,
    isMuted: false,
    playbackRate: 1,
    isLoading: false,
    error: null
  })

  const currentItem = playlist.items[currentItemIndex]

  // Detect actual media type from URL if not properly set
  const actualMediaType = currentItem ? detectMediaType(currentItem.url) : 'article'
  const isVideo = actualMediaType === 'video'
  const isAudio = actualMediaType === 'audio'
  const isDirectMedia = currentItem ? isDirectMediaUrl(currentItem.url) : false

  const formatTime = useCallback((seconds: number): string => {
    if (!seconds || isNaN(seconds)) return '0:00'
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }, [])

  const handleBackdropClick = useCallback((e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }, [onClose])

  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen)
  }, [isFullscreen])

  const togglePlayPause = useCallback(async () => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (!mediaElement) return

    try {
      if (playbackState.isPlaying) {
        mediaElement.pause()
      } else {
        await mediaElement.play()
      }
    } catch (error) {
      console.error('Playback error:', error)
      setPlaybackState(prev => ({
        ...prev,
        error: 'Playback failed. Media may not be supported.',
        isPlaying: false
      }))
    }
  }, [isVideo, playbackState.isPlaying])

  const playPrevious = useCallback(() => {
    if (currentItemIndex > 0) {
      setCurrentItemIndex(currentItemIndex - 1)
    }
  }, [currentItemIndex])

  const playNext = useCallback(() => {
    if (currentItemIndex < playlist.items.length - 1) {
      setCurrentItemIndex(currentItemIndex + 1)
    }
  }, [currentItemIndex, playlist.items.length])

  const toggleMute = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      const newMuted = !playbackState.isMuted
      mediaElement.muted = newMuted
      setPlaybackState(prev => ({ ...prev, isMuted: newMuted }))
    }
  }, [isVideo, playbackState.isMuted])

  const setVolume = useCallback((volume: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.volume = volume / 100
      setPlaybackState(prev => ({ ...prev, volume, isMuted: false }))
    }
  }, [isVideo])

  const seekTo = useCallback((time: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.currentTime = time
      setPlaybackState(prev => ({ ...prev, currentTime: time }))
    }
  }, [isVideo])

  const setPlaybackRate = useCallback((rate: number) => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      mediaElement.playbackRate = rate
      setPlaybackState(prev => ({ ...prev, playbackRate: rate }))
    }
  }, [isVideo])

  const handleLoadedMetadata = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      setPlaybackState(prev => ({
        ...prev,
        duration: mediaElement.duration || 0,
        isLoading: false,
        error: null
      }))
    }
  }, [isVideo])

  const handleTimeUpdate = useCallback(() => {
    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement) {
      setPlaybackState(prev => ({
        ...prev,
        currentTime: mediaElement.currentTime || 0
      }))
    }
  }, [isVideo])

  const handlePlay = useCallback(() => {
    setPlaybackState(prev => ({ ...prev, isPlaying: true }))
  }, [])

  const handlePause = useCallback(() => {
    setPlaybackState(prev => ({ ...prev, isPlaying: false }))
  }, [])

  const handleError = useCallback((e: React.SyntheticEvent<HTMLMediaElement>) => {
    console.error('Media playback error:', e)
    setPlaybackState(prev => ({
      ...prev,
      error: 'Failed to load media. The URL may not be accessible or supported.',
      isLoading: false
    }))
  }, [])

  // Load media when current item changes
  React.useEffect(() => {
    if (!currentItem || !isDirectMedia) {
      setPlaybackState(prev => ({
        ...prev,
        error: currentItem && !isDirectMedia ? 'This URL type is not directly playable. Try opening it in a new tab.' : null,
        isLoading: false
      }))
      return
    }

    const mediaElement = isVideo ? videoRef.current : audioRef.current
    if (mediaElement && currentItem.url) {
      setPlaybackState(prev => ({
        ...prev,
        isLoading: true,
        error: null,
        currentTime: 0,
        duration: 0,
        isPlaying: false
      }))

      // Set the media source
      mediaElement.src = currentItem.url
      mediaElement.load()

      // Set initial volume and playback rate
      mediaElement.volume = playbackState.volume / 100
      mediaElement.muted = playbackState.isMuted
      mediaElement.playbackRate = playbackState.playbackRate
    }
  }, [currentItem, isVideo, isAudio, isDirectMedia, playbackState.volume, playbackState.isMuted, playbackState.playbackRate])

  if (!isOpen) return null

  return (
    <div
      ref={modalRef}
      className={cn(
        "fixed inset-0 z-50 flex items-center justify-center",
        isModernTheme
          ? "bg-black/60 backdrop-blur-md"
          : "bg-black/80 backdrop-blur-sm"
      )}
      onClick={handleBackdropClick}
    >
      <div
        className={cn(
          "import-panel multimedia-modal",
          isModernTheme && "modern-enhanced",
          isFullscreen ? "fullscreen" : "windowed"
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="import-header">
          <div className="flex items-center space-x-3">
            <div className={cn(
              "p-2 rounded-lg",
              isModernTheme
                ? "bg-blue-500/20 text-blue-400"
                : "bg-blue-100 text-blue-600"
            )}>
              <Play className="w-5 h-5" />
            </div>
            <div>
              <h2 className="import-title">
                🎵 {currentItem?.title || 'Multimedia Player'}
              </h2>
              <p className={cn(
                "text-xs",
                isModernTheme ? "text-gray-300" : "text-gray-600"
              )}>
                {currentItemIndex + 1} of {playlist.items.length} • {currentItem?.type || 'Unknown'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowPlaylist(!showPlaylist)}
              className={cn(
                "close-btn",
                showPlaylist && "bg-blue-100 dark:bg-blue-900"
              )}
              aria-label="Toggle playlist"
            >
              <ChevronLeft className={cn(
                "w-4 h-4 transition-transform",
                showPlaylist && "rotate-180"
              )} />
            </button>

            <button
              onClick={toggleFullscreen}
              className="close-btn"
              aria-label={isFullscreen ? "Exit fullscreen" : "Enter fullscreen"}
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </button>

            <button
              onClick={onClose}
              className="close-btn"
              aria-label="Close player"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>

        <div className="import-content"
          style={{
            maxHeight: isFullscreen ? '100vh' : 'calc(85vh - 120px)',
            overflowY: 'auto',
            overflowX: 'hidden'
          }}
        >
          {/* Media Player Section */}
          <div className="import-section">
            <h3 className="section-title">🎬 Media Player</h3>
            <p className="section-description">
              {currentItem ? `Now playing: ${currentItem.title}` : 'Select a media item from the playlist below'}
            </p>

            <div className={cn(
              "relative overflow-hidden rounded-lg border",
              isModernTheme
                ? "bg-black/80 border-white/10"
                : "bg-black border-gray-300"
            )} style={{ minHeight: '300px' }}>
              {playbackState.error ? (
                <div className={cn(
                  "text-center p-8 flex items-center justify-center h-full",
                  isModernTheme ? "text-white" : "text-white"
                )}>
                  <div>
                    <div className="text-red-400 mb-4 text-4xl">⚠️</div>
                    <p className="text-lg mb-2 font-semibold">Playback Error</p>
                    <p className={cn(
                      "text-sm",
                      isModernTheme ? "text-gray-300" : "text-gray-200"
                    )}>{playbackState.error}</p>
                  </div>
                </div>
              ) : currentItem ? (
                <>
                  {isVideo && isDirectMedia && (
                    <video
                      ref={videoRef}
                      className="w-full h-full object-contain"
                      onLoadedMetadata={handleLoadedMetadata}
                      onTimeUpdate={handleTimeUpdate}
                      onPlay={handlePlay}
                      onPause={handlePause}
                      onError={handleError}
                      controls={false}
                    />
                  )}

                  {isAudio && isDirectMedia && (
                    <div className="text-center text-white p-8 flex items-center justify-center h-full">
                      <div>
                        <audio
                          ref={audioRef}
                          onLoadedMetadata={handleLoadedMetadata}
                          onTimeUpdate={handleTimeUpdate}
                          onPlay={handlePlay}
                          onPause={handlePause}
                          onError={handleError}
                          className="hidden"
                        />
                        <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                          <Volume2 className="w-12 h-12" />
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{currentItem.title}</h3>
                        <p className="text-sm opacity-75 truncate max-w-xs">{currentItem.url}</p>
                      </div>
                    </div>
                  )}

                  {!isDirectMedia && (
                    <div className="text-center text-white p-8 flex items-center justify-center h-full">
                      <div>
                        <div className="w-24 h-24 mx-auto mb-4 bg-gradient-to-br from-orange-500 to-red-600 rounded-full flex items-center justify-center">
                          <span className="text-3xl">🔗</span>
                        </div>
                        <h3 className="text-lg font-semibold mb-2">{currentItem.title}</h3>
                        <p className="text-sm opacity-75 mb-4 max-w-xs mx-auto">
                          This content cannot be played directly. Click below to open in a new tab.
                        </p>
                        <button
                          onClick={() => window.open(currentItem.url, '_blank')}
                          className="px-4 py-2 bg-blue-500 hover:bg-blue-600 rounded-lg text-white font-medium transition-colors"
                        >
                          Open in New Tab
                        </button>
                      </div>
                    </div>
                  )}

                  {playbackState.isLoading && (
                    <div className="absolute inset-0 flex items-center justify-center bg-black/50">
                      <div className="text-white text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                        <p>Loading...</p>
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <div className={cn(
                  "text-center p-8 flex items-center justify-center h-full",
                  isModernTheme ? "text-white" : "text-white"
                )}>
                  <div>
                    <div className={cn(
                      "w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center",
                      isModernTheme
                        ? "bg-white/10 text-white/50"
                        : "bg-gray-700 text-gray-400"
                    )}>
                      <Play className="w-8 h-8" />
                    </div>
                    <p className={cn(
                      "text-lg font-medium",
                      isModernTheme ? "text-white/80" : "text-gray-200"
                    )}>No media selected</p>
                    <p className={cn(
                      "text-sm mt-2",
                      isModernTheme ? "text-white/60" : "text-gray-400"
                    )}>Choose a media item from the playlist to start playback</p>
                  </div>
                </div>
              )}
            </div>

            {/* Media Controls */}
            <div className="import-section">
              <h3 className="section-title">🎛️ Playback Controls</h3>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className={cn(
                  "flex justify-between text-sm mb-2",
                  isModernTheme ? "text-gray-300" : "text-gray-600"
                )}>
                  <span>{formatTime(playbackState.currentTime)}</span>
                  <span>{formatTime(playbackState.duration)}</span>
                </div>
                <div className={cn(
                  "w-full rounded-full h-2 cursor-pointer border",
                  isModernTheme
                    ? "bg-white/10 border-white/20"
                    : "bg-gray-200 border-gray-300"
                )}
                  onClick={(e) => {
                    const rect = e.currentTarget.getBoundingClientRect()
                    const percent = (e.clientX - rect.left) / rect.width
                    seekTo(percent * playbackState.duration)
                  }}>
                  <div
                    className={cn(
                      "h-2 rounded-full transition-all",
                      isModernTheme ? "bg-blue-400" : "bg-blue-500"
                    )}
                    style={{
                      width: `${playbackState.duration ? (playbackState.currentTime / playbackState.duration) * 100 : 0}%`
                    }}
                  />
                </div>
              </div>

              {/* Playback Controls */}
              <div className="flex items-center justify-center gap-3 mb-4">
                <button
                  onClick={playPrevious}
                  disabled={currentItemIndex === 0}
                  className={cn(
                    "close-btn",
                    "disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                  aria-label="Previous track"
                  title="Previous track"
                >
                  <SkipBack className="w-5 h-5" />
                </button>

                <button
                  onClick={togglePlayPause}
                  className={cn(
                    "format-option flex items-center justify-center",
                    "w-12 h-12 rounded-full border-2 transition-all",
                    isModernTheme
                      ? "bg-blue-500/20 border-blue-400/50 text-blue-400 hover:bg-blue-500/30 hover:border-blue-400"
                      : "bg-blue-50 border-blue-200 text-blue-600 hover:bg-blue-100 hover:border-blue-300"
                  )}
                  aria-label={playbackState.isPlaying ? "Pause" : "Play"}
                  title={playbackState.isPlaying ? "Pause" : "Play"}
                >
                  {playbackState.isPlaying ? (
                    <Pause className="w-6 h-6" />
                  ) : (
                    <Play className="w-6 h-6" />
                  )}
                </button>

                <button
                  onClick={playNext}
                  disabled={currentItemIndex === playlist.items.length - 1}
                  className={cn(
                    "close-btn",
                    "disabled:opacity-50 disabled:cursor-not-allowed"
                  )}
                  aria-label="Next track"
                  title="Next track"
                >
                  <SkipForward className="w-5 h-5" />
                </button>
              </div>

              {/* Volume and Settings */}
              <div className="flex items-center justify-between gap-4">
                <div className="flex items-center gap-3 flex-1">
                  <button
                    onClick={toggleMute}
                    className="close-btn"
                    aria-label={playbackState.isMuted ? "Unmute" : "Mute"}
                    title={playbackState.isMuted ? "Unmute" : "Mute"}
                  >
                    {playbackState.isMuted ? (
                      <VolumeX className="w-4 h-4" />
                    ) : (
                      <Volume2 className="w-4 h-4" />
                    )}
                  </button>

                  <div className="flex-1 max-w-24">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={playbackState.isMuted ? 0 : playbackState.volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className={cn(
                        "w-full h-2 rounded-lg appearance-none cursor-pointer border",
                        isModernTheme
                          ? "bg-white/10 border-white/20"
                          : "bg-gray-200 border-gray-300"
                      )}
                    />
                  </div>

                  <span className={cn(
                    "text-sm min-w-[3rem] text-center",
                    isModernTheme ? "text-gray-300" : "text-gray-600"
                  )}>
                    {playbackState.isMuted ? 0 : playbackState.volume}%
                  </span>
                </div>

                <div className="flex items-center gap-2">
                  <span className={cn(
                    "text-sm",
                    isModernTheme ? "text-gray-300" : "text-gray-600"
                  )}>Speed:</span>
                  <select
                    value={playbackState.playbackRate}
                    onChange={(e) => setPlaybackRate(Number(e.target.value))}
                    className={cn(
                      "text-sm border rounded px-2 py-1 min-w-[4rem]",
                      isModernTheme
                        ? "bg-black/20 border-white/20 text-white"
                        : "bg-white border-gray-300 text-gray-900"
                    )}
                  >
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1}>1x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                    <option value={2}>2x</option>
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Playlist Section */}
          {showPlaylist && (
            <div className="import-section">
              <h3 className="section-title">🎵 Playlist</h3>
              <p className="section-description">
                {playlist.name} • {playlist.items.length} items
              </p>

              <div className="scrollable-content">
                {playlist.items.map((item, index) => (
                  <button
                    key={item.id}
                    onClick={() => setCurrentItemIndex(index)}
                    className={cn(
                      "format-option w-full text-left mb-2 transition-all",
                      index === currentItemIndex && "active"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className={cn(
                        "w-8 h-8 rounded flex items-center justify-center text-xs font-medium",
                        index === currentItemIndex
                          ? isModernTheme
                            ? "bg-blue-500 text-white"
                            : "bg-blue-500 text-white"
                          : isModernTheme
                            ? "bg-white/10 text-gray-300"
                            : "bg-gray-200 text-gray-600"
                      )}>
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <span className="font-medium block truncate">
                          {item.title}
                        </span>
                        <small className={cn(
                          "block mt-1",
                          isModernTheme ? "text-gray-400" : "text-gray-500"
                        )}>
                          {item.type} • {item.duration ? formatTime(item.duration) : 'Unknown duration'}
                        </small>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
