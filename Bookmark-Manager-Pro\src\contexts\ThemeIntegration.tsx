import React, { useEffect } from 'react'
import { useModernTheme } from './ModernThemeContext'
import { useTheme } from './ThemeContext'

/**
 * Theme Integration Component
 * Bridges the modern theme system with the existing theme system
 * Applies modern styling enhancements to any selected theme
 */
export const ThemeIntegration: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentTheme } = useTheme()
  const { themeMode } = useModernTheme()

  useEffect(() => {
    const root = document.documentElement
    
    // Extract RGB values from hex colors for modern theme usage
    const hexToRgb = (hex: string): string => {
      const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
      if (result) {
        const r = parseInt(result[1], 16)
        const g = parseInt(result[2], 16)
        const b = parseInt(result[3], 16)
        return `${r}, ${g}, ${b}`
      }
      return '59, 130, 246' // fallback blue
    }

    // When modern theme is active, enhance the current theme with modern styling
    if (themeMode === 'modern') {
      // Set RGB versions of colors for modern theme usage
      root.style.setProperty('--primary-bg-rgb', hexToRgb(currentTheme.colors.primaryBg))
      root.style.setProperty('--secondary-bg-rgb', hexToRgb(currentTheme.colors.secondaryBg))
      root.style.setProperty('--accent-color-rgb', hexToRgb(currentTheme.colors.accentColor))
      root.style.setProperty('--text-primary-rgb', hexToRgb(currentTheme.colors.textPrimary))
      
      // Apply modern enhancements while preserving theme colors
      root.style.setProperty('--modern-glass-bg', `rgba(${hexToRgb(currentTheme.colors.primaryBg)}, 0.95)`)
      root.style.setProperty('--modern-glass-secondary', `rgba(${hexToRgb(currentTheme.colors.secondaryBg)}, 0.95)`)
      
      // Enhanced shadows that work with any theme
      root.style.setProperty('--modern-shadow-color', 
        currentTheme.colors.primaryBg === '#ffffff' ? '0, 0, 0' : '255, 255, 255')
      
      // Modern gradients using theme colors
      const accentRgb = hexToRgb(currentTheme.colors.accentColor)
      const accentHoverRgb = hexToRgb(currentTheme.colors.accentHover)
      root.style.setProperty('--modern-gradient-primary', 
        `linear-gradient(135deg, rgba(${accentRgb}, 0.8) 0%, rgba(${accentHoverRgb}, 0.9) 100%)`)
      
      console.log(`🎨 Modern theme enhancements applied to: ${currentTheme.name}`)
    } else {
      // Remove modern enhancements in classic mode
      root.style.removeProperty('--primary-bg-rgb')
      root.style.removeProperty('--secondary-bg-rgb')
      root.style.removeProperty('--accent-color-rgb')
      root.style.removeProperty('--text-primary-rgb')
      root.style.removeProperty('--modern-glass-bg')
      root.style.removeProperty('--modern-glass-secondary')
      root.style.removeProperty('--modern-shadow-color')
      root.style.removeProperty('--modern-gradient-primary')
    }
  }, [currentTheme, themeMode])

  return <>{children}</>
}

/**
 * Hook to get theme-aware modern styling
 */
export const useThemeAwareModernStyling = () => {
  const { currentTheme } = useTheme()
  const { themeMode } = useModernTheme()

  const getModernStyles = (baseStyles: React.CSSProperties = {}): React.CSSProperties => {
    if (themeMode !== 'modern') return baseStyles

    return {
      ...baseStyles,
      background: `rgba(${currentTheme.colors.primaryBg === '#ffffff' ? '255, 255, 255' : '0, 0, 0'}, 0.95)`,
      backdropFilter: 'blur(20px)',
      border: `2px solid ${currentTheme.colors.borderColor}`,
      borderRadius: '12px',
      boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.3s ease',
    }
  }

  const getModernButtonStyles = (variant: 'primary' | 'secondary' = 'primary'): React.CSSProperties => {
    if (themeMode !== 'modern') return {}

    const baseStyles = {
      padding: '8px 16px',
      borderRadius: '8px',
      border: '2px solid transparent',
      fontWeight: 500,
      transition: 'all 0.15s ease',
      cursor: 'pointer',
    }

    if (variant === 'primary') {
      return {
        ...baseStyles,
        background: currentTheme.colors.accentColor,
        color: currentTheme.colors.primaryBg,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      }
    }

    return {
      ...baseStyles,
      background: currentTheme.colors.secondaryBg,
      color: currentTheme.colors.textPrimary,
      borderColor: currentTheme.colors.borderColor,
    }
  }

  const getModernInputStyles = (): React.CSSProperties => {
    if (themeMode !== 'modern') return {}

    return {
      border: `2px solid ${currentTheme.colors.borderColor}`,
      borderRadius: '8px',
      padding: '8px 16px',
      background: currentTheme.colors.primaryBg,
      color: currentTheme.colors.textPrimary,
      transition: 'all 0.15s ease',
      fontSize: '14px',
    }
  }

  return {
    isModernTheme: themeMode === 'modern',
    currentTheme,
    getModernStyles,
    getModernButtonStyles,
    getModernInputStyles,
  }
}

/**
 * Component wrapper that applies modern styling conditionally
 */
export const ModernWrapper: React.FC<{
  children: React.ReactNode
  className?: string
  style?: React.CSSProperties
  variant?: 'panel' | 'button' | 'input' | 'card'
}> = ({ children, className = '', style = {}, variant = 'panel' }) => {
  const { getModernStyles, getModernButtonStyles, getModernInputStyles, isModernTheme } = useThemeAwareModernStyling()

  const getVariantStyles = () => {
    switch (variant) {
      case 'button':
        return getModernButtonStyles()
      case 'input':
        return getModernInputStyles()
      case 'card':
      case 'panel':
      default:
        return getModernStyles()
    }
  }

  const modernStyles = isModernTheme ? getVariantStyles() : {}

  return (
    <div 
      className={`${className} ${isModernTheme ? 'modern-enhanced' : ''}`}
      style={{ ...modernStyles, ...style }}
    >
      {children}
    </div>
  )
}

/**
 * Custom hook for modern theme class generation
 */
export const useModernThemeClasses = (baseClasses: string = ''): string => {
  const { themeMode } = useModernTheme()
  
  const modernClasses = themeMode === 'modern' ? [
    'modern-enhanced',
    'modern-glass',
    'modern-shadows',
    'modern-transitions'
  ].join(' ') : ''

  return `${baseClasses} ${modernClasses}`.trim()
}
