{"rules": {"require_new_terminal_for_tests": true, "require_environment_path_setup": true, "detect_ide_embedded_terminals": true, "enable_session_management": true, "enable_parallel_execution": true}, "windows_terminal": {"executable_path": "wt.exe", "default_profile": "PowerShell", "session_persistence": true, "max_parallel_sessions": 4, "tab_naming_pattern": "Test-{type}-{timestamp}"}, "session_management": {"session_storage_path": "./.terminal_sessions", "auto_cleanup_hours": 24, "session_recovery_enabled": true}, "terminal_detection": {"ide_environment_variables": ["VSCODE_PID", "PYCHARM_HOSTED", "JUPYTER_SERVER_ROOT", "SPYDER_ARGS", "THEIA_WORKSPACE_ROOT"], "ide_process_names": ["code.exe", "pycharm64.exe", "jupyter.exe", "spyder.exe"]}, "test_types": {"unit": {"description": "Unit tests", "command_pattern": "pytest tests/unit/", "timeout_minutes": 10}, "integration": {"description": "Integration tests", "command_pattern": "pytest tests/integration/", "timeout_minutes": 30}, "performance": {"description": "Performance tests", "command_pattern": "pytest tests/performance/", "timeout_minutes": 60}, "advanced": {"description": "Advanced tests", "command_pattern": "pytest tests/advanced/", "timeout_minutes": 45}}, "parallel_execution": {"default_test_types": ["unit", "integration"], "resource_limits": {"max_memory_mb": 2048, "max_cpu_percent": 80}, "coordination": {"wait_between_starts_ms": 1000, "health_check_interval_s": 30}}, "logging": {"level": "INFO", "file_path": "./logs/terminal_management.log", "max_file_size_mb": 10, "backup_count": 5}}