/**
 * Collection Color Test - Verify color assignments are working correctly
 */

import { getCollectionColor, getConsistentCollectionColor } from './collectionColors'

/**
 * Test collection color assignments
 */
export const testCollectionColors = () => {
  console.log('🎨 Testing Collection Color Assignments...')
  
  const testCollections = [
    'Development',
    'Quick Add',
    'Learning',
    'Design',
    'News',
    'Entertainment',
    'Shopping',
    'Finance',
    'Health',
    'Travel',
    'Personal',
    'Business'
  ]
  
  console.log('\n📋 Collection Color Assignments:')
  console.log('================================')
  
  testCollections.forEach(collection => {
    const color = getCollectionColor(collection)
    const consistentColors = getConsistentCollectionColor(collection)
    
    console.log(`${collection.padEnd(15)} → ${color} (Border: ${consistentColors.borderColor === color ? '✅' : '❌'})`)
  })
  
  // Test consistency
  console.log('\n🔍 Testing Consistency:')
  console.log('=======================')
  
  const developmentColor1 = getCollectionColor('Development')
  const developmentColor2 = getCollectionColor('development')
  const developmentColor3 = getCollectionColor('DEVELOPMENT')
  
  console.log(`Development (case test): ${developmentColor1} | ${developmentColor2} | ${developmentColor3}`)
  console.log(`Consistent: ${developmentColor1 === developmentColor2 && developmentColor2 === developmentColor3 ? '✅' : '❌'}`)
  
  // Test Quick Add specifically
  const quickAddColor = getCollectionColor('Quick Add')
  console.log(`\n⚡ Quick Add Color: ${quickAddColor}`)
  console.log(`Expected: #84CC16 (Lime) - Match: ${quickAddColor === '#84CC16' ? '✅' : '❌'}`)
  
  // Test Development specifically
  const devColor = getCollectionColor('Development')
  console.log(`💻 Development Color: ${devColor}`)
  console.log(`Expected: #3B82F6 (Blue) - Match: ${devColor === '#3B82F6' ? '✅' : '❌'}`)
  
  console.log('\n🎨 Color Test Complete!')
}

/**
 * Auto-run test in development
 */
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  // Run test after a short delay to ensure everything is loaded
  setTimeout(testCollectionColors, 1000)
}
