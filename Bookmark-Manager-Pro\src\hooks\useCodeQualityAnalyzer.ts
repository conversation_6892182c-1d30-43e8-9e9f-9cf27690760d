import * as fs from 'fs';
import { useEffect, useRef, useState } from 'react';

interface CodeAnalysisResult {
  file: string;
  suggestions: CodeSuggestion[];
  timestamp: Date;
  severity: 'low' | 'medium' | 'high';
}

interface CodeSuggestion {
  type: 'code-smell' | 'design-pattern' | 'best-practice' | 'performance' | 'readability' | 'maintainability';
  line: number;
  column?: number;
  message: string;
  suggestion: string;
  example?: string;
}

interface UseCodeQualityAnalyzerOptions {
  watchPaths?: string[];
  excludePatterns?: string[];
  debounceMs?: number;
  enableRealTimeAnalysis?: boolean;
  analysisTypes?: CodeSuggestion['type'][];
}

interface UseCodeQualityAnalyzerReturn {
  analysisResults: CodeAnalysisResult[];
  isAnalyzing: boolean;
  startWatching: () => void;
  stopWatching: () => void;
  analyzeFile: (filePath: string) => Promise<CodeAnalysisResult | null>;
  clearResults: () => void;
  getResultsForFile: (filePath: string) => CodeAnalysisResult[];
}

/**
 * Custom hook for testing code quality analysis and file watching functionality
 * Monitors source code files for changes and provides quality improvement suggestions
 */
export const useCodeQualityAnalyzer = (options: UseCodeQualityAnalyzerOptions = {}): UseCodeQualityAnalyzerReturn => {
  const {
    watchPaths = ['src/**/*.{ts,tsx,js,jsx}'],
    excludePatterns = ['node_modules/**', 'dist/**', 'build/**', '**/*.test.*', '**/*.spec.*'],
    debounceMs = 1000,
    enableRealTimeAnalysis = true,
    analysisTypes = ['code-smell', 'design-pattern', 'best-practice', 'performance', 'readability', 'maintainability']
  } = options;

  const [analysisResults, setAnalysisResults] = useState<CodeAnalysisResult[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const watcherRef = useRef<NodeJS.Timeout | null>(null);
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * Analyzes code content for potential improvements
   */
  const analyzeCodeContent = (content: string, filePath: string): CodeSuggestion[] => {
    const suggestions: CodeSuggestion[] = [];
    const lines = content.split('\n');

    lines.forEach((line, index) => {
      const lineNumber = index + 1;
      const trimmedLine = line.trim();

      // Code smell detection
      if (analysisTypes.includes('code-smell')) {
        // Long parameter lists
        if (trimmedLine.includes('function') || trimmedLine.includes('=>')) {
          const paramMatch = trimmedLine.match(/\(([^)]+)\)/);
          if (paramMatch && paramMatch[1].split(',').length > 4) {
            suggestions.push({
              type: 'code-smell',
              line: lineNumber,
              message: 'Function has too many parameters',
              suggestion: 'Consider using an options object or breaking down the function',
              example: 'function example(options: { param1: string, param2: number, ... }) { }'
            });
          }
        }

        // Magic numbers
        const magicNumberMatch = trimmedLine.match(/\b\d{2,}\b/);
        if (magicNumberMatch && !trimmedLine.includes('const') && !trimmedLine.includes('let')) {
          suggestions.push({
            type: 'code-smell',
            line: lineNumber,
            message: 'Magic number detected',
            suggestion: 'Extract magic numbers into named constants',
            example: 'const MAX_RETRY_ATTEMPTS = 3;'
          });
        }

        // Long lines
        if (line.length > 120) {
          suggestions.push({
            type: 'readability',
            line: lineNumber,
            message: 'Line is too long',
            suggestion: 'Break long lines into multiple lines for better readability'
          });
        }
      }

      // Design pattern suggestions
      if (analysisTypes.includes('design-pattern')) {
        // Factory pattern opportunity
        if (trimmedLine.includes('new ') && trimmedLine.includes('switch') || trimmedLine.includes('if')) {
          suggestions.push({
            type: 'design-pattern',
            line: lineNumber,
            message: 'Consider using Factory pattern',
            suggestion: 'Replace conditional object creation with Factory pattern',
            example: 'class ObjectFactory { static create(type: string) { ... } }'
          });
        }

        // Observer pattern for event handling
        if (trimmedLine.includes('addEventListener') || trimmedLine.includes('on')) {
          suggestions.push({
            type: 'design-pattern',
            line: lineNumber,
            message: 'Consider Observer pattern for event management',
            suggestion: 'Implement Observer pattern for better event handling'
          });
        }
      }

      // Best practices
      if (analysisTypes.includes('best-practice')) {
        // Missing error handling
        if (trimmedLine.includes('fetch(') || trimmedLine.includes('axios.') || trimmedLine.includes('await ')) {
          const hasErrorHandling = content.includes('try') || content.includes('catch') || content.includes('.catch(');
          if (!hasErrorHandling) {
            suggestions.push({
              type: 'best-practice',
              line: lineNumber,
              message: 'Missing error handling for async operation',
              suggestion: 'Add proper error handling with try-catch or .catch()',
              example: 'try { await operation(); } catch (error) { handleError(error); }'
            });
          }
        }

        // Console.log in production code
        if (trimmedLine.includes('console.log') || trimmedLine.includes('console.warn')) {
          suggestions.push({
            type: 'best-practice',
            line: lineNumber,
            message: 'Console statement found',
            suggestion: 'Remove console statements or replace with proper logging',
            example: 'logger.info("message") or remove entirely'
          });
        }
      }

      // Performance optimizations
      if (analysisTypes.includes('performance')) {
        // Inefficient array operations
        if (trimmedLine.includes('.find(') && trimmedLine.includes('.filter(')) {
          suggestions.push({
            type: 'performance',
            line: lineNumber,
            message: 'Chained array operations detected',
            suggestion: 'Consider combining operations or using more efficient alternatives',
            example: 'Use reduce() or a single loop instead of chaining'
          });
        }

        // Missing React.memo or useMemo
        if (trimmedLine.includes('export const') && trimmedLine.includes('= (') && filePath.includes('.tsx')) {
          suggestions.push({
            type: 'performance',
            line: lineNumber,
            message: 'Consider memoization for React component',
            suggestion: 'Wrap component with React.memo() if props are stable',
            example: 'export const Component = React.memo((props) => { ... });'
          });
        }
      }

      // Maintainability improvements
      if (analysisTypes.includes('maintainability')) {
        // Missing TypeScript types
        if (trimmedLine.includes('any') && !trimmedLine.includes('// @ts-ignore')) {
          suggestions.push({
            type: 'maintainability',
            line: lineNumber,
            message: 'Using "any" type',
            suggestion: 'Replace "any" with specific types for better type safety',
            example: 'interface SpecificType { property: string; }'
          });
        }

        // Large functions
        if (trimmedLine.includes('function') || trimmedLine.includes('=>')) {
          const functionContent = content.substring(content.indexOf(trimmedLine));
          const braceCount = (functionContent.match(/{/g) || []).length;
          if (braceCount > 10) {
            suggestions.push({
              type: 'maintainability',
              line: lineNumber,
              message: 'Function appears to be too large',
              suggestion: 'Break down large functions into smaller, focused functions'
            });
          }
        }
      }
    });

    return suggestions;
  };

  /**
   * Analyzes a specific file for code quality issues
   */
  const analyzeFile = async (filePath: string): Promise<CodeAnalysisResult | null> => {
    try {
      setIsAnalyzing(true);
      
      // Check if file should be excluded based on patterns
      const shouldExclude = excludePatterns.some(pattern => {
        const regex = new RegExp(pattern.replace(/\*\*/g, '.*').replace(/\*/g, '[^/]*'));
        return regex.test(filePath);
      });
      
      if (shouldExclude) {
        console.log(`File excluded from analysis: ${filePath}`);
        return null;
      }
      
      if (!fs.existsSync(filePath)) {
        console.warn(`File not found: ${filePath}`);
        return null;
      }

      const content = fs.readFileSync(filePath, 'utf-8');
      const suggestions = analyzeCodeContent(content, filePath);

      const result: CodeAnalysisResult = {
        file: filePath,
        suggestions,
        timestamp: new Date(),
        severity: suggestions.length > 10 ? 'high' : suggestions.length > 5 ? 'medium' : 'low'
      };

      setAnalysisResults(prev => {
        const filtered = prev.filter(r => r.file !== filePath);
        return [...filtered, result];
      });

      return result;
    } catch (error) {
      console.error(`Error analyzing file ${filePath}:`, error);
      return null;
    } finally {
      setIsAnalyzing(false);
    }
  };

  /**
   * Handles file change events with debouncing
   */
  const handleFileChange = (filePath: string) => {
    if (!enableRealTimeAnalysis) return;

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    debounceTimerRef.current = setTimeout(() => {
      analyzeFile(filePath);
    }, debounceMs);
  };

  /**
   * Starts watching files for changes (simplified implementation)
   */
  const startWatching = () => {
    if (watcherRef.current) {
      clearInterval(watcherRef.current);
    }

    try {
      // Simple polling-based file watching (fallback when chokidar is not available)
      watcherRef.current = setInterval(() => {
        // In a real implementation, you would check file modification times
        // For demonstration, simulate file change detection
        if (enableRealTimeAnalysis) {
          console.log(`File watching active (polling mode) - Watching paths: ${watchPaths.join(', ')}`);
          console.log(`File change handler available with ${debounceMs}ms debounce`);
          
          // Demonstrate handleFileChange function availability
          // In a real file watcher, this would be called when files actually change
          if (typeof handleFileChange === 'function') {
            console.log('File change handler is ready for file system events');
          }
        }
      }, 5000);

      console.log(`Code quality analyzer started watching files (polling mode) - Paths: ${watchPaths.join(', ')}`);
      console.log(`File change handler ready - debounce: ${debounceMs}ms`);
    } catch (error) {
      console.error('Error starting file watcher:', error);
    }
  };

  /**
   * Stops watching files
   */
  const stopWatching = () => {
    if (watcherRef.current) {
      clearInterval(watcherRef.current);
      watcherRef.current = null;
    }

    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
      debounceTimerRef.current = null;
    }

    console.log('Code quality analyzer stopped watching files');
  };

  /**
   * Clears all analysis results
   */
  const clearResults = () => {
    setAnalysisResults([]);
  };

  /**
   * Gets analysis results for a specific file
   */
  const getResultsForFile = (filePath: string): CodeAnalysisResult[] => {
    return analysisResults.filter(result => result.file === filePath);
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopWatching();
    };
  }, []);

  return {
    analysisResults,
    isAnalyzing,
    startWatching,
    stopWatching,
    analyzeFile,
    clearResults,
    getResultsForFile
  };
};

export default useCodeQualityAnalyzer;