import os
import sys
import json
import psutil
import subprocess
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging
from datetime import datetime

class TerminalComplianceChecker:
    """Validates terminal execution rules and environment compliance."""
    
    def __init__(self, config_path: str = "terminal_rules_enhanced.json"):
        self.config = self.load_config(config_path)
        self.setup_logging()
        self.violations = []
        
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"Warning: Configuration file {config_path} not found. Using defaults.")
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            "rules": {
                "require_new_terminal_for_tests": True,
                "require_environment_path_setup": True,
                "detect_ide_embedded_terminals": True,
                "enable_session_management": True
            },
            "terminal_detection": {
                "ide_environment_variables": [
                    "VSCODE_PID", "PYCHARM_HOSTED", "JUPYTER_SERVER_ROOT",
                    "SPYDER_ARGS", "THEIA_WORKSPACE_ROOT"
                ],
                "ide_process_names": [
                    "code.exe", "pycharm64.exe", "jupyter.exe", "spyder.exe"
                ]
            },
            "logging": {
                "level": "INFO",
                "file_path": "./logs/terminal_compliance.log"
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config.get("logging", {})
        log_level = getattr(logging, log_config.get("level", "INFO"))
        log_file = log_config.get("file_path", "./logs/terminal_compliance.log")
        
        # Ensure log directory exists
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def check_ide_environment(self) -> Tuple[bool, List[str]]:
        """Check if running in an IDE environment."""
        detected_ides = []
        
        # Check environment variables
        ide_env_vars = self.config["terminal_detection"]["ide_environment_variables"]
        for env_var in ide_env_vars:
            if os.getenv(env_var):
                detected_ides.append(f"Environment variable: {env_var}")
                self.logger.warning(f"IDE environment detected via {env_var}")
        
        # Check running processes
        ide_processes = self.config["terminal_detection"]["ide_process_names"]
        for process in psutil.process_iter(['name']):
            try:
                if process.info['name'] in ide_processes:
                    detected_ides.append(f"Process: {process.info['name']}")
                    self.logger.warning(f"IDE process detected: {process.info['name']}")
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return len(detected_ides) > 0, detected_ides
    
    def check_terminal_type(self) -> Tuple[str, Dict]:
        """Determine the current terminal type and characteristics."""
        terminal_info = {
            "type": "unknown",
            "parent_process": None,
            "is_embedded": False,
            "supports_tabs": False
        }
        
        try:
            # Get current process and its parent
            current_process = psutil.Process()
            parent_process = current_process.parent()
            
            if parent_process:
                parent_name = parent_process.name().lower()
                terminal_info["parent_process"] = parent_name
                
                # Detect terminal types
                if "windowsterminal" in parent_name or "wt.exe" in parent_name:
                    terminal_info["type"] = "windows_terminal"
                    terminal_info["supports_tabs"] = True
                elif "powershell" in parent_name:
                    terminal_info["type"] = "powershell"
                elif "cmd" in parent_name:
                    terminal_info["type"] = "cmd"
                elif "conhost" in parent_name:
                    terminal_info["type"] = "conhost"
                elif any(ide in parent_name for ide in ["code", "pycharm", "jupyter", "spyder"]):
                    terminal_info["type"] = "ide_embedded"
                    terminal_info["is_embedded"] = True
                
                self.logger.info(f"Terminal type detected: {terminal_info['type']}")
        
        except Exception as e:
            self.logger.error(f"Error detecting terminal type: {e}")
        
        return terminal_info["type"], terminal_info
    
    def check_windows_terminal_availability(self) -> bool:
        """Check if Windows Terminal is available."""
        try:
            result = subprocess.run(
                ["wt.exe", "--version"],
                capture_output=True,
                text=True,
                timeout=5
            )
            if result.returncode == 0:
                self.logger.info(f"Windows Terminal available: {result.stdout.strip()}")
                return True
        except (subprocess.TimeoutExpired, FileNotFoundError, subprocess.SubprocessError) as e:
            self.logger.warning(f"Windows Terminal not available: {e}")
        
        return False
    
    def check_environment_path_setup(self) -> Tuple[bool, List[str]]:
        """Check if required paths are in environment."""
        issues = []
        path_env = os.getenv("PATH", "")
        
        # Check for Python in PATH
        try:
            result = subprocess.run(["python", "--version"], capture_output=True, text=True)
            if result.returncode != 0:
                issues.append("Python not found in PATH")
        except FileNotFoundError:
            issues.append("Python not found in PATH")
        
        # Check for PowerShell
        try:
            result = subprocess.run(["powershell", "-Command", "$PSVersionTable.PSVersion"], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                issues.append("PowerShell not accessible")
        except FileNotFoundError:
            issues.append("PowerShell not found")
        
        # Check current working directory
        cwd = os.getcwd()
        if not os.access(cwd, os.R_OK | os.W_OK):
            issues.append(f"Insufficient permissions in current directory: {cwd}")
        
        return len(issues) == 0, issues
    
    def validate_session_management_setup(self) -> Tuple[bool, List[str]]:
        """Validate session management configuration."""
        issues = []
        
        # Check if session management is enabled
        if not self.config["rules"].get("enable_session_management", False):
            issues.append("Session management is disabled")
            return False, issues
        
        # Check session storage directory
        session_config = self.config.get("session_management", {})
        storage_path = session_config.get("session_storage_path", "./.terminal_sessions")
        
        try:
            Path(storage_path).mkdir(parents=True, exist_ok=True)
            if not os.access(storage_path, os.R_OK | os.W_OK):
                issues.append(f"Insufficient permissions for session storage: {storage_path}")
        except Exception as e:
            issues.append(f"Cannot create session storage directory: {e}")
        
        # Check for required Python packages
        required_packages = ["psutil"]
        for package in required_packages:
            try:
                result = subprocess.run(
                    ["python", "-c", f"import {package}"],
                    capture_output=True,
                    text=True
                )
                if result.returncode != 0:
                    issues.append(f"Required Python package missing: {package}")
            except Exception as e:
                issues.append(f"Cannot check Python package {package}: {e}")
        
        return len(issues) == 0, issues
    
    def run_compliance_check(self) -> Dict:
        """Run comprehensive compliance check."""
        self.logger.info("Starting terminal compliance check")
        
        results = {
            "timestamp": datetime.now().isoformat(),
            "overall_compliant": True,
            "checks": {},
            "violations": [],
            "recommendations": []
        }
        
        # Check 1: IDE Environment Detection
        ide_detected, ide_details = self.check_ide_environment()
        results["checks"]["ide_environment"] = {
            "passed": not ide_detected if self.config["rules"]["detect_ide_embedded_terminals"] else True,
            "details": ide_details,
            "message": "IDE environment detected" if ide_detected else "No IDE environment detected"
        }
        
        if ide_detected and self.config["rules"]["require_new_terminal_for_tests"]:
            results["violations"].append("Running in IDE environment but new terminal required for tests")
            results["recommendations"].append("Use Windows Terminal or external PowerShell for test execution")
            results["overall_compliant"] = False
        
        # Check 2: Terminal Type
        terminal_type, terminal_info = self.check_terminal_type()
        results["checks"]["terminal_type"] = {
            "passed": terminal_type != "ide_embedded" if self.config["rules"]["require_new_terminal_for_tests"] else True,
            "details": terminal_info,
            "message": f"Terminal type: {terminal_type}"
        }
        
        if terminal_info["is_embedded"] and self.config["rules"]["require_new_terminal_for_tests"]:
            results["violations"].append("Using embedded IDE terminal but external terminal required")
            results["overall_compliant"] = False
        
        # Check 3: Windows Terminal Availability
        wt_available = self.check_windows_terminal_availability()
        results["checks"]["windows_terminal"] = {
            "passed": wt_available,
            "details": {"available": wt_available},
            "message": "Windows Terminal available" if wt_available else "Windows Terminal not available"
        }
        
        if not wt_available:
            results["recommendations"].append("Install Windows Terminal for enhanced session management")
        
        # Check 4: Environment Path Setup
        path_ok, path_issues = self.check_environment_path_setup()
        results["checks"]["environment_path"] = {
            "passed": path_ok,
            "details": {"issues": path_issues},
            "message": "Environment path setup correct" if path_ok else f"Path issues: {', '.join(path_issues)}"
        }
        
        if not path_ok:
            results["violations"].extend(path_issues)
            results["overall_compliant"] = False
        
        # Check 5: Session Management Setup
        session_ok, session_issues = self.validate_session_management_setup()
        results["checks"]["session_management"] = {
            "passed": session_ok,
            "details": {"issues": session_issues},
            "message": "Session management setup correct" if session_ok else f"Session issues: {', '.join(session_issues)}"
        }
        
        if not session_ok:
            results["violations"].extend(session_issues)
            if self.config["rules"]["enable_session_management"]:
                results["overall_compliant"] = False
        
        # Log results
        if results["overall_compliant"]:
            self.logger.info("✅ Terminal compliance check PASSED")
        else:
            self.logger.error("❌ Terminal compliance check FAILED")
            for violation in results["violations"]:
                self.logger.error(f"  - {violation}")
        
        return results
    
    def generate_compliance_report(self, output_file: str = None) -> str:
        """Generate a detailed compliance report."""
        results = self.run_compliance_check()
        
        report = []
        report.append("=" * 60)
        report.append("TERMINAL COMPLIANCE REPORT")
        report.append("=" * 60)
        report.append(f"Timestamp: {results['timestamp']}")
        report.append(f"Overall Status: {'✅ COMPLIANT' if results['overall_compliant'] else '❌ NON-COMPLIANT'}")
        report.append("")
        
        # Individual checks
        report.append("INDIVIDUAL CHECKS:")
        report.append("-" * 30)
        for check_name, check_result in results["checks"].items():
            status = "✅ PASS" if check_result["passed"] else "❌ FAIL"
            report.append(f"{check_name.upper()}: {status}")
            report.append(f"  Message: {check_result['message']}")
            if check_result["details"]:
                report.append(f"  Details: {check_result['details']}")
            report.append("")
        
        # Violations
        if results["violations"]:
            report.append("VIOLATIONS:")
            report.append("-" * 30)
            for i, violation in enumerate(results["violations"], 1):
                report.append(f"{i}. {violation}")
            report.append("")
        
        # Recommendations
        if results["recommendations"]:
            report.append("RECOMMENDATIONS:")
            report.append("-" * 30)
            for i, recommendation in enumerate(results["recommendations"], 1):
                report.append(f"{i}. {recommendation}")
            report.append("")
        
        report.append("=" * 60)
        
        report_text = "\n".join(report)
        
        if output_file:
            with open(output_file, 'w') as f:
                f.write(report_text)
            self.logger.info(f"Compliance report saved to: {output_file}")
        
        return report_text

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="Terminal Compliance Checker")
    parser.add_argument("--config", default="terminal_rules_enhanced.json", 
                       help="Configuration file path")
    parser.add_argument("--report", help="Output file for compliance report")
    parser.add_argument("--quiet", action="store_true", help="Suppress console output")
    
    args = parser.parse_args()
    
    checker = TerminalComplianceChecker(args.config)
    
    if args.quiet:
        # Just run the check and return exit code
        results = checker.run_compliance_check()
        sys.exit(0 if results["overall_compliant"] else 1)
    else:
        # Generate and display report
        report = checker.generate_compliance_report(args.report)
        print(report)
        
        # Exit with appropriate code
        results = checker.run_compliance_check()
        sys.exit(0 if results["overall_compliant"] else 1)

if __name__ == "__main__":
    main()