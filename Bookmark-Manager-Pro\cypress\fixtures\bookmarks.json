{"singleBookmark": {"id": "1", "title": "Example Website", "url": "https://example.com", "dateAdded": "2024-01-01T00:00:00.000Z", "tags": ["example", "test"], "summary": "A simple example website for testing purposes", "favicon": "https://example.com/favicon.ico"}, "multipleBookmarks": [{"id": "1", "title": "React Documentation", "url": "https://react.dev", "dateAdded": "2024-01-01T00:00:00.000Z", "tags": ["react", "documentation", "frontend"], "summary": "Official React documentation with guides and API reference", "favicon": "https://react.dev/favicon.ico"}, {"id": "2", "title": "TypeScript Handbook", "url": "https://www.typescriptlang.org/docs/", "dateAdded": "2024-01-02T00:00:00.000Z", "tags": ["typescript", "documentation", "javascript"], "summary": "Comprehensive guide to TypeScript language features", "favicon": "https://www.typescriptlang.org/favicon.ico"}, {"id": "3", "title": "Jest Testing Framework", "url": "https://jestjs.io", "dateAdded": "2024-01-03T00:00:00.000Z", "tags": ["jest", "testing", "javascript"], "summary": "Delightful JavaScript testing framework", "favicon": "https://jestjs.io/favicon.ico"}, {"id": "4", "title": "Cypress Testing", "url": "https://www.cypress.io", "dateAdded": "2024-01-04T00:00:00.000Z", "tags": ["cypress", "testing", "e2e"], "summary": "Fast, easy and reliable testing for anything that runs in a browser", "favicon": "https://www.cypress.io/favicon.ico"}, {"id": "5", "title": "Vite Build Tool", "url": "https://vitejs.dev", "dateAdded": "2024-01-05T00:00:00.000Z", "tags": ["vite", "build-tool", "frontend"], "summary": "Next generation frontend tooling", "favicon": "https://vitejs.dev/favicon.ico"}], "youtubeBookmark": {"id": "youtube-1", "title": "React Tutorial for Beginners", "url": "https://www.youtube.com/watch?v=SqcY0GlETPk", "dateAdded": "2024-01-06T00:00:00.000Z", "tags": ["react", "tutorial", "beginner"], "summary": "Complete React tutorial for beginners", "youtubeData": {"videoId": "SqcY0GlETPk", "title": "React Tutorial for Beginners - <PERSON>rn React in 1 Hour", "channelTitle": "Programming with <PERSON><PERSON>", "thumbnailUrl": "https://img.youtube.com/vi/SqcY0GlETPk/maxresdefault.jpg", "duration": "PT1H8M32S", "viewCount": "2500000", "publishedAt": "2023-03-15T10:00:00Z"}}, "bookmarkWithoutSummary": {"id": "no-summary-1", "title": "GitHub Repository", "url": "https://github.com/user/repo", "dateAdded": "2024-01-07T00:00:00.000Z", "tags": ["github", "repository"], "favicon": "https://github.com/favicon.ico"}, "bookmarkWithoutTags": {"id": "no-tags-1", "title": "Stack Overflow Question", "url": "https://stackoverflow.com/questions/12345", "dateAdded": "2024-01-08T00:00:00.000Z", "tags": [], "summary": "How to implement a specific feature in React", "favicon": "https://stackoverflow.com/favicon.ico"}, "largeDataset": {"bookmarks": [], "count": 100}, "searchTestData": [{"id": "search-1", "title": "React Hooks Guide", "url": "https://react.dev/hooks", "dateAdded": "2024-01-09T00:00:00.000Z", "tags": ["react", "hooks", "guide"], "summary": "Complete guide to React Hooks"}, {"id": "search-2", "title": "Vue.js Documentation", "url": "https://vuejs.org", "dateAdded": "2024-01-10T00:00:00.000Z", "tags": ["vue", "documentation", "frontend"], "summary": "Progressive JavaScript framework"}, {"id": "search-3", "title": "Angular Tutorial", "url": "https://angular.io/tutorial", "dateAdded": "2024-01-11T00:00:00.000Z", "tags": ["angular", "tutorial", "frontend"], "summary": "Tour of Heroes tutorial for <PERSON><PERSON>"}], "tagFilterTestData": [{"id": "tag-1", "title": "Frontend Development", "url": "https://frontend.dev", "dateAdded": "2024-01-12T00:00:00.000Z", "tags": ["frontend", "development", "web"], "summary": "Frontend development resources"}, {"id": "tag-2", "title": "Backend Development", "url": "https://backend.dev", "dateAdded": "2024-01-13T00:00:00.000Z", "tags": ["backend", "development", "api"], "summary": "Backend development resources"}, {"id": "tag-3", "title": "Full Stack Development", "url": "https://fullstack.dev", "dateAdded": "2024-01-14T00:00:00.000Z", "tags": ["frontend", "backend", "fullstack"], "summary": "Full stack development guide"}], "performanceTestData": {"count": 1000, "batchSize": 50}, "exportTestData": {"format": "json", "includeMetadata": true, "filename": "bookmarks-export-test.json"}, "importTestData": {"validFile": "valid-bookmarks.json", "invalidFile": "invalid-bookmarks.json", "emptyFile": "empty-bookmarks.json"}}