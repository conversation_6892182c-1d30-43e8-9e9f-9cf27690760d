import React, { useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import '../styles/optimized-panels.css'

interface HealthCheckPanelProps {
  isOpen: boolean
  onClose: () => void
  onOpenPanel?: (panelType: string) => void
}

export const HealthCheckPanelOptimized: React.FC<HealthCheckPanelProps> = ({ isOpen, onClose, onOpenPanel }) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const [isChecking, setIsChecking] = useState(false)
  const [checkProgress, setCheckProgress] = useState(0)
  const [results, setResults] = useState<any>(null)
  const [autoFixEnabled, setAutoFixEnabled] = useState(true)
  const [detailedResultsExpanded, setDetailedResultsExpanded] = useState(false)
  const [settingsExpanded, setSettingsExpanded] = useState(false)

  const startHealthCheck = () => {
    setIsChecking(true)
    setCheckProgress(0)
    // Health check logic here
    console.log('Starting health check...')
  }

  const fixIssues = () => {
    console.log('Fixing issues...')
  }

  return (
    <div className="import-panel">
      {/* Quick Health Check */}
      <div className="import-section">
        <h3 className="section-title">{t('health.quickCheck')}</h3>
        <p className="section-description">{t('health.quickCheckDescription')}</p>
        
        <div className="controls-grid">
          <button 
            className="btn-compact primary" 
            onClick={startHealthCheck}
            disabled={isChecking}
          >
            {isChecking ? t('health.checking') : t('health.startCheck')}
          </button>
          <button className="btn-compact" onClick={fixIssues}>
            {t('health.autoFix')}
          </button>
        </div>

        {isChecking && (
          <div className="progress-compact">
            <div className="progress-bar" style={{ width: `${checkProgress}%` }} />
          </div>
        )}
      </div>

      {/* Health Summary */}
      {results && (
        <div className="import-section">
          <h3 className="section-title">{t('health.summary')}</h3>
          
          <div className="controls-grid">
            <div className="status-compact">
              <div className="status-icon success" />
              <span>{results.healthy || 0} {t('health.healthy')}</span>
            </div>
            <div className="status-compact">
              <div className="status-icon warning" />
              <span>{results.warnings || 0} {t('health.warnings')}</span>
            </div>
          </div>
          
          <div className="controls-grid">
            <div className="status-compact">
              <div className="status-icon error" />
              <span>{results.broken || 0} {t('health.broken')}</span>
            </div>
            <div className="status-compact">
              <div className="status-icon info" />
              <span>{results.duplicates || 0} {t('health.duplicates')}</span>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions */}
      <div className="import-section">
        <h3 className="section-title">{t('health.quickActions')}</h3>
        
        <div className="button-grid">
          <button className="btn-compact">{t('health.removeBroken')}</button>
          <button className="btn-compact">{t('health.fixDuplicates')}</button>
          <button className="btn-compact">{t('health.updateFavicons')}</button>
        </div>
        
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('export')}>
            {t('health.exportResults')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('summary')}>
            {t('health.generateReport')}
          </button>
        </div>
      </div>

      {/* Check Settings - Collapsible */}
      <div className={`collapsible-section ${settingsExpanded ? 'expanded' : ''}`}>
        <button 
          className="collapsible-header"
          onClick={() => setSettingsExpanded(!settingsExpanded)}
        >
          <span>{t('health.checkSettings')}</span>
          <span className="collapsible-icon">▼</span>
        </button>
        <div className="collapsible-content">
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('health.checkUrls')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('health.checkFavicons')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" defaultChecked />
              {t('health.findDuplicates')}
            </label>
          </div>
          <div className="form-row">
            <label>
              <input type="checkbox" />
              {t('health.deepScan')}
            </label>
          </div>
          <div className="form-row">
            <label>{t('health.timeout')}:</label>
            <input type="number" defaultValue="10" className="input-compact" />
          </div>
          <div className="form-row">
            <label>
              <input 
                type="checkbox" 
                checked={autoFixEnabled}
                onChange={(e) => setAutoFixEnabled(e.target.checked)}
              />
              {t('health.autoFix')}
            </label>
          </div>
        </div>
      </div>

      {/* Detailed Results - Collapsible */}
      {results && (
        <div className={`collapsible-section ${detailedResultsExpanded ? 'expanded' : ''}`}>
          <button 
            className="collapsible-header"
            onClick={() => setDetailedResultsExpanded(!detailedResultsExpanded)}
          >
            <span>{t('health.detailedResults')}</span>
            <span className="collapsible-icon">▼</span>
          </button>
          <div className="collapsible-content">
            <div className="scrollable-content">
              <div className="status-compact">
                <div className="status-icon error" />
                <span>{t('health.brokenLinks')}: {results.brokenLinks?.length || 0}</span>
              </div>
              <div className="status-compact">
                <div className="status-icon warning" />
                <span>{t('health.slowLinks')}: {results.slowLinks?.length || 0}</span>
              </div>
              <div className="status-compact">
                <div className="status-icon info" />
                <span>{t('health.redirects')}: {results.redirects?.length || 0}</span>
              </div>
              <div className="status-compact">
                <div className="status-icon warning" />
                <span>{t('health.missingFavicons')}: {results.missingFavicons?.length || 0}</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Navigation */}
      <div className="import-section">
        <h3 className="section-title">{t('health.relatedTools')}</h3>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('import')}>
            {t('health.importBookmarks')}
          </button>
          <button className="btn-compact" onClick={() => onOpenPanel?.('domain')}>
            {t('health.domainAnalysis')}
          </button>
        </div>
        <div className="controls-grid">
          <button className="btn-compact" onClick={() => onOpenPanel?.('content')}>
            {t('health.contentAnalysis')}
          </button>
          <button className="btn-compact" onClick={onClose}>
            {t('common.close')}
          </button>
        </div>
      </div>
    </div>
  )
}