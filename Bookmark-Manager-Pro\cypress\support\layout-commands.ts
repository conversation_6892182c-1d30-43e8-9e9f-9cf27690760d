// Layout testing support commands for Cypress

// Command to check element overflow
Cypress.Commands.add('checkElementOverflow', (selector: string) => {
  cy.get(selector).then($el => {
    const element = $el[0];
    const hasHorizontalOverflow = element.scrollWidth > element.clientWidth;
    const hasVerticalOverflow = element.scrollHeight > element.clientHeight;
    
    if (hasHorizontalOverflow) {
      cy.log(`⚠️ Horizontal overflow detected in ${selector}`);
      cy.log(`ScrollWidth: ${element.scrollWidth}, ClientWidth: ${element.clientWidth}`);
    }
    
    if (hasVerticalOverflow) {
      cy.log(`⚠️ Vertical overflow detected in ${selector}`);
      cy.log(`ScrollHeight: ${element.scrollHeight}, ClientHeight: ${element.clientHeight}`);
    }
    
    expect(hasHorizontalOverflow, `No horizontal overflow in ${selector}`).to.be.false;
  });
});

// Command to check responsive breakpoints
Cypress.Commands.add('testResponsiveBreakpoints', (breakpoints: Array<{name: string, width: number, height: number}>) => {
  breakpoints.forEach(breakpoint => {
    cy.viewport(breakpoint.width, breakpoint.height);
    cy.log(`Testing ${breakpoint.name} viewport: ${breakpoint.width}x${breakpoint.height}`);
    
    // Wait for layout to settle
    cy.wait(500);
    
    // Check that main content is visible
    cy.get('body').should('be.visible');
    
    // Check for horizontal scrollbar
    cy.window().then(win => {
      const hasHorizontalScroll = win.document.body.scrollWidth > win.innerWidth;
      if (hasHorizontalScroll) {
        cy.log(`⚠️ Horizontal scroll detected at ${breakpoint.name}`);
      }
      expect(hasHorizontalScroll, `No horizontal scroll at ${breakpoint.name}`).to.be.false;
    });
  });
});

// Command to measure layout shift
Cypress.Commands.add('measureLayoutShift', (callback?: () => void) => {
  cy.window().then(win => {
    let cumulativeLayoutShift = 0;
    
    // Create a PerformanceObserver to measure layout shifts
    const observer = new win.PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
          cumulativeLayoutShift += (entry as any).value;
        }
      }
    });
    
    observer.observe({ entryTypes: ['layout-shift'] });
    
    // Execute callback if provided
    if (callback) {
      callback();
    }
    
    // Wait for layout shifts to settle
    cy.wait(1000).then(() => {
      observer.disconnect();
      cy.log(`Cumulative Layout Shift: ${cumulativeLayoutShift}`);
      
      // CLS should be less than 0.1 for good user experience
      expect(cumulativeLayoutShift, 'Cumulative Layout Shift should be minimal').to.be.at.most(0.1);
    });
  });
});

// Command to check text readability
Cypress.Commands.add('checkTextReadability', (selector: string) => {
  cy.get(selector).then($el => {
    const element = $el[0];
    const styles = win.getComputedStyle(element);
    
    // Check font size
    const fontSize = parseFloat(styles.fontSize);
    expect(fontSize, 'Font size should be readable').to.be.at.least(12);
    
    // Check line height
    const lineHeight = parseFloat(styles.lineHeight);
    if (!isNaN(lineHeight)) {
      expect(lineHeight / fontSize, 'Line height should be appropriate').to.be.at.least(1.2);
    }
    
    // Check text is not cut off
    const textOverflow = styles.textOverflow;
    const overflow = styles.overflow;
    
    if (textOverflow === 'ellipsis' || overflow === 'hidden') {
      cy.log(`Text overflow handling detected in ${selector}`);
    }
  });
});

// Command to check touch target sizes
Cypress.Commands.add('checkTouchTargets', (minSize: number = 44) => {
  cy.get('button, a, [role="button"], input[type="button"], input[type="submit"]').each($el => {
    const rect = $el[0].getBoundingClientRect();
    const size = Math.min(rect.width, rect.height);
    
    if (size < minSize) {
      cy.log(`⚠️ Touch target too small: ${size}px (minimum: ${minSize}px)`);
      cy.log(`Element:`, $el[0]);
    }
    
    expect(size, 'Touch target should be large enough').to.be.at.least(minSize - 5); // 5px tolerance
  });
});

// Command to check focus indicators
Cypress.Commands.add('checkFocusIndicators', () => {
  cy.get('a, button, input, select, textarea, [tabindex]').each($el => {
    cy.wrap($el).focus();
    
    cy.wrap($el).then($focused => {
      const styles = win.getComputedStyle($focused[0]);
      const hasOutline = styles.outline !== 'none' && styles.outline !== '0px';
      const hasBoxShadow = styles.boxShadow !== 'none';
      const hasBorder = styles.borderWidth !== '0px';
      const hasBackground = styles.backgroundColor !== 'rgba(0, 0, 0, 0)';
      
      const hasFocusIndicator = hasOutline || hasBoxShadow || hasBorder || hasBackground;
      
      if (!hasFocusIndicator) {
        cy.log(`⚠️ No focus indicator detected for element:`, $focused[0]);
      }
      
      expect(hasFocusIndicator, 'Element should have visible focus indicator').to.be.true;
    });
  });
});

// Command to simulate slow network for layout testing
Cypress.Commands.add('simulateSlowNetwork', () => {
  cy.intercept('**/*', (req) => {
    // Add delay to simulate slow network
    req.reply((res) => {
      return new Promise(resolve => {
        setTimeout(() => resolve(res), 100);
      });
    });
  });
});

// Command to check for layout consistency across page loads
Cypress.Commands.add('checkLayoutConsistency', (selector: string) => {
  let initialLayout: DOMRect;
  
  cy.get(selector).then($el => {
    initialLayout = $el[0].getBoundingClientRect();
  });
  
  cy.reload();
  
  cy.get(selector).then($el => {
    const currentLayout = $el[0].getBoundingClientRect();
    
    expect(Math.abs(currentLayout.width - initialLayout.width), 'Width should be consistent').to.be.at.most(5);
    expect(Math.abs(currentLayout.height - initialLayout.height), 'Height should be consistent').to.be.at.most(5);
    expect(Math.abs(currentLayout.top - initialLayout.top), 'Top position should be consistent').to.be.at.most(5);
    expect(Math.abs(currentLayout.left - initialLayout.left), 'Left position should be consistent').to.be.at.most(5);
  });
});

// Command to load test bookmarks
Cypress.Commands.add('loadTestBookmarks', (bookmarks?: any[]) => {
  const defaultBookmarks = [
    {
      id: 'test-1',
      title: '🔖 Test Bookmark 1',
      url: 'https://example1.com',
      addDate: Date.now(),
      tags: ['test', 'example']
    },
    {
      id: 'test-2',
      title: '📚 Test Bookmark 2 with a Very Long Title That Should Be Handled Gracefully',
      url: 'https://example2.com/very/long/path/that/should/not/break/layout',
      addDate: Date.now(),
      tags: ['test', 'long-title']
    }
  ];
  
  cy.window().then(win => {
    win.localStorage.setItem('bookmarks', JSON.stringify(bookmarks || defaultBookmarks));
  });
});

// Declare custom commands for TypeScript
declare global {
  namespace Cypress {
    interface Chainable {
      checkElementOverflow(selector: string): Chainable<void>;
      testResponsiveBreakpoints(breakpoints: Array<{name: string, width: number, height: number}>): Chainable<void>;
      measureLayoutShift(callback?: () => void): Chainable<void>;
      checkTextReadability(selector: string): Chainable<void>;
      checkTouchTargets(minSize?: number): Chainable<void>;
      checkFocusIndicators(): Chainable<void>;
      simulateSlowNetwork(): Chainable<void>;
      checkLayoutConsistency(selector: string): Chainable<void>;
      loadTestBookmarks(bookmarks?: any[]): Chainable<void>;
    }
  }
}