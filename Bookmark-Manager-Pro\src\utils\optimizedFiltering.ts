import { Bookmark } from '../../types'

export interface FilterOptions {
  searchQuery?: string
  selectedCollection?: string
  selectedTags?: string[]
  filterType?: 'all' | 'favorites' | 'recent'
  limit?: number
  offset?: number
}

export interface FilterResult {
  bookmarks: Bookmark[]
  totalCount: number
  hasMore: boolean
  processingTime: number
}

/**
 * Memory-efficient filtering for large bookmark datasets
 */
export class OptimizedBookmarkFilter {
  private searchIndex: Map<string, Set<number>> = new Map()
  private isIndexBuilt = false
  
  /**
   * Build search index for faster filtering
   */
  private buildSearchIndex(bookmarks: Bookmark[]): void {
    console.log('🔍 Building search index for', bookmarks.length, 'bookmarks')
    const startTime = performance.now()
    
    this.searchIndex.clear()
    
    bookmarks.forEach((bookmark, index) => {
      // Index title words
      const titleWords = bookmark.title.toLowerCase().split(/\s+/)
      titleWords.forEach(word => {
        if (word.length > 2) { // Skip very short words
          if (!this.searchIndex.has(word)) {
            this.searchIndex.set(word, new Set())
          }
          this.searchIndex.get(word)!.add(index)
        }
      })
      
      // Index description words
      if (bookmark.description) {
        const descWords = bookmark.description.toLowerCase().split(/\s+/)
        descWords.forEach(word => {
          if (word.length > 2) {
            if (!this.searchIndex.has(word)) {
              this.searchIndex.set(word, new Set())
            }
            this.searchIndex.get(word)!.add(index)
          }
        })
      }
      
      // Index tags
      bookmark.tags?.forEach(tag => {
        const tagLower = tag.toLowerCase()
        if (!this.searchIndex.has(tagLower)) {
          this.searchIndex.set(tagLower, new Set())
        }
        this.searchIndex.get(tagLower)!.add(index)
      })
    })
    
    this.isIndexBuilt = true
    const buildTime = performance.now() - startTime
    console.log(`🔍 Search index built in ${buildTime.toFixed(2)}ms`)
  }
  
  /**
   * Fast search using pre-built index
   */
  private searchWithIndex(bookmarks: Bookmark[], query: string): Set<number> {
    const queryWords = query.toLowerCase().split(/\s+/).filter(word => word.length > 2)
    
    if (queryWords.length === 0) {
      return new Set(Array.from({ length: bookmarks.length }, (_, i) => i))
    }
    
    let resultIndices: Set<number> | null = null
    
    queryWords.forEach(word => {
      const matchingIndices = new Set<number>()
      
      // Find exact matches and partial matches
      for (const [indexedWord, indices] of Array.from(this.searchIndex.entries())) {
        if (indexedWord.includes(word)) {
          indices.forEach(index => matchingIndices.add(index))
        }
      }
      
      if (resultIndices === null) {
        resultIndices = matchingIndices
      } else {
        // Intersection for AND logic
        resultIndices = new Set(Array.from(resultIndices).filter(x => matchingIndices.has(x)))
      }
    })
    
    return resultIndices || new Set()
  }
  
  /**
   * Optimized filtering with pagination support
   */
  public filterBookmarks(bookmarks: Bookmark[], options: FilterOptions): FilterResult {
    const startTime = performance.now()
    
    // Build index if not already built or if bookmark count changed significantly
    if (!this.isIndexBuilt || this.searchIndex.size === 0) {
      this.buildSearchIndex(bookmarks)
    }
    
    let filteredIndices: Set<number>
    
    // Apply search filter using index
    if (options.searchQuery?.trim()) {
      filteredIndices = this.searchWithIndex(bookmarks, options.searchQuery)
    } else {
      filteredIndices = new Set(Array.from({ length: bookmarks.length }, (_, i) => i))
    }
    
    // Convert indices to bookmarks and apply other filters
    let filtered = Array.from(filteredIndices).map(index => bookmarks[index])
    
    // Apply collection filter
    if (options.selectedCollection && options.selectedCollection !== 'all') {
      if (options.selectedCollection === 'favorites') {
        filtered = filtered.filter(bookmark => bookmark.isFavorite)
      } else if (options.selectedCollection === 'recent') {
        filtered = filtered.filter(bookmark => bookmark.isRecentlyAdded)
      } else {
        const collectionLower = options.selectedCollection.toLowerCase()
        filtered = filtered.filter(bookmark =>
          bookmark.collection.toLowerCase() === collectionLower
        )
      }
    }
    
    // Apply tag filter
    if (options.selectedTags && options.selectedTags.length > 0) {
      filtered = filtered.filter(bookmark => {
        if (!bookmark.tags) return false
        
        // Use Set for better performance with large tag arrays
        if (bookmark.tags.length > 10) {
          const bookmarkTagSet = new Set(bookmark.tags.map(tag => tag.toLowerCase()))
          return options.selectedTags!.every(tag => bookmarkTagSet.has(tag.toLowerCase()))
        }
        
        return options.selectedTags!.every(tag =>
          bookmark.tags!.some(bookmarkTag =>
            bookmarkTag.toLowerCase() === tag.toLowerCase()
          )
        )
      })
    }
    
    // Apply type filter and sort
    switch (options.filterType) {
      case 'favorites':
        filtered = filtered.filter(bookmark => bookmark.isFavorite)
        break
      case 'recent':
        filtered = filtered
          .filter(bookmark => bookmark.isRecentlyAdded)
          .sort((a, b) => {
            const timeA = a.addedTimestamp ? new Date(a.addedTimestamp).getTime() : 0
            const timeB = b.addedTimestamp ? new Date(b.addedTimestamp).getTime() : 0
            return timeB - timeA
          })
        break
      default:
        filtered = filtered.sort((a, b) => (b.visits || 0) - (a.visits || 0))
    }
    
    const totalCount = filtered.length
    
    // Apply pagination
    const limit = options.limit || totalCount
    const offset = options.offset || 0
    const paginatedResults = filtered.slice(offset, offset + limit)
    const hasMore = offset + limit < totalCount
    
    const processingTime = performance.now() - startTime
    
    console.log(`🔍 Filtered ${bookmarks.length} → ${totalCount} bookmarks in ${processingTime.toFixed(2)}ms`)
    
    return {
      bookmarks: paginatedResults,
      totalCount,
      hasMore,
      processingTime
    }
  }
  
  /**
   * Clear the search index (useful when bookmarks change significantly)
   */
  public clearIndex(): void {
    this.searchIndex.clear()
    this.isIndexBuilt = false
  }
}

// Singleton instance for the app
export const optimizedFilter = new OptimizedBookmarkFilter()

/**
 * Hook for using optimized filtering
 */
export const useOptimizedFiltering = () => {
  return {
    filterBookmarks: (bookmarks: Bookmark[], options: FilterOptions) =>
      optimizedFilter.filterBookmarks(bookmarks, options),
    clearIndex: () => optimizedFilter.clearIndex()
  }
}
