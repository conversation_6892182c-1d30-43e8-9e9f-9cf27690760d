import React, { useMemo, useCallback } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Bookmark, BookmarkProcessingState, SortConfig } from '../types';
import BookmarkItem from './BookmarkItem';
import { ChevronUpIcon, ChevronDownIcon } from './icons/HeroIcons';

interface VirtualizedBookmarkListProps {
  bookmarks: Bookmark[];
  selectedBookmarkIds: Set<string>;
  processingStates: Record<string, BookmarkProcessingState>;
  onToggleSelect: (id: string) => void;
  onToggleSelectAll: () => void;
  isAllSelected: boolean;
  sortConfig: SortConfig;
  onSort: (key: keyof Bookmark) => void;
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  height?: number;
  itemHeight?: number;
}

interface BookmarkRowProps {
  index: number;
  style: React.CSSProperties;
  data: {
    bookmarks: Bookmark[];
    selectedBookmarkIds: Set<string>;
    processingStates: Record<string, BookmarkProcessingState>;
    onToggleSelect: (id: string) => void;
    onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  };
}

const SortableHeader: React.FC<{
  label: string;
  sortKey: keyof Bookmark;
  currentSortConfig: SortConfig;
  onSort: (key: keyof Bookmark) => void;
  className?: string;
  colSpan?: number;
}> = ({ label, sortKey, currentSortConfig, onSort, className = "", colSpan }) => {
  const isActive = currentSortConfig.key === sortKey;
  const directionIcon = isActive ? (
    currentSortConfig.direction === 'asc' ? 
      <ChevronUpIcon className="w-4 h-4 ml-1" /> : 
      <ChevronDownIcon className="w-4 h-4 ml-1" />
  ) : null;
  
  return (
    <th 
      scope="col" 
      className={`py-3.5 px-3 text-left text-sm font-semibold text-slate-300 cursor-pointer hover:text-sky-300 transition-colors ${className}`}
      onClick={() => onSort(sortKey)}
      aria-sort={isActive ? (currentSortConfig.direction === 'asc' ? 'ascending' : 'descending') : 'none'}
      colSpan={colSpan}
    >
      <div className="flex items-center">
        {label}
        {directionIcon}
      </div>
    </th>
  );
};

const BookmarkRow: React.FC<BookmarkRowProps> = React.memo(({ index, style, data }) => {
  const {
    bookmarks,
    selectedBookmarkIds,
    processingStates,
    onToggleSelect,
    onUpdateBookmark
  } = data;
  
  const bookmark = bookmarks[index];
  
  if (!bookmark) {
    return (
      <div style={style} className="flex items-center justify-center text-slate-400">
        Loading...
      </div>
    );
  }
  
  return (
    <div style={style} className="border-b border-slate-700">
      <BookmarkItem
        bookmark={bookmark}
        isSelected={selectedBookmarkIds.has(bookmark.id)}
        processingState={processingStates[bookmark.id] || { isProcessing: false }}
        onToggleSelect={onToggleSelect}
        onUpdateBookmark={onUpdateBookmark}
        onDeleteBookmark={() => {}}
        onGenerateSummary={() => {}}
        onGenerateTags={() => {}}
      />
    </div>
  );
});

BookmarkRow.displayName = 'BookmarkRow';

const VirtualizedBookmarkList: React.FC<VirtualizedBookmarkListProps> = ({
  bookmarks,
  selectedBookmarkIds,
  processingStates,
  onToggleSelect,
  onToggleSelectAll,
  isAllSelected,
  sortConfig,
  onSort,
  onUpdateBookmark,
  height = 600,
  itemHeight = 120
}) => {
  // Memoize the data object to prevent unnecessary re-renders
  const listData = useMemo(() => ({
    bookmarks,
    selectedBookmarkIds,
    processingStates,
    onToggleSelect,
    onUpdateBookmark
  }), [
    bookmarks,
    selectedBookmarkIds,
    processingStates,
    onToggleSelect,
    onUpdateBookmark
  ]);

  const handleSelectAllChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    e.preventDefault();
    onToggleSelectAll();
  }, [onToggleSelectAll]);

  if (bookmarks.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-400 text-lg">No bookmarks found</p>
        <p className="text-slate-500 text-sm mt-2">
          Import a bookmark file or adjust your search filters
        </p>
      </div>
    );
  }

  return (
    <div className="bg-slate-800 rounded-lg overflow-hidden">
      {/* Header */}
      <div className="bg-slate-700">
        <table className="w-full">
          <thead>
            <tr>
              <th scope="col" className="py-3.5 px-3 text-left">
                <input
                  type="checkbox"
                  checked={isAllSelected}
                  onChange={handleSelectAllChange}
                  className="rounded border-slate-600 bg-slate-700 text-sky-600 focus:ring-sky-500 focus:ring-offset-slate-800"
                  aria-label="Select all bookmarks"
                />
              </th>
              <SortableHeader
                label="Title"
                sortKey="title"
                currentSortConfig={sortConfig}
                onSort={onSort}
                className="min-w-0 w-1/3"
              />
              <SortableHeader
                label="URL"
                sortKey="url"
                currentSortConfig={sortConfig}
                onSort={onSort}
                className="min-w-0 w-1/3"
              />
              <SortableHeader
                label="Date Added"
                sortKey="addDate"
                currentSortConfig={sortConfig}
                onSort={onSort}
                className="min-w-0 w-32"
              />
              <th scope="col" className="py-3.5 px-3 text-left text-sm font-semibold text-slate-300 w-24">
                Actions
              </th>
            </tr>
          </thead>
        </table>
      </div>

      {/* Virtualized List */}
      <div className="relative">
        <List
          height={height}
          width="100%"
          itemCount={bookmarks.length}
          itemSize={itemHeight}
          itemData={listData}
          overscanCount={5} // Render 5 extra items outside visible area for smooth scrolling
        >
          {BookmarkRow}
        </List>
      </div>

      {/* Footer with count */}
      <div className="bg-slate-700 px-4 py-2 text-sm text-slate-300">
        Showing {bookmarks.length} bookmark{bookmarks.length !== 1 ? 's' : ''}
        {selectedBookmarkIds.size > 0 && (
          <span className="ml-4">
            {selectedBookmarkIds.size} selected
          </span>
        )}
      </div>
    </div>
  );
};

export default React.memo(VirtualizedBookmarkList);