import React, { useState } from 'react'
import { Palette, Sun, Moon, <PERSON>, Settings, Sparkles } from 'lucide-react'
import { useModernTheme, THEME_CONFIGS, COLOR_SCHEME_CONFIGS } from '../contexts/ModernThemeContext'

interface ModernThemeToggleProps {
  showLabel?: boolean
  compact?: boolean
}

export const ModernThemeToggle: React.FC<ModernThemeToggleProps> = ({ 
  showLabel = true, 
  compact = false 
}) => {
  const { 
    themeMode, 
    colorScheme, 
    isDarkMode, 
    setThemeMode, 
    setColorScheme, 
    toggleTheme, 
    toggleColorScheme 
  } = useModernTheme()
  
  const [isExpanded, setIsExpanded] = useState(false)

  const getColorSchemeIcon = () => {
    switch (colorScheme) {
      case 'light': return <Sun size={16} />
      case 'dark': return <Moon size={16} />
      case 'auto': return <Monitor size={16} />
      default: return <Sun size={16} />
    }
  }

  if (compact) {
    return (
      <div className="modern-theme-toggle-compact">
        <button
          onClick={toggleTheme}
          className="modern-theme-toggle-btn compact"
          title={`Switch to ${themeMode === 'classic' ? 'Modern' : 'Classic'} theme`}
        >
          {themeMode === 'modern' ? <Sparkles size={16} /> : <Palette size={16} />}
          {showLabel && (
            <span className="theme-label">
              {THEME_CONFIGS[themeMode].name}
            </span>
          )}
        </button>
        
        <button
          onClick={toggleColorScheme}
          className="color-scheme-toggle-btn compact"
          title={`Color scheme: ${COLOR_SCHEME_CONFIGS[colorScheme].name}`}
        >
          {getColorSchemeIcon()}
        </button>
      </div>
    )
  }

  return (
    <div className={`modern-theme-toggle-container ${isExpanded ? 'expanded' : ''}`}>
      {/* Quick Toggle Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="modern-theme-toggle-trigger"
        title="Theme Settings"
      >
        {themeMode === 'modern' ? <Sparkles size={18} /> : <Settings size={18} />}
        {showLabel && <span>Theme</span>}
      </button>

      {/* Expanded Theme Panel */}
      {isExpanded && (
        <div className="modern-theme-panel">
          <div className="modern-theme-panel-header">
            <h3>Theme Settings</h3>
            <button 
              onClick={() => setIsExpanded(false)}
              className="close-btn"
            >
              ×
            </button>
          </div>

          {/* Theme Mode Selection */}
          <div className="modern-theme-section">
            <h4>Theme Style</h4>
            <div className="modern-theme-options">
              {Object.entries(THEME_CONFIGS).map(([key, config]) => (
                <button
                  key={key}
                  onClick={() => setThemeMode(key as any)}
                  className={`modern-theme-option ${themeMode === key ? 'active' : ''}`}
                >
                  <div className="theme-preview">
                    <div className={`preview-card theme-${key}`}>
                      <div className="preview-header"></div>
                      <div className="preview-content">
                        <div className="preview-line"></div>
                        <div className="preview-line short"></div>
                      </div>
                    </div>
                  </div>
                  <div className="theme-info">
                    <span className="theme-name">{config.name}</span>
                    <span className="theme-description">{config.description}</span>
                  </div>
                  {themeMode === key && (
                    <div className="active-indicator">✓</div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Color Scheme Selection */}
          <div className="modern-theme-section">
            <h4>Color Scheme</h4>
            <div className="color-scheme-options">
              {Object.entries(COLOR_SCHEME_CONFIGS).map(([key, config]) => (
                <button
                  key={key}
                  onClick={() => setColorScheme(key as any)}
                  className={`color-scheme-option ${colorScheme === key ? 'active' : ''}`}
                >
                  <span className="scheme-icon">{config.icon}</span>
                  <span className="scheme-name">{config.name}</span>
                  {colorScheme === key && (
                    <div className="active-indicator">✓</div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Current Status */}
          <div className="modern-theme-status">
            <div className="status-item">
              <span>Current Theme:</span>
              <span className="status-value">
                {THEME_CONFIGS[themeMode].name}
              </span>
            </div>
            <div className="status-item">
              <span>Color Scheme:</span>
              <span className="status-value">
                {COLOR_SCHEME_CONFIGS[colorScheme].name}
                {colorScheme === 'auto' && ` (${isDarkMode ? 'Dark' : 'Light'})`}
              </span>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="modern-theme-actions">
            <button 
              onClick={toggleTheme}
              className="action-btn primary"
            >
              {themeMode === 'modern' ? <Palette size={16} /> : <Sparkles size={16} />}
              Switch to {themeMode === 'classic' ? 'Modern' : 'Classic'}
            </button>
            <button 
              onClick={toggleColorScheme}
              className="action-btn secondary"
            >
              {getColorSchemeIcon()}
              Cycle Colors
            </button>
          </div>
        </div>
      )}

      {/* Backdrop */}
      {isExpanded && (
        <div 
          className="modern-theme-backdrop"
          onClick={() => setIsExpanded(false)}
        />
      )}
    </div>
  )
}

// Simple theme indicator for status bars
export const ModernThemeIndicator: React.FC = () => {
  const { themeMode, colorScheme, isDarkMode } = useModernTheme()
  
  return (
    <div className="modern-theme-indicator">
      <span className="theme-mode-indicator">
        {THEME_CONFIGS[themeMode].name}
      </span>
      <span className="color-scheme-indicator">
        {colorScheme === 'auto' ? (isDarkMode ? 'Dark' : 'Light') : COLOR_SCHEME_CONFIGS[colorScheme].name}
      </span>
    </div>
  )
}

// Quick theme switcher for header/toolbar
export const QuickModernThemeSwitch: React.FC = () => {
  const { themeMode, toggleTheme } = useModernTheme()
  
  return (
    <button
      onClick={toggleTheme}
      className="quick-modern-theme-switch"
      title={`Switch to ${themeMode === 'classic' ? 'Modern' : 'Classic'} theme`}
    >
      {themeMode === 'modern' ? <Palette size={18} /> : <Sparkles size={18} />}
      <span className="switch-label">
        {themeMode === 'classic' ? 'Modern' : 'Classic'}
      </span>
    </button>
  )
}
