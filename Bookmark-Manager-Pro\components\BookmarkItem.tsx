
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Bookmark, BookmarkProcessingState } from '../types';
import { <PERSON><PERSON>, <PERSON> } from './base';
import { DocumentTextIcon, PencilSquareIcon, TagIcon, TrashIcon, XMarkIcon } from './icons/HeroIcons';
import { ExclamationCircleIcon as SolidExclamationCircleIcon, TagIcon as SolidTagIcon } from './icons/HeroIconsSolid';
import SpinnerIcon from './icons/SpinnerIcon';

interface BookmarkItemProps {
  bookmark: Bookmark;
  isSelected: boolean;
  processingState: BookmarkProcessingState;
  isDragOver?: boolean;
  onToggleSelect?: (id: string) => void;
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void;
  onDeleteBookmark: (id: string) => void;
  onGenerateSummary: (id: string) => void;
  onGenerateTags: (id: string) => void;
  onProcessYouTube?: (bookmark: Bookmark) => void;
}

interface EditableFieldProps {
  value: string;
  onSave: (newValue: string) => void;
  fieldName: string;
  isTextarea?: boolean;
  className?: string;
  inputClassName?: string;
  placeholder?: string;
}

const EditableField: React.FC<EditableFieldProps> = React.memo(({ value, onSave, fieldName, isTextarea = false, className = "", inputClassName = "", placeholder }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [currentValue, setCurrentValue] = useState(value);
  const inputRef = useRef<HTMLInputElement | HTMLTextAreaElement>(null);

  // Clear any pending operations on unmount to prevent memory leaks
  useEffect(() => {
    return () => {
      // Cleanup function for any pending operations
    };
  }, []);

  useEffect(() => {
    setCurrentValue(value); // Sync with prop changes
  }, [value]);

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      if (inputRef.current instanceof HTMLInputElement) {
        inputRef.current.select();
      } else if (inputRef.current instanceof HTMLTextAreaElement) {
        inputRef.current.select();
      }
    }
  }, [isEditing]);

  const handleSave = useCallback(() => {
    if (currentValue.trim() !== value.trim() && currentValue.trim() !== "") { // Save if changed and not empty
        onSave(currentValue.trim());
    } else if (currentValue.trim() === "" && fieldName !== "summary") { // For title/URL, revert if empty
        setCurrentValue(value); 
    } else if (currentValue.trim() === "" && fieldName === "summary") { // Allow empty summary
        onSave("");
    }
    setIsEditing(false);
  }, [currentValue, value, onSave, fieldName]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (!isTextarea || (isTextarea && e.ctrlKey)) { // Ctrl+Enter for textarea save
        handleSave();
      }
    } else if (e.key === 'Escape') {
      setCurrentValue(value);
      setIsEditing(false);
    }
  }, [isTextarea, handleSave, value]);

  if (isEditing) {
    return isTextarea ? (
      <textarea
        ref={inputRef as React.RefObject<HTMLTextAreaElement>}
        value={currentValue}
        onChange={(e) => setCurrentValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        className={`min-h-[60px] ${inputClassName}`}
        placeholder={placeholder || "Enter summary..."}
        aria-label={`Edit ${fieldName}`}
        autoFocus
      />
    ) : (
      <input
        ref={inputRef as React.RefObject<HTMLInputElement>}
        type="text"
        value={currentValue}
        onChange={(e) => setCurrentValue(e.target.value)}
        onBlur={handleSave}
        onKeyDown={handleKeyDown}
        className={inputClassName}
        aria-label={`Edit ${fieldName}`}
        autoFocus
      />
    );
  }

  // Special handling for URL fields to make them clickable
  if (fieldName === 'URL' && value) {
    const isYouTubeUrl = value.includes('youtube.com') || value.includes('youtu.be');
    
    // Ensure URL has proper protocol
    const formatUrl = (url: string) => {
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        return 'https://' + url;
      }
      return url;
    };
    
    const handleUrlClick = (e: React.MouseEvent) => {
      e.stopPropagation();
      e.preventDefault();
      
      const formattedUrl = formatUrl(value);
      
      if (isYouTubeUrl) {
        // For YouTube URLs, open in a popup window to preserve the app
        const popup = window.open(
          formattedUrl,
          'youtube-popup',
          'width=1200,height=800,scrollbars=yes,resizable=yes,toolbar=no,menubar=no,location=no,status=no'
        );
        
        // Fallback to new tab if popup is blocked
        if (!popup || popup.closed || typeof popup.closed === 'undefined') {
          window.open(formattedUrl, '_blank', 'noopener,noreferrer');
        }
      } else {
        // For other URLs, open in new tab
        window.open(formattedUrl, '_blank', 'noopener,noreferrer');
      }
    };
    
    // Extract domain from URL for display
    const getDomainFromUrl = (url: string) => {
      try {
        const urlObj = new URL(formatUrl(url));
        const hostname = urlObj.hostname;
        // Highlight the main domain part
        const parts = hostname.split('.');
        if (parts.length > 2) {
          const mainDomain = parts[parts.length - 2];
          const tld = parts[parts.length - 1];
          const subdomain = parts.slice(0, -2).join('.');
          return { subdomain, mainDomain, tld, full: hostname };
        }
        return { subdomain: '', mainDomain: parts[0] || hostname, tld: parts[1] || '', full: hostname };
      } catch {
        const truncated = url.length > 30 ? url.substring(0, 30) + '...' : url;
        return { subdomain: '', mainDomain: truncated, tld: '', full: truncated };
      }
    };

    const domainInfo = getDomainFromUrl(value);
    
    return (
      <div className={`group relative ${className}`}>
        <div className="flex items-center gap-2">
          <a
            href={formatUrl(value)}
            target="_blank"
            rel="noopener noreferrer"
            onClick={(e) => {
              e.preventDefault();
              handleUrlClick(e);
            }}
            className="text-slate-400 hover:text-sky-400 underline cursor-pointer flex-1 block"
            title={`${value} - Click to open in ${isYouTubeUrl ? 'popup window' : 'new tab'}`}
          >
            <span className="font-mono text-sm">
              {domainInfo.subdomain && (
                <span className="text-slate-500">{domainInfo.subdomain}.</span>
              )}
              <span className="text-slate-300 font-medium">{domainInfo.mainDomain}</span>
              {domainInfo.tld && (
                <span className="text-slate-400">.{domainInfo.tld}</span>
              )}
            </span>
            {isYouTubeUrl && (
              <span className="ml-1 text-xs text-red-400">📺</span>
            )}
            <span className="ml-1 text-xs text-slate-500">↗</span>
          </a>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            className="opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0"
            title="Edit URL"
          >
            <PencilSquareIcon className="w-3 h-3 text-slate-500 hover:text-sky-300" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div 
      onClick={() => setIsEditing(true)} 
      className={`cursor-pointer hover:bg-slate-600/50 p-1 rounded group relative transition-colors ${className}`}
      title={`Click to edit ${fieldName}`}
      role="button"
      tabIndex={0}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          setIsEditing(true);
        }
      }}
      aria-label={`Edit ${fieldName}`}
    >
      {value || (isTextarea ? <span className="text-slate-500 italic">Click to add summary</span> : 'N/A')}
      <PencilSquareIcon className="w-3 h-3 text-slate-500 group-hover:text-sky-300 absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity" />
    </div>
  );
});

EditableField.displayName = 'EditableField';


const BookmarkItem: React.FC<BookmarkItemProps> = ({ 
    bookmark, 
    isSelected, 
    processingState, 
    onToggleSelect,
    onUpdateBookmark,
    onDeleteBookmark,
    onGenerateSummary,
    onGenerateTags,
    onProcessYouTube
}) => {
  const { id, title, url, summary, tags, path, addDate, icon, summaryError, tagsError } = bookmark;
  const { isSummarizing = false, isTagging = false } = processingState || {};

  const [faviconSrc, setFaviconSrc] = useState(icon || '');


  useEffect(() => {
    setFaviconSrc(icon || ''); // Update if bookmark.icon changes (e.g. after parsing new file)
  }, [icon]);

  const handleFaviconError = () => {
    setFaviconSrc(''); // Fallback to GlobeAltIcon will be handled by lack of src
  };

  const displayDate = useMemo(() => {
    // Handle ISO string dates (from dateAdded)
    if (bookmark.dateAdded) {
      try {
        return new Date(bookmark.dateAdded).toLocaleDateString();
      } catch {
        // Fall through to addDate handling
      }
    }
    // Handle string timestamps (from addDate)
    if (typeof addDate === 'string') {
      const timestamp = parseInt(addDate, 10);
      if (!isNaN(timestamp)) {
        return new Date(timestamp * 1000).toLocaleDateString();
      }
    }
    return 'N/A';
  }, [bookmark.dateAdded, addDate]);




  const handleRemoveTag = useCallback((tagToRemove: string) => {
    const updatedTags = (tags || []).filter(tag => tag !== tagToRemove);
    onUpdateBookmark(id, { tags: updatedTags });
  }, [tags, id, onUpdateBookmark]);

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Handle drop logic here if needed
  }, []);

  const handleClick = useCallback((e: React.MouseEvent) => {
    if (onToggleSelect && e.target instanceof HTMLElement && !e.target.closest('button, input, textarea')) {
      onToggleSelect(id);
    }
  }, [onToggleSelect, id]);

  const isYouTubeUrl = url.includes('youtube.com') || url.includes('youtu.be');

  return (
    <>
      {/* CSS Grid Row - Each cell is a direct child of the grid container */}
      {onToggleSelect && (
        <Card 
        variant={isSelected ? 'elevated' : 'default'}
        padding="md"
        className="flex items-center justify-center cursor-pointer border-b border-slate-600/30"
          onClick={(e) => {
            e.stopPropagation();
            onToggleSelect(bookmark.id);
          }}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => {}}
            className="h-4 w-4 rounded border-slate-500 text-sky-500 focus:ring-sky-400 accent-sky-500"
          />
        </Card>
      )}
      
      {/* Title Cell with Favicon and Metadata */}
      <Card 
        variant={isSelected ? 'elevated' : 'default'}
        padding="md"
        className="cursor-pointer border-b border-slate-600/30"
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="title-metadata-group">
          <div className="flex items-center space-x-3 mb-1">
            <div className="flex-shrink-0">
              {faviconSrc ? (
                <img src={faviconSrc} alt="" className="bookmark-favicon" onError={handleFaviconError} />
              ) : (
                <div className="bookmark-favicon bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center text-xs font-medium text-white">
                  {title?.charAt(0)?.toUpperCase() || url?.charAt(8)?.toUpperCase() || '?'}
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <EditableField 
                value={title}
                onSave={(newTitle) => onUpdateBookmark(id, { title: newTitle })}
                fieldName="title"
                className="font-semibold text-slate-100 break-words text-lg leading-tight"
                inputClassName="text-lg font-semibold"
              />
            </div>
          </div>
          {path && path.length > 0 && (
            <div className="text-slate-500 text-xs ml-8 truncate">
              {path.join(' / ')}
            </div>
          )}
        </div>
      </Card>
      
      {/* URL & Details Cell */}
      <Card 
        variant={isSelected ? 'elevated' : 'default'}
        padding="md"
        className="cursor-pointer border-b border-slate-600/30"
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="space-y-4 w-full">
          {/* URL Display */}
          <div>
            <EditableField
              value={url}
              onSave={(newUrl) => onUpdateBookmark(id, { url: newUrl })}
              fieldName="URL"
              className="text-slate-400 hover:text-sky-400 transition-colors text-sm"
              inputClassName="text-sm"
            />
          </div>

          {/* AI Processing Status */}
          {(isSummarizing || isTagging) && (
            <div className="flex items-center text-sm text-sky-400">
              <SpinnerIcon className="w-4 h-4 mr-2" /> 
              <span>
                {isSummarizing && isTagging ? 'Processing AI Summary & Tags...' :
                 isSummarizing ? 'Generating Summary...' : 'Generating Tags...'}
              </span>
              {processingState.processingTier && (
                <span className="ml-2 text-slate-400">({processingState.processingTier})</span>
              )}
            </div>
          )}

          {/* Generated Summary Display */}
          {!isSummarizing && summary && (
            <div className="mt-4">
              <div className="text-xs font-medium text-slate-400 mb-2">AI Summary</div>
              <div className="text-sm text-slate-200 bg-slate-800/50 p-4 rounded-lg border-l-4 border-sky-500 leading-relaxed">
                {summary}
              </div>
              {processingState.confidence && (
                <div className="text-xs text-slate-400 mt-2">
                  Confidence: {processingState.confidence}
                  {processingState.processingTier && (
                    <span className="ml-2">• {processingState.processingTier}</span>
                  )}
                </div>
              )}
            </div>
          )}
          {summaryError && !isSummarizing && (
            <div className="flex items-center text-sm text-red-400">
              <SolidExclamationCircleIcon className="w-4 h-4 mr-2" /> {summaryError}
            </div>
          )}

          {/* Enhanced Tags Display */}
          {tags && tags.length > 0 && !isTagging && (
            <div className="mt-4">
              <div className="text-xs font-medium text-slate-400 mb-3">Tags</div>
              <div className="flex flex-wrap gap-2">
                {tags.map(tag => (
                  <span key={tag} className="badge badge-secondary hover:badge-primary transition-colors inline-flex items-center">
                    <SolidTagIcon className="w-3 h-3 mr-2 text-sky-400" />
                    <span>{tag}</span>
                    <button 
                      onClick={(e) => { e.stopPropagation(); handleRemoveTag(tag); }} 
                      className="ml-2 text-slate-400 hover:text-red-400 transition-colors flex-shrink-0" 
                      title={`Remove tag: ${tag}`}
                    >
                      <XMarkIcon className="w-4 h-4" />
                    </button>
                  </span>
                ))}
              </div>
            </div>
          )}
          {tagsError && !isTagging && (
            <div className="flex items-center text-sm text-red-400">
              <SolidExclamationCircleIcon className="w-4 h-4 mr-2" /> 
              <span>{tagsError}</span>
            </div>
          )}
        </div>
      </Card>
      
      {/* Date Cell */}
      <Card 
        variant={isSelected ? 'elevated' : 'default'}
        padding="md"
        className="cursor-pointer text-center border-b border-slate-600/30"
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <span className="text-sm font-medium text-slate-400">{displayDate}</span>
      </Card>
      
      {/* Actions Cell */}
      <Card 
        variant={isSelected ? 'elevated' : 'default'}
        padding="md"
        className="cursor-pointer text-center border-b border-slate-600/30"
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <div className="flex items-center justify-center gap-2">
          <Button
            onClick={() => onGenerateSummary(id)}
            disabled={isSummarizing}
            variant="ghost"
            size="sm"
            leftIcon={isSummarizing ? <SpinnerIcon className="w-5 h-5" /> : <DocumentTextIcon className="w-5 h-5" />}
            title={isSummarizing ? 'AI is generating summary...' : 'Generate AI Summary'}
            loading={isSummarizing}
          />
          <Button
            onClick={() => onGenerateTags(id)}
            disabled={isTagging}
            variant="ghost"
            size="sm"
            leftIcon={isTagging ? <SpinnerIcon className="w-5 h-5" /> : <TagIcon className="w-5 h-5" />}
            title="Generate AI Tags"
            loading={isTagging}
          />
          {isYouTubeUrl && onProcessYouTube && (
            <Button
              onClick={() => onProcessYouTube(bookmark)}
              variant="ghost"
              size="sm"
              leftIcon={<span className="text-lg">📺</span>}
              title="Extract and process YouTube video content"
            />
          )}
          <Button
            onClick={() => onDeleteBookmark(id)}
            variant="ghost"
            size="sm"
            leftIcon={<TrashIcon className="w-5 h-5" />}
            title="Delete Bookmark"
            className="text-red-400 hover:text-red-300"
          />
        </div>
      </Card>
      
      {/* CSS styles are handled by Tailwind classes and grid layout */}
    </>
  );
};

BookmarkItem.displayName = 'BookmarkItem';

export default BookmarkItem;
