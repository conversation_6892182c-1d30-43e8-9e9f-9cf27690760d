/**
 * ENHANCED MULTIMEDIA PLAYLIST PANEL
 * Bookmark Manager Pro - Enhanced Design System
 *
 * A comprehensive multimedia playlist panel that provides:
 * - Gym-mode interface with video queues and text-to-speech
 * - Multi-device export capabilities
 * - Modern UI design consistent with the app theme
 * - Responsive layout and accessibility features
 * - Integration with the multimedia playlist service
 */

import {
    BookOpen,
    Download, ExternalLink, FileText,
    Headphones, Image, Mic, MicOff, Pause, Play, Repeat,
    Settings, Shuffle, SkipBack, SkipForward, Video, Volume2, X
} from 'lucide-react'
import React, { useEffect, useRef, useState } from 'react'
import type { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import {
    DeviceInfo, ExportFormat, MultimediaPlaylist,
    MultimediaPlaylistItem, multimediaPlaylistService, PlaybackSession
} from '../services/multimedia/MultimediaPlaylistService'

interface MultimediaPlaylistPanelProps {
  isOpen: boolean
  onClose: () => void
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
}

export const MultimediaPlaylistPanel: React.FC<MultimediaPlaylistPanelProps> = ({
  isOpen,
  onClose,
  selectedBookmarks,
  collectionId,
  mindMapSelection
}) => {
  const { bookmarks, createPlaylist } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // State management
  const [currentPlaylist, setCurrentPlaylist] = useState<MultimediaPlaylist | null>(null)
  const [playbackSession, setPlaybackSession] = useState<PlaybackSession | null>(null)
  const [currentItem, setCurrentItem] = useState<MultimediaPlaylistItem | null>(null)
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [volume, setVolume] = useState(80)
  const [playbackSpeed, setPlaybackSpeed] = useState(1.0)
  const [gymMode, setGymMode] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [showExportPanel, setShowExportPanel] = useState(false)
  const [ttsEnabled, setTtsEnabled] = useState(false)
  const [ttsVoice, setTtsVoice] = useState('default')
  const [autoAdvance, setAutoAdvance] = useState(true)
  const [shuffle, setShuffle] = useState(false)
  const [repeat, setRepeat] = useState<'none' | 'one' | 'all'>('none')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Export state
  const [exportFormat, setExportFormat] = useState<ExportFormat['type']>('email')
  const [exportDestination, setExportDestination] = useState('')
  const [exportProgress, setExportProgress] = useState(0)
  const [exportStatus, setExportStatus] = useState<'idle' | 'exporting' | 'success' | 'error'>('idle')

  // Device detection
  const [currentDevice, setCurrentDevice] = useState<DeviceInfo>({
    id: 'current-device',
    name: 'Current Device',
    type: 'desktop',
    capabilities: {
      video: true,
      audio: true,
      textToSpeech: 'speechSynthesis' in window,
      offline: false,
      touchScreen: 'ontouchstart' in window
    }
  })

  // Refs
  const videoRef = useRef<HTMLVideoElement>(null)
  const audioRef = useRef<HTMLAudioElement>(null)
  const ttsRef = useRef<SpeechSynthesisUtterance | null>(null)

  // Initialize playlist on open
  useEffect(() => {
    if (isOpen && !currentPlaylist) {
      initializePlaylist()
    }
  }, [isOpen, selectedBookmarks, collectionId, mindMapSelection])

  // Cleanup on close
  useEffect(() => {
    if (!isOpen) {
      stopPlayback()
      setCurrentPlaylist(null)
      setPlaybackSession(null)
    }
  }, [isOpen])

  const initializePlaylist = useCallback(async () => {
    setLoading(true)
    setError(null)

    try {
      let targetBookmarks: Bookmark[] = []
      let playlistName = 'Multimedia Playlist'

      if (selectedBookmarks && selectedBookmarks.length > 0) {
        targetBookmarks = selectedBookmarks
        playlistName = 'Selected Bookmarks Playlist'
      } else if (collectionId) {
        targetBookmarks = bookmarks.filter(b => b.collection === collectionId)
        playlistName = `Collection: ${collectionId}`
      } else if (mindMapSelection && mindMapSelection.length > 0) {
        targetBookmarks = bookmarks.filter(b => mindMapSelection.includes(b.id))
        playlistName = 'Mind Map Selection'
      } else {
        targetBookmarks = bookmarks.slice(0, 10) // Default to first 10 bookmarks
      }

      if (targetBookmarks.length === 0) {
        setError('No bookmarks available for playlist creation')
        return
      }

      const playlist = await multimediaPlaylistService.createMultimediaPlaylist(
        playlistName,
        `Enhanced multimedia playlist with ${targetBookmarks.length} items`,
        targetBookmarks,
        {
          autoDetectTypes: true,
          enableTTS: true,
          defaultPlaybackSettings: {
            autoPlay: false,
            textToSpeechEnabled: true,
            volume: 80
          },
          aiIntegration: {
            autoSummarization: true,
            contentAnalysis: true,
            topicExtraction: true
          }
        }
      )

      setCurrentPlaylist(playlist)
      if (playlist.items.length > 0) {
        setCurrentItem(playlist.items[0])
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create playlist')
    } finally {
      setLoading(false)
    }
  }, [selectedBookmarks, collectionId, mindMapSelection, bookmarks])

  const startGymMode = useCallback(async () => {
    if (!currentPlaylist) return

    try {
      const session = await multimediaPlaylistService.startPlaybackSession(
        currentPlaylist.id,
        'current-user',
        currentDevice,
        {
          gymMode: true,
          autoAdvance: true,
          textToSpeech: ttsEnabled
        }
      )

      setPlaybackSession(session)
      setGymMode(true)
      setAutoAdvance(true)
      console.log('🏃‍♂️ Gym mode activated!')
    } catch (err) {
      setError('Failed to start gym mode')
    }
  }, [currentPlaylist, currentDevice, ttsEnabled])

  const playItem = useCallback(async (item: MultimediaPlaylistItem) => {
    setCurrentItem(item)
    setIsPlaying(true)

    try {
      switch (item.type) {
        case 'video':
          await playVideo(item)
          break
        case 'audio':
          await playAudio(item)
          break
        case 'document':
        case 'article':
          if (ttsEnabled) {
            await playTextToSpeech(item)
          } else {
            // Open in new tab for reading
            window.open(item.url, '_blank')
          }
          break
        default:
          window.open(item.url, '_blank')
      }
    } catch (err) {
      setError(`Failed to play ${item.title}`)
    }
  }, [ttsEnabled, playbackSpeed, volume])

  const playVideo = useCallback(async (item: MultimediaPlaylistItem) => {
    if (videoRef.current) {
      videoRef.current.src = item.url
      videoRef.current.playbackRate = playbackSpeed
      videoRef.current.volume = volume / 100
      await videoRef.current.play()
    }
  }, [playbackSpeed, volume])

  const playAudio = useCallback(async (item: MultimediaPlaylistItem) => {
    if (audioRef.current) {
      audioRef.current.src = item.url
      audioRef.current.playbackRate = playbackSpeed
      audioRef.current.volume = volume / 100
      await audioRef.current.play()
    }
  }, [playbackSpeed, volume])

  const playTextToSpeech = useCallback(async (item: MultimediaPlaylistItem) => {
    if (!('speechSynthesis' in window)) {
      setError('Text-to-speech not supported in this browser')
      return
    }

    const text = `${item.title}. ${item.url}` // Could fetch actual content
    const utterance = new SpeechSynthesisUtterance(text)
    utterance.rate = playbackSpeed
    utterance.volume = volume / 100
    utterance.voice = speechSynthesis.getVoices().find(v => v.name === ttsVoice) || null

    utterance.onend = () => {
      if (autoAdvance) {
        playNextItem()
      }
    }

    ttsRef.current = utterance
    speechSynthesis.speak(utterance)
  }, [playbackSpeed, volume, ttsVoice, autoAdvance])

  const stopPlayback = useCallback(() => {
    setIsPlaying(false)

    if (videoRef.current) {
      videoRef.current.pause()
    }
    if (audioRef.current) {
      audioRef.current.pause()
    }
    if (ttsRef.current) {
      speechSynthesis.cancel()
    }
  }, [])

  const playNextItem = useCallback(() => {
    if (!currentPlaylist || !currentItem) return

    const currentIndex = currentPlaylist.items.findIndex(item => item.id === currentItem.id)
    let nextIndex = currentIndex + 1

    if (shuffle) {
      nextIndex = Math.floor(Math.random() * currentPlaylist.items.length)
    } else if (nextIndex >= currentPlaylist.items.length) {
      if (repeat === 'all') {
        nextIndex = 0
      } else {
        setIsPlaying(false)
        return
      }
    }

    const nextItem = currentPlaylist.items[nextIndex]
    if (nextItem) {
      playItem(nextItem)
    }
  }, [currentPlaylist, currentItem, shuffle, repeat, playItem])

  const playPreviousItem = useCallback(() => {
    if (!currentPlaylist || !currentItem) return

    const currentIndex = currentPlaylist.items.findIndex(item => item.id === currentItem.id)
    const prevIndex = currentIndex > 0 ? currentIndex - 1 : currentPlaylist.items.length - 1
    const prevItem = currentPlaylist.items[prevIndex]

    if (prevItem) {
      playItem(prevItem)
    }
  }, [currentPlaylist, currentItem, playItem])

  const exportPlaylist = async () => {
    if (!currentPlaylist) return

    setExportStatus('exporting')
    setExportProgress(0)

    try {
      const format: ExportFormat = {
        type: exportFormat,
        settings: {
          includeContent: true,
          includeMetadata: true,
          compression: 'medium',
          chapterBreaks: true,
          tableOfContents: true
        }
      }

      const result = await multimediaPlaylistService.exportPlaylist(
        currentPlaylist,
        format,
        exportDestination
      )

      if (result.success) {
        setExportStatus('success')
        if (result.url) {
          // Download or open the exported file
          const link = document.createElement('a')
          link.href = result.url
          link.download = `${currentPlaylist.name}.${exportFormat}`
          link.click()
        }
      } else {
        setExportStatus('error')
        setError(result.error || 'Export failed')
      }
    } catch (err) {
      setExportStatus('error')
      setError('Export failed')
    }
  }

  const getItemIcon = (type: MultimediaPlaylistItem['type']) => {
    switch (type) {
      case 'video': return <Video className="w-4 h-4" />
      case 'audio': return <Headphones className="w-4 h-4" />
      case 'document': return <FileText className="w-4 h-4" />
      case 'article': return <BookOpen className="w-4 h-4" />
      case 'image': return <Image className="w-4 h-4" />
      default: return <ExternalLink className="w-4 h-4" />
    }
  }

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  if (!isOpen) return null

  return (
    <div className={cn("import-panel", isModernTheme && "modern-enhanced")}>
      <div className="import-header">
        <div className="flex items-center space-x-3">
          <div className={cn(
            "p-2 rounded-lg",
            isModernTheme
              ? "bg-blue-500/20 text-blue-400"
              : "bg-blue-100 text-blue-600"
          )}>
            <Video className="w-5 h-5" />
          </div>
          <div>
            <h2 className="import-title">
              🎵 {currentPlaylist?.name || 'Multimedia Playlist'}
            </h2>
            <p className={cn(
              "text-xs",
              isModernTheme ? "text-gray-300" : "text-gray-600"
            )}>
              {currentPlaylist?.items.length || 0} items • {gymMode ? 'Gym Mode' : 'Standard Mode'}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => setGymMode(!gymMode)}
            className={cn(
              "px-3 py-1 rounded text-xs font-medium transition-colors",
              gymMode
                ? "bg-green-500 text-white"
                : isModernTheme
                  ? "bg-white/10 text-white hover:bg-white/20"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
            )}
            aria-label={gymMode ? "Exit gym mode" : "Enter gym mode"}
          >
            🏃‍♂️ {gymMode ? 'Exit' : 'Gym'}
          </button>

          <button
            onClick={() => setShowSettings(!showSettings)}
            className="close-btn"
            aria-label="Settings"
          >
            <Settings className="w-4 h-4" />
          </button>

          <button
            onClick={() => setShowExportPanel(!showExportPanel)}
            className="close-btn"
            aria-label="Export playlist"
          >
            <Download className="w-4 h-4" />
          </button>

          <button
            onClick={onClose}
            className="close-btn"
            aria-label="Close panel"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      <div className="import-content"
        style={{
          maxHeight: 'calc(100vh - 120px)',
          overflowY: 'auto',
          overflowX: 'hidden'
        }}
      >

        {/* Playlist Queue Section */}
        <div className="import-section">
          <h3 className="section-title">Playlist Queue</h3>
          <p className="section-description">
            {currentPlaylist?.items.length || 0} multimedia items ready for playback
          </p>

          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-600">
                Creating playlist...
              </p>
            </div>
          ) : error ? (
            <div className="text-center py-8">
              <p className="text-red-500 text-sm mb-3">{error}</p>
              <button
                onClick={initializePlaylist}
                className="btn-primary"
              >
                Retry
              </button>
            </div>
          ) : (
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {currentPlaylist?.items.map((item, index) => (
                <button
                  key={item.id}
                  onClick={() => playItem(item)}
                  className={cn(
                    "format-option w-full text-left",
                    currentItem?.id === item.id && "active"
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div className="flex-shrink-0">
                      {getItemIcon(item.type)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <span className="font-medium">
                        {item.title}
                      </span>
                      <small className="block mt-1">
                        {item.type} • {item.duration ? formatDuration(item.duration) : 'Unknown duration'}
                        {item.readingTime && ` • ${item.readingTime}min read`}
                      </small>
                    </div>

                    <div className="text-xs text-gray-500 flex-shrink-0">
                      #{index + 1}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          )}
        </div>

        {/* Current Media Section */}
        <div className="import-section">
          <h3 className="section-title">Now Playing</h3>
          <p className="section-description">
            {currentItem ? `${currentItem.type}: ${currentItem.title}` : 'No media selected'}
          </p>

          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            {currentItem ? (
              <div className="text-center">
                {currentItem.type === 'video' && (
                  <video
                    ref={videoRef}
                    className="w-full max-w-md mx-auto rounded-lg shadow-sm mb-3"
                    controls={!gymMode}
                    poster={currentItem.thumbnail}
                  />
                )}

                {currentItem.type === 'audio' && (
                  <div>
                    <audio
                      ref={audioRef}
                      className="w-full mb-3"
                      controls
                    />
                    <div className="flex items-center justify-center space-x-2 text-gray-600">
                      <Headphones className="w-5 h-5" />
                      <span className="text-sm">{currentItem.title}</span>
                    </div>
                  </div>
                )}

                {(currentItem.type === 'document' || currentItem.type === 'article') && (
                  <div className="text-center">
                    <BookOpen className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                    <h4 className="font-medium mb-2">{currentItem.title}</h4>
                    <p className="text-sm text-gray-600 mb-3">
                      {ttsEnabled ? 'Playing with text-to-speech' : 'Ready to open'}
                    </p>
                    {!ttsEnabled && (
                      <button
                        onClick={() => window.open(currentItem.url, '_blank')}
                        className="btn-secondary"
                      >
                        <ExternalLink className="w-4 h-4" />
                        Open Article
                      </button>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8">
                <Play className="w-12 h-12 mx-auto mb-3 text-gray-400" />
                <p className="text-gray-600">Select an item to start playing</p>
              </div>
            )}
          </div>
        </div>

        {/* Playback Controls Section */}
        <div className="import-section">
          <h3 className="section-title">Playback Controls</h3>
          <p className="section-description">
            Control playback, shuffle, repeat, and volume settings
          </p>

          {/* Main Controls */}
          <div className="format-options mb-4">
            <div className="flex items-center justify-center space-x-3 mb-3">
              <button
                onClick={playPreviousItem}
                className="btn-secondary p-2"
                aria-label="Previous item"
              >
                <SkipBack className="w-4 h-4" />
              </button>

              <button
                onClick={() => isPlaying ? stopPlayback() : currentItem && playItem(currentItem)}
                className={cn(
                  "btn-primary p-3",
                  isPlaying && "bg-red-500 hover:bg-red-600"
                )}
                aria-label={isPlaying ? "Stop playback" : "Start playback"}
              >
                {isPlaying ? <Pause className="w-5 h-5" /> : <Play className="w-5 h-5" />}
              </button>

              <button
                onClick={playNextItem}
                className="btn-secondary p-2"
                aria-label="Next item"
              >
                <SkipForward className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Additional Controls */}
          <div className="format-options">
            <button
              onClick={() => setShuffle(!shuffle)}
              className={cn("format-option", shuffle && "active")}
              aria-label={shuffle ? "Disable shuffle" : "Enable shuffle"}
            >
              <Shuffle className="w-4 h-4" />
              <div>
                <span>Shuffle</span>
                <small>{shuffle ? 'Enabled' : 'Disabled'}</small>
              </div>
            </button>

            <button
              onClick={() => {
                const modes: Array<'none' | 'one' | 'all'> = ['none', 'one', 'all']
                const currentIndex = modes.indexOf(repeat)
                setRepeat(modes[(currentIndex + 1) % modes.length])
              }}
              className={cn("format-option", repeat !== 'none' && "active")}
              aria-label={`Repeat mode: ${repeat}`}
            >
              <Repeat className="w-4 h-4" />
              <div>
                <span>Repeat</span>
                <small>{repeat === 'none' ? 'Off' : repeat === 'one' ? 'One' : 'All'}</small>
              </div>
            </button>

            <button
              onClick={() => setTtsEnabled(!ttsEnabled)}
              className={cn("format-option", ttsEnabled && "active")}
              aria-label={ttsEnabled ? "Disable text-to-speech" : "Enable text-to-speech"}
            >
              {ttsEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
              <div>
                <span>Text-to-Speech</span>
                <small>{ttsEnabled ? 'Enabled' : 'Disabled'}</small>
              </div>
            </button>
          </div>

          {/* Volume Control */}
          <div className="form-row mt-4">
            <label className="flex items-center space-x-2 w-full">
              <Volume2 className="w-4 h-4 text-gray-600" />
              <input
                type="range"
                min="0"
                max="100"
                value={volume}
                onChange={(e) => setVolume(parseInt(e.target.value))}
                className="flex-1"
                aria-label="Volume control"
              />
              <span className="text-sm text-gray-600 w-8">{volume}</span>
            </label>
          </div>
        </div>

        {/* Export Panel */}
        {showExportPanel && (
          <div className="import-section">
            <h3 className="section-title">Export Playlist</h3>
            <p className="section-description">
              Export your multimedia playlist to various formats and destinations
            </p>

            <div className="format-options">
              <div className="form-group">
                <label className="form-label">Export Format</label>
                <select
                  value={exportFormat}
                  onChange={(e) => setExportFormat(e.target.value as ExportFormat['type'])}
                  className="w-full p-2 rounded border border-gray-300 bg-white text-gray-900"
                >
                  <option value="email">📧 Email</option>
                  <option value="pdf">📄 PDF</option>
                  <option value="epub">📚 EPUB</option>
                  <option value="kindle">📖 Kindle</option>
                  <option value="youtube-playlist">🎥 YouTube Playlist</option>
                  <option value="podcast">🎧 Podcast Feed</option>
                  <option value="rss">📡 RSS Feed</option>
                  <option value="json">💾 JSON</option>
                </select>
              </div>

              <div className="form-group">
                <label className="form-label">Destination</label>
                <input
                  type="text"
                  value={exportDestination}
                  onChange={(e) => setExportDestination(e.target.value)}
                  placeholder={exportFormat === 'email' ? '<EMAIL>' : 'Device or service'}
                  className="w-full p-2 rounded border border-gray-300 bg-white text-gray-900 placeholder-gray-500"
                />
              </div>

              <button
                onClick={exportPlaylist}
                disabled={exportStatus === 'exporting' || !exportDestination}
                className="btn-primary w-full"
              >
                {exportStatus === 'exporting' ? 'Exporting...' : 'Export Playlist'}
              </button>
            </div>

            {exportStatus === 'success' && (
              <div className="mt-4 p-3 rounded text-sm bg-green-100 border border-green-300 text-green-800">
                ✅ Playlist exported successfully!
              </div>
            )}

            {exportStatus === 'error' && (
              <div className="mt-4 p-3 rounded text-sm bg-red-100 border border-red-300 text-red-800">
                ❌ Export failed. Please try again.
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

export default MultimediaPlaylistPanel