/**
 * Memory Recovery Implementation
 * Comprehensive system for monitoring memory recovery, browser refresh management,
 * status checking, and preventive monitoring automation
 */

class MemoryRecoverySystem {
    constructor() {
        this.config = {
            monitoringInterval: 2000, // 2 seconds
            criticalThreshold: 85,
            warningThreshold: 70,
            safeThreshold: 50,
            emergencyThreshold: 95,
            recoveryCheckDuration: 30000, // 30 seconds
            autoRefreshThreshold: 90,
            maxRecoveryAttempts: 3
        };
        
        this.state = {
            isMonitoring: false,
            lastMemoryUsage: 0,
            recoveryAttempts: 0,
            cleanupHistory: [],
            systemHealth: 'unknown',
            preventiveActive: false,
            emergencyMode: false
        };
        
        this.intervals = new Map();
        this.callbacks = new Map();
        
        this.init();
    }

    /**
     * Initialize the memory recovery system
     */
    init() {
        console.log('🛡️ Memory Recovery System initializing...');
        
        this.setupEventListeners();
        this.startPreventiveMonitoring();
        this.checkInitialMemoryState();
        this.registerGlobalInterface();
        
        console.log('✅ Memory Recovery System ready');
    }

    /**
     * 1. Monitor Memory Recovery - Check current memory usage to verify cleanup effectiveness
     */
    async monitorMemoryRecovery(duration = this.config.recoveryCheckDuration) {
        console.log('📊 Starting memory recovery monitoring...');
        
        const startTime = Date.now();
        const initialUsage = this.getCurrentMemoryUsage();
        const recoveryData = {
            startTime,
            initialUsage,
            samples: [],
            duration,
            status: 'monitoring'
        };
        
        // Clear any existing recovery monitoring
        if (this.intervals.has('recovery')) {
            clearInterval(this.intervals.get('recovery'));
        }
        
        // Start recovery monitoring
        const recoveryInterval = setInterval(() => {
            const currentUsage = this.getCurrentMemoryUsage();
            const elapsed = Date.now() - startTime;
            
            recoveryData.samples.push({
                timestamp: Date.now(),
                usage: currentUsage,
                elapsed
            });
            
            // Log significant changes
            if (Math.abs(currentUsage - recoveryData.initialUsage) > 5) {
                const change = currentUsage - recoveryData.initialUsage;
                console.log(`📈 Memory change: ${change > 0 ? '+' : ''}${change.toFixed(1)}% (${currentUsage.toFixed(1)}%)`);
            }
            
            // Check if monitoring duration completed
            if (elapsed >= duration) {
                this.completeRecoveryMonitoring(recoveryData);
                clearInterval(recoveryInterval);
                this.intervals.delete('recovery');
            }
        }, 1000);
        
        this.intervals.set('recovery', recoveryInterval);
        
        return new Promise((resolve) => {
            this.callbacks.set('recoveryComplete', resolve);
        });
    }

    /**
     * Complete recovery monitoring and analyze results
     */
    completeRecoveryMonitoring(recoveryData) {
        const finalUsage = recoveryData.samples[recoveryData.samples.length - 1]?.usage || recoveryData.initialUsage;
        const totalChange = finalUsage - recoveryData.initialUsage;
        const avgUsage = recoveryData.samples.reduce((sum, sample) => sum + sample.usage, 0) / recoveryData.samples.length;
        
        recoveryData.finalUsage = finalUsage;
        recoveryData.totalChange = totalChange;
        recoveryData.avgUsage = avgUsage;
        recoveryData.status = 'completed';
        
        // Determine recovery effectiveness
        let effectiveness = 'unknown';
        if (totalChange < -10) {
            effectiveness = 'excellent';
            console.log('🎉 Excellent memory recovery detected!');
        } else if (totalChange < -5) {
            effectiveness = 'good';
            console.log('✅ Good memory recovery detected');
        } else if (totalChange < 0) {
            effectiveness = 'moderate';
            console.log('📉 Moderate memory recovery detected');
        } else if (totalChange < 5) {
            effectiveness = 'minimal';
            console.log('⚠️ Minimal memory recovery detected');
        } else {
            effectiveness = 'poor';
            console.log('❌ Poor memory recovery - usage increased');
        }
        
        recoveryData.effectiveness = effectiveness;
        
        // Store in history
        this.state.cleanupHistory.push(recoveryData);
        
        // Trigger callback if registered
        const callback = this.callbacks.get('recoveryComplete');
        if (callback) {
            callback(recoveryData);
            this.callbacks.delete('recoveryComplete');
        }
        
        // Recommend actions based on results
        this.recommendRecoveryActions(recoveryData);
    }

    /**
     * Recommend actions based on recovery monitoring results
     */
    recommendRecoveryActions(recoveryData) {
        const { effectiveness, finalUsage } = recoveryData;
        
        if (effectiveness === 'poor' || finalUsage > this.config.criticalThreshold) {
            console.log('🚨 Recommending browser refresh due to poor recovery');
            this.recommendBrowserRefresh('Poor memory recovery detected');
        } else if (effectiveness === 'minimal' || finalUsage > this.config.warningThreshold) {
            console.log('⚠️ Recommending additional cleanup');
            this.performAdditionalCleanup();
        } else {
            console.log('✅ Memory recovery successful - no additional action needed');
        }
    }

    /**
     * 2. Browser Refresh - If memory remains critical, refresh the browser page to reset memory state
     */
    async refreshBrowser(reason = 'Manual refresh requested', delay = 3000) {
        console.log(`🔄 Browser refresh initiated: ${reason}`);
        
        // Save current state before refresh
        this.saveStateBeforeRefresh();
        
        // Show countdown
        this.showRefreshCountdown(delay);
        
        // Perform cleanup before refresh
        await this.performPreRefreshCleanup();
        
        // Schedule refresh
        setTimeout(() => {
            console.log('🔄 Refreshing browser...');
            window.location.reload();
        }, delay);
    }

    /**
     * Recommend browser refresh with user confirmation
     */
    recommendBrowserRefresh(reason) {
        const message = `Memory recovery incomplete. ${reason}\n\nRefresh browser to reset memory state?`;
        
        if (confirm(message)) {
            this.refreshBrowser(`User confirmed: ${reason}`);
        } else {
            console.log('🔄 Browser refresh declined by user');
            this.performAlternativeRecovery();
        }
    }

    /**
     * Auto refresh if memory exceeds critical threshold
     */
    checkAutoRefresh() {
        const currentUsage = this.getCurrentMemoryUsage();
        
        if (currentUsage > this.config.autoRefreshThreshold) {
            console.log(`🚨 Auto-refresh triggered at ${currentUsage.toFixed(1)}% memory usage`);
            this.refreshBrowser(`Critical memory usage: ${currentUsage.toFixed(1)}%`, 5000);
            return true;
        }
        
        return false;
    }

    /**
     * Save state before refresh for recovery
     */
    saveStateBeforeRefresh() {
        const stateData = {
            timestamp: Date.now(),
            memoryUsage: this.getCurrentMemoryUsage(),
            cleanupHistory: this.state.cleanupHistory,
            recoveryAttempts: this.state.recoveryAttempts,
            reason: 'browser_refresh'
        };
        
        try {
            sessionStorage.setItem('memoryRecoveryState', JSON.stringify(stateData));
            console.log('💾 State saved before refresh');
        } catch (error) {
            console.warn('⚠️ Failed to save state before refresh:', error);
        }
    }

    /**
     * Show refresh countdown to user
     */
    showRefreshCountdown(delay) {
        let remaining = Math.ceil(delay / 1000);
        
        const countdownInterval = setInterval(() => {
            console.log(`🔄 Refreshing in ${remaining} seconds...`);
            
            // Update page title if possible
            if (document.title) {
                document.title = `Refreshing in ${remaining}s - ${document.title.replace(/^Refreshing in \d+s - /, '')}`;
            }
            
            remaining--;
            
            if (remaining <= 0) {
                clearInterval(countdownInterval);
            }
        }, 1000);
    }

    /**
     * Perform cleanup before refresh
     */
    async performPreRefreshCleanup() {
        console.log('🧹 Performing pre-refresh cleanup...');
        
        try {
            // Stop all intervals
            this.intervals.forEach((interval, key) => {
                clearInterval(interval);
                console.log(`⏹️ Stopped interval: ${key}`);
            });
            
            // Clear caches
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                console.log('🗑️ Browser caches cleared');
            }
            
            // Force garbage collection if available
            if (window.gc) {
                window.gc();
                console.log('🗑️ Garbage collection forced');
            }
            
        } catch (error) {
            console.warn('⚠️ Pre-refresh cleanup error:', error);
        }
    }

    /**
     * 3. Status Check - Use window.silentEmergencyCleanup.getStatus() for detailed status
     */
    async checkCleanupStatus() {
        console.log('✅ Checking cleanup system status...');
        
        const status = {
            timestamp: Date.now(),
            memoryUsage: this.getCurrentMemoryUsage(),
            systemHealth: this.state.systemHealth,
            preventiveActive: this.state.preventiveActive,
            emergencyMode: this.state.emergencyMode,
            cleanupSystems: {}
        };
        
        // Check silent emergency cleanup
        try {
            if (typeof window.silentEmergencyCleanup !== 'undefined') {
                const silentStatus = window.silentEmergencyCleanup.getStatus();
                status.cleanupSystems.silentEmergency = {
                    available: true,
                    status: silentStatus,
                    lastExecution: silentStatus.lastExecution || 'never'
                };
                console.log('🔍 Silent emergency cleanup status:', silentStatus);
            } else {
                status.cleanupSystems.silentEmergency = {
                    available: false,
                    reason: 'Not loaded or not available'
                };
                console.log('⚠️ Silent emergency cleanup not available');
            }
        } catch (error) {
            status.cleanupSystems.silentEmergency = {
                available: false,
                error: error.message
            };
            console.error('❌ Error checking silent emergency cleanup:', error);
        }
        
        // Check memory protection manager
        try {
            if (typeof window.memoryProtectionManager !== 'undefined') {
                const protectionStatus = window.memoryProtectionManager.getStatus();
                status.cleanupSystems.memoryProtection = {
                    available: true,
                    status: protectionStatus
                };
                console.log('🛡️ Memory protection manager status:', protectionStatus);
            } else {
                status.cleanupSystems.memoryProtection = {
                    available: false,
                    reason: 'Not loaded or not available'
                };
            }
        } catch (error) {
            status.cleanupSystems.memoryProtection = {
                available: false,
                error: error.message
            };
        }
        
        // Check browser memory API
        status.browserMemoryAPI = {
            available: !!performance.memory,
            details: performance.memory ? {
                usedJSHeapSize: performance.memory.usedJSHeapSize,
                totalJSHeapSize: performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: performance.memory.jsHeapSizeLimit
            } : null
        };
        
        // Check garbage collection availability
        status.garbageCollection = {
            available: !!window.gc,
            manual: typeof window.gc === 'function'
        };
        
        // Log comprehensive status
        console.log('📋 Comprehensive cleanup status:', status);
        
        return status;
    }

    /**
     * 4. Preventive Monitoring - Automatic system handles future memory emergencies
     */
    startPreventiveMonitoring() {
        if (this.state.preventiveActive) {
            console.log('🛡️ Preventive monitoring already active');
            return;
        }
        
        console.log('🛡️ Starting preventive monitoring system...');
        
        this.state.preventiveActive = true;
        
        // Main monitoring loop
        const preventiveInterval = setInterval(() => {
            this.performPreventiveCheck();
        }, this.config.monitoringInterval);
        
        this.intervals.set('preventive', preventiveInterval);
        
        // Health check interval
        const healthInterval = setInterval(() => {
            this.checkSystemHealth();
        }, 10000); // Every 10 seconds
        
        this.intervals.set('health', healthInterval);
        
        console.log('✅ Preventive monitoring system active');
    }

    /**
     * Stop preventive monitoring
     */
    stopPreventiveMonitoring() {
        if (!this.state.preventiveActive) {
            return;
        }
        
        console.log('⏹️ Stopping preventive monitoring...');
        
        ['preventive', 'health'].forEach(key => {
            if (this.intervals.has(key)) {
                clearInterval(this.intervals.get(key));
                this.intervals.delete(key);
            }
        });
        
        this.state.preventiveActive = false;
        console.log('⏹️ Preventive monitoring stopped');
    }

    /**
     * Perform preventive check
     */
    performPreventiveCheck() {
        const currentUsage = this.getCurrentMemoryUsage();
        const usageChange = currentUsage - this.state.lastMemoryUsage;
        
        // Check for rapid memory growth
        if (usageChange > 10 && currentUsage > this.config.warningThreshold) {
            console.log(`⚠️ Rapid memory growth detected: +${usageChange.toFixed(1)}%`);
            this.handleRapidMemoryGrowth(currentUsage, usageChange);
        }
        
        // Check thresholds
        if (currentUsage > this.config.emergencyThreshold) {
            this.handleEmergencyThreshold(currentUsage);
        } else if (currentUsage > this.config.criticalThreshold) {
            this.handleCriticalThreshold(currentUsage);
        } else if (currentUsage > this.config.warningThreshold) {
            this.handleWarningThreshold(currentUsage);
        }
        
        this.state.lastMemoryUsage = currentUsage;
    }

    /**
     * Handle emergency memory threshold
     */
    handleEmergencyThreshold(usage) {
        if (!this.state.emergencyMode) {
            console.log(`🚨 EMERGENCY: Memory usage at ${usage.toFixed(1)}%`);
            this.state.emergencyMode = true;
            this.executeEmergencyProtocol(usage);
        }
    }

    /**
     * Handle critical memory threshold
     */
    handleCriticalThreshold(usage) {
        console.log(`🔴 CRITICAL: Memory usage at ${usage.toFixed(1)}%`);
        this.executeCriticalCleanup(usage);
    }

    /**
     * Handle warning memory threshold
     */
    handleWarningThreshold(usage) {
        console.log(`🟡 WARNING: Memory usage at ${usage.toFixed(1)}%`);
        this.executePreventiveCleanup(usage);
    }

    /**
     * Handle rapid memory growth
     */
    handleRapidMemoryGrowth(usage, growth) {
        console.log(`📈 Rapid memory growth: ${growth.toFixed(1)}% increase`);
        
        // Trigger immediate cleanup
        this.executePreventiveCleanup(usage);
        
        // Start intensive monitoring
        this.startIntensiveMonitoring();
    }

    /**
     * Execute emergency protocol
     */
    async executeEmergencyProtocol(usage) {
        console.log('🚨 Executing emergency protocol...');
        
        try {
            // Try silent emergency cleanup first
            if (typeof window.silentEmergencyCleanup !== 'undefined') {
                await window.silentEmergencyCleanup.execute();
                console.log('✅ Silent emergency cleanup executed');
            }
            
            // Force garbage collection
            if (window.gc) {
                window.gc();
                console.log('🗑️ Emergency garbage collection forced');
            }
            
            // Clear all possible caches
            await this.emergencyCacheClear();
            
            // If still critical after cleanup, recommend refresh
            setTimeout(() => {
                const newUsage = this.getCurrentMemoryUsage();
                if (newUsage > this.config.criticalThreshold) {
                    this.recommendBrowserRefresh(`Emergency cleanup insufficient: ${newUsage.toFixed(1)}%`);
                } else {
                    this.state.emergencyMode = false;
                    console.log('✅ Emergency protocol successful');
                }
            }, 2000);
            
        } catch (error) {
            console.error('❌ Emergency protocol failed:', error);
            this.recommendBrowserRefresh('Emergency protocol failed');
        }
    }

    /**
     * Execute critical cleanup
     */
    executeCriticalCleanup(usage) {
        console.log('🔴 Executing critical cleanup...');
        
        try {
            // Force garbage collection
            if (window.gc) {
                window.gc();
            }
            
            // Clear session storage
            try {
                const keys = Object.keys(sessionStorage);
                keys.forEach(key => {
                    if (!key.includes('memoryRecovery')) {
                        sessionStorage.removeItem(key);
                    }
                });
            } catch (e) {
                console.warn('⚠️ Session storage cleanup failed:', e);
            }
            
            console.log('✅ Critical cleanup completed');
        } catch (error) {
            console.error('❌ Critical cleanup failed:', error);
        }
    }

    /**
     * Execute preventive cleanup
     */
    executePreventiveCleanup(usage) {
        console.log('🟡 Executing preventive cleanup...');
        
        try {
            // Light garbage collection
            if (window.gc) {
                window.gc();
            }
            
            // Clear old cache entries
            this.clearOldCacheEntries();
            
            console.log('✅ Preventive cleanup completed');
        } catch (error) {
            console.error('❌ Preventive cleanup failed:', error);
        }
    }

    /**
     * Emergency cache clearing
     */
    async emergencyCacheClear() {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                console.log('🗑️ Emergency cache clear completed');
            }
        } catch (error) {
            console.warn('⚠️ Emergency cache clear failed:', error);
        }
    }

    /**
     * Clear old cache entries
     */
    async clearOldCacheEntries() {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                // Only clear caches older than 1 hour
                const oldCaches = cacheNames.filter(name => {
                    // Simple heuristic - you might want to implement proper cache age checking
                    return name.includes('old') || name.includes('temp');
                });
                
                await Promise.all(oldCaches.map(name => caches.delete(name)));
                console.log(`🗑️ Cleared ${oldCaches.length} old cache entries`);
            }
        } catch (error) {
            console.warn('⚠️ Old cache clearing failed:', error);
        }
    }

    /**
     * Start intensive monitoring during critical periods
     */
    startIntensiveMonitoring() {
        if (this.intervals.has('intensive')) {
            return; // Already running
        }
        
        console.log('🔍 Starting intensive monitoring...');
        
        const intensiveInterval = setInterval(() => {
            const usage = this.getCurrentMemoryUsage();
            console.log(`🔍 Intensive monitor: ${usage.toFixed(1)}%`);
            
            // Stop intensive monitoring if memory stabilizes
            if (usage < this.config.warningThreshold) {
                console.log('✅ Memory stabilized - stopping intensive monitoring');
                clearInterval(intensiveInterval);
                this.intervals.delete('intensive');
            }
        }, 500); // Every 500ms
        
        this.intervals.set('intensive', intensiveInterval);
        
        // Auto-stop after 2 minutes
        setTimeout(() => {
            if (this.intervals.has('intensive')) {
                clearInterval(this.intervals.get('intensive'));
                this.intervals.delete('intensive');
                console.log('⏰ Intensive monitoring auto-stopped');
            }
        }, 120000);
    }

    /**
     * Check system health
     */
    checkSystemHealth() {
        try {
            const start = performance.now();
            
            // Simple performance test
            for (let i = 0; i < 10000; i++) {
                Math.random();
            }
            
            const duration = performance.now() - start;
            
            if (duration > 50) {
                this.state.systemHealth = 'degraded';
                console.log(`⚠️ System performance degraded: ${duration.toFixed(1)}ms`);
            } else if (duration > 20) {
                this.state.systemHealth = 'warning';
            } else {
                this.state.systemHealth = 'healthy';
            }
            
        } catch (error) {
            this.state.systemHealth = 'error';
            console.error('❌ System health check failed:', error);
        }
    }

    /**
     * Check initial memory state
     */
    checkInitialMemoryState() {
        const usage = this.getCurrentMemoryUsage();
        console.log(`📊 Initial memory usage: ${usage.toFixed(1)}%`);
        
        // Check for previous state
        try {
            const savedState = sessionStorage.getItem('memoryRecoveryState');
            if (savedState) {
                const state = JSON.parse(savedState);
                console.log('📋 Previous session state recovered:', state);
                
                // If previous session had high memory usage, start with intensive monitoring
                if (state.memoryUsage > this.config.criticalThreshold) {
                    console.log('🔍 Starting with intensive monitoring due to previous high usage');
                    this.startIntensiveMonitoring();
                }
            }
        } catch (error) {
            console.warn('⚠️ Failed to recover previous state:', error);
        }
        
        this.state.lastMemoryUsage = usage;
    }

    /**
     * Get current memory usage percentage
     */
    getCurrentMemoryUsage() {
        try {
            if (performance.memory) {
                const used = performance.memory.usedJSHeapSize;
                const limit = performance.memory.jsHeapSizeLimit;
                return (used / limit) * 100;
            } else {
                // Fallback estimation
                return this.estimateMemoryUsage();
            }
        } catch (error) {
            console.warn('⚠️ Memory usage calculation failed:', error);
            return 0;
        }
    }

    /**
     * Estimate memory usage when performance.memory is not available
     */
    estimateMemoryUsage() {
        try {
            const domElements = document.querySelectorAll('*').length;
            const scripts = document.scripts.length;
            const stylesheets = document.styleSheets.length;
            
            // Simple heuristic estimation
            const estimated = Math.min(
                30 + (domElements / 100) + (scripts * 2) + (stylesheets * 1.5),
                95
            );
            
            return estimated;
        } catch (error) {
            return 50; // Default fallback
        }
    }

    /**
     * Perform additional cleanup when recovery is insufficient
     */
    performAdditionalCleanup() {
        console.log('🧹 Performing additional cleanup...');
        
        try {
            // Clear console if possible
            if (console.clear) {
                console.clear();
            }
            
            // Remove event listeners from removed elements
            this.cleanupOrphanedListeners();
            
            // Clear any large objects from window
            this.clearLargeGlobalObjects();
            
            console.log('✅ Additional cleanup completed');
        } catch (error) {
            console.error('❌ Additional cleanup failed:', error);
        }
    }

    /**
     * Cleanup orphaned event listeners
     */
    cleanupOrphanedListeners() {
        // This is a simplified approach - in practice, you'd need more sophisticated tracking
        try {
            const elements = document.querySelectorAll('*');
            elements.forEach(element => {
                if (!element.isConnected) {
                    // Element is not in DOM, clone to remove listeners
                    const clone = element.cloneNode(true);
                    if (element.parentNode) {
                        element.parentNode.replaceChild(clone, element);
                    }
                }
            });
        } catch (error) {
            console.warn('⚠️ Orphaned listener cleanup failed:', error);
        }
    }

    /**
     * Clear large global objects
     */
    clearLargeGlobalObjects() {
        try {
            // Clear large arrays or objects that might be hanging around
            Object.keys(window).forEach(key => {
                if (key.startsWith('temp') || key.startsWith('cache')) {
                    try {
                        delete window[key];
                    } catch (e) {
                        // Ignore errors for non-configurable properties
                    }
                }
            });
        } catch (error) {
            console.warn('⚠️ Global object cleanup failed:', error);
        }
    }

    /**
     * Perform alternative recovery when browser refresh is declined
     */
    performAlternativeRecovery() {
        console.log('🔄 Performing alternative recovery...');
        
        // Increase recovery attempts
        this.state.recoveryAttempts++;
        
        if (this.state.recoveryAttempts < this.config.maxRecoveryAttempts) {
            // Try more aggressive cleanup
            this.executeEmergencyProtocol(this.getCurrentMemoryUsage());
        } else {
            console.log('❌ Maximum recovery attempts reached');
            alert('Memory recovery failed. Please save your work and refresh the browser manually.');
        }
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // Page visibility change
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                console.log('👁️ Page hidden - reducing monitoring frequency');
                // Could reduce monitoring frequency here
            } else {
                console.log('👁️ Page visible - resuming normal monitoring');
                this.performPreventiveCheck();
            }
        });
        
        // Before unload
        window.addEventListener('beforeunload', () => {
            this.saveStateBeforeRefresh();
            this.stopPreventiveMonitoring();
        });
        
        // Error handling
        window.addEventListener('error', (event) => {
            console.warn('⚠️ JavaScript error detected:', event.error);
            // Could trigger cleanup if errors are memory-related
        });
    }

    /**
     * Register global interface for external access
     */
    registerGlobalInterface() {
        window.memoryRecoverySystem = {
            // Public methods
            monitorRecovery: (duration) => this.monitorMemoryRecovery(duration),
            refreshBrowser: (reason) => this.refreshBrowser(reason),
            checkStatus: () => this.checkCleanupStatus(),
            getState: () => ({ ...this.state }),
            getCurrentUsage: () => this.getCurrentMemoryUsage(),
            
            // Control methods
            startMonitoring: () => this.startPreventiveMonitoring(),
            stopMonitoring: () => this.stopPreventiveMonitoring(),
            
            // Emergency methods
            emergencyCleanup: () => this.executeEmergencyProtocol(this.getCurrentMemoryUsage()),
            forceRefresh: () => this.refreshBrowser('Force refresh requested', 1000)
        };
        
        console.log('🌐 Global interface registered: window.memoryRecoverySystem');
    }

    /**
     * Get comprehensive system report
     */
    async getSystemReport() {
        const status = await this.checkCleanupStatus();
        
        return {
            timestamp: Date.now(),
            memoryUsage: this.getCurrentMemoryUsage(),
            systemState: { ...this.state },
            cleanupStatus: status,
            recommendations: this.generateRecommendations(),
            history: this.state.cleanupHistory.slice(-10) // Last 10 entries
        };
    }

    /**
     * Generate recommendations based on current state
     */
    generateRecommendations() {
        const usage = this.getCurrentMemoryUsage();
        const recommendations = [];
        
        if (usage > this.config.emergencyThreshold) {
            recommendations.push({
                priority: 'critical',
                action: 'immediate_refresh',
                message: 'Immediate browser refresh required'
            });
        } else if (usage > this.config.criticalThreshold) {
            recommendations.push({
                priority: 'high',
                action: 'emergency_cleanup',
                message: 'Execute emergency cleanup protocols'
            });
        } else if (usage > this.config.warningThreshold) {
            recommendations.push({
                priority: 'medium',
                action: 'preventive_cleanup',
                message: 'Perform preventive cleanup'
            });
        } else {
            recommendations.push({
                priority: 'low',
                action: 'continue_monitoring',
                message: 'Continue normal monitoring'
            });
        }
        
        return recommendations;
    }
}

// Initialize the system when script loads
const memoryRecoverySystem = new MemoryRecoverySystem();

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MemoryRecoverySystem;
}

console.log('🛡️ Memory Recovery Implementation loaded successfully');
console.log('📋 Available commands:');
console.log('  - window.memoryRecoverySystem.monitorRecovery()');
console.log('  - window.memoryRecoverySystem.refreshBrowser()');
console.log('  - window.memoryRecoverySystem.checkStatus()');
console.log('  - window.memoryRecoverySystem.getCurrentUsage()');