import { FC, useCallback, useMemo, useState } from 'react';
import { Bookmark } from '../types';
import { CheckIcon, FolderIcon, PencilIcon, TagIcon, XMarkIcon } from './icons/HeroIcons';

interface BulkEditModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedBookmarks: Bookmark[];
  onSave: (updates: { id: string; updates: Partial<Bookmark> }[]) => void;
}

interface BulkEditState {
  addTags: string[];
  removeTags: string[];
  setFolder: string[];
  updateTitles: boolean;
  titlePrefix: string;
  titleSuffix: string;
}

const BulkEditModal: FC<BulkEditModalProps> = ({
  isOpen,
  onClose,
  selectedBookmarks,
  onSave
}) => {
  const [editState, setEditState] = useState<BulkEditState>({
    addTags: [],
    removeTags: [],
    setFolder: [],
    updateTitles: false,
    titlePrefix: '',
    titleSuffix: ''
  });

  const [newTagInput, setNewTagInput] = useState('');
  const [removeTagInput, setRemoveTagInput] = useState('');
  const [folderInput, setFolderInput] = useState('');

  // Extract all unique tags from selected bookmarks
  const availableTags = useMemo(() => {
    const tagSet = new Set<string>();
    selectedBookmarks.forEach(bookmark => {
      bookmark.tags?.forEach(tag => tagSet.add(tag));
    });
    return Array.from(tagSet).sort();
  }, [selectedBookmarks]);

  // Extract all unique folders from selected bookmarks
  const availableFolders = useMemo(() => {
    const folderSet = new Set<string>();
    selectedBookmarks.forEach(bookmark => {
      if (bookmark.path && bookmark.path.length > 0) {
        const folderPath = bookmark.path.join(' > ');
        folderSet.add(folderPath);
      }
    });
    return Array.from(folderSet).sort();
  }, [selectedBookmarks]);

  const handleAddTag = useCallback((tag: string) => {
    if (tag && !editState.addTags.includes(tag)) {
      setEditState(prev => ({
        ...prev,
        addTags: [...prev.addTags, tag]
      }));
      setNewTagInput('');
    }
  }, [editState.addTags]);

  const handleRemoveAddTag = useCallback((tag: string) => {
    setEditState(prev => ({
      ...prev,
      addTags: prev.addTags.filter(t => t !== tag)
    }));
  }, []);

  const handleAddRemoveTag = useCallback((tag: string) => {
    if (tag && !editState.removeTags.includes(tag)) {
      setEditState(prev => ({
        ...prev,
        removeTags: [...prev.removeTags, tag]
      }));
      setRemoveTagInput('');
    }
  }, [editState.removeTags]);

  const handleRemoveRemoveTag = useCallback((tag: string) => {
    setEditState(prev => ({
      ...prev,
      removeTags: prev.removeTags.filter(t => t !== tag)
    }));
  }, []);

  const handleSetFolder = useCallback(() => {
    if (folderInput) {
      const folderPath = folderInput.split(' > ').map(f => f.trim()).filter(f => f);
      setEditState(prev => ({
        ...prev,
        setFolder: folderPath
      }));
      setFolderInput('');
    }
  }, [folderInput]);

  const handleClearFolder = useCallback(() => {
    setEditState(prev => ({
      ...prev,
      setFolder: []
    }));
  }, []);

  const handleSave = useCallback(() => {
    const updates = selectedBookmarks.map(bookmark => {
      const bookmarkUpdates: Partial<Bookmark> = {};

      // Handle tag additions
      if (editState.addTags.length > 0) {
        const currentTags = bookmark.tags || [];
        const newTags = [...currentTags];
        editState.addTags.forEach(tag => {
          if (!newTags.includes(tag)) {
            newTags.push(tag);
          }
        });
        bookmarkUpdates.tags = newTags;
      }

      // Handle tag removals
      if (editState.removeTags.length > 0) {
        const currentTags = bookmarkUpdates.tags || bookmark.tags || [];
        bookmarkUpdates.tags = currentTags.filter(tag => !editState.removeTags.includes(tag));
      }

      // Handle folder changes
      if (editState.setFolder.length > 0 || (editState.setFolder.length === 0 && folderInput === '')) {
        bookmarkUpdates.path = editState.setFolder;
      }

      // Handle title updates
      if (editState.updateTitles && (editState.titlePrefix || editState.titleSuffix)) {
        const currentTitle = bookmark.title;
        let newTitle = currentTitle;
        
        if (editState.titlePrefix) {
          newTitle = editState.titlePrefix + newTitle;
        }
        
        if (editState.titleSuffix) {
          newTitle = newTitle + editState.titleSuffix;
        }
        
        bookmarkUpdates.title = newTitle;
      }

      return {
        id: bookmark.id,
        updates: bookmarkUpdates
      };
    });

    onSave(updates);
  }, [selectedBookmarks, editState, folderInput, onSave]);

  const resetForm = useCallback(() => {
    setEditState({
      addTags: [],
      removeTags: [],
      setFolder: [],
      updateTitles: false,
      titlePrefix: '',
      titleSuffix: ''
    });
    setNewTagInput('');
    setRemoveTagInput('');
    setFolderInput('');
  }, []);

  const handleClose = useCallback(() => {
    resetForm();
    onClose();
  }, [resetForm, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={handleClose}></div>
        
        <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        
        <div className="inline-block align-bottom bg-slate-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium text-white flex items-center">
              <PencilIcon className="w-5 h-5 mr-2" />
              Bulk Edit {selectedBookmarks.length} Bookmark{selectedBookmarks.length !== 1 ? 's' : ''}
            </h3>
            <button
              onClick={handleClose}
              className="text-slate-400 hover:text-white transition-colors"
              aria-label="Close modal"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <div className="space-y-6">
            {/* Add Tags Section */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-3">
                <TagIcon className="w-4 h-4 inline mr-1" />
                Add Tags
              </label>
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2 mb-2">
                  {editState.addTags.map(tag => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-600 text-white"
                    >
                      {tag}
                      <button
                        onClick={() => handleRemoveAddTag(tag)}
                        className="ml-1 text-green-200 hover:text-white"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={newTagInput}
                    onChange={(e) => setNewTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddTag(newTagInput)}
                    placeholder="Add new tag..."
                    className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                  />
                  <button
                    onClick={() => handleAddTag(newTagInput)}
                    className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                  {availableTags.map(tag => (
                    <button
                      key={tag}
                      onClick={() => handleAddTag(tag)}
                      className="px-2 py-1 text-xs bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors"
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Remove Tags Section */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-3">
                <TagIcon className="w-4 h-4 inline mr-1" />
                Remove Tags
              </label>
              <div className="space-y-2">
                <div className="flex flex-wrap gap-2 mb-2">
                  {editState.removeTags.map(tag => (
                    <span
                      key={tag}
                      className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-600 text-white"
                    >
                      {tag}
                      <button
                        onClick={() => handleRemoveRemoveTag(tag)}
                        className="ml-1 text-red-200 hover:text-white"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    </span>
                  ))}
                </div>
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={removeTagInput}
                    onChange={(e) => setRemoveTagInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleAddRemoveTag(removeTagInput)}
                    placeholder="Tag to remove..."
                    className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                  />
                  <button
                    onClick={() => handleAddRemoveTag(removeTagInput)}
                    className="px-3 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
                  >
                    Add
                  </button>
                </div>
                <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                  {availableTags.map(tag => (
                    <button
                      key={tag}
                      onClick={() => handleAddRemoveTag(tag)}
                      className="px-2 py-1 text-xs bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors"
                    >
                      {tag}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Folder Management Section */}
            <div>
              <label className="block text-sm font-medium text-slate-300 mb-3">
                <FolderIcon className="w-4 h-4 inline mr-1" />
                Move to Folder
              </label>
              <div className="space-y-2">
                {editState.setFolder.length > 0 && (
                  <div className="flex items-center gap-2 mb-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                      {editState.setFolder.join(' > ')}
                      <button
                        onClick={handleClearFolder}
                        className="ml-1 text-blue-200 hover:text-white"
                      >
                        <XMarkIcon className="w-3 h-3" />
                      </button>
                    </span>
                  </div>
                )}
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={folderInput}
                    onChange={(e) => setFolderInput(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSetFolder()}
                    placeholder="Folder path (e.g., Work > Projects > Web)"
                    className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                  />
                  <button
                    onClick={handleSetFolder}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Set
                  </button>
                </div>
                <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
                  {availableFolders.map(folder => (
                    <button
                      key={folder}
                      onClick={() => {
                        setFolderInput(folder);
                        handleSetFolder();
                      }}
                      className="px-2 py-1 text-xs bg-slate-600 text-slate-300 rounded hover:bg-slate-500 transition-colors"
                    >
                      {folder}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            {/* Title Updates Section */}
            <div>
              <label className="flex items-center text-sm font-medium text-slate-300 mb-3">
                <input
                  type="checkbox"
                  checked={editState.updateTitles}
                  onChange={(e) => setEditState(prev => ({ ...prev, updateTitles: e.target.checked }))}
                  className="mr-2 rounded border-slate-600 bg-slate-700 text-sky-600 focus:ring-sky-500"
                />
                <PencilIcon className="w-4 h-4 mr-1" />
                Update Titles
              </label>
              {editState.updateTitles && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-xs text-slate-400 mb-1">Prefix</label>
                    <input
                      type="text"
                      value={editState.titlePrefix}
                      onChange={(e) => setEditState(prev => ({ ...prev, titlePrefix: e.target.value }))}
                      placeholder="Text to add before title"
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                    />
                  </div>
                  <div>
                    <label className="block text-xs text-slate-400 mb-1">Suffix</label>
                    <input
                      type="text"
                      value={editState.titleSuffix}
                      onChange={(e) => setEditState(prev => ({ ...prev, titleSuffix: e.target.value }))}
                      placeholder="Text to add after title"
                      className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-between mt-8">
            <button
              onClick={resetForm}
              className="px-4 py-2 text-slate-400 hover:text-white transition-colors"
            >
              Reset Form
            </button>
            <div className="flex gap-3">
              <button
                onClick={handleClose}
                className="px-4 py-2 bg-slate-600 text-white rounded-md hover:bg-slate-700 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSave}
                className="px-4 py-2 bg-sky-600 text-white rounded-md hover:bg-sky-700 transition-colors flex items-center"
              >
                <CheckIcon className="w-4 h-4 mr-2" />
                Apply Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BulkEditModal;