# Component Guide - Bookmark Manager Pro

## Overview
This guide provides detailed documentation for all React components in the Bookmark Manager Pro application, including their props, usage examples, and best practices.

## Core Components

### App Component
**File**: `App.tsx`  
**Type**: Main Application Container

#### Description
The root component that manages global application state and orchestrates all other components.

#### Key Features
- Global state management for bookmarks and user data
- File import/export handling
- Drag and drop functionality
- User authentication state
- Batch processing coordination

#### State Management
```typescript
const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);
const [filteredBookmarks, setFilteredBookmarks] = useState<Bookmark[]>([]);
const [selectedBookmarks, setSelectedBookmarks] = useState<Set<string>>(new Set());
const [userTier, setUserTier] = useState<UserTier>(userTierService.getCurrentTier());
// ... other state variables
```

#### Usage
```typescript
import App from './App';

function Root() {
  return <App />;
}
```

---

### BookmarkList Component
**File**: `components/BookmarkList.tsx`  
**Type**: Data Display Component

#### Props Interface
```typescript
interface BookmarkListProps {
  bookmarks: Bookmark[];                                    // Bookmarks to display
  selectedBookmarkIds: Set<string>;                        // Currently selected bookmark IDs
  processingStates: Record<string, BookmarkProcessingState>; // Processing status for each bookmark
  onToggleSelect: (id: string) => void;                     // Toggle bookmark selection
  onToggleSelectAll: () => void;                            // Toggle all bookmarks selection
  isAllSelected: boolean;                                   // Whether all bookmarks are selected
  sortConfig: SortConfig;                                   // Current sort configuration
  onSort: (key: keyof Bookmark) => void;                   // Sort handler
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void; // Update bookmark
  onAddTag: (bookmarkId: string, tag: string) => void;     // Add tag to bookmark
  onRemoveTag: (bookmarkId: string, tag: string) => void;  // Remove tag from bookmark
  onTagClick: (tag: string) => void;                        // Handle tag click for filtering
}
```

#### Features
- Sortable table headers with visual indicators
- Checkbox selection for individual and bulk operations
- Real-time processing state display
- Tag management with click-to-filter
- Responsive design for mobile devices

#### Usage Example
```typescript
<BookmarkList
  bookmarks={currentBookmarks}
  selectedBookmarkIds={selectedBookmarks}
  processingStates={processingStates}
  onToggleSelect={(id) => handleToggleSelect(id)}
  onToggleSelectAll={handleToggleSelectAll}
  isAllSelected={selectedBookmarks.size === currentBookmarks.length}
  sortConfig={sortConfig}
  onSort={handleSort}
  onUpdateBookmark={handleUpdateBookmark}
  onAddTag={handleAddTag}
  onRemoveTag={handleRemoveTag}
  onTagClick={handleTagClick}
/>
```

---

### BaseBookmarkComponent (New - Refactored)
**File**: `components/base/BaseBookmarkComponent.tsx`  
**Type**: Base Data Display Component

⚠️ **REFACTORING NOTE**: This replaces the duplicate logic between `BookmarkItem.tsx` and `ModernBookmarkCard.tsx`

#### Props Interface
```typescript
interface BaseBookmarkComponentProps {
  bookmark: Bookmark;                           // Bookmark data
  isSelected: boolean;                          // Selection state
  processingState?: BookmarkProcessingState;    // Processing status
  displayMode: 'list' | 'card' | 'compact';    // Display variant
  onToggleSelect: (id: string) => void;         // Toggle selection
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void; // Update bookmark
  onAddTag: (bookmarkId: string, tag: string) => void;     // Add tag
  onRemoveTag: (bookmarkId: string, tag: string) => void;  // Remove tag
  onTagClick: (tag: string) => void;            // Handle tag click
  className?: string;                           // Additional styling
}
```

#### Features
- **Unified Display Logic**: Single component for all bookmark display modes
- **Configurable Layouts**: List, card, and compact views
- **Shared State Logic**: Consistent behavior across display modes
- **Reusable Components**: Uses `EditableField` and `DropdownMenu`
- **Performance Optimized**: Memoized for large lists
- **Accessibility**: Full keyboard navigation and screen reader support

#### Usage Example
```typescript
// List view
<BaseBookmarkComponent
  bookmark={bookmark}
  displayMode="list"
  isSelected={isSelected}
  onToggleSelect={onToggleSelect}
  // ... other props
/>

// Card view
<BaseBookmarkComponent
  bookmark={bookmark}
  displayMode="card"
  isSelected={isSelected}
  onToggleSelect={onToggleSelect}
  // ... other props
/>
```

---

### BookmarkItem Component (Legacy - Being Phased Out)
**File**: `components/BookmarkItem.tsx`  
**Type**: Data Display Component
**Status**: ⚠️ **DEPRECATED** - Being replaced by `BaseBookmarkComponent`

#### Migration Notes
- This component will be refactored to use `BaseBookmarkComponent`
- Existing functionality will be preserved
- Display logic will be consolidated
- Props interface will remain compatible during transition

---

### YouTubeProcessor Component
**File**: `components/YouTubeProcessor.tsx`  
**Type**: Feature Component

#### Props Interface
```typescript
interface YouTubeProcessorProps {
  bookmark: Bookmark;                           // YouTube bookmark to process
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void; // Update callback
  onClose?: () => void;                         // Close modal callback
}
```

#### Features
- YouTube URL detection and validation
- Video information extraction
- Transcript processing with MCP agent
- AI-powered instruction generation
- Progress tracking with multiple stages
- Error handling and retry mechanisms
- Instruction slide viewer integration

#### Usage Example
```typescript
<YouTubeProcessor
  bookmark={selectedBookmark}
  onUpdateBookmark={handleUpdateBookmark}
  onClose={() => setShowProcessor(false)}
/>
```

---

### VirtualizedBookmarkList Component
**File**: `components/VirtualizedBookmarkList.tsx`  
**Type**: Performance Component

#### Props Interface
```typescript
interface VirtualizedBookmarkListProps {
  bookmarks: Bookmark[];                        // Bookmarks to display
  selectedBookmarkIds: Set<string>;            // Selected bookmark IDs
  processingStates: Record<string, BookmarkProcessingState>; // Processing states
  onToggleSelect: (id: string) => void;         // Toggle selection
  onToggleSelectAll: () => void;                // Toggle all selection
  isAllSelected: boolean;                       // All selected state
  sortConfig: SortConfig;                       // Sort configuration
  onSort: (key: keyof Bookmark) => void;        // Sort handler
  onUpdateBookmark: (id: string, updates: Partial<Bookmark>) => void; // Update bookmark
  onAddTag: (bookmarkId: string, tag: string) => void;     // Add tag
  onRemoveTag: (bookmarkId: string, tag: string) => void;  // Remove tag
  onTagClick: (tag: string) => void;            // Tag click handler
  height?: number;                              // Container height
  itemHeight?: number;                          // Item height
}
```

#### Features
- Virtual scrolling for large datasets (1000+ bookmarks)
- Sortable table headers
- Bulk selection capabilities
- Optimized rendering performance
- Configurable item heights

---

### ErrorBoundary Component
**File**: `components/ErrorBoundary.tsx`  
**Type**: Error Handling Component

#### Props Interface
```typescript
interface Props {
  children: ReactNode;                          // Child components
  fallback?: ReactNode;                         // Custom fallback UI
  onError?: (error: Error, errorInfo: ErrorInfo) => void; // Error callback
}
```

#### Features
- Catches JavaScript errors in component tree
- Displays fallback UI when errors occur
- Logs error details for debugging
- Retry functionality
- Custom error reporting

---

### ToastNotification Component
**File**: `components/ToastNotification.tsx`  
**Type**: UI Feedback Component

#### Features
- Success, error, warning, and info notifications
- Auto-dismiss with configurable timeout
- Manual dismiss capability
- Stacking multiple notifications
- Smooth animations

---

### InstructionSlideViewer Component
**File**: `components/InstructionSlideViewer.tsx`  
**Type**: Content Display Component

#### Props Interface
```typescript
interface InstructionSlideViewerProps {
  instructions: InstructionStep[];              // Instruction steps to display
  onClose: () => void;                          // Close viewer callback
}
```

#### Features
- Step-by-step instruction display
- Navigation between slides
- Progress indicator
- Responsive design
- Keyboard navigation support

---

### BookmarkQA Component
**File**: `components/BookmarkQA.tsx`  
**Type**: AI Feature Component

#### Features
- Natural language questions about bookmarks
- AI-powered answers using bookmark context
- Question history
- Source attribution
- Real-time processing indicators

## Component Best Practices

### Performance Optimization
- Use `React.memo` for components that receive stable props
- Implement `useMemo` and `useCallback` for expensive computations
- Use `VirtualizedBookmarkList` for large datasets (>100 items)
- Lazy load heavy components like `YouTubeProcessor`

### Error Handling
- Wrap feature components with `ErrorBoundary`
- Implement proper loading and error states
- Use `errorService` for centralized error logging
- Provide meaningful error messages to users

### Accessibility
- Use semantic HTML elements
- Implement proper ARIA labels and roles
- Ensure keyboard navigation support
- Maintain proper color contrast ratios

### State Management
- Keep component state minimal and focused
- Lift state up when shared between components
- Use proper TypeScript interfaces for props
- Implement proper cleanup in useEffect hooks

---

### ActionToolbar Component
**File**: `components/ActionToolbar.tsx`  
**Type**: Action Component

#### Props Interface
```typescript
interface ActionToolbarProps {
  onSummarize: () => void;                      // Trigger summarization
  onTag: () => void;                            // Trigger tagging
  onExportSelectedCSV: () => void;              // Export selected to CSV
  onExportAllCSV: () => void;                   // Export all to CSV
  onExportSelectedPDF: () => void;              // Export selected to PDF
  onExportAllPDF: () => void;                   // Export all to PDF
  onExportSelectedHTML: () => void;             // Export selected to HTML
  onExportAllHTML: () => void;                  // Export all to HTML
  onExportSelectedCSVByEmail: () => void;       // Email selected as CSV
  onExportAllCSVByEmail: () => void;            // Email all as CSV
  onExportSelectedPDFByEmail: () => void;       // Email selected as PDF
  onExportAllPDFByEmail: () => void;            // Email all as PDF
  onExportSelectedHTMLByEmail: () => void;      // Email selected as HTML
  onExportAllHTMLByEmail: () => void;           // Email all as HTML
  onDeleteSelected: () => void;                 // Delete selected bookmarks
  numSelected: number;                          // Number of selected bookmarks
  isProcessing: boolean;                        // Whether processing is active
  hasAnyBookmarks: boolean;                     // Whether any bookmarks exist
}
```

#### Features
- Contextual button states based on selection
- Processing indicators and disabled states
- Dropdown menus for export options
- User tier-based feature access
- Responsive button layout

#### Button Variants
```typescript
type ButtonVariant = 'primary' | 'danger' | 'success' | 'info' | 'warning' | 'neutral';
```

#### Usage Example
```typescript
<ActionToolbar
  onSummarize={handleSummarizeSelected}
  onTag={handleTagSelected}
  onExportSelectedCSV={handleExportSelectedCSV}
  onExportAllCSV={handleExportAllCSV}
  onDeleteSelected={handleDeleteSelected}
  numSelected={selectedBookmarks.size}
  isProcessing={isProcessing}
  hasAnyBookmarks={bookmarks.length > 0}
  // ... other export handlers
/>
```

---

### BookmarkImporter Component
**File**: `components/BookmarkImporter.tsx`  
**Type**: Input Component

#### Props Interface
```typescript
interface BookmarkImporterProps {
  onFileChange: (event: ChangeEvent<HTMLInputElement>) => void; // File selection handler
  isLoading: boolean;                                           // Loading state
  onClearImport: () => void;                                    // Clear import handler
  hasImportedFile: boolean;                                     // Whether file is imported
  resetToken: number;                                           // Key for resetting input
}
```

#### Features
- File input with HTML file filtering
- Loading state with spinner
- Clear import functionality
- Drag and drop support (handled by parent)
- Responsive design

#### Usage Example
```typescript
<BookmarkImporter
  onFileChange={handleFileChange}
  isLoading={isLoadingFile}
  onClearImport={handleClearImport}
  hasImportedFile={bookmarks.length > 0}
  resetToken={importerKey}
/>
```

---

### FolderFilter Component
**File**: `components/FolderFilter.tsx`  
**Type**: Filter Component

#### Props Interface
```typescript
interface FolderFilterProps {
  folders: string[];                            // Available folders
  selectedFolder: string;                       // Currently selected folder
  onFolderChange: (folder: string) => void;     // Folder selection handler
}
```

#### Features
- Dropdown folder selection
- "All Folders" option
- Folder hierarchy display
- Search within folders

#### Usage Example
```typescript
<FolderFilter
  folders={availableFolders}
  selectedFolder={selectedFolder}
  onFolderChange={setSelectedFolder}
/>
```

---

### PaginationControls Component
**File**: `components/PaginationControls.tsx`  
**Type**: Navigation Component

#### Props Interface
```typescript
interface PaginationControlsProps {
  currentPage: number;                          // Current page number
  totalPages: number;                           // Total number of pages
  onPageChange: (page: number) => void;         // Page change handler
}
```

#### Features
- Previous/Next navigation
- Direct page number input
- Page range display
- Disabled states for boundary pages

#### Usage Example
```typescript
<PaginationControls
  currentPage={currentPage}
  totalPages={totalPages}
  onPageChange={setCurrentPage}
/>
```

---

### BookmarkQA Component
**File**: `components/BookmarkQA.tsx`  
**Type**: AI Interface Component

#### Props Interface
```typescript
interface BookmarkQAProps {
  bookmarks: Bookmark[];                        // Available bookmarks for context
  onSearch: (query: string) => void;            // Search handler
  isSearching: boolean;                         // Search loading state
  searchResults?: SearchGroundedResult;         // Search results
  searchError?: string;                         // Search error message
}
```

#### Features
- Natural language query input
- AI-powered search results
- Source attribution
- Error handling and retry
- Search history

#### Usage Example
```typescript
<BookmarkQA
  bookmarks={bookmarks}
  onSearch={handleWebSearch}
  isSearching={isSearchingWeb}
  searchResults={searchResults}
  searchError={searchError}
/>
```

---

### AuthModal Component
**File**: `components/AuthModal.tsx`  
**Type**: Modal Component

#### Props Interface
```typescript
interface AuthModalProps {
  isOpen: boolean;                              // Modal visibility
  onClose: () => void;                          // Close handler
  onAuthSuccess: (token: string) => void;       // Authentication success handler
}
```

#### Features
- Login/signup forms
- Form validation
- Loading states
- Error handling
- Modal overlay and focus management

#### Usage Example
```typescript
<AuthModal
  isOpen={isAuthModalOpen}
  onClose={() => setIsAuthModalOpen(false)}
  onAuthSuccess={handleAuthSuccess}
/>
```

---

### UserProfile Component
**File**: `components/UserProfile.tsx`  
**Type**: User Interface Component

#### Props Interface
```typescript
interface UserProfileProps {
  userTier: UserTier;                           // Current user tier
  onUpgrade: () => void;                        // Upgrade handler
  onLogout: () => void;                         // Logout handler
}
```

#### Features
- User tier display
- Feature access overview
- Upgrade prompts
- Account management

#### Usage Example
```typescript
<UserProfile
  userTier={userTier}
  onUpgrade={() => setIsAuthModalOpen(true)}
  onLogout={handleLogout}
/>
```

## Icon Components

### HeroIcons
**File**: `components/icons/HeroIcons.tsx`  
**Type**: Icon Library

#### Available Icons
- `SparklesIcon` - AI/magic features
- `MagnifyingGlassIcon` - Search functionality
- `LinkIcon` - External links
- `TrashIcon` - Delete actions
- `DocumentArrowDownIcon` - Export actions
- `TagIcon` - Tagging features
- `ShieldCheckIcon` - Security/premium features
- `ChevronUpIcon` / `ChevronDownIcon` - Sort indicators
- `ArrowPathIcon` - Refresh/reload

#### Usage Example
```typescript
import { SparklesIcon, TrashIcon } from './components/icons/HeroIcons';

<button>
  <SparklesIcon className="w-5 h-5" />
  Generate Summary
</button>
```

### SpinnerIcon
**File**: `components/icons/SpinnerIcon.tsx`  
**Type**: Loading Indicator

#### Props Interface
```typescript
interface SpinnerIconProps {
  className?: string;                           // CSS classes
}
```

#### Usage Example
```typescript
import SpinnerIcon from './components/icons/SpinnerIcon';

{isLoading && <SpinnerIcon className="w-6 h-6 text-sky-400" />}
```

## Component Patterns

### State Management Pattern
```typescript
// Local state for component-specific data
const [localState, setLocalState] = useState(initialValue);

// Props for parent-child communication
interface ComponentProps {
  data: DataType;
  onAction: (param: ParamType) => void;
}

// Event handlers
const handleAction = useCallback((param: ParamType) => {
  // Handle action
  onAction(param);
}, [onAction]);
```

### Loading State Pattern
```typescript
const [isLoading, setIsLoading] = useState(false);

const handleAsyncAction = async () => {
  setIsLoading(true);
  try {
    await asyncOperation();
  } catch (error) {
    console.error('Operation failed:', error);
  } finally {
    setIsLoading(false);
  }
};

// In render
{isLoading ? <SpinnerIcon /> : <ActionButton />}
```

### Error Handling Pattern
```typescript
const [error, setError] = useState<string | null>(null);

const handleAction = async () => {
  setError(null);
  try {
    await riskyOperation();
  } catch (err) {
    setError(err instanceof Error ? err.message : 'Unknown error');
  }
};

// In render
{error && (
  <div className="text-red-500 text-sm mt-2">
    {error}
  </div>
)}
```

## Styling Guidelines

### Tailwind CSS Classes
The application uses Tailwind CSS with a dark theme. Common class patterns:

#### Colors
- Primary: `bg-sky-600 hover:bg-sky-700 text-white`
- Secondary: `bg-slate-600 hover:bg-slate-700 text-white`
- Danger: `bg-red-600 hover:bg-red-700 text-white`
- Success: `bg-emerald-600 hover:bg-emerald-700 text-white`

#### Layout
- Container: `max-w-7xl mx-auto px-4 sm:px-6 lg:px-8`
- Card: `bg-slate-800 rounded-lg shadow-lg p-6`
- Button: `px-4 py-2 rounded-md font-medium transition-colors`

#### Responsive Design
- Mobile-first approach with `sm:`, `md:`, `lg:` breakpoints
- Flexible layouts using `flex` and `grid`
- Responsive text sizing with `text-sm sm:text-base lg:text-lg`

### Component Structure
```typescript
const Component: React.FC<ComponentProps> = ({ prop1, prop2, onAction }) => {
  // Hooks
  const [state, setState] = useState(initialValue);
  
  // Event handlers
  const handleEvent = useCallback(() => {
    // Handle event
  }, [dependencies]);
  
  // Render
  return (
    <div className="component-container">
      {/* Component content */}
    </div>
  );
};

export default Component;
```

## Accessibility Guidelines

### ARIA Labels
- Use `aria-label` for buttons without text
- Use `aria-describedby` for form field descriptions
- Use `aria-expanded` for collapsible content

### Keyboard Navigation
- Ensure all interactive elements are focusable
- Implement proper tab order
- Support Enter and Space for button activation

### Screen Reader Support
- Use semantic HTML elements
- Provide alternative text for images
- Use proper heading hierarchy

## Performance Optimization

### React.memo Usage
```typescript
const OptimizedComponent = React.memo<ComponentProps>(({ data, onAction }) => {
  // Component implementation
}, (prevProps, nextProps) => {
  // Custom comparison function if needed
  return prevProps.data === nextProps.data;
});
```

### useMemo and useCallback
```typescript
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);

const memoizedCallback = useCallback((param) => {
  onAction(param);
}, [onAction]);
```

### Virtual Scrolling (Future Enhancement)
```typescript
import { FixedSizeList as List } from 'react-window';

const VirtualizedList = ({ items }) => (
  <List
    height={600}
    itemCount={items.length}
    itemSize={80}
    itemData={items}
  >
    {({ index, style, data }) => (
      <div style={style}>
        <BookmarkItem bookmark={data[index]} />
      </div>
    )}
  </List>
);
```

## Testing Components

### Unit Testing Example
```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import BookmarkItem from './BookmarkItem';

test('renders bookmark item with title', () => {
  const mockBookmark = {
    id: '1',
    title: 'Test Bookmark',
    url: 'https://example.com'
  };
  
  render(
    <BookmarkItem
      bookmark={mockBookmark}
      isSelected={false}
      onToggleSelect={jest.fn()}
      onUpdate={jest.fn()}
      onAddTag={jest.fn()}
      onRemoveTag={jest.fn()}
      onTagClick={jest.fn()}
    />
  );
  
  expect(screen.getByText('Test Bookmark')).toBeInTheDocument();
});
```

### Integration Testing
```typescript
test('bookmark selection workflow', async () => {
  render(<App />);
  
  // Import bookmarks
  const fileInput = screen.getByLabelText(/import bookmarks/i);
  fireEvent.change(fileInput, { target: { files: [mockFile] } });
  
  // Wait for bookmarks to load
  await screen.findByText('Test Bookmark');
  
  // Select bookmark
  const checkbox = screen.getByRole('checkbox');
  fireEvent.click(checkbox);
  
  // Verify selection
  expect(checkbox).toBeChecked();
});
```

This component guide provides comprehensive documentation for all components in the Bookmark Manager Pro application. It should be updated as new components are added or existing ones are modified.