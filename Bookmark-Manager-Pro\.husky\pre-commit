#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Run TypeScript type checking via ESLint (NO_TSC_RULE compliant)
echo "📝 Checking TypeScript types via ESLint..."
npm run lint
if [ $? -ne 0 ]; then
  echo "❌ TypeScript/ESLint check failed. Please fix the errors before committing."
  exit 1
fi

# Run ESLint
echo "🔧 Running ESLint..."
npx eslint . --ext .ts,.tsx --max-warnings 0
if [ $? -ne 0 ]; then
  echo "❌ ESLint check failed. Please fix the linting errors before committing."
  exit 1
fi

# Run Prettier check
echo "💅 Checking code formatting..."
npx prettier --check .
if [ $? -ne 0 ]; then
  echo "❌ Code formatting check failed. Run 'npm run format' to fix formatting issues."
  exit 1
fi

# Run tests
echo "🧪 Running tests..."
npm run test:unit
if [ $? -ne 0 ]; then
  echo "❌ Tests failed. Please fix the failing tests before committing."
  exit 1
fi

echo "✅ All pre-commit checks passed!"