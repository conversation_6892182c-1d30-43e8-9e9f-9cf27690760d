import { act, renderHook } from '@testing-library/react';
import * as fs from 'fs';
import { useCodeQualityAnalyzer } from './useCodeQualityAnalyzer';

// Mock fs module
jest.mock('fs');

const mockFs = fs as jest.Mocked<typeof fs>;

describe('useCodeQualityAnalyzer', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFs.existsSync.mockReturnValue(true);
  });

  describe('Hook Initialization', () => {
    it('should initialize with default values', () => {
      const { result } = renderHook(() => useCodeQualityAnalyzer());

      expect(result.current.analysisResults).toEqual([]);
      expect(result.current.isAnalyzing).toBe(false);
      expect(typeof result.current.startWatching).toBe('function');
      expect(typeof result.current.stopWatching).toBe('function');
      expect(typeof result.current.analyzeFile).toBe('function');
      expect(typeof result.current.clearResults).toBe('function');
      expect(typeof result.current.getResultsForFile).toBe('function');
    });

    it('should accept custom options', () => {
      const options = {
        watchPaths: ['custom/**/*.ts'],
        excludePatterns: ['custom/exclude/**'],
        debounceMs: 500,
        enableRealTimeAnalysis: false,
        analysisTypes: ['code-smell', 'performance'] as ('code-smell' | 'performance' | 'design-pattern' | 'best-practice' | 'readability' | 'maintainability')[]
      };

      const { result } = renderHook(() => useCodeQualityAnalyzer(options));
      
      // Hook should initialize successfully with custom options
      expect(result.current.analysisResults).toEqual([]);
      expect(result.current.isAnalyzing).toBe(false);
    });
  });

  describe('Code Analysis Functionality', () => {
    it('should detect code smells - long parameter lists', async () => {
      const codeWithLongParams = `
function badFunction(param1: string, param2: number, param3: boolean, param4: object, param5: any) {
  return param1 + param2;
}
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithLongParams);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'code-smell',
            message: 'Function has too many parameters',
            suggestion: 'Consider using an options object or breaking down the function'
          })
        );
      });
    });

    it('should detect magic numbers', async () => {
      const codeWithMagicNumbers = `
if (attempts > 42) {
  throw new Error('Too many attempts');
}
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithMagicNumbers);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'code-smell',
            message: 'Magic number detected',
            suggestion: 'Extract magic numbers into named constants'
          })
        );
      });
    });

    it('should detect missing error handling', async () => {
      const codeWithoutErrorHandling = `
const data = await fetch('/api/data');
const result = await data.json();
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithoutErrorHandling);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'best-practice',
            message: 'Missing error handling for async operation',
            suggestion: 'Add proper error handling with try-catch or .catch()'
          })
        );
      });
    });

    it('should detect console statements', async () => {
      const codeWithConsole = `
console.log('Debug message');
console.warn('Warning message');
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithConsole);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        const consoleSuggestions = analysisResult?.suggestions.filter(
          s => s.message === 'Console statement found'
        );
        expect(consoleSuggestions).toHaveLength(2);
      });
    });

    it('should detect performance issues - chained array operations', async () => {
      const codeWithChainedOperations = `
const result = items.filter(item => item.active).find(item => item.id === targetId);
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithChainedOperations);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'performance',
            message: 'Chained array operations detected',
            suggestion: 'Consider combining operations or using more efficient alternatives'
          })
        );
      });
    });

    it('should suggest React.memo for components', async () => {
      const reactComponentCode = `
export const MyComponent = (props: Props) => {
  return <div>{props.title}</div>;
};
`;
      
      mockFs.readFileSync.mockReturnValue(reactComponentCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('Component.tsx');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'performance',
            message: 'Consider memoization for React component',
            suggestion: 'Wrap component with React.memo() if props are stable'
          })
        );
      });
    });

    it('should detect "any" type usage', async () => {
      const codeWithAnyType = `
function processData(data: any) {
  return data.someProperty;
}
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithAnyType);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'maintainability',
            message: 'Using "any" type',
            suggestion: 'Replace "any" with specific types for better type safety'
          })
        );
      });
    });

    it('should detect long lines', async () => {
      const codeWithLongLine = `
const veryLongVariableName = someVeryLongFunctionName(parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7);
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithLongLine);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        expect(analysisResult?.suggestions).toContainEqual(
          expect.objectContaining({
            type: 'readability',
            message: 'Line is too long',
            suggestion: 'Break long lines into multiple lines for better readability'
          })
        );
      });
    });
  });

  describe('Analysis Results Management', () => {
    it('should store analysis results', async () => {
      const testCode = 'console.log("test");';
      mockFs.readFileSync.mockReturnValue(testCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        await result.current.analyzeFile('test.ts');
      });
      
      expect(result.current.analysisResults).toHaveLength(1);
      expect(result.current.analysisResults[0].file).toBe('test.ts');
      expect(result.current.analysisResults[0].suggestions.length).toBeGreaterThan(0);
    });

    it('should update existing results for the same file', async () => {
      const testCode1 = 'console.log("test1");';
      const testCode2 = 'console.log("test2"); console.warn("warning");';
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      // First analysis
      mockFs.readFileSync.mockReturnValue(testCode1);
      await act(async () => {
        await result.current.analyzeFile('test.ts');
      });
      
      expect(result.current.analysisResults).toHaveLength(1);
      expect(result.current.analysisResults[0].suggestions).toHaveLength(1);
      
      // Second analysis of same file
      mockFs.readFileSync.mockReturnValue(testCode2);
      await act(async () => {
        await result.current.analyzeFile('test.ts');
      });
      
      expect(result.current.analysisResults).toHaveLength(1); // Still one file
      expect(result.current.analysisResults[0].suggestions).toHaveLength(2); // But more suggestions
    });

    it('should clear all results', async () => {
      const testCode = 'console.log("test");';
      mockFs.readFileSync.mockReturnValue(testCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        await result.current.analyzeFile('test.ts');
      });
      
      expect(result.current.analysisResults).toHaveLength(1);
      
      act(() => {
        result.current.clearResults();
      });
      
      expect(result.current.analysisResults).toHaveLength(0);
    });

    it('should get results for specific file', async () => {
      const testCode = 'console.log("test");';
      mockFs.readFileSync.mockReturnValue(testCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        await result.current.analyzeFile('test1.ts');
        await result.current.analyzeFile('test2.ts');
      });
      
      const test1Results = result.current.getResultsForFile('test1.ts');
      const test2Results = result.current.getResultsForFile('test2.ts');
      const nonExistentResults = result.current.getResultsForFile('nonexistent.ts');
      
      expect(test1Results).toHaveLength(1);
      expect(test2Results).toHaveLength(1);
      expect(nonExistentResults).toHaveLength(0);
      expect(test1Results[0].file).toBe('test1.ts');
      expect(test2Results[0].file).toBe('test2.ts');
    });
  });

  describe('Severity Assessment', () => {
    it('should assign correct severity levels', async () => {
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      // Low severity (few issues)
      const lowSeverityCode = 'const x = 1;';
      mockFs.readFileSync.mockReturnValue(lowSeverityCode);
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('low.ts');
        expect(analysisResult?.severity).toBe('low');
      });
      
      // High severity (many issues)
      const highSeverityCode = Array(15).fill('console.log("issue");').join('\n');
      mockFs.readFileSync.mockReturnValue(highSeverityCode);
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('high.ts');
        expect(analysisResult?.severity).toBe('high');
      });
    });
  });

  describe('File Watching', () => {
    it('should start and stop watching', () => {
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      act(() => {
        result.current.startWatching();
      });
      
      // Verify watcher was created (mocked)
      expect(require('chokidar').watch).toHaveBeenCalled();
      
      act(() => {
        result.current.stopWatching();
      });
      
      // Should not throw errors
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent files gracefully', async () => {
      mockFs.existsSync.mockReturnValue(false);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('nonexistent.ts');
        expect(analysisResult).toBeNull();
      });
    });

    it('should handle file read errors gracefully', async () => {
      mockFs.existsSync.mockReturnValue(true);
      mockFs.readFileSync.mockImplementation(() => {
        throw new Error('File read error');
      });
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('error.ts');
        expect(analysisResult).toBeNull();
      });
    });

    it('should set isAnalyzing state correctly during analysis', async () => {
      const testCode = 'console.log("test");';
      mockFs.readFileSync.mockReturnValue(testCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      expect(result.current.isAnalyzing).toBe(false);
      
      const analysisPromise = act(async () => {
        return result.current.analyzeFile('test.ts');
      });
      
      await analysisPromise;
      
      expect(result.current.isAnalyzing).toBe(false);
    });
  });

  describe('Custom Analysis Types', () => {
    it('should only analyze specified types', async () => {
      const codeWithMultipleIssues = `
console.log("debug"); // best-practice issue
function longParams(a: any, b: any, c: any, d: any, e: any) {} // code-smell issue
const result = items.filter().find(); // performance issue
`;
      
      mockFs.readFileSync.mockReturnValue(codeWithMultipleIssues);
      
      // Only analyze performance issues
      const { result } = renderHook(() => 
        useCodeQualityAnalyzer({ analysisTypes: ['performance'] })
      );
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('test.ts');
        const suggestionTypes = analysisResult?.suggestions.map(s => s.type) || [];
        
        expect(suggestionTypes).toContain('performance');
        expect(suggestionTypes).not.toContain('best-practice');
        expect(suggestionTypes).not.toContain('code-smell');
      });
    });
  });

  describe('Integration Tests', () => {
    it('should handle complex real-world code analysis', async () => {
      const complexCode = `
import React, { useState, useEffect } from 'react';

export const ComplexComponent = (props: any) => {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetch('/api/data').then(response => response.json()).then(setData);
  }, []);
  
  const processItems = (items: any[]) => {
    console.log('Processing items:', items);
    return items.filter(item => item.active).find(item => item.priority > 5);
  };
  
  const handleClick = (param1: string, param2: number, param3: boolean, param4: object, param5: any) => {
    if (param2 > 100) {
      console.warn('High value detected');
    }
  };
  
  return (
    <div onClick={() => handleClick('test', 42, true, {}, null)}>
      {data && processItems(data.items)}
    </div>
  );
};
`;
      
      mockFs.readFileSync.mockReturnValue(complexCode);
      
      const { result } = renderHook(() => useCodeQualityAnalyzer());
      
      await act(async () => {
        const analysisResult = await result.current.analyzeFile('ComplexComponent.tsx');
        
        expect(analysisResult?.suggestions.length).toBeGreaterThan(5);
        
        const suggestionTypes = analysisResult?.suggestions.map(s => s.type) || [];
        expect(suggestionTypes).toContain('maintainability'); // any type
        expect(suggestionTypes).toContain('best-practice'); // console statements
        expect(suggestionTypes).toContain('performance'); // React.memo, chained operations
        expect(suggestionTypes).toContain('code-smell'); // long parameters, magic number
      });
    });
  });
});