{"name": "bookmark-studio", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:a11y": "playwright test tests/accessibility.spec.js", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:comprehensive": "playwright test tests/comprehensive-testing.spec.js", "test:performance": "playwright test tests/performance.spec.js", "test:accessibility": "playwright test tests/accessibility.spec.js", "test:cross-browser": "playwright test tests/cross-browser.spec.js", "test:report": "playwright show-report", "test:playlist": "node scripts/test-playlist-feature.js", "test:playlist:unit": "vitest run src/tests/playlist.test.ts", "test:playlist:integration": "vitest run src/tests/playlist-integration.test.ts", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "lint": "eslint . --ext js,jsx,ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext js,jsx,ts,tsx --fix", "type-check": "tsc --noEmit", "design-tokens": "node scripts/generate-design-tokens.js", "dev:memory": "node --max-old-space-size=8192 node_modules/.bin/vite", "emergency:cleanup": "node scripts/emergency-memory-cleanup.js", "emergency:dev": "npm run emergency:cleanup && npm run dev:memory", "vibe-test": "node scripts/run-vibe-tests.js", "vibe-test:star": "node scripts/run-vibe-tests.js star", "vibe-test:bulk": "node scripts/run-vibe-tests.js bulk", "vibe-test:suggestions": "node scripts/run-vibe-tests.js suggestions", "vibe-test:all": "node scripts/run-vibe-tests.js all --report", "vibe-test:debug": "node scripts/run-vibe-tests.js all --debug", "vibe-test:headed": "node scripts/run-vibe-tests.js all --headed"}, "dependencies": {"@google/genai": "^1.4.0", "@hello-pangea/dnd": "^18.0.1", "@modelcontextprotocol/sdk": "^1.12.1", "@tanstack/react-query": "^5.80.7", "@types/jspdf": "^1.3.3", "@types/react-window": "^1.8.8", "compromise": "^14.14.4", "d3": "^7.9.0", "jspdf": "^3.0.1", "lucide-react": "^0.516.0", "natural": "^8.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "react-window": "^1.8.11", "framer-motion": "^11.15.0", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-tooltip": "^1.1.6", "@radix-ui/react-select": "^2.1.4", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "clsx": "^2.1.1", "class-variance-authority": "^0.7.1", "tailwind-merge": "^2.6.0"}, "devDependencies": {"@axe-core/playwright": "^4.10.2", "@axe-core/react": "^4.10.2", "@cypress/react": "^9.0.1", "@cypress/vite-dev-server": "^6.0.3", "@eslint/js": "^9.29.0", "@playwright/test": "^1.53.0", "@tailwindcss/postcss": "^4.1.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/d3": "^7.4.3", "@types/jest": "^29.5.5", "@types/node": "^22.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.34.1", "@typescript-eslint/parser": "^8.34.1", "@vitejs/plugin-react": "^4.5.2", "@vitest/coverage-v8": "^3.2.3", "@vitest/ui": "^3.2.3", "autoprefixer": "^10.4.21", "axe-core": "^4.10.3", "cypress": "^14.4.1", "cypress-axe": "^1.6.0", "eslint": "^9.29.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-transform-stub": "^2.0.0", "jsdom": "^26.1.0", "msw": "^2.10.2", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.1.1", "typescript": "~5.7.2", "vite": "^6.2.0", "vitest": "^3.2.3", "@types/styled-components": "^5.1.34", "@storybook/react": "^8.5.3", "@storybook/addon-essentials": "^8.5.3", "@storybook/addon-interactions": "^8.5.3", "@storybook/addon-links": "^8.5.3", "@storybook/blocks": "^8.5.3", "@storybook/test": "^8.5.3", "storybook": "^8.5.3"}}