import React from 'react';
import { PlusCircleIcon, DocumentArrowDownIcon, SparklesIcon, GlobeAltIcon } from './icons/HeroIcons';

interface EmptyStateProps {
  onAddBookmark: () => void;
  onImportBookmarks: () => void;
  onShowOnboarding?: () => void;
  hasSearchQuery?: boolean;
  searchQuery?: string;
  onClearSearch?: () => void;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  onAddBookmark,
  onImportBookmarks,
  onShowOnboarding,
  hasSearchQuery = false,
  searchQuery,
  onClearSearch
}) => {
  if (hasSearchQuery) {
    return (
      <div className="empty-state-container">
        <div className="empty-state-content">
          <div className="empty-state-icon">
            🔍
          </div>
          <h3 className="empty-state-title">No bookmarks found</h3>
          <p className="empty-state-description">
            {searchQuery ? `No results found for "${searchQuery}"` : 'No bookmarks match your search criteria'}
          </p>
          <div className="empty-state-actions">
            {onClearSearch && (
              <button
                onClick={onClearSearch}
                className="btn btn-secondary"
              >
                Clear Search
              </button>
            )}
            <button
              onClick={onAddBookmark}
              className="btn btn-primary"
            >
              <PlusCircleIcon className="w-5 h-5 mr-2" />
              Add New Bookmark
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="empty-state-container">
      <div className="empty-state-content">
        <div className="empty-state-hero">
          <div className="empty-state-icon-large">
            📚
          </div>
          <h2 className="empty-state-title-large">Welcome to Bookmark Manager Pro</h2>
          <p className="empty-state-subtitle">
            Your intelligent bookmark organization starts here
          </p>
        </div>

        <div className="empty-state-features">
          <div className="feature-grid">
            <div className="feature-item">
              <SparklesIcon className="feature-icon" />
              <h4>AI-Powered Organization</h4>
              <p>Automatically categorize and tag your bookmarks with smart suggestions</p>
            </div>
            <div className="feature-item">
              <GlobeAltIcon className="feature-icon" />
              <h4>Universal Import</h4>
              <p>Import from any browser or bookmark service with our advanced parser</p>
            </div>
            <div className="feature-item">
              <DocumentArrowDownIcon className="feature-icon" />
              <h4>Smart Search</h4>
              <p>Find any bookmark instantly with powerful search and filtering</p>
            </div>
          </div>
        </div>

        <div className="empty-state-actions">
          <div className="action-buttons">
            <button
              onClick={onAddBookmark}
              className="btn btn-primary btn-large"
            >
              <PlusCircleIcon className="w-6 h-6 mr-3" />
              Add Your First Bookmark
            </button>
            <button
              onClick={onImportBookmarks}
              className="btn btn-secondary btn-large"
            >
              <DocumentArrowDownIcon className="w-6 h-6 mr-3" />
              Import Existing Bookmarks
            </button>
          </div>
          
          {onShowOnboarding && (
            <div className="onboarding-link">
              <button
                onClick={onShowOnboarding}
                className="text-link"
              >
                Take a quick tour to get started
              </button>
            </div>
          )}
        </div>

        <div className="empty-state-tips">
          <h4>Quick Tips:</h4>
          <ul>
            <li>Use <kbd>Ctrl</kbd> + <kbd>K</kbd> to open the command palette</li>
            <li>Drag and drop bookmark files to import them instantly</li>
            <li>Right-click bookmarks for quick actions and AI features</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default EmptyState;
