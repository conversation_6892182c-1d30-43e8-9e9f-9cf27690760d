import React, { useState, useEffect, useRef } from 'react';
import { Play, Pause, SkipBack, SkipForward, Volume2, VolumeX, Maximize2 } from 'lucide-react';

interface MediaControlsProps {
  playlist: any[];
  currentItem: any;
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  volume: number;
  isMuted: boolean;
  onSeek: (time: number) => void;
  onPlayPause: () => void;
  onPrevious: () => void;
  onNext: () => void;
  onVolumeChange: (volume: number) => void;
  onMute: () => void;
  onFullscreen?: () => void;
  className?: string;
}

interface SliderProps {
  value: number;
  max: number;
  onChange: (value: number) => void;
  'aria-label': string;
  className?: string;
}

const Slider: React.FC<SliderProps> = ({ value, max, onChange, 'aria-label': ariaLabel, className = '' }) => {
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true);
    updateValue(e);
  };

  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      updateValue(e);
    }
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const updateValue = (e: MouseEvent | React.MouseEvent) => {
    if (!sliderRef.current) return;
    
    const rect = sliderRef.current.getBoundingClientRect();
    const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
    const newValue = percentage * max;
    onChange(newValue);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    const step = max / 100; // 1% steps
    switch (e.key) {
      case 'ArrowLeft':
        e.preventDefault();
        onChange(Math.max(0, value - step));
        break;
      case 'ArrowRight':
        e.preventDefault();
        onChange(Math.min(max, value + step));
        break;
      case 'Home':
        e.preventDefault();
        onChange(0);
        break;
      case 'End':
        e.preventDefault();
        onChange(max);
        break;
    }
  };

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging]);

  const percentage = max > 0 ? (value / max) * 100 : 0;

  return (
    <div
      ref={sliderRef}
      className={`multimedia-slider ${className}`}
      onMouseDown={handleMouseDown}
      role="slider"
      aria-label={ariaLabel}
      aria-valuemin={0}
      aria-valuemax={max}
      aria-valuenow={value}
      tabIndex={0}
      onKeyDown={handleKeyDown}
    >
      <div className="multimedia-slider__track">
        <div 
          className="multimedia-slider__progress" 
          style={{ width: `${percentage}%` }}
        />
        <div 
          className="multimedia-slider__thumb" 
          style={{ left: `${percentage}%` }}
        />
      </div>
    </div>
  );
};

const Button: React.FC<{
  onClick: () => void;
  'aria-label': string;
  className?: string;
  children: React.ReactNode;
  disabled?: boolean;
}> = ({ onClick, 'aria-label': ariaLabel, className = '', children, disabled = false }) => {
  return (
    <button
      onClick={onClick}
      aria-label={ariaLabel}
      className={`multimedia-btn ${className} ${disabled ? 'multimedia-btn--disabled' : ''}`}
      disabled={disabled}
      type="button"
    >
      {children}
    </button>
  );
};

const formatTime = (seconds: number): string => {
  if (!isFinite(seconds) || isNaN(seconds)) return '0:00';
  
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);
  
  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  }
  return `${minutes}:${secs.toString().padStart(2, '0')}`;
};

export const MediaControls: React.FC<MediaControlsProps> = ({
  playlist,
  currentItem,
  currentTime,
  duration,
  isPlaying,
  volume,
  isMuted,
  onSeek,
  onPlayPause,
  onPrevious,
  onNext,
  onVolumeChange,
  onMute,
  onFullscreen,
  className = ''
}) => {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const volumeRef = useRef<HTMLDivElement>(null);

  // Close volume slider when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (volumeRef.current && !volumeRef.current.contains(event.target as Node)) {
        setShowVolumeSlider(false);
      }
    };

    if (showVolumeSlider) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showVolumeSlider]);

  const currentIndex = playlist.findIndex(item => item.id === currentItem?.id);
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex < playlist.length - 1;

  return (
    <div 
      className={`multimedia-media-controls ${className}`}
      role="region" 
      aria-label="Media controls"
    >
      {/* Progress Section */}
      <div className="multimedia-media-controls__progress">
        <Slider
          value={currentTime}
          max={duration}
          onChange={onSeek}
          aria-label="Seek position"
          className="multimedia-media-controls__progress-slider"
        />
        <div className="multimedia-media-controls__time-display">
          <span className="multimedia-media-controls__current-time">
            {formatTime(currentTime)}
          </span>
          <span className="multimedia-media-controls__duration">
            {formatTime(duration)}
          </span>
        </div>
      </div>

      {/* Control Buttons Section */}
      <div className="multimedia-media-controls__buttons">
        {/* Previous Button */}
        <Button
          onClick={onPrevious}
          aria-label="Previous item"
          className="multimedia-media-controls__btn multimedia-media-controls__btn--previous"
          disabled={!hasPrevious}
        >
          <SkipBack size={20} />
        </Button>

        {/* Play/Pause Button */}
        <Button
          onClick={onPlayPause}
          aria-label={isPlaying ? 'Pause' : 'Play'}
          className="multimedia-media-controls__btn multimedia-media-controls__btn--play-pause"
        >
          {isPlaying ? <Pause size={24} /> : <Play size={24} />}
        </Button>

        {/* Next Button */}
        <Button
          onClick={onNext}
          aria-label="Next item"
          className="multimedia-media-controls__btn multimedia-media-controls__btn--next"
          disabled={!hasNext}
        >
          <SkipForward size={20} />
        </Button>

        {/* Volume Controls */}
        <div className="multimedia-media-controls__volume" ref={volumeRef}>
          <Button
            onClick={() => {
              onMute();
              setShowVolumeSlider(!showVolumeSlider);
            }}
            aria-label={isMuted ? 'Unmute' : 'Mute'}
            className="multimedia-media-controls__btn multimedia-media-controls__btn--volume"
          >
            {isMuted || volume === 0 ? <VolumeX size={20} /> : <Volume2 size={20} />}
          </Button>
          
          {showVolumeSlider && (
            <div className="multimedia-media-controls__volume-slider">
              <Slider
                value={isMuted ? 0 : volume}
                max={100}
                onChange={(value) => {
                  onVolumeChange(value);
                  if (value > 0 && isMuted) {
                    onMute(); // Unmute if volume is increased
                  }
                }}
                aria-label="Volume level"
                className="multimedia-media-controls__volume-slider-input"
              />
            </div>
          )}
        </div>

        {/* Fullscreen Button */}
        {onFullscreen && (
          <Button
            onClick={onFullscreen}
            aria-label="Enter fullscreen"
            className="multimedia-media-controls__btn multimedia-media-controls__btn--fullscreen"
          >
            <Maximize2 size={20} />
          </Button>
        )}
      </div>

      {/* Current Item Info */}
      {currentItem && (
        <div className="multimedia-media-controls__info">
          <div className="multimedia-media-controls__title">
            {currentItem.title || currentItem.url}
          </div>
          <div className="multimedia-media-controls__meta">
            {currentIndex + 1} of {playlist.length}
          </div>
        </div>
      )}
    </div>
  );
};

export default MediaControls;