// Dr<PERSON> <PERSON>'s Vibe Testing Configuration
// Specialized config for detecting micro-UX issues and emotional responses

import { defineConfig } from '@playwright/test';

export const vibeTestConfig = defineConfig({
  testDir: './tests/vibe',
  timeout: 30000,
  
  // Vibe testing requires more sensitive timing
  expect: {
    timeout: 100, // Catch micro-delays that break flow
    toHaveScreenshot: { 
      threshold: 0.1, // Sensitive to visual changes
      animations: 'disabled' // Consistent visual testing
    }
  },
  
  use: {
    // Slower interactions to catch UX friction
    actionTimeout: 50,
    navigationTimeout: 5000,
    
    // Enhanced debugging for vibe issues
    trace: 'retain-on-failure',
    video: 'retain-on-failure',
    screenshot: 'only-on-failure',
    
    // Simulate real user conditions
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // Custom test attributes for vibe testing
    testIdAttribute: 'data-vibe-testid'
  },
  
  projects: [
    {
      name: 'vibe-desktop',
      use: { 
        ...devices['Desktop Chrome'],
        // Simulate typical user setup
        colorScheme: 'light',
        reducedMotion: 'no-preference'
      }
    },
    {
      name: 'vibe-mobile',
      use: { 
        ...devices['iPhone 13'],
        // Test touch interactions for favorites
        hasTouch: true
      }
    },
    {
      name: 'vibe-accessibility',
      use: {
        ...devices['Desktop Chrome'],
        // Test with accessibility tools
        reducedMotion: 'reduce',
        colorScheme: 'dark'
      }
    }
  ],
  
  // Custom reporter for vibe metrics
  reporter: [
    ['html', { outputFolder: 'test-results/vibe-report' }],
    ['./tests/vibe/reporters/vibe-metrics-reporter.js'],
    ['./tests/vibe/reporters/emotional-journey-reporter.js']
  ],
  
  // Global setup for vibe testing
  globalSetup: './tests/vibe/setup/global-vibe-setup.js',
  globalTeardown: './tests/vibe/setup/global-vibe-teardown.js'
});