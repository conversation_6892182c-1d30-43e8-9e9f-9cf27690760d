/**
 * MULTIMEDIA PLAYLIST PANEL - REDESIGNED
 * User-friendly multimedia playlist creation and management
 * Inspired by ImportPanel design patterns for better UX
 */

import {
    AlertCircle,
    BookOpen,
    Check,
    FileText,
    Globe,
    Headphones,
    Mic,
    Play,
    Video,
    Volume2,
    X,
    Zap
} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useLocalization } from '../contexts/LocalizationContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import type { Bookmark } from '../types'

interface MultimediaPlaylistPanelProps {
  isOpen: boolean
  onClose: () => void
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
}

export const MultimediaPlaylistPanelNew: React.FC<MultimediaPlaylistPanelProps> = ({
  isOpen,
  onClose,
  selectedBookmarks,
  collectionId,
  mindMapSelection
}) => {
  const { bookmarks } = useBookmarks()
  const { t } = useLocalization()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // State management
  const [playlistType, setPlaylistType] = useState<'video' | 'audio' | 'reading' | 'mixed'>('mixed')
  const [playlistName, setPlaylistName] = useState('')
  const [playlistDescription, setPlaylistDescription] = useState('')
  const [creationStatus, setCreationStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle')
  const [createdPlaylist, setCreatedPlaylist] = useState<any>(null)
  const [selectedBookmarksForPlaylist, setSelectedBookmarksForPlaylist] = useState<Bookmark[]>([])
  const [enableTTS, setEnableTTS] = useState(false)
  const [enableAI, setEnableAI] = useState(true)
  const [showPlayer, setShowPlayer] = useState(false)
  const [showVideoBuilder, setShowVideoBuilder] = useState(false)

  // Enhanced player state for preloading
  const [currentVideoIndex, setCurrentVideoIndex] = useState(0)
  const [isPlaying, setIsPlaying] = useState(false)
  const [preloadedVideos, setPreloadedVideos] = useState<Set<number>>(new Set())
  const [videoElements, setVideoElements] = useState<Map<number, HTMLVideoElement>>(new Map())
  const [autoAdvance, setAutoAdvance] = useState(true)
  const [autoAdvanceTimer, setAutoAdvanceTimer] = useState(5) // Default 5 minutes
  const [playbackSpeed, setPlaybackSpeed] = useState(1)
  const [showCountdown, setShowCountdown] = useState(false)
  const [countdownSeconds, setCountdownSeconds] = useState(5)

  // Initialize selected bookmarks
  useEffect(() => {
    if (selectedBookmarks && selectedBookmarks.length > 0) {
      setSelectedBookmarksForPlaylist(selectedBookmarks)
    } else if (collectionId) {
      const collectionBookmarks = bookmarks.filter(b => b.collection === collectionId)
      setSelectedBookmarksForPlaylist(collectionBookmarks.slice(0, 20)) // Limit for performance
    } else {
      setSelectedBookmarksForPlaylist(bookmarks.slice(0, 10)) // Default selection
    }
  }, [selectedBookmarks, collectionId, bookmarks])

  // Auto-generate playlist name based on type and content
  useEffect(() => {
    if (selectedBookmarksForPlaylist.length > 0 && !playlistName) {
      const typeNames = {
        video: '🎥 Video Playlist',
        audio: '🎵 Audio Playlist', 
        reading: '📚 Reading List',
        mixed: '🎬 Mixed Media Playlist'
      }
      const baseName = typeNames[playlistType]
      const contextName = collectionId ? ` - ${collectionId}` : ''
      setPlaylistName(`${baseName}${contextName}`)
    }
  }, [playlistType, collectionId, selectedBookmarksForPlaylist.length, playlistName])

  const handleCreatePlaylist = async () => {
    if (selectedBookmarksForPlaylist.length === 0) {
      setCreationStatus('error')
      return
    }

    setCreationStatus('processing')

    try {
      // Simulate playlist creation (replace with actual service call)
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      const newPlaylist = {
        id: Date.now().toString(),
        name: playlistName,
        description: playlistDescription,
        type: playlistType,
        bookmarks: selectedBookmarksForPlaylist,
        enableTTS,
        enableAI,
        createdAt: new Date().toISOString()
      }

      setCreatedPlaylist(newPlaylist)
      setCreationStatus('success')
      
      console.log('🎬 Playlist created:', newPlaylist)
    } catch (error) {
      console.error('Failed to create playlist:', error)
      setCreationStatus('error')
    }
  }

  const resetForm = () => {
    setCreationStatus('idle')
    setCreatedPlaylist(null)
    setPlaylistName('')
    setPlaylistDescription('')
    setSelectedBookmarksForPlaylist([])
  }

  const convertBookmarksToPlaylistItems = (bookmarks: Bookmark[]) => {
    if (!bookmarks || !Array.isArray(bookmarks)) {
      return []
    }

    return bookmarks.map(bookmark => {
      if (!bookmark || !bookmark.url || !bookmark.title) {
        return null
      }

      const url = bookmark.url.toLowerCase()
      let type: 'video' | 'audio' | 'document' | 'web' = 'web'

      if (url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')) {
        type = 'video'
      } else if (url.includes('spotify.com') || url.includes('soundcloud.com') || url.includes('audio')) {
        type = 'audio'
      } else if (url.includes('pdf') || bookmark.title.toLowerCase().includes('article') || bookmark.title.toLowerCase().includes('doc')) {
        type = 'document'
      }

      return {
        id: bookmark.id,
        title: bookmark.title,
        url: bookmark.url,
        type,
        thumbnail: bookmark.favicon
      }
    }).filter(Boolean) // Remove any null items
  }

  // Enhanced video preloading system
  const preloadVideo = (index: number) => {
    if (!createdPlaylist?.items || preloadedVideos.has(index)) return

    const item = createdPlaylist.items[index]
    if (!item || item.type !== 'video') return

    console.log(`🎬 Preloading video ${index + 1}: ${item.title}`)

    const video = document.createElement('video')
    video.preload = 'metadata'
    video.crossOrigin = 'anonymous'

    // For YouTube videos, we'll use the embed URL for better compatibility
    if (item.url.includes('youtube.com') || item.url.includes('youtu.be')) {
      const videoId = extractYouTubeId(item.url)
      if (videoId) {
        video.src = `https://www.youtube.com/embed/${videoId}?autoplay=0&controls=1`
      }
    } else if (item.url.includes('vimeo.com')) {
      const videoId = extractVimeoId(item.url)
      if (videoId) {
        video.src = `https://player.vimeo.com/video/${videoId}`
      }
    } else {
      video.src = item.url
    }

    video.addEventListener('loadedmetadata', () => {
      console.log(`✅ Video ${index + 1} metadata loaded`)
      setPreloadedVideos(prev => new Set([...prev, index]))
    })

    video.addEventListener('error', (e) => {
      console.warn(`⚠️ Failed to preload video ${index + 1}:`, e)
    })

    setVideoElements(prev => new Map([...prev, [index, video]]))
  }

  // Extract YouTube video ID from URL
  const extractYouTubeId = (url: string): string | null => {
    const regex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  // Extract Vimeo video ID from URL
  const extractVimeoId = (url: string): string | null => {
    const regex = /vimeo\.com\/(?:.*\/)?(\d+)/
    const match = url.match(regex)
    return match ? match[1] : null
  }

  // Preload next videos intelligently
  const preloadNextVideos = (currentIndex: number) => {
    if (!createdPlaylist?.items) return

    // Preload next 2-3 videos for smooth playback
    const preloadCount = Math.min(3, createdPlaylist.items.length - currentIndex - 1)

    for (let i = 1; i <= preloadCount; i++) {
      const nextIndex = currentIndex + i
      if (nextIndex < createdPlaylist.items.length) {
        preloadVideo(nextIndex)
      }
    }
  }

  // Auto-advance to next video
  const advanceToNext = () => {
    if (!createdPlaylist?.items || !autoAdvance) return

    const nextIndex = currentVideoIndex + 1
    if (nextIndex < createdPlaylist.items.length) {
      console.log(`🎬 Auto-advancing to video ${nextIndex + 1}`)
      setCurrentVideoIndex(nextIndex)
      preloadNextVideos(nextIndex)
    } else {
      console.log('🎬 Playlist completed')
      setIsPlaying(false)
    }
  }

  // Smart video player with intelligent end detection
  const playVideo = (index: number) => {
    setCurrentVideoIndex(index)
    setIsPlaying(true)
    preloadNextVideos(index)

    const item = createdPlaylist?.items?.[index]
    if (item) {
      console.log(`🎬 Playing video ${index + 1}: ${item.title}`)

      // Check if it's a YouTube video for intelligent player
      if (item.url.includes('youtube.com') || item.url.includes('youtu.be')) {
        const videoId = extractYouTubeId(item.url)
        if (videoId) {
          // Create intelligent YouTube player with end detection
          createIntelligentYouTubePlayer(videoId, index)
          return
        }
      }

      // For non-YouTube videos, open in new tab with smart notification
      window.open(item.url, '_blank')

      // Show smart advance notification
      if (autoAdvance) {
        showSmartAdvanceNotification(index)
      }
    }
  }

  // Show smart advance notification with "Video Finished" button
  const showSmartAdvanceNotification = (index: number) => {
    const notification = document.createElement('div')
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: `linear-gradient(135deg, var(--accent-color), rgba(var(--accent-color-rgb), 0.8))`;
      color: white;
      padding: 20px;
      border-radius: 16px;
      box-shadow: 0 12px 40px rgba(var(--accent-color-rgb), 0.4);
      z-index: 10000;
      max-width: 320px;
      font-family: system-ui, -apple-system, sans-serif;
      animation: slideIn 0.3s ease-out;
    `

    notification.innerHTML = `
      <style>
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes pulse {
          0%, 100% { transform: scale(1); }
          50% { transform: scale(1.05); }
        }
        .pulse-btn { animation: pulse 2s infinite; }
      </style>
      <div style="font-weight: 600; margin-bottom: 8px; display: flex; align-items: center; gap: 8px;">
        🎬 Video Playing
        <span style="font-size: 12px; background: rgba(255,255,255,0.2); padding: 2px 6px; border-radius: 4px;">
          ${index + 1}/${createdPlaylist?.items?.length || 0}
        </span>
      </div>
      <div style="font-size: 14px; margin-bottom: 16px; opacity: 0.9;">
        Click when video finishes for instant advance
      </div>
      <div style="display: flex; gap: 8px;">
        <button onclick="this.parentElement.parentElement.remove()" style="
          padding: 8px 12px;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 12px;
          transition: all 0.2s ease;
        ">Dismiss</button>
        <button onclick="window.smartVideoFinished(); this.parentElement.parentElement.remove();" style="
          padding: 10px 16px;
          background: rgba(34, 197, 94, 0.9);
          color: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          font-size: 13px;
          font-weight: 600;
          transition: all 0.2s ease;
        " class="pulse-btn">✅ Video Finished</button>
      </div>
    `

    document.body.appendChild(notification)

    // Auto-remove after 30 seconds (longer than before)
    setTimeout(() => {
      if (document.body.contains(notification)) {
        document.body.removeChild(notification)
      }
    }, 30000)

    // Add global function for video finished
    ;(window as any).smartVideoFinished = () => {
      startCountdownAdvance()
    }
  }

  // Start 5-second countdown before advancing
  const startCountdownAdvance = () => {
    if (!autoAdvance || currentVideoIndex >= (createdPlaylist?.items?.length || 0) - 1) {
      console.log('🎬 Playlist completed or auto-advance disabled')
      return
    }

    setShowCountdown(true)
    setCountdownSeconds(5)

    console.log('🎬 Starting 5-second countdown before next video...')

    const countdownInterval = setInterval(() => {
      setCountdownSeconds(prev => {
        if (prev <= 1) {
          clearInterval(countdownInterval)
          setShowCountdown(false)
          advanceToNext()
          return 5
        }
        return prev - 1
      })
    }, 1000)

    // Store interval for potential cancellation
    ;(window as any).countdownInterval = countdownInterval
  }

  // Cancel countdown if user wants to stay
  const cancelCountdown = () => {
    if ((window as any).countdownInterval) {
      clearInterval((window as any).countdownInterval)
    }
    setShowCountdown(false)
    setCountdownSeconds(5)
    console.log('🎬 Auto-advance cancelled by user')
  }

  // Create intelligent YouTube player with simple controls
  const createIntelligentYouTubePlayer = (videoId: string, index: number) => {
    // Create a clean modal with embedded YouTube player
    const modal = document.createElement('div')
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.95);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
    `

    const playerContainer = document.createElement('div')
    playerContainer.style.cssText = `
      width: 90%;
      max-width: 1200px;
      height: 80%;
      background: black;
      border-radius: 16px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
    `

    // Close button
    const closeButton = document.createElement('button')
    closeButton.innerHTML = '✕'
    closeButton.style.cssText = `
      position: absolute;
      top: 16px;
      right: 16px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      border: none;
      border-radius: 50%;
      width: 44px;
      height: 44px;
      font-size: 18px;
      cursor: pointer;
      z-index: 10001;
      transition: all 0.2s ease;
    `
    closeButton.onmouseover = () => closeButton.style.background = 'rgba(239, 68, 68, 0.9)'
    closeButton.onmouseout = () => closeButton.style.background = 'rgba(0, 0, 0, 0.8)'
    closeButton.onclick = () => {
      document.body.removeChild(modal)
      setIsPlaying(false)
    }

    // YouTube iframe
    const iframe = document.createElement('iframe')
    iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`
    iframe.style.cssText = `
      width: 100%;
      height: calc(100% - 80px);
      border: none;
    `
    iframe.allow = 'autoplay; encrypted-media'

    // Video info overlay
    const videoInfo = document.createElement('div')
    videoInfo.style.cssText = `
      position: absolute;
      top: 16px;
      left: 16px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 12px 16px;
      border-radius: 8px;
      font-size: 14px;
      z-index: 10001;
    `
    videoInfo.innerHTML = `
      <div style="font-weight: 600; margin-bottom: 4px;">Video ${index + 1} of ${createdPlaylist?.items?.length || 0}</div>
      <div style="font-size: 12px; opacity: 0.8; max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
        ${createdPlaylist?.items?.[index]?.title || ''}
      </div>
    `

    // Simple bottom controls
    const controlsBar = document.createElement('div')
    controlsBar.style.cssText = `
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 80px;
      background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 16px;
      z-index: 10001;
    `

    // Video Finished button (main action)
    const finishedButton = document.createElement('button')
    finishedButton.innerHTML = '✅ Video Finished'
    finishedButton.style.cssText = `
      padding: 12px 24px;
      background: `linear-gradient(135deg, var(--success-color, #10b981), var(--success-color-dark, #059669))`;
      color: white;
      border: none;
      border-radius: 12px;
      cursor: pointer;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.2s ease;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    `
    finishedButton.onmouseover = () => {
      finishedButton.style.transform = 'scale(1.05)'
      finishedButton.style.boxShadow = '0 6px 20px rgba(16, 185, 129, 0.4)'
    }
    finishedButton.onmouseout = () => {
      finishedButton.style.transform = 'scale(1)'
      finishedButton.style.boxShadow = '0 4px 12px rgba(16, 185, 129, 0.3)'
    }
    finishedButton.onclick = () => {
      document.body.removeChild(modal)
      setIsPlaying(false)
      startCountdownAdvance()
    }

    // Previous button (if available)
    if (index > 0) {
      const prevButton = document.createElement('button')
      prevButton.innerHTML = '⏮️ Previous'
      prevButton.style.cssText = `
        padding: 10px 16px;
        background: rgba(var(--accent-color-rgb), 0.9);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s ease;
      `
      prevButton.onclick = () => {
        document.body.removeChild(modal)
        setCurrentVideoIndex(index - 1)
        playVideo(index - 1)
      }
      controlsBar.appendChild(prevButton)
    }

    controlsBar.appendChild(finishedButton)

    // Next button (if available)
    if (index < (createdPlaylist?.items?.length || 0) - 1) {
      const nextButton = document.createElement('button')
      nextButton.innerHTML = 'Skip ⏭️'
      nextButton.style.cssText = `
        padding: 10px 16px;
        background: rgba(var(--accent-color-rgb), 0.9);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s ease;
      `
      nextButton.onclick = () => {
        document.body.removeChild(modal)
        advanceToNext()
      }
      controlsBar.appendChild(nextButton)
    }

    playerContainer.appendChild(iframe)
    playerContainer.appendChild(closeButton)
    playerContainer.appendChild(videoInfo)
    playerContainer.appendChild(controlsBar)
    modal.appendChild(playerContainer)
    document.body.appendChild(modal)

    // Close on background click
    modal.onclick = (e) => {
      if (e.target === modal) {
        document.body.removeChild(modal)
        setIsPlaying(false)
      }
    }
  }

  // Create enhanced YouTube player with better auto-advance
  const createEnhancedYouTubePlayer = (videoId: string, index: number) => {
    // Create a modal with embedded YouTube player
    const modal = document.createElement('div')
    modal.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.9);
      z-index: 10000;
      display: flex;
      align-items: center;
      justify-content: center;
    `

    const playerContainer = document.createElement('div')
    playerContainer.style.cssText = `
      width: 90%;
      max-width: 1200px;
      height: 80%;
      background: black;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
    `

    // Close button
    const closeButton = document.createElement('button')
    closeButton.innerHTML = '✕'
    closeButton.style.cssText = `
      position: absolute;
      top: 10px;
      right: 10px;
      background: rgba(0, 0, 0, 0.7);
      color: white;
      border: none;
      border-radius: 50%;
      width: 40px;
      height: 40px;
      font-size: 20px;
      cursor: pointer;
      z-index: 10001;
    `
    closeButton.onclick = () => {
      document.body.removeChild(modal)
      setIsPlaying(false)
    }

    // Enhanced YouTube iframe with better controls
    const iframe = document.createElement('iframe')
    iframe.src = `https://www.youtube.com/embed/${videoId}?autoplay=1&rel=0&modestbranding=1`
    iframe.style.cssText = `
      width: 100%;
      height: calc(100% - 60px);
      border: none;
    `
    iframe.allow = 'autoplay; encrypted-media'

    // Video info display
    const videoInfo = document.createElement('div')
    videoInfo.style.cssText = `
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 14px;
      z-index: 10001;
    `
    videoInfo.innerHTML = `
      <div style="font-weight: 600;">Video ${index + 1} of ${createdPlaylist?.items?.length || 0}</div>
      <div style="font-size: 12px; opacity: 0.8;">${createdPlaylist?.items?.[index]?.title || ''}</div>
    `

    // Auto-advance timer (since we can't reliably detect video end)
    let timerHandle: NodeJS.Timeout | null = null

    // Configurable auto-advance timer
    const startAutoAdvanceTimer = () => {
      if (autoAdvance && index < (createdPlaylist?.items?.length || 0) - 1) {
        // Use user-configured timer (in minutes, convert to milliseconds)
        const timerDuration = autoAdvanceTimer * 60 * 1000

        timerHandle = setTimeout(() => {
          console.log('🎬 Auto-advancing to next video...')
          if (document.body.contains(modal)) {
            document.body.removeChild(modal)
          }
          setIsPlaying(false)
          advanceToNext()
        }, timerDuration)

        console.log(`⏰ Auto-advance timer set for ${autoAdvanceTimer} minutes`)
      }
    }

    // Start timer after iframe loads
    iframe.onload = () => {
      startAutoAdvanceTimer()
    }

    // Enhanced playlist controls overlay
    const controlsOverlay = document.createElement('div')
    controlsOverlay.style.cssText = `
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background: linear-gradient(to top, rgba(0,0,0,0.9), transparent);
      padding: 20px;
      display: flex;
      justify-content: center;
      gap: 12px;
      z-index: 10001;
    `

    // Previous button
    if (index > 0) {
      const prevButton = document.createElement('button')
      prevButton.innerHTML = '⏮️ Previous'
      prevButton.style.cssText = `
        padding: 10px 16px;
        background: rgba(var(--accent-color-rgb), 0.9);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s ease;
      `
      prevButton.onmouseover = () => prevButton.style.background = 'rgba(var(--accent-color-rgb), 1)'
      prevButton.onmouseout = () => prevButton.style.background = 'rgba(var(--accent-color-rgb), 0.9)'
      prevButton.onclick = () => {
        if (timerHandle) clearTimeout(timerHandle)
        document.body.removeChild(modal)
        setCurrentVideoIndex(index - 1)
        playVideo(index - 1)
      }
      controlsOverlay.appendChild(prevButton)
    }

    // Video Finished button (manual advance)
    const finishedButton = document.createElement('button')
    finishedButton.innerHTML = '✅ Video Finished'
    finishedButton.style.cssText = `
      padding: 10px 20px;
      background: rgba(34, 197, 94, 0.9);
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.2s ease;
    `
    finishedButton.onmouseover = () => finishedButton.style.background = 'rgba(34, 197, 94, 1)'
    finishedButton.onmouseout = () => finishedButton.style.background = 'rgba(34, 197, 94, 0.9)'
    finishedButton.onclick = () => {
      if (timerHandle) clearTimeout(timerHandle)
      document.body.removeChild(modal)
      setIsPlaying(false)

      if (index < (createdPlaylist?.items?.length || 0) - 1) {
        advanceToNext()
      } else {
        console.log('🎬 Playlist completed!')
      }
    }
    controlsOverlay.appendChild(finishedButton)

    // Next button (skip)
    if (index < (createdPlaylist?.items?.length || 0) - 1) {
      const nextButton = document.createElement('button')
      nextButton.innerHTML = 'Skip ⏭️'
      nextButton.style.cssText = `
        padding: 10px 16px;
        background: rgba(var(--accent-color-rgb), 0.9);
        color: white;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-weight: 600;
        font-size: 14px;
        transition: all 0.2s ease;
      `
      nextButton.onmouseover = () => nextButton.style.background = 'rgba(var(--accent-color-rgb), 1)'
      nextButton.onmouseout = () => nextButton.style.background = 'rgba(var(--accent-color-rgb), 0.9)'
      nextButton.onclick = () => {
        if (timerHandle) clearTimeout(timerHandle)
        document.body.removeChild(modal)
        advanceToNext()
      }
      controlsOverlay.appendChild(nextButton)
    }

    // Auto-advance status indicator
    const statusIndicator = document.createElement('div')
    statusIndicator.style.cssText = `
      position: absolute;
      top: 60px;
      right: 10px;
      background: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 8px 12px;
      border-radius: 6px;
      font-size: 12px;
      z-index: 10001;
    `
    statusIndicator.innerHTML = autoAdvance ?
      `🔄 Auto-advance: ON<br><small>Will advance in ${autoAdvanceTimer} min</small>` :
      '⏸️ Auto-advance: OFF<br><small>Click "Video Finished"</small>'

    playerContainer.appendChild(iframe)
    playerContainer.appendChild(closeButton)
    playerContainer.appendChild(videoInfo)
    playerContainer.appendChild(statusIndicator)
    playerContainer.appendChild(controlsOverlay)
    modal.appendChild(playerContainer)
    document.body.appendChild(modal)

    // Close on background click
    modal.onclick = (e) => {
      if (e.target === modal) {
        if (timerHandle) clearTimeout(timerHandle)
        document.body.removeChild(modal)
        setIsPlaying(false)
      }
    }

    // Cleanup function
    const cleanup = () => {
      if (timerHandle) {
        clearTimeout(timerHandle)
        timerHandle = null
      }
    }

    // Store cleanup function for external access
    ;(window as any).playlistCleanup = cleanup
  }

  const playPlaylist = () => {
    if (createdPlaylist) {
      setShowPlayer(true)
      setCurrentVideoIndex(0)
      setIsPlaying(false)
      // Start preloading first few videos
      preloadNextVideos(-1) // This will preload videos 0, 1, 2
    }
  }

  const handleVideoBuilderPlaylist = (playlist: any) => {
    setCreatedPlaylist(playlist)
    setPlaylistName(playlist.name)
    setPlaylistType('video')
    setCreationStatus('success')
    setShowVideoBuilder(false)
  }

  const getBookmarkTypeIcon = (bookmark: Bookmark) => {
    const url = bookmark.url.toLowerCase()
    if (url.includes('youtube.com') || url.includes('vimeo.com')) return <Video size={16} />
    if (url.includes('spotify.com') || url.includes('soundcloud.com')) return <Headphones size={16} />
    if (url.includes('pdf') || bookmark.title.toLowerCase().includes('article')) return <FileText size={16} />
    return <Globe size={16} />
  }

  const getBookmarkTypeCount = (type: string) => {
    if (!selectedBookmarksForPlaylist || !Array.isArray(selectedBookmarksForPlaylist)) {
      return 0
    }

    switch (type) {
      case 'video':
        return selectedBookmarksForPlaylist.filter(b =>
          b?.url?.toLowerCase().includes('youtube.com') ||
          b?.url?.toLowerCase().includes('vimeo.com')
        ).length
      case 'audio':
        return selectedBookmarksForPlaylist.filter(b =>
          b?.url?.toLowerCase().includes('spotify.com') ||
          b?.url?.toLowerCase().includes('soundcloud.com')
        ).length
      case 'reading':
        return selectedBookmarksForPlaylist.filter(b =>
          b?.url?.toLowerCase().includes('pdf') ||
          b?.title?.toLowerCase().includes('article')
        ).length
      default:
        return selectedBookmarksForPlaylist.length
    }
  }

  // Early return if panel is not open
  if (!isOpen) return null

  return (
    <div className="import-panel">
      <div className="import-header">
        <h2 className="import-title">🎬 Create Multimedia Playlist</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close multimedia panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{ maxHeight: 'calc(80vh - 96px)', overflowY: 'auto' }}>
        
        {/* Large Video Collection Helper */}
        {getBookmarkTypeCount('video') > 100 && (
          <div className="import-section">
            <h3 className="section-title">🎥 Large Video Collection Detected</h3>
            <p className="section-description">
              You have {getBookmarkTypeCount('video')} videos! Use our advanced Video Builder for better selection and organization.
            </p>
            <div style={{
              padding: '16px',
              backgroundColor: isModernTheme ? 'rgba(var(--accent-color-rgb), 0.1)' : '#f3f4f6',
        border: isModernTheme ? '1px solid rgba(var(--accent-color-rgb), 0.3)' : '1px solid #d1d5db',
              borderRadius: '8px',
              textAlign: 'center'
            }}>
              <div style={{ fontSize: '32px', marginBottom: '12px' }}>🎬</div>
              <button
                onClick={() => setShowVideoBuilder(true)}
                className="template-btn"
                style={{
                  backgroundColor: 'var(--accent-color)',
                  color: 'white',
                  border: 'none',
                  padding: '12px 24px',
                  fontSize: '16px',
                  fontWeight: '600',
                  borderRadius: '8px',
                  cursor: 'pointer',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '8px',
                  margin: '0 auto'
                }}
              >
                <Video size={20} />
                🎯 Advanced Video Builder
              </button>
              <p style={{
                fontSize: '12px',
                color: isModernTheme ? '#9ca3af' : '#6b7280',
                marginTop: '8px'
              }}>
                Smart filtering • Batch selection • Platform sorting • Collection grouping
              </p>
            </div>
          </div>
        )}

        {/* Playlist Type Selection */}
        <div className="import-section">
          <h3 className="section-title">Select Playlist Type</h3>
          <div className="format-options">
            <button
              onClick={() => setPlaylistType('video')}
              className={`format-option ${playlistType === 'video' ? 'active' : ''}`}
            >
              <Video size={20} />
              <span>Video Queue</span>
              <small>{getBookmarkTypeCount('video')} videos available</small>
            </button>
            <button
              onClick={() => setPlaylistType('audio')}
              className={`format-option ${playlistType === 'audio' ? 'active' : ''}`}
            >
              <Headphones size={20} />
              <span>Audio Playlist</span>
              <small>{getBookmarkTypeCount('audio')} audio items</small>
            </button>
            <button
              onClick={() => setPlaylistType('reading')}
              className={`format-option ${playlistType === 'reading' ? 'active' : ''}`}
            >
              <BookOpen size={20} />
              <span>Reading List</span>
              <small>{getBookmarkTypeCount('reading')} articles/docs</small>
            </button>
            <button
              onClick={() => setPlaylistType('mixed')}
              className={`format-option ${playlistType === 'mixed' ? 'active' : ''}`}
            >
              <Zap size={20} />
              <span>Mixed Media</span>
              <small>{selectedBookmarksForPlaylist.length} total items</small>
            </button>
          </div>
        </div>

        {/* Playlist Configuration */}
        <div className="import-section">
          <h3 className="section-title">Playlist Details</h3>
          <div className="playlist-config">
            <div className="config-field">
              <label htmlFor="playlist-name">Playlist Name</label>
              <input
                id="playlist-name"
                type="text"
                value={playlistName}
                onChange={(e) => setPlaylistName(e.target.value)}
                placeholder="Enter playlist name..."
                className="config-input"
              />
            </div>
            <div className="config-field">
              <label htmlFor="playlist-description">Description (Optional)</label>
              <textarea
                id="playlist-description"
                value={playlistDescription}
                onChange={(e) => setPlaylistDescription(e.target.value)}
                placeholder="Describe your playlist..."
                className="config-textarea"
                rows={3}
              />
            </div>
          </div>
        </div>

        {/* Enhancement Options */}
        <div className="import-section">
          <h3 className="section-title">Enhancement Options</h3>
          <div className="enhancement-options">
            <label className="enhancement-option">
              <input
                type="checkbox"
                checked={enableAI}
                onChange={(e) => setEnableAI(e.target.checked)}
              />
              <div className="option-content">
                <Zap size={16} />
                <span>AI Enhancement</span>
                <small>Auto-generate summaries and optimize content</small>
              </div>
            </label>
            <label className="enhancement-option">
              <input
                type="checkbox"
                checked={enableTTS}
                onChange={(e) => setEnableTTS(e.target.checked)}
              />
              <div className="option-content">
                <Mic size={16} />
                <span>Text-to-Speech</span>
                <small>Enable voice narration for hands-free experience</small>
              </div>
            </label>
          </div>
        </div>

        {/* Bookmark Selection */}
        <div className="import-section">
          <h3 className="section-title">Select Bookmarks</h3>
          <p className="section-description">
            Choose which bookmarks to include in your multimedia playlist.
          </p>
          <div style={{ border: '1px solid #e5e7eb', borderRadius: '8px', padding: '16px' }}>
            {/* Simple bookmark selector for now - will use BookmarkSelector component when import works */}
            <div className="simple-bookmark-selector">
              <div className="selector-summary">
                <span>📚 {selectedBookmarksForPlaylist.length} bookmarks selected</span>
                <div style={{ display: 'flex', gap: '8px' }}>
                  {selectedBookmarksForPlaylist.length > 0 && (
                    <button
                      onClick={() => {
                        // Quick play without creating formal playlist
                        const quickPlaylist = {
                          id: 'quick-play',
                          name: `Quick Play - ${playlistType}`,
                          items: convertBookmarksToPlaylistItems(selectedBookmarksForPlaylist)
                        }
                        setCreatedPlaylist(quickPlaylist)
                        setShowPlayer(true)
                      }}
                      className="template-btn"
                      style={{
                        fontSize: '12px',
                        padding: '4px 8px',
                        backgroundColor: 'var(--success-color, #10B981)',
                        color: 'white',
                        border: 'none'
                      }}
                    >
                      ▶️ Quick Play
                    </button>
                  )}
                  <button
                    onClick={() => {
                      const allBookmarks = bookmarks.slice(0, 20)
                      setSelectedBookmarksForPlaylist(allBookmarks)
                    }}
                    className="template-btn"
                    style={{ fontSize: '12px', padding: '4px 8px' }}
                  >
                    Select All ({Math.min(bookmarks.length, 20)})
                  </button>
                </div>
              </div>

              <div className="bookmarks-preview" style={{ marginTop: '12px' }}>
                {selectedBookmarksForPlaylist.slice(0, 5).map((bookmark, index) => (
                  <div key={bookmark.id} className="bookmark-preview-item">
                    {getBookmarkTypeIcon(bookmark)}
                    <span className="bookmark-title">{bookmark.title}</span>
                    <span className="bookmark-url">{new URL(bookmark.url).hostname}</span>
                    <button
                      onClick={() => {
                        setSelectedBookmarksForPlaylist(prev =>
                          prev.filter(b => b.id !== bookmark.id)
                        )
                      }}
                      style={{
                        background: 'none',
                        border: 'none',
                        color: '#ef4444',
                        cursor: 'pointer',
                        padding: '2px'
                      }}
                      title="Remove from playlist"
                    >
                      <X size={14} />
                    </button>
                  </div>
                ))}
                {selectedBookmarksForPlaylist.length > 5 && (
                  <div className="bookmark-preview-more">
                    +{selectedBookmarksForPlaylist.length - 5} more bookmarks
                  </div>
                )}
                {selectedBookmarksForPlaylist.length === 0 && (
                  <div style={{
                    textAlign: 'center',
                    padding: '20px',
                    color: '#6b7280',
                    fontStyle: 'italic'
                  }}>
                    No bookmarks selected. Click "Select All" to get started.
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Create Playlist Action */}
        <div className="import-section">
          <h3 className="section-title">Create Playlist</h3>
          <div
            className={`upload-area ${creationStatus === 'processing' ? 'processing' : ''}`}
            style={{ cursor: creationStatus === 'idle' ? 'pointer' : 'default' }}
            onClick={creationStatus === 'idle' ? handleCreatePlaylist : undefined}
          >
            {creationStatus === 'idle' && (
              <>
                <Play size={32} />
                <p className="upload-text">
                  Ready to create your {playlistType} playlist
                </p>
                <p className="upload-hint">
                  Click to create playlist with {selectedBookmarksForPlaylist.length} bookmarks
                </p>
              </>
            )}

            {creationStatus === 'processing' && (
              <>
                <div className="processing-spinner" />
                <p className="upload-text">Creating your multimedia playlist...</p>
                <p className="upload-hint">
                  Processing {selectedBookmarksForPlaylist.length} bookmarks
                </p>
              </>
            )}

            {creationStatus === 'success' && (
              <>
                <Check size={32} className="success-icon" />
                <p className="upload-text">
                  Playlist created successfully!
                </p>
                <p className="upload-hint">
                  "{playlistName}" is ready to use
                </p>
                <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', marginTop: '16px' }}>
                  <button
                    onClick={playPlaylist}
                    className="template-btn"
                    style={{
                      backgroundColor: 'var(--accent-color)',
                      color: 'white',
                      border: 'none',
                      padding: '12px 24px',
                      fontSize: '14px',
                      fontWeight: '600'
                    }}
                  >
                    <Play size={16} style={{ marginRight: '6px' }} />
                    ▶️ Play Playlist
                  </button>
                  <button
                    onClick={resetForm}
                    className="import-another-btn"
                  >
                    🎬 Create Another
                  </button>
                </div>
              </>
            )}

            {creationStatus === 'error' && (
              <>
                <AlertCircle size={32} className="error-icon" />
                <p className="upload-text">Failed to create playlist</p>
                <p className="upload-hint">Please try again or select different bookmarks</p>
              </>
            )}
          </div>
        </div>

        {/* Quick Templates */}
        <div className="import-section">
          <h3 className="section-title">Quick Templates</h3>
          <p className="section-description">
            Create playlists quickly with pre-configured settings for common use cases.
          </p>
          <div className="template-actions">
            <button
              onClick={() => {
                setPlaylistType('video')
                setPlaylistName('🏃‍♂️ Gym Workout Videos')
                setEnableTTS(true)
                setEnableAI(true)
              }}
              className="template-btn"
            >
              <Play size={16} />
              Gym Mode
            </button>
            <button
              onClick={() => {
                setPlaylistType('reading')
                setPlaylistName('📚 Study Session')
                setEnableTTS(true)
                setEnableAI(true)
              }}
              className="template-btn"
            >
              <BookOpen size={16} />
              Study Mode
            </button>
            <button
              onClick={() => {
                setPlaylistType('mixed')
                setPlaylistName('🌙 Evening Relaxation')
                setEnableTTS(false)
                setEnableAI(true)
              }}
              className="template-btn"
            >
              <Volume2 size={16} />
              Relax Mode
            </button>
          </div>
        </div>

      </div>

      {/* Playlist Player */}
      {showPlayer && createdPlaylist && (
        <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1000 }}>
          <div style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(4px)'
          }}>
            <div style={{
              width: '90%',
              maxWidth: '1200px',
              height: '80%',
              backgroundColor: isModernTheme ? 'rgba(0, 0, 0, 0.9)' : 'white',
              borderRadius: '12px',
              border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #e5e7eb',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            }}>

              {/* Player Header */}
              <div style={{
                padding: '16px 24px',
                borderBottom: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #e5e7eb',
                backgroundColor: isModernTheme ? 'rgba(255, 255, 255, 0.05)' : '#f9fafb',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div>
                  <h2 style={{
                    fontSize: '18px',
                    fontWeight: '600',
                    color: isModernTheme ? 'white' : '#111827',
                    margin: 0
                  }}>
                    🎬 {createdPlaylist?.name || 'Multimedia Playlist'}
                  </h2>
                  <p style={{
                    fontSize: '14px',
                    color: isModernTheme ? '#9ca3af' : '#6b7280',
                    margin: '4px 0 0 0'
                  }}>
                    {createdPlaylist?.items?.length || 0} items • Ready to play
                  </p>
                </div>
                <button
                  onClick={() => setShowPlayer(false)}
                  style={{
                    padding: '8px',
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderRadius: '6px',
                    color: isModernTheme ? 'white' : '#6b7280',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(255, 255, 255, 0.1)' : '#f3f4f6'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                >
                  <X size={20} />
                </button>
              </div>

              {/* Simple Player Content */}
              <div style={{ flex: 1, padding: '24px', textAlign: 'center' }}>
                <div style={{
                  fontSize: '64px',
                  marginBottom: '24px',
                  opacity: 0.7
                }}>
                  🎬
                </div>

                <h3 style={{
                  fontSize: '24px',
                  fontWeight: '600',
                  color: isModernTheme ? 'white' : '#111827',
                  marginBottom: '12px'
                }}>
                  {createdPlaylist?.name || 'Multimedia Playlist'}
                </h3>

                <p style={{
                  fontSize: '16px',
                  color: isModernTheme ? '#9ca3af' : '#6b7280',
                  marginBottom: '16px'
                }}>
                  {createdPlaylist?.items?.length || 0} items • {preloadedVideos.size} preloaded
                </p>

                {/* Current Video Display */}
                {createdPlaylist?.items && createdPlaylist.items.length > 0 && (
                  <div style={{
                    backgroundColor: isModernTheme ? 'rgba(255, 255, 255, 0.1)' : '#f9fafb',
                    border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #e5e7eb',
                    borderRadius: '12px',
                    padding: '16px',
                    marginBottom: '24px',
                    maxWidth: '500px',
                    margin: '0 auto 24px auto'
                  }}>
                    <div style={{
                      fontSize: '14px',
                      color: isModernTheme ? '#9ca3af' : '#6b7280',
                      marginBottom: '8px'
                    }}>
                      Now Playing: {currentVideoIndex + 1} of {createdPlaylist.items.length}
                    </div>
                    <div style={{
                      fontSize: '18px',
                      fontWeight: '600',
                      color: isModernTheme ? 'white' : '#111827',
                      marginBottom: '8px'
                    }}>
                      {createdPlaylist.items[currentVideoIndex]?.title || 'Select a video to play'}
                    </div>
                    <div style={{
                      fontSize: '12px',
                      color: isModernTheme ? '#9ca3af' : '#6b7280'
                    }}>
                      {preloadedVideos.has(currentVideoIndex) ? '✅ Ready to play' : '⏳ Loading...'}
                    </div>
                  </div>
                )}

                {/* Playback Controls */}
                <div style={{
                  display: 'flex',
                  gap: '12px',
                  justifyContent: 'center',
                  marginBottom: '24px',
                  flexWrap: 'wrap'
                }}>
                  <button
                    onClick={() => {
                      const prevIndex = Math.max(0, currentVideoIndex - 1)
                      setCurrentVideoIndex(prevIndex)
                    }}
                    disabled={currentVideoIndex === 0}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: currentVideoIndex === 0 ? 'transparent' : (isModernTheme ? 'rgba(var(--accent-color-rgb), 0.2)' : '#f3f4f6'),
                      color: currentVideoIndex === 0 ? '#9ca3af' : (isModernTheme ? 'white' : '#374151'),
                      border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: currentVideoIndex === 0 ? 'not-allowed' : 'pointer'
                    }}
                  >
                    ⏮️ Previous
                  </button>

                  <button
                    onClick={() => playVideo(currentVideoIndex)}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'var(--accent-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                  >
                    <Play size={20} />
                    {isPlaying ? 'Playing...' : 'Play Current'}
                  </button>

                  <button
                    onClick={() => {
                      const nextIndex = Math.min(createdPlaylist?.items?.length - 1 || 0, currentVideoIndex + 1)
                      setCurrentVideoIndex(nextIndex)
                    }}
                    disabled={currentVideoIndex >= (createdPlaylist?.items?.length || 1) - 1}
                    style={{
                      padding: '8px 16px',
                      backgroundColor: currentVideoIndex >= (createdPlaylist?.items?.length || 1) - 1 ? 'transparent' : (isModernTheme ? 'rgba(var(--accent-color-rgb), 0.2)' : '#f3f4f6'),
                      color: currentVideoIndex >= (createdPlaylist?.items?.length || 1) - 1 ? '#9ca3af' : (isModernTheme ? 'white' : '#374151'),
                      border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '14px',
                      cursor: currentVideoIndex >= (createdPlaylist?.items?.length || 1) - 1 ? 'not-allowed' : 'pointer'
                    }}
                  >
                    Next ⏭️
                  </button>
                </div>

                {/* Smart Auto-advance Toggle */}
                <div style={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: '12px',
                  marginBottom: '24px'
                }}>
                  <label style={{
                    display: 'flex',
                    alignItems: 'center',
                    gap: '8px',
                    fontSize: '14px',
                    color: isModernTheme ? '#9ca3af' : '#6b7280',
                    cursor: 'pointer'
                  }}>
                    <input
                      type="checkbox"
                      checked={autoAdvance}
                      onChange={(e) => setAutoAdvance(e.target.checked)}
                      style={{
                        width: '16px',
                        height: '16px',
                        accentColor: 'var(--accent-color)'
                      }}
                    />
                    Smart auto-advance (5s countdown after video ends)
                  </label>
                </div>

                {/* Auto-advance Info */}
                {autoAdvance && (
                  <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    gap: '8px',
                    marginBottom: '24px',
                    padding: '12px 16px',
                    backgroundColor: isModernTheme ? 'rgba(34, 197, 94, 0.1)' : '#f0fdf4',
                    border: isModernTheme ? '1px solid rgba(34, 197, 94, 0.2)' : '1px solid #bbf7d0',
                    borderRadius: '8px',
                    maxWidth: '400px',
                    margin: '0 auto 24px auto'
                  }}>
                    <div style={{
                      fontSize: '13px',
                      color: isModernTheme ? '#10b981' : '#059669',
                      textAlign: 'center',
                      fontWeight: '600'
                    }}>
                      ✨ Intelligent Auto-Advance Enabled
                    </div>
                    <div style={{
                      fontSize: '11px',
                      color: isModernTheme ? '#9ca3af' : '#6b7280',
                      textAlign: 'center',
                      lineHeight: '1.4'
                    }}>
                      Click "Video Finished" when done watching → 5-second countdown → Next video plays automatically
                    </div>
                  </div>
                )}

                <div style={{ display: 'flex', gap: '16px', justifyContent: 'center', flexWrap: 'wrap' }}>
                  {(createdPlaylist?.items || []).slice(0, 3).map((item, index) => (
                    <button
                      key={item.id}
                      onClick={() => window.open(item.url, '_blank')}
                      style={{
                        padding: '12px 16px',
                        backgroundColor: isModernTheme ? 'rgba(var(--accent-color-rgb), 0.2)' : '#f3f4f6',
                        border: isModernTheme ? '1px solid rgba(var(--accent-color-rgb), 0.3)' : '1px solid #d1d5db',
                        borderRadius: '8px',
                        color: isModernTheme ? 'white' : '#374151',
                        cursor: 'pointer',
                        fontSize: '14px',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        gap: '8px'
                      }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(var(--accent-color-rgb), 0.3)' : '#e5e7eb'
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(var(--accent-color-rgb), 0.2)' : '#f3f4f6'
                      }}
                    >
                      <span style={{ fontSize: '16px' }}>
                        {item.type === 'video' ? '🎥' :
                         item.type === 'audio' ? '🎵' :
                         item.type === 'document' ? '📄' : '🌐'}
                      </span>
                      <span style={{ maxWidth: '150px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                        {item.title}
                      </span>
                    </button>
                  ))}
                </div>

                {(createdPlaylist?.items?.length || 0) > 3 && (
                  <p style={{
                    fontSize: '14px',
                    color: isModernTheme ? '#9ca3af' : '#6b7280',
                    marginTop: '16px',
                    fontStyle: 'italic'
                  }}>
                    +{(createdPlaylist?.items?.length || 0) - 3} more items in your playlist
                  </p>
                )}

                {/* Enhanced Playlist Queue */}
                <div style={{
                  backgroundColor: isModernTheme ? 'rgba(255, 255, 255, 0.05)' : '#f8fafc',
                  border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.1)' : '1px solid #e2e8f0',
                  borderRadius: '12px',
                  padding: '16px',
                  marginTop: '24px',
                  maxWidth: '600px',
                  margin: '24px auto 0 auto'
                }}>
                  <h4 style={{
                    fontSize: '16px',
                    fontWeight: '600',
                    color: isModernTheme ? 'white' : '#111827',
                    marginBottom: '12px',
                    textAlign: 'center'
                  }}>
                    🎬 Playlist Queue
                  </h4>

                  <div style={{
                    maxHeight: '200px',
                    overflowY: 'auto',
                    marginBottom: '16px'
                  }}>
                    {(createdPlaylist?.items || []).map((item, index) => (
                      <div
                        key={item.id}
                        onClick={() => setCurrentVideoIndex(index)}
                        style={{
                          padding: '8px 12px',
                          backgroundColor: index === currentVideoIndex ?
                            (isModernTheme ? 'rgba(var(--accent-color-rgb), 0.3)' : '#e0e7ff') :
                            'transparent',
                          border: index === currentVideoIndex ?
                            (isModernTheme ? '1px solid rgba(var(--accent-color-rgb), 0.5)' : '1px solid var(--accent-color)') :
                            '1px solid transparent',
                          borderRadius: '8px',
                          marginBottom: '4px',
                          cursor: 'pointer',
                          display: 'flex',
                          alignItems: 'center',
                          gap: '12px',
                          transition: 'all 0.2s ease'
                        }}
                      >
                        <div style={{
                          fontSize: '14px',
                          color: isModernTheme ? '#9ca3af' : '#6b7280',
                          minWidth: '24px'
                        }}>
                          {index + 1}.
                        </div>
                        <div style={{ fontSize: '16px' }}>
                          {item.type === 'video' ? '🎥' :
                           item.type === 'audio' ? '🎵' :
                           item.type === 'document' ? '📄' : '🌐'}
                        </div>
                        <div style={{
                          flex: 1,
                          fontSize: '14px',
                          color: isModernTheme ? 'white' : '#374151',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}>
                          {item.title}
                        </div>
                        <div style={{
                          fontSize: '12px',
                          color: isModernTheme ? '#9ca3af' : '#6b7280'
                        }}>
                          {preloadedVideos.has(index) ? '✅' : '⏳'}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div style={{
                    fontSize: '12px',
                    color: isModernTheme ? '#9ca3af' : '#6b7280',
                    textAlign: 'center',
                    marginBottom: '16px'
                  }}>
                    {preloadedVideos.size} of {createdPlaylist?.items?.length || 0} videos preloaded
                  </div>
                </div>

                <div style={{ marginTop: '32px', display: 'flex', gap: '12px', justifyContent: 'center' }}>
                  <button
                    onClick={() => playVideo(currentVideoIndex)}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'var(--accent-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--accent-color-dark)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--accent-color)'
                    }}
                  >
                    <Play size={20} />
                    Start Playlist ({createdPlaylist?.items?.length || 0} videos)
                  </button>

                  <button
                    onClick={() => setShowPlayer(false)}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'transparent',
                      color: isModernTheme ? '#9ca3af' : '#6b7280',
                      border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(255, 255, 255, 0.1)' : '#f3f4f6'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent'
                    }}
                  >
                    Close Player
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Video Playlist Builder */}
      {showVideoBuilder && (
        <div style={{ position: 'fixed', top: 0, left: 0, right: 0, bottom: 0, zIndex: 1001 }}>
          {/* VideoPlaylistBuilder component - using inline implementation for now */}
          <div style={{
            position: 'absolute',
            inset: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backdropFilter: 'blur(4px)'
          }}>
            <div style={{
              width: '90%',
              maxWidth: '1400px',
              height: '85%',
              backgroundColor: isModernTheme ? 'rgba(0, 0, 0, 0.95)' : 'white',
              borderRadius: '12px',
              border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #e5e7eb',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden',
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
            }}>

              {/* Video Builder Header */}
              <div style={{
                padding: '20px 24px',
                borderBottom: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #e5e7eb',
                backgroundColor: isModernTheme ? 'rgba(255, 255, 255, 0.05)' : '#f9fafb',
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center'
              }}>
                <div>
                  <h2 style={{
                    fontSize: '20px',
                    fontWeight: '600',
                    color: isModernTheme ? 'white' : '#111827',
                    margin: 0
                  }}>
                    🎥 Advanced Video Playlist Builder
                  </h2>
                  <p style={{
                    fontSize: '14px',
                    color: isModernTheme ? '#9ca3af' : '#6b7280',
                    margin: '4px 0 0 0'
                  }}>
                    Select from {getBookmarkTypeCount('video')} videos • Smart filtering and batch selection
                  </p>
                </div>
                <button
                  onClick={() => setShowVideoBuilder(false)}
                  style={{
                    padding: '8px',
                    backgroundColor: 'transparent',
                    border: 'none',
                    borderRadius: '6px',
                    color: isModernTheme ? 'white' : '#6b7280',
                    cursor: 'pointer',
                    transition: 'background-color 0.2s ease'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(255, 255, 255, 0.1)' : '#f3f4f6'
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent'
                  }}
                >
                  <X size={20} />
                </button>
              </div>

              {/* Video Builder Content */}
              <div style={{ flex: 1, padding: '24px', textAlign: 'center' }}>
                <div style={{
                  fontSize: '64px',
                  marginBottom: '24px',
                  opacity: 0.7
                }}>
                  🎬
                </div>

                <h3 style={{
                  fontSize: '24px',
                  fontWeight: '600',
                  color: isModernTheme ? 'white' : '#111827',
                  marginBottom: '12px'
                }}>
                  Advanced Video Selection Coming Soon!
                </h3>

                <p style={{
                  fontSize: '16px',
                  color: isModernTheme ? '#9ca3af' : '#6b7280',
                  marginBottom: '32px',
                  maxWidth: '600px',
                  margin: '0 auto 32px auto'
                }}>
                  The advanced video builder will include smart filtering by platform (YouTube, Vimeo),
                  collection grouping, batch selection, search, and sorting options perfect for managing
                  large video collections like your {getBookmarkTypeCount('video')} videos.
                </p>

                <div style={{ display: 'flex', gap: '16px', justifyContent: 'center' }}>
                  <button
                    onClick={() => {
                      // For now, create a quick video playlist with first 20 videos
                      const videoBookmarks = bookmarks.filter(b => {
                        const url = b.url.toLowerCase()
                        return url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')
                      }).slice(0, 20)

                      const quickPlaylist = {
                        id: `video-playlist-${Date.now()}`,
                        name: `Quick Video Playlist (${videoBookmarks.length} videos)`,
                        type: 'video',
                        items: videoBookmarks.map(video => ({
                          id: video.id,
                          title: video.title,
                          url: video.url,
                          type: 'video' as const,
                          thumbnail: video.favicon
                        }))
                      }

                      handleVideoBuilderPlaylist(quickPlaylist)
                    }}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'var(--accent-color)',
                      color: 'white',
                      border: 'none',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '600',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      transition: 'background-color 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--accent-color-dark)'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'var(--accent-color)'
                    }}
                  >
                    <Play size={20} />
                    Quick Video Playlist (First 20)
                  </button>

                  <button
                    onClick={() => setShowVideoBuilder(false)}
                    style={{
                      padding: '12px 24px',
                      backgroundColor: 'transparent',
                      color: isModernTheme ? '#9ca3af' : '#6b7280',
                      border: isModernTheme ? '1px solid rgba(255, 255, 255, 0.2)' : '1px solid #d1d5db',
                      borderRadius: '8px',
                      fontSize: '16px',
                      fontWeight: '500',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = isModernTheme ? 'rgba(255, 255, 255, 0.1)' : '#f3f4f6'
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent'
                    }}
                  >
                    Back to Simple Builder
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Smart Countdown Overlay */}
      {showCountdown && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0, 0, 0, 0.8)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 10000,
          backdropFilter: 'blur(4px)'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, var(--accent-color), var(--accent-color-dark))',
            color: 'white',
            padding: '32px 40px',
            borderRadius: '20px',
            textAlign: 'center',
            boxShadow: '0 20px 60px rgba(var(--accent-color-rgb), 0.4)',
            maxWidth: '400px'
          }}>
            <div style={{
              fontSize: '48px',
              marginBottom: '16px',
              fontWeight: 'bold',
              textShadow: '0 2px 4px rgba(0,0,0,0.3)'
            }}>
              {countdownSeconds}
            </div>
            <div style={{
              fontSize: '18px',
              fontWeight: '600',
              marginBottom: '8px'
            }}>
              Next video starting in...
            </div>
            <div style={{
              fontSize: '14px',
              opacity: 0.9,
              marginBottom: '20px'
            }}>
              Video {currentVideoIndex + 2} of {createdPlaylist?.items?.length || 0}
            </div>
            <button
              onClick={cancelCountdown}
              style={{
                padding: '10px 20px',
                background: 'rgba(255, 255, 255, 0.2)',
                color: 'white',
                border: '1px solid rgba(255, 255, 255, 0.3)',
                borderRadius: '8px',
                cursor: 'pointer',
                fontSize: '14px',
                fontWeight: '600',
                transition: 'all 0.2s ease'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.3)'
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.2)'
              }}
            >
              Cancel Auto-Advance
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default MultimediaPlaylistPanelNew
