
import React from 'react';
import { DocumentArrowDownIcon, ShieldCheckIcon, SparklesIcon, TagIcon, TrashIcon } from './icons/HeroIcons';
// import SpinnerIcon from './icons/SpinnerIcon';
import { Button, Dropdown } from './base';
// import { createExportEvent } from '../utils/eventUtils';
import { userTierService } from '../services/userTierService';
import type { DropdownItem } from './base';

interface ActionToolbarProps {
  onSummarize: () => void;
  onTag: () => void;
  onExportSelectedCSV: () => void;
  onExportAllCSV: () => void;
  onExportSelectedPDF: () => void;
  onExportAllPDF: () => void;
  onExportSelectedHTML: () => void;
  onExportAllHTML: () => void;
  onExportSelectedCSVByEmail: () => void;
  onExportAllCSVByEmail: () => void;
  onExportSelectedPDFByEmail: () => void;
  onExportAllPDFByEmail: () => void;
  onExportSelectedHTMLByEmail: () => void;
  onExportAllHTMLByEmail: () => void;
  onDeleteSelected: () => void;
  numSelected: number;
  isProcessing: boolean;
  hasAnyBookmarks: boolean;
}

// ActionButton component is now replaced by the reusable Button component

const ActionToolbar: React.FC<ActionToolbarProps> = ({
  onSummarize,
  onTag,
  onExportSelectedCSV,
  onExportAllCSV,
  onExportSelectedPDF,
  onExportAllPDF,
  onExportSelectedHTML,
  onExportAllHTML,
  onExportSelectedCSVByEmail,
  onExportAllCSVByEmail,
  onExportSelectedPDFByEmail,
  onExportAllPDFByEmail,
  onExportSelectedHTMLByEmail,
  onExportAllHTMLByEmail,
  onDeleteSelected,
  numSelected,
  isProcessing,
  hasAnyBookmarks
}) => {
  // CSV Export dropdown items
  const csvItems: DropdownItem[] = [
    {
      id: 'csv-download-selected',
      label: `📥 Download CSV (${numSelected})`,
      onClick: onExportSelectedCSV,
      disabled: numSelected === 0
    },
    {
      id: 'csv-email-selected',
      label: `📧 Email CSV (${numSelected})`,
      onClick: onExportSelectedCSVByEmail,
      disabled: numSelected === 0
    },
    { id: 'csv-divider', type: 'divider' },
    {
      id: 'csv-download-all',
      label: '📥 Download All CSV',
      onClick: onExportAllCSV,
      disabled: !hasAnyBookmarks
    },
    {
      id: 'csv-email-all',
      label: '📧 Email All CSV',
      onClick: onExportAllCSVByEmail,
      disabled: !hasAnyBookmarks
    }
  ];

  // PDF Export dropdown items
  const pdfItems: DropdownItem[] = [
    {
      id: 'pdf-download-selected',
      label: `📥 Download PDF (${numSelected})`,
      onClick: onExportSelectedPDF,
      disabled: numSelected === 0
    },
    {
      id: 'pdf-email-selected',
      label: `📧 Email PDF (${numSelected})`,
      onClick: onExportSelectedPDFByEmail,
      disabled: numSelected === 0
    },
    { id: 'pdf-divider', type: 'divider' },
    {
      id: 'pdf-download-all',
      label: '📥 Download All PDF',
      onClick: onExportAllPDF,
      disabled: !hasAnyBookmarks
    },
    {
      id: 'pdf-email-all',
      label: '📧 Email All PDF',
      onClick: onExportAllPDFByEmail,
      disabled: !hasAnyBookmarks
    }
  ];

  // HTML Export dropdown items
  const htmlItems: DropdownItem[] = [
    {
      id: 'html-download-selected',
      label: `📥 Download HTML (${numSelected})`,
      onClick: onExportSelectedHTML,
      disabled: numSelected === 0
    },
    {
      id: 'html-email-selected',
      label: `📧 Email HTML (${numSelected})`,
      onClick: onExportSelectedHTMLByEmail,
      disabled: numSelected === 0
    },
    { id: 'html-divider', type: 'divider' },
    {
      id: 'html-download-all',
      label: '📥 Download All HTML',
      onClick: onExportAllHTML,
      disabled: !hasAnyBookmarks
    },
    {
      id: 'html-email-all',
      label: '📧 Email All HTML',
      onClick: onExportAllHTMLByEmail,
      disabled: !hasAnyBookmarks
    }
  ];
  const currentTier = userTierService.getCurrentTier();
  const processingConfig = userTierService.getProcessingConfig();

  return (
    <div className="card mb-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:flex xl:flex-wrap items-center justify-between gap-3">
        
        {/* Left Aligned Actions (Primary Operations) */}
        <div className="flex flex-wrap gap-3 xl:flex-grow">
          <Button 
            onClick={onSummarize} 
            disabled={isProcessing || numSelected === 0} 
            title="Summarize selected bookmarks with AI"
            variant="primary"
            size="sm"
            loading={isProcessing}
            leftIcon={isProcessing ? undefined : <SparklesIcon className="w-5 h-5" />}
          >
            Summarize ({numSelected})
          </Button>
          <Button 
            onClick={onTag} 
            disabled={isProcessing || numSelected === 0} 
            title="Generate tags for selected bookmarks with AI"
            variant="primary"
            size="sm"
            loading={isProcessing}
            leftIcon={isProcessing ? undefined : <TagIcon className="w-5 h-5" />}
          >
            Tag ({numSelected})
          </Button>
          <Button 
            onClick={onDeleteSelected} 
            disabled={isProcessing || numSelected === 0} 
            title="Delete selected bookmarks"
            variant="danger"
            size="sm"
            loading={isProcessing}
            leftIcon={isProcessing ? undefined : <TrashIcon className="w-5 h-5" />}
          >
            Delete ({numSelected})
          </Button>
        </div>
        
        {/* Right Aligned Actions (Export & Utility) */}
        <div className="flex flex-wrap gap-3 xl:justify-end col-span-1 lg:col-span-2 xl:col-auto">
          {/* CSV Export Options */}
          <Dropdown
            trigger={
              <Button 
                disabled={!hasAnyBookmarks} 
                title="Export selected bookmarks to CSV" 
                variant="secondary"
                size="sm"
                leftIcon={<DocumentArrowDownIcon className="w-5 h-5" />}
              >
                CSV ({numSelected})
              </Button>
            }
            items={csvItems}
            placement="bottom-end"
          />

          {/* PDF Export Options */}
          <Dropdown
            trigger={
              <Button 
                disabled={!hasAnyBookmarks} 
                title="Export selected bookmarks to PDF" 
                variant="secondary"
                size="sm"
                leftIcon={<DocumentArrowDownIcon className="w-5 h-5" />}
              >
                PDF ({numSelected})
              </Button>
            }
            items={pdfItems}
            placement="bottom-end"
          />

          {/* HTML Export Options */}
          <Dropdown
            trigger={
              <Button 
                disabled={!hasAnyBookmarks} 
                title="Export bookmarks to HTML" 
                variant="secondary"
                size="sm"
                leftIcon={<DocumentArrowDownIcon className="w-5 h-5" />}
              >
                HTML
              </Button>
            }
            items={htmlItems}
            placement="bottom-end"
          />
        </div>
      </div>
       {numSelected > 0 && (
         <p className="text-xs text-muted mt-3 text-left">
            {numSelected} bookmark{numSelected === 1 ? '' : 's'} selected.
         </p>
       )}
       
       {/* Tier Indicator */}
       <div className="mt-3 flex items-center justify-between text-xs">
         <div className="flex items-center space-x-2">
           <ShieldCheckIcon className={`w-4 h-4 ${
             currentTier.type === 'authenticated' ? 'text-amber-400' : 'text-slate-400'
           }`} />
           <span className="text-slate-300">
             {currentTier.type === 'authenticated' ? 'Premium' : 'Basic'} Tier
           </span>
           <span className="text-slate-500">•</span>
           <span className="text-slate-400">
             {processingConfig.enableAI ? 'AI Processing' : 'Client-side Processing'}
           </span>
         </div>
         {currentTier.type === 'authenticated' && (
           <div className="text-slate-400">
             Premium features enabled
           </div>
         )}
       </div>
    </div>
  );
};

export default ActionToolbar;
