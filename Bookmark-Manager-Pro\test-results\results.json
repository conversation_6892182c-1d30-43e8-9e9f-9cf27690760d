{"config": {"configFile": "C:\\Nexicon\\Bookmark-Manager-Pro\\playwright.config.js", "rootDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\global-setup.js", "globalTeardown": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\global-teardown.js", "globalTimeout": 600000, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["junit", {"outputFile": "test-results/results.xml"}], ["C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\reporters\\vibe-metrics-reporter.js", null], ["C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\reporters\\emotional-journey-reporter.js", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Microsoft Edge", "name": "Microsoft Edge", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Google Chrome", "name": "Google Chrome", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "vibe-desktop", "name": "vibe-desktop", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "vibe-mobile", "name": "vibe-mobile", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}, {"outputDir": "C:/Nexicon/Bookmark-Manager-Pro/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "vibe-accessibility", "name": "vibe-accessibility", "testDir": "C:/Nexicon/Bookmark-Manager-Pro/tests/vibe", "testIgnore": [], "testMatch": ["**/*.spec.js", "**/*.test.js", "**/*.e2e.js"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:5173", "reuseExistingServer": true, "timeout": 120000}}, "suites": [], "errors": [{"message": "SyntaxError: C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\suggestion-timing-intelligence.spec.js: Missing semicolon. (143:43)\n\n\u001b[0m \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 143 |\u001b[39m   calculateRelevanceScore(text\u001b[33m,\u001b[39m userTopics) {\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 144 |\u001b[39m     \u001b[36mconst\u001b[39m lowerText \u001b[33m=\u001b[39m text\u001b[33m.\u001b[39mtoLowerCase()\u001b[33m;\u001b[39m\n \u001b[90m 145 |\u001b[39m     \u001b[36mconst\u001b[39m matches \u001b[33m=\u001b[39m userTopics\u001b[33m.\u001b[39mfilter(topic \u001b[33m=>\u001b[39m lowerText\u001b[33m.\u001b[39mincludes(topic\u001b[33m.\u001b[39mtoLowerCase()))\u001b[33m;\u001b[39m\n \u001b[90m 146 |\u001b[39m     \u001b[36mreturn\u001b[39m matches\u001b[33m.\u001b[39mlength \u001b[33m/\u001b[39m userTopics\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\u001b[0m", "stack": "SyntaxError: C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\suggestion-timing-intelligence.spec.js: Missing semicolon. (143:43)\n\n\u001b[0m \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 143 |\u001b[39m   calculateRelevanceScore(text\u001b[33m,\u001b[39m userTopics) {\n \u001b[90m     |\u001b[39m                                            \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 144 |\u001b[39m     \u001b[36mconst\u001b[39m lowerText \u001b[33m=\u001b[39m text\u001b[33m.\u001b[39mtoLowerCase()\u001b[33m;\u001b[39m\n \u001b[90m 145 |\u001b[39m     \u001b[36mconst\u001b[39m matches \u001b[33m=\u001b[39m userTopics\u001b[33m.\u001b[39mfilter(topic \u001b[33m=>\u001b[39m lowerText\u001b[33m.\u001b[39mincludes(topic\u001b[33m.\u001b[39mtoLowerCase()))\u001b[33m;\u001b[39m\n \u001b[90m 146 |\u001b[39m     \u001b[36mreturn\u001b[39m matches\u001b[33m.\u001b[39mlength \u001b[33m/\u001b[39m userTopics\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\u001b[0m", "location": {"file": "C:\\Nexicon\\Bookmark-Manager-Pro\\tests\\vibe\\core\\suggestion-timing-intelligence.spec.js", "line": 143, "column": 43}, "snippet": "\u001b[90m   at \u001b[39mvibe\\core\\suggestion-timing-intelligence.spec.js:143\n\n\u001b[0m \u001b[90m 141 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 142 |\u001b[39m   \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 143 |\u001b[39m   calculateRelevanceScore(text\u001b[33m,\u001b[39m userTopics) {\n \u001b[90m     |\u001b[39m                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 144 |\u001b[39m     \u001b[36mconst\u001b[39m lowerText \u001b[33m=\u001b[39m text\u001b[33m.\u001b[39mtoLowerCase()\u001b[33m;\u001b[39m\n \u001b[90m 145 |\u001b[39m     \u001b[36mconst\u001b[39m matches \u001b[33m=\u001b[39m userTopics\u001b[33m.\u001b[39mfilter(topic \u001b[33m=>\u001b[39m lowerText\u001b[33m.\u001b[39mincludes(topic\u001b[33m.\u001b[39mtoLowerCase()))\u001b[33m;\u001b[39m\n \u001b[90m 146 |\u001b[39m     \u001b[36mreturn\u001b[39m matches\u001b[33m.\u001b[39mlength \u001b[33m/\u001b[39m userTopics\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\u001b[0m"}], "stats": {"startTime": "2025-07-16T14:34:40.965Z", "duration": 336.692, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}