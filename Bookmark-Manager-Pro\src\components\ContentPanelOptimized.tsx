import React, { useState } from 'react'
import { X, FileText, BookOpen, Hash, CheckCircle, Settings, Eye, Zap } from 'lucide-react'
import '../styles/optimized-panels.css'

interface ContentPanelOptimizedProps {
  onClose: () => void
  bookmarks: any[]
  isModernTheme?: boolean
  onOrganize?: (config: any) => void
}

export const ContentPanelOptimized: React.FC<ContentPanelOptimizedProps> = ({
  onClose,
  bookmarks,
  isModernTheme = false,
  onOrganize
}) => {
  const [preserveExistingFolders, setPreserveExistingFolders] = useState(true)
  const [topicModeling, setTopicModeling] = useState(true)
  const [keywordExtraction, setKeywordExtraction] = useState(true)
  const [contentDepth, setContentDepth] = useState(2)
  const [isOrganizing, setIsOrganizing] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [activeButton, setActiveButton] = useState<string | null>(null)
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [showPreview, setShowPreview] = useState(false)

  const handleOrganize = async () => {
    setIsOrganizing(true)
    setShowResults(false)
    
    try {
      const config = {
        preserveExistingFolders,
        topicModeling,
        keywordExtraction,
        contentDepth
      }
      
      // Simulate organization process
      const results = [
        'Analyzing content patterns...',
        'Extracting keywords and topics...',
        'Grouping similar content...',
        'Creating folder structure...',
        `Organized ${bookmarks.length} bookmarks successfully`
      ]
      
      for (let i = 0; i < results.length; i++) {
        await new Promise(resolve => setTimeout(resolve, 800))
        setOrganizationResults(prev => [...prev, results[i]])
      }
      
      setShowResults(true)
      onOrganize?.(config)
    } catch (error) {
      console.error('Organization failed:', error)
    } finally {
      setIsOrganizing(false)
    }
  }

  const generatePreview = () => {
    setShowPreview(!showPreview)
  }

  const getContentDepthLabel = () => {
    switch (contentDepth) {
      case 1: return 'Title only'
      case 2: return 'Title + Description'
      case 3: return 'Full content'
      default: return 'Title + Description'
    }
  }

  return (
    <div className={`optimized-panel ${isModernTheme ? 'modern-enhanced' : ''}`}>
      {/* Header */}
      <div className="panel-header">
        <div className="panel-title">
          <FileText size={18} />
          <span>Expert Content Organization</span>
        </div>
        <button onClick={onClose} className="close-btn" aria-label="Close">
          <X size={18} />
        </button>
      </div>

      {/* Main Content */}
      <div className="panel-content">
        {/* Quick Actions */}
        <div className="quick-actions">
          <button
            onClick={generatePreview}
            className="action-btn secondary"
            disabled={isOrganizing || bookmarks.length === 0}
          >
            <Eye size={16} />
            Preview
          </button>
          <button
            onClick={() => {
              if (!isOrganizing && bookmarks.length > 0) {
                setActiveButton('organize')
                setTimeout(() => setActiveButton(null), 200)
                handleOrganize()
              }
            }}
            className={`action-btn primary ${activeButton === 'organize' ? 'active' : ''}`}
            disabled={bookmarks.length === 0 || isOrganizing}
          >
            {isOrganizing ? (
              <div className="spinner" />
            ) : (
              <Zap size={16} />
            )}
            {isOrganizing ? 'Organizing...' : 'Start Organization'}
          </button>
        </div>

        {/* Progress */}
        {isOrganizing && (
          <div className="progress-section">
            <div className="progress-bar">
              <div className="progress-fill" style={{ width: `${(organizationResults.length / 5) * 100}%` }} />
            </div>
            <div className="progress-text">
              {organizationResults[organizationResults.length - 1] || 'Starting...'}
            </div>
          </div>
        )}

        {/* Statistics */}
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-value">{bookmarks.length}</span>
            <span className="stat-label">Bookmarks</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{contentDepth}</span>
            <span className="stat-label">Depth Level</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{topicModeling ? 'ON' : 'OFF'}</span>
            <span className="stat-label">Topic Modeling</span>
          </div>
        </div>

        {/* Strategy Overview */}
        <div className="section-compact">
          <h3 className="section-title-compact">
            <BookOpen size={14} />
            Content Strategy
          </h3>
          <div className="feature-grid">
            <div className="feature-item">
              <Hash size={12} />
              <span>Topic Modeling (LDA)</span>
            </div>
            <div className="feature-item">
              <CheckCircle size={12} />
              <span>Keyword Extraction</span>
            </div>
            <div className="feature-item">
              <FileText size={12} />
              <span>Intent Classification</span>
            </div>
          </div>
        </div>

        {/* Basic Configuration */}
        <div className="section-compact">
          <h3 className="section-title-compact">
            <Settings size={14} />
            Configuration
          </h3>
          <div className="option-list">
            <label className="option-item">
              <input
                type="checkbox"
                checked={preserveExistingFolders}
                onChange={(e) => setPreserveExistingFolders(e.target.checked)}
              />
              <span>Preserve existing folders</span>
            </label>
            <label className="option-item">
              <input
                type="checkbox"
                checked={topicModeling}
                onChange={(e) => setTopicModeling(e.target.checked)}
              />
              <span>Topic modeling</span>
            </label>
            <label className="option-item">
              <input
                type="checkbox"
                checked={keywordExtraction}
                onChange={(e) => setKeywordExtraction(e.target.checked)}
              />
              <span>Keyword extraction</span>
            </label>
          </div>
        </div>

        {/* Collapsible Advanced Options */}
        <div className="collapsible-section">
          <button
            className="collapsible-header"
            onClick={() => setShowAdvanced(!showAdvanced)}
          >
            <Settings size={14} />
            <span>Advanced Options</span>
            <span className={`chevron ${showAdvanced ? 'expanded' : ''}`}>▼</span>
          </button>
          {showAdvanced && (
            <div className="collapsible-content">
              <div className="option-item">
                <div className="range-option">
                  <div className="range-header">
                    <span>Content Analysis Depth</span>
                    <span className="range-value">{getContentDepthLabel()}</span>
                  </div>
                  <input
                    type="range"
                    min="1"
                    max="3"
                    step="1"
                    value={contentDepth}
                    onChange={(e) => setContentDepth(parseInt(e.target.value))}
                    className="range-input"
                  />
                  <small>Deeper analysis = more accurate but slower</small>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Collapsible Preview */}
        {showPreview && (
          <div className="collapsible-section">
            <button
              className="collapsible-header"
              onClick={() => setShowPreview(false)}
            >
              <Eye size={14} />
              <span>Organization Preview</span>
              <span className="chevron expanded">▼</span>
            </button>
            <div className="collapsible-content">
              <div className="preview-content">
                <p>Preview of content organization structure will appear here...</p>
                <div className="preview-stats">
                  <span>Estimated folders: 8-12</span>
                  <span>Uncategorized: ~5%</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Results */}
        {showResults && (
          <div className="results-section">
            <h4 className="results-title">Organization Results</h4>
            <div className="results-content scrollable-content">
              {organizationResults.map((result, index) => (
                <div key={index} className="result-item">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Status */}
        <div className="status-section">
          <div className="status-indicator success">
            <FileText size={16} />
            <div className="status-text">
              <span>Content Analysis Ready</span>
              <small>{bookmarks.length} bookmarks ready for organization</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ContentPanelOptimized