/* Memory Monitor Styles */
.memory-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.memory-monitor-compact {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.memory-monitor-compact:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-1px);
}

.memory-monitor-expanded {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(15px);
  color: white;
  border-radius: 12px;
  padding: 16px;
  min-width: 280px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.memory-monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.memory-monitor-header h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.close-button:hover {
  background: rgba(255, 255, 255, 0.1);
}

.memory-stats {
  margin-bottom: 12px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 6px;
  font-size: 12px;
}

.stat-row span:first-child {
  color: rgba(255, 255, 255, 0.7);
}

.stat-row span:last-child {
  font-weight: 500;
}

.memory-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 12px;
}

.memory-bar-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

.memory-bar-fill.low {
  background: linear-gradient(90deg, #10b981, #34d399);
}

.memory-bar-fill.medium {
  background: linear-gradient(90deg, #f59e0b, #fbbf24);
}

.memory-bar-fill.high {
  background: linear-gradient(90deg, #ef4444, #f87171);
}

.memory-history {
  margin-bottom: 12px;
}

.history-label {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 6px;
}

.history-chart {
  display: flex;
  align-items: end;
  gap: 2px;
  height: 40px;
}

.history-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
  transition: height 0.3s ease;
}

.memory-actions {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.gc-button {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.gc-button:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Emergency Cleanup Button */
.emergency-cleanup-button {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.emergency-cleanup-button:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(239, 68, 68, 0.4);
}

.emergency-cleanup-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(239, 68, 68, 0.3);
}

/* Critical Cleanup Button - For 80%+ Memory Usage */
.critical-cleanup-button {
  background: linear-gradient(135deg, #7f1d1d, #991b1b);
  color: white;
  border: 2px solid #fca5a5;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
  box-shadow: 0 4px 8px rgba(127, 29, 29, 0.4);
  animation: pulse-critical 1.5s infinite;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.critical-cleanup-button:hover {
  background: linear-gradient(135deg, #991b1b, #7f1d1d);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(127, 29, 29, 0.6);
  border-color: #ef4444;
}

.critical-cleanup-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(127, 29, 29, 0.4);
}

/* Force Reload Button */
.force-reload-button {
  background: linear-gradient(135deg, #1f2937, #374151);
  color: white;
  border: 2px solid #6b7280;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-left: 8px;
  box-shadow: 0 4px 8px rgba(31, 41, 55, 0.4);
}

.force-reload-button:hover {
  background: linear-gradient(135deg, #374151, #4b5563);
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(31, 41, 55, 0.6);
  border-color: #9ca3af;
}

.force-reload-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(31, 41, 55, 0.4);
}

@keyframes pulse-critical {
  0%, 100% {
    border-color: #fca5a5;
    box-shadow: 0 4px 8px rgba(127, 29, 29, 0.4), 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    border-color: #ef4444;
    box-shadow: 0 6px 12px rgba(127, 29, 29, 0.6), 0 0 0 6px rgba(239, 68, 68, 0.1);
  }
}

.memory-warning {
  font-size: 11px;
  color: #fbbf24;
  text-align: center;
  padding: 6px;
  background: rgba(251, 191, 36, 0.1);
  border-radius: 4px;
  border: 1px solid rgba(251, 191, 36, 0.2);
}

/* Enhanced Memory Warning States */
.memory-warning.critical {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 2px solid #ef4444;
  color: #991b1b;
  font-weight: 600;
  animation: pulse-warning 2s infinite;
  font-size: 12px;
  padding: 8px 12px;
  text-align: left;
}

.memory-warning.elevated {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
  border: 2px solid #f59e0b;
  color: #92400e;
  font-weight: 500;
  font-size: 12px;
  padding: 8px 12px;
  text-align: left;
}

@keyframes pulse-warning {
  0%, 100% {
    border-color: #ef4444;
    box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.4);
  }
  50% {
    border-color: #dc2626;
    box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
  }
}

/* Virtualized Grid Styles */
.virtualized-grid-container {
  width: 100%;
  height: 100%;
  min-height: calc(100vh - 200px); /* Ensure minimum height for full screen utilization */
  position: relative;
  display: flex;
  flex-direction: column;
}

.virtualized-grid-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
}

.loading-spinner {
  display: flex;
  align-items: center;
  gap: 8px;
}

.loading-spinner::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.virtualized-grid-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px;
  color: #666;
  font-size: 16px;
}

.bookmark-grid-small {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
  padding: 16px;
}

.memory-optimization-indicator {
  position: absolute;
  bottom: 16px;
  right: 16px;
  background: rgba(59, 130, 246, 0.9);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 10;
}

.load-more-indicator {
  grid-column: 1 / -1;
  text-align: center;
  padding: 20px;
  background: rgba(59, 130, 246, 0.1);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 8px;
  color: #3b82f6;
  font-weight: 500;
  margin: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .memory-monitor {
    top: 10px;
    right: 10px;
  }
  
  .memory-monitor-expanded {
    min-width: 240px;
  }
  
  .bookmark-grid-small {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 12px;
    padding: 12px;
  }
}

@media (max-width: 480px) {
  .memory-monitor-compact {
    padding: 6px 10px;
    font-size: 12px;
  }
  
  .memory-monitor-expanded {
    min-width: 200px;
    padding: 12px;
  }
  
  .bookmark-grid-small {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 8px;
  }
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
  .memory-monitor-compact {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
  
  .memory-monitor-expanded {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* Optimization Suggestion Toast */
.optimization-suggestion {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  animation: slideUp 0.3s ease-out;
}

.suggestion-content {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  max-width: 400px;
}

.suggestion-close {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  flex-shrink: 0;
}

.suggestion-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* Performance optimizations */
.virtualized-grid-container * {
  will-change: transform;
}

.memory-monitor {
  contain: layout style paint;
}

.bookmark-grid-small {
  contain: layout;
}
