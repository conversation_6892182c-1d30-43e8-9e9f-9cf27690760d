/**
 * Favicon Service - Handles favicon loading with proper error handling and fallbacks
 */

interface FaviconCache {
  [url: string]: {
    faviconUrl: string | null;
    timestamp: number;
    failed: boolean;
  }
}

class FaviconService {
  private static instance: FaviconService;
  private cache: FaviconCache = {};
  private readonly CACHE_DURATION = 24 * 60 * 60 * 1000; // 24 hours
  private readonly FAILED_RETRY_DURATION = 60 * 60 * 1000; // 1 hour
  private failedDomains = new Set<string>();
  private loadingQueue = new Set<string>();
  private readonly MAX_CONCURRENT_LOADS = 5; // Limit concurrent favicon loads
  private readonly LAZY_LOAD_THRESHOLD = 100; // Only load favicons for first 100 bookmarks

  static getInstance(): FaviconService {
    if (!FaviconService.instance) {
      FaviconService.instance = new FaviconService();
    }
    return FaviconService.instance;
  }

  /**
   * Get favicon URL with performance-optimized loading
   */
  getFaviconUrl(url: string, bookmarkFavicon?: string, priority: 'high' | 'low' = 'low'): string | null {
    // Use provided favicon if available
    if (bookmarkFavicon && bookmarkFavicon.trim() !== '') {
      return bookmarkFavicon;
    }

    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      // Check if domain has failed recently
      if (this.failedDomains.has(domain)) {
        return null;
      }

      // Check cache first
      const cached = this.cache[domain];
      if (cached) {
        const now = Date.now();
        const isExpired = now - cached.timestamp > this.CACHE_DURATION;
        const shouldRetryFailed = cached.failed && (now - cached.timestamp > this.FAILED_RETRY_DURATION);

        if (!isExpired && !shouldRetryFailed) {
          return cached.faviconUrl;
        }
      }

      // Performance control: Limit concurrent loads
      if (this.loadingQueue.size >= this.MAX_CONCURRENT_LOADS && priority === 'low') {
        return null; // Skip low priority loads when queue is full
      }

      // Generate favicon URL with fallback strategies
      const faviconUrl = this.generateFaviconUrl(domain);

      // Add to loading queue for tracking
      this.loadingQueue.add(domain);

      return faviconUrl;
    } catch (error) {
      // Suppress URL parsing errors to avoid console spam
      return null;
    }
  }

  /**
   * Generate favicon URL with multiple fallback strategies
   */
  private generateFaviconUrl(domain: string): string {
    // Strategy 1: Try direct favicon.ico
    const directFavicon = `https://${domain}/favicon.ico`;
    
    // Strategy 2: Use Google's favicon service (most reliable)
    const googleFavicon = `https://www.google.com/s2/favicons?domain=${domain}&sz=32`;
    
    // For now, use Google's service as it's most reliable
    // In the future, we could implement a fallback chain
    return googleFavicon;
  }

  /**
   * Handle favicon loading error with performance tracking
   */
  handleFaviconError(url: string): void {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      // Remove from loading queue
      this.loadingQueue.delete(domain);

      // Mark domain as failed
      this.failedDomains.add(domain);

      // Cache the failure
      this.cache[domain] = {
        faviconUrl: null,
        timestamp: Date.now(),
        failed: true
      };

      // Remove from failed domains after retry duration
      setTimeout(() => {
        this.failedDomains.delete(domain);
      }, this.FAILED_RETRY_DURATION);

    } catch (error) {
      // Suppress errors to avoid console spam
    }
  }

  /**
   * Handle successful favicon load
   */
  handleFaviconSuccess(url: string, faviconUrl: string): void {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');

      // Remove from loading queue
      this.loadingQueue.delete(domain);

      // Cache the success
      this.cache[domain] = {
        faviconUrl,
        timestamp: Date.now(),
        failed: false
      };
    } catch (error) {
      // Suppress errors
    }
  }

  /**
   * Preload favicon for a URL (optional optimization)
   */
  preloadFavicon(url: string, bookmarkFavicon?: string): void {
    const faviconUrl = this.getFaviconUrl(url, bookmarkFavicon);
    if (!faviconUrl) return;

    // Create a hidden image to preload the favicon
    const img = new Image();
    img.onload = () => {
      // Cache successful load
      try {
        const urlObj = new URL(url);
        const domain = urlObj.hostname.replace(/^www\./, '');
        this.cache[domain] = {
          faviconUrl,
          timestamp: Date.now(),
          failed: false
        };
      } catch (error) {
        // Suppress errors
      }
    };
    img.onerror = () => {
      this.handleFaviconError(url);
    };
    img.src = faviconUrl;
  }

  /**
   * Clear cache (useful for testing or memory management)
   */
  clearCache(): void {
    this.cache = {};
    this.failedDomains.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { total: number; failed: number; successful: number } {
    const entries = Object.values(this.cache);
    return {
      total: entries.length,
      failed: entries.filter(e => e.failed).length,
      successful: entries.filter(e => !e.failed).length
    };
  }
}

// Export singleton instance
export const faviconService = FaviconService.getInstance();

// Export utility function for easy use
export const getFaviconUrl = (url: string, bookmarkFavicon?: string, priority: 'high' | 'low' = 'low'): string | null => {
  return faviconService.getFaviconUrl(url, bookmarkFavicon, priority);
};

export const handleFaviconError = (url: string): void => {
  faviconService.handleFaviconError(url);
};

export const handleFaviconSuccess = (url: string, faviconUrl: string): void => {
  faviconService.handleFaviconSuccess(url, faviconUrl);
};

// Enhanced console error suppression for better user experience
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

// Suppress favicon, tracking, and development-related console noise
console.error = (...args: unknown[]) => {
  const message = args.join(' ');

  // Suppress favicon-related errors
  if (
    message.includes('Failed to load resource') &&
    (message.includes('gstatic.com') ||
     message.includes('favicon') ||
     message.includes('t0.gstatic.com') ||
     message.includes('t1.gstatic.com') ||
     message.includes('t2.gstatic.com') ||
     message.includes('t3.gstatic.com'))
  ) {
    return; // Suppress these errors
  }

  // Suppress tracking prevention warnings
  if (message.includes('Tracking Prevention blocked access to storage')) {
    return; // Suppress these warnings
  }

  // Suppress 404 errors for favicons
  if (message.includes('404') && message.includes('favicon')) {
    return; // Suppress favicon 404s
  }

  // Suppress WebSocket development errors (Vite HMR)
  if (
    message.includes('WebSocket connection') ||
    message.includes('WebSocket closed without opened') ||
    message.includes('ws://localhost') ||
    message.includes('[vite]') && message.includes('websocket')
  ) {
    return; // Suppress WebSocket development errors
  }

  // Allow other errors through
  originalConsoleError.apply(console, args);
};

// Also suppress warnings for tracking prevention and development noise
console.warn = (...args: unknown[]) => {
  const message = args.join(' ');

  if (message.includes('Tracking Prevention blocked access to storage')) {
    return; // Suppress these warnings
  }

  // Suppress Vite WebSocket warnings
  if (
    message.includes('[vite]') ||
    message.includes('WebSocket') ||
    message.includes('websocket')
  ) {
    return; // Suppress Vite development warnings
  }

  // Allow other warnings through
  originalConsoleWarn.apply(console, args);
};

// Handle uncaught promise rejections from WebSocket connections
window.addEventListener('unhandledrejection', (event) => {
  const error = event.reason;
  const message = error?.message || error?.toString() || '';

  // Suppress WebSocket-related promise rejections
  if (
    message.includes('WebSocket') ||
    message.includes('websocket') ||
    message.includes('ws://localhost')
  ) {
    event.preventDefault(); // Prevent the error from being logged
    return;
  }

  // Allow other promise rejections through (they might be important)
});

export default faviconService;
