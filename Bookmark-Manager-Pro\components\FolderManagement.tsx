import React, { useEffect, useState } from 'react';
import { Bookmark, Folder } from '../types';

interface FolderManagementProps {
  folders: Folder[];
  bookmarks: Bookmark[];
  onCreateFolder: (name: string, parentId?: string) => Promise<void>;
  onUpdateFolder: (id: string, updates: Partial<Folder>) => Promise<void>;
  onDeleteFolder: (id: string) => Promise<void>;
  onMoveFolder: (folderId: string, newParentId?: string) => Promise<void>;
  onClose: () => void;
}

interface FolderTreeNode extends Folder {
  children: FolderTreeNode[];
  bookmarkCount: number;
}

const FolderManagement: React.FC<FolderManagementProps> = ({
  folders,
  bookmarks,
  onCreateFolder,
  onUpdateFolder,
  onDeleteFolder,
  onMoveFolder,
  onClose
}) => {
  const [folderTree, setFolderTree] = useState<FolderTreeNode[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [editingFolder, setEditingFolder] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [newFolderParent, setNewFolderParent] = useState<string | null>(null);
  const [newFolderName, setNewFolderName] = useState('');
  const [draggedFolder, setDraggedFolder] = useState<string | null>(null);
  const [dropTarget, setDropTarget] = useState<string | null>(null);

  // Build folder tree structure
  useEffect(() => {
    const buildTree = () => {
      const folderMap = new Map<string, FolderTreeNode>();
      const rootFolders: FolderTreeNode[] = [];

      // Initialize all folders
      folders.forEach(folder => {
        const bookmarkCount = bookmarks.filter(b => b.folder === folder.name).length;
        folderMap.set(folder.id, {
          ...folder,
          children: [],
          bookmarkCount
        });
      });

      // Build tree structure
      folders.forEach(folder => {
        const node = folderMap.get(folder.id)!;
        if (folder.parentId) {
          const parent = folderMap.get(folder.parentId);
          if (parent) {
            parent.children.push(node);
          } else {
            rootFolders.push(node);
          }
        } else {
          rootFolders.push(node);
        }
      });

      // Sort folders alphabetically
      const sortFolders = (folders: FolderTreeNode[]) => {
        folders.sort((a, b) => a.name.localeCompare(b.name));
        folders.forEach(folder => sortFolders(folder.children));
      };

      sortFolders(rootFolders);
      return rootFolders;
    };

    setFolderTree(buildTree());
  }, [folders, bookmarks]);

  const toggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const startEditing = (folder: FolderTreeNode) => {
    setEditingFolder(folder.id);
    setEditingName(folder.name);
  };

  const saveEdit = async () => {
    if (editingFolder && editingName.trim()) {
      try {
        await onUpdateFolder(editingFolder, { name: editingName.trim() });
        setEditingFolder(null);
        setEditingName('');
      } catch (error) {
        console.error('Failed to update folder:', error);
      }
    }
  };

  const cancelEdit = () => {
    setEditingFolder(null);
    setEditingName('');
  };

  const createFolder = async () => {
    if (newFolderName.trim()) {
      try {
        await onCreateFolder(newFolderName.trim(), newFolderParent || undefined);
        setNewFolderName('');
        setNewFolderParent(null);
      } catch (error) {
        console.error('Failed to create folder:', error);
      }
    }
  };

  const deleteFolder = async (folder: FolderTreeNode) => {
    const hasChildren = folder.children.length > 0;
    const hasBookmarks = folder.bookmarkCount > 0;
    
    let message = `Are you sure you want to delete the folder "${folder.name}"?`;
    if (hasChildren) {
      message += ` This will also delete ${folder.children.length} subfolder(s).`;
    }
    if (hasBookmarks) {
      message += ` This folder contains ${folder.bookmarkCount} bookmark(s) that will be moved to "Uncategorized".`;
    }

    if (window.confirm(message)) {
      try {
        await onDeleteFolder(folder.id);
      } catch (error) {
        console.error('Failed to delete folder:', error);
      }
    }
  };

  const handleDragStart = (e: React.DragEvent, folderId: string) => {
    setDraggedFolder(folderId);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e: React.DragEvent, folderId: string) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
    setDropTarget(folderId);
  };

  const handleDragLeave = () => {
    setDropTarget(null);
  };

  const handleDrop = async (e: React.DragEvent, targetFolderId: string) => {
    e.preventDefault();
    setDropTarget(null);

    if (draggedFolder && draggedFolder !== targetFolderId) {
      // Prevent dropping a folder into its own descendant
      const isDescendant = (parentId: string, childId: string): boolean => {
        const folder = folders.find(f => f.id === childId);
        if (!folder) return false;
        if (folder.parentId === parentId) return true;
        if (folder.parentId) return isDescendant(parentId, folder.parentId);
        return false;
      };

      if (!isDescendant(draggedFolder, targetFolderId)) {
        try {
          await onMoveFolder(draggedFolder, targetFolderId);
        } catch (error) {
          console.error('Failed to move folder:', error);
        }
      }
    }

    setDraggedFolder(null);
  };

  const renderFolder = (folder: FolderTreeNode, depth: number = 0) => {
    const isExpanded = expandedFolders.has(folder.id);
    const isEditing = editingFolder === folder.id;
    const isDragTarget = dropTarget === folder.id;
    const isDragging = draggedFolder === folder.id;

    return (
      <div key={folder.id} className="select-none">
        <div
          className={`flex items-center py-2 px-3 rounded-lg transition-colors ${
            isDragTarget ? 'bg-blue-100 dark:bg-blue-900' : 'hover:bg-gray-50 dark:hover:bg-gray-700'
          } ${isDragging ? 'opacity-50' : ''}`}
          style={{ marginLeft: `${depth * 20}px` }}
          draggable={!isEditing}
          onDragStart={(e) => handleDragStart(e, folder.id)}
          onDragOver={(e) => handleDragOver(e, folder.id)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, folder.id)}
        >
          {/* Expand/Collapse Button */}
          <button
            onClick={() => toggleFolder(folder.id)}
            className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            {folder.children.length > 0 ? (
              <svg
                className={`w-4 h-4 transition-transform ${isExpanded ? 'rotate-90' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            ) : (
              <div className="w-4 h-4" />
            )}
          </button>

          {/* Folder Icon */}
          <div className="w-6 h-6 flex items-center justify-center mr-2">
            <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"
              />
            </svg>
          </div>

          {/* Folder Name */}
          <div className="flex-1 min-w-0">
            {isEditing ? (
              <input
                type="text"
                value={editingName}
                onChange={(e) => setEditingName(e.target.value)}
                onBlur={saveEdit}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') saveEdit();
                  if (e.key === 'Escape') cancelEdit();
                }}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
                autoFocus
              />
            ) : (
              <span className="text-sm font-medium text-gray-900 dark:text-white truncate">
                {folder.name}
              </span>
            )}
          </div>

          {/* Bookmark Count */}
          <span className="text-xs text-gray-500 dark:text-gray-400 mr-2">
            {folder.bookmarkCount}
          </span>

          {/* Actions */}
          {!isEditing && (
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setNewFolderParent(folder.id)}
                className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-green-600 dark:hover:text-green-400"
                title="Add subfolder"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </button>
              <button
                onClick={() => startEditing(folder)}
                className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-blue-600 dark:hover:text-blue-400"
                title="Rename folder"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              <button
                onClick={() => deleteFolder(folder)}
                className="w-6 h-6 flex items-center justify-center text-gray-400 hover:text-red-600 dark:hover:text-red-400"
                title="Delete folder"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>
          )}
        </div>

        {/* Children */}
        {isExpanded && folder.children.length > 0 && (
          <div>
            {folder.children.map(child => renderFolder(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Folder Management
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {/* New Folder Form */}
          <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
              {newFolderParent ? 'Create Subfolder' : 'Create New Folder'}
            </h3>
            
            {newFolderParent && (
              <div className="mb-3">
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Parent folder: <span className="font-medium">
                    {folders.find(f => f.id === newFolderParent)?.name || 'Unknown'}
                  </span>
                </span>
                <button
                  onClick={() => setNewFolderParent(null)}
                  className="ml-2 text-xs text-blue-600 dark:text-blue-400 hover:underline"
                >
                  Change
                </button>
              </div>
            )}
            
            <div className="flex space-x-2">
              <input
                type="text"
                value={newFolderName}
                onChange={(e) => setNewFolderName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && createFolder()}
                placeholder="Folder name..."
                className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:text-white"
              />
              <button
                onClick={createFolder}
                disabled={!newFolderName.trim()}
                className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Create
              </button>
              {newFolderParent && (
                <button
                  onClick={() => {
                    setNewFolderParent(null);
                    setNewFolderName('');
                  }}
                  className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>

          {/* Folder Tree */}
          <div className="space-y-1">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                Folder Structure
              </h3>
              <div className="text-sm text-gray-500 dark:text-gray-400">
                {folders.length} folder(s) • Drag to reorganize
              </div>
            </div>

            {folderTree.length === 0 ? (
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
                  </svg>
                </div>
                <p className="text-gray-500 dark:text-gray-400">
                  No folders created yet. Create your first folder above.
                </p>
              </div>
            ) : (
              <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                {folderTree.map(folder => renderFolder(folder))}
              </div>
            )}
          </div>

          {/* Instructions */}
          <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100 mb-2">
              Tips:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Drag and drop folders to reorganize the hierarchy</li>
              <li>• Click the + button next to a folder to create a subfolder</li>
              <li>• Use the edit button to rename folders</li>
              <li>• Deleting a folder moves its bookmarks to "Uncategorized"</li>
              <li>• Numbers next to folder names show bookmark counts</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FolderManagement;