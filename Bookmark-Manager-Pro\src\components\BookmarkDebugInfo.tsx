import React from 'react';
import { useBookmarks } from '../contexts/BookmarkContext';

const BookmarkDebugInfo: React.FC = () => {
  const {
    bookmarks,
    filteredBookmarks,
    collections,
    selectedCollection,
    filterType,
    searchQuery,
    selectedTags,
    selectedPlaylist
  } = useBookmarks();

  const debugInfo = {
    totalBookmarks: bookmarks.length,
    filteredBookmarks: filteredBookmarks.length,
    collections: collections.length,
    selectedCollection,
    filterType,
    searchQuery,
    selectedTags,
    selectedPlaylist,
    firstFewBookmarks: bookmarks.slice(0, 3).map((b: any) => ({
      title: b.title,
      collection: b.collection,
      url: b.url.substring(0, 50) + '...'
    })),
    firstFewFiltered: filteredBookmarks.slice(0, 3).map((b: any) => ({
      title: b.title,
      collection: b.collection,
      url: b.url.substring(0, 50) + '...'
    })),
    collectionsData: collections.map((c: any) => ({
      name: c.name,
      count: c.count
    }))
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      background: '#1a1a1a',
      color: '#fff',
      padding: '16px',
      borderRadius: '8px',
      border: '1px solid #444',
      fontSize: '12px',
      fontFamily: 'monospace',
      maxWidth: '400px',
      maxHeight: '80vh',
      overflow: 'auto',
      zIndex: 9999,
      boxShadow: '0 4px 12px rgba(0,0,0,0.5)'
    }}>
      <h3 style={{ margin: '0 0 12px 0', color: '#4ade80' }}>🐛 Bookmark Debug Info</h3>
      
      <div style={{ marginBottom: '12px' }}>
        <strong style={{ color: '#22d3ee' }}>Counts:</strong>
        <div>Total Bookmarks: {debugInfo.totalBookmarks}</div>
        <div>Filtered Bookmarks: {debugInfo.filteredBookmarks}</div>
        <div>Collections: {debugInfo.collections}</div>
      </div>

      <div style={{ marginBottom: '12px' }}>
        <strong style={{ color: '#22d3ee' }}>Filters:</strong>
        <div>Selected Collection: "{debugInfo.selectedCollection}"</div>
        <div>Filter Type: "{debugInfo.filterType}"</div>
        <div>Search Query: "{debugInfo.searchQuery}"</div>
        <div>Selected Tags: [{debugInfo.selectedTags.join(', ')}]</div>
        <div>Selected Playlist: {debugInfo.selectedPlaylist || 'none'}</div>
      </div>

      <div style={{ marginBottom: '12px' }}>
        <strong style={{ color: '#22d3ee' }}>Collections:</strong>
        {debugInfo.collectionsData.map((col: any, index: number) => (
          <div key={index} style={{ fontSize: '11px' }}>
            "{col.name}": {col.count} bookmarks
          </div>
        ))}
      </div>

      <div style={{ marginBottom: '12px' }}>
        <strong style={{ color: '#22d3ee' }}>Sample Bookmarks (All):</strong>
        {debugInfo.firstFewBookmarks.map((bookmark: any, index: number) => (
          <div key={index} style={{ fontSize: '11px', marginLeft: '8px' }}>
            {index + 1}. "{bookmark.title}" → "{bookmark.collection}"
          </div>
        ))}
      </div>

      <div>
        <strong style={{ color: '#22d3ee' }}>Sample Bookmarks (Filtered):</strong>
        {debugInfo.firstFewFiltered.length > 0 ? (
          debugInfo.firstFewFiltered.map((bookmark: any, index: number) => (
            <div key={index} style={{ fontSize: '11px', marginLeft: '8px' }}>
              {index + 1}. "{bookmark.title}" → "{bookmark.collection}"
            </div>
          ))
        ) : (
          <div style={{ fontSize: '11px', marginLeft: '8px', color: '#ef4444' }}>
            No filtered bookmarks found!
          </div>
        )}
      </div>
    </div>
  );
};

export default BookmarkDebugInfo;
