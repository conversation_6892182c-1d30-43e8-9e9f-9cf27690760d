import { useCallback, useState } from 'react'
import { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { summaryService } from '../services/summaryService'

interface SummaryGenerationState {
  isGenerating: boolean
  progress: number
  completed: number
  total: number
  errors: string[]
}

export const useSummaryGeneration = () => {
  const { bookmarks, updateBookmark } = useBookmarks()
  const [state, setState] = useState<SummaryGenerationState>({
    isGenerating: false,
    progress: 0,
    completed: 0,
    total: 0,
    errors: []
  })

  // Get bookmarks that need summaries
  const bookmarksNeedingSummaries = bookmarks.filter(
    bookmark => !bookmark.summary || bookmark.summary.trim() === ''
  )

  const generateSummariesForAll = useCallback(async () => {
    const bookmarksToProcess = bookmarksNeedingSummaries
    if (bookmarksToProcess.length === 0) return

    setState({
      isGenerating: true,
      progress: 0,
      completed: 0,
      total: bookmarksToProcess.length,
      errors: []
    })

    const errors: string[] = []
    let completed = 0
    const batchSize = 5 // Process in batches to reduce re-renders
    const summaryUpdates: Array<{ id: string; summary: string }> = []

    for (let i = 0; i < bookmarksToProcess.length; i += batchSize) {
      const batch = bookmarksToProcess.slice(i, i + batchSize)

      // Process batch concurrently
      const batchPromises = batch.map(async (bookmark) => {
        try {
          const result = await summaryService.generateSummary(bookmark)
          return {
            id: bookmark.id,
            summary: result.quickSummary,
            success: true
          }
        } catch (error) {
          const errorMessage = `Failed to generate summary for "${bookmark.title}": ${
            error instanceof Error ? error.message : 'Unknown error'
          }`
          errors.push(errorMessage)
          console.error(errorMessage)
          return {
            id: bookmark.id,
            summary: '',
            success: false
          }
        }
      })

      const batchResults = await Promise.all(batchPromises)

      // Batch update successful summaries
      const successfulUpdates = batchResults.filter(result => result.success)
      if (successfulUpdates.length > 0) {
        // Update all bookmarks in this batch at once
        successfulUpdates.forEach(result => {
          updateBookmark(result.id, { summary: result.summary })
        })
      }

      completed += batch.length
      setState(prev => ({
        ...prev,
        completed,
        progress: (completed / bookmarksToProcess.length) * 100
      }))

      // Small delay between batches to prevent overwhelming the system
      if (i + batchSize < bookmarksToProcess.length) {
        await new Promise(resolve => setTimeout(resolve, 200))
      }
    }

    setState(prev => ({
      ...prev,
      isGenerating: false,
      errors
    }))
  }, [bookmarksNeedingSummaries, updateBookmark])

  const generateSummaryForBookmark = useCallback(async (bookmark: Bookmark) => {
    try {
      const result = await summaryService.generateSummary(bookmark)
      
      updateBookmark(bookmark.id, {
        summary: result.quickSummary
      })

      return result
    } catch (error) {
      console.error(`Failed to generate summary for bookmark ${bookmark.id}:`, error)
      throw error
    }
  }, [updateBookmark])

  const clearSummaryCache = useCallback(() => {
    summaryService.clearCache()
  }, [])

  // Memory monitoring to prevent page reload issues
  React.useEffect(() => {
    if (!state.isGenerating) return;

    const memoryMonitor = setInterval(() => {
      // Check memory usage if available
      if ('memory' in performance) {
        const memInfo = (performance as any).memory;
        const usedMB = memInfo.usedJSHeapSize / 1024 / 1024;
        const limitMB = memInfo.jsHeapSizeLimit / 1024 / 1024;

        console.log(`🧠 Memory usage: ${usedMB.toFixed(1)}MB / ${limitMB.toFixed(1)}MB`);

        // Warning if memory usage is high
        if (usedMB > limitMB * 0.8) {
          console.warn('⚠️ High memory usage detected during summary generation');
          // Clear caches to free memory
          summaryService.clearCache();

          // Suggest garbage collection if available
          if ('gc' in window) {
            (window as any).gc();
          }
        }
      }
    }, 5000); // Check every 5 seconds

    return () => clearInterval(memoryMonitor);
  }, [state.isGenerating]);

  return {
    state,
    bookmarksNeedingSummaries,
    generateSummariesForAll,
    generateSummaryForBookmark,
    clearSummaryCache
  }
}

export default useSummaryGeneration
