import React, { forwardRef } from 'react';

export interface TextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'filled' | 'outlined';
  fullWidth?: boolean;
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
  autoResize?: boolean;
}

const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>((
  {
    label,
    error,
    helperText,
    size = 'md',
    variant = 'default',
    fullWidth = false,
    resize = 'vertical',
    autoResize = false,
    className = '',
    disabled,
    rows = 3,
    ...props
  },
  ref
) => {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'md':
        return 'px-3 py-2 text-sm';
      case 'lg':
        return 'px-4 py-3 text-base';
      default:
        return 'px-3 py-2 text-sm';
    }
  };

  const getVariantClasses = () => {
    const baseClasses = 'border rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500';
    
    switch (variant) {
      case 'filled':
        return `${baseClasses} bg-slate-100 dark:bg-slate-800 border-transparent focus:bg-white dark:focus:bg-slate-700`;
      case 'outlined':
        return `${baseClasses} bg-transparent border-2 border-slate-300 dark:border-slate-600 focus:border-primary-500`;
      default: // default
        return `${baseClasses} bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600 focus:border-primary-500`;
    }
  };

  const getResizeClasses = () => {
    switch (resize) {
      case 'none':
        return 'resize-none';
      case 'vertical':
        return 'resize-y';
      case 'horizontal':
        return 'resize-x';
      case 'both':
        return 'resize';
      default:
        return 'resize-y';
    }
  };

  const textareaClasses = `
    ${getSizeClasses()}
    ${getVariantClasses()}
    ${getResizeClasses()}
    ${fullWidth ? 'w-full' : ''}
    ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}
    ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
    text-slate-900 dark:text-slate-100
    placeholder-slate-500 dark:placeholder-slate-400
    ${className}
  `;

  const handleInput = (e: React.FormEvent<HTMLTextAreaElement>) => {
    if (autoResize) {
      const target = e.target as HTMLTextAreaElement;
      target.style.height = 'auto';
      target.style.height = `${target.scrollHeight}px`;
    }
    
    if (props.onInput) {
      props.onInput(e);
    }
  };

  return (
    <div className={fullWidth ? 'w-full' : ''}>
      {/* Label */}
      {label && (
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          {label}
        </label>
      )}

      {/* Textarea */}
      <textarea
        ref={ref}
        disabled={disabled}
        rows={rows}
        className={textareaClasses}
        onInput={handleInput}
        {...props}
      />

      {/* Helper Text or Error */}
      {(error || helperText) && (
        <p className={`mt-1 text-xs ${
          error ? 'text-red-600 dark:text-red-400' : 'text-slate-500 dark:text-slate-400'
        }`}>
          {error || helperText}
        </p>
      )}
    </div>
  );
});

Textarea.displayName = 'Textarea';

export default Textarea;