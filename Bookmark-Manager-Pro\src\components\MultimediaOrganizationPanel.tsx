import {
  CheckCircle,
  FileText,
  Headphones,
  Mic,
  Play,
  Settings,
  Video,
  X, Zap
} from 'lucide-react'
import React, { useEffect, useState } from 'react'
import type { Bookmark } from '../../types'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import {
  MultimediaPlaylist
} from '../services/multimedia/MultimediaPlaylistService'
import { useInfiniteLoopDetection } from '../utils/infiniteLoopDetector'
import { MultimediaPlaybackModal } from './MultimediaPlaybackModal'
// Import the established design system
import '../styles/optimized-panels.css'

interface MultimediaOrganizationPanelProps {
  isOpen: boolean
  onClose: () => void
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
}

export const MultimediaOrganizationPanel: React.FC<MultimediaOrganizationPanelProps> = ({
  isOpen,
  onClose,
  selectedBookmarks = [],
  collectionId,
  mindMapSelection = []
}) => {
  const { bookmarks } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // Monitor for infinite loops
  useInfiniteLoopDetection('MultimediaOrganizationPanel')

  // Load saved playlists from local storage (memoized to prevent infinite loops)
  const loadSavedPlaylists = React.useCallback(() => {
    try {
      const saved = localStorage.getItem('multimediaPlaylists')
      if (saved) {
        const playlists = JSON.parse(saved)
        setSavedPlaylists(playlists)
        console.log(`📂 Loaded ${playlists.length} saved playlists`)
      } else {
        setSavedPlaylists([])
      }
    } catch (error) {
      console.error('Failed to load saved playlists:', error)
      setSavedPlaylists([])
    }
  }, [])

  // State for organization options
  const [enableTTS, setEnableTTS] = useState<boolean>(true)
  const [enableAIEnhancement, setEnableAIEnhancement] = useState<boolean>(true)
  const [playlistName, setPlaylistName] = useState<string>('')
  const [playlistDescription, setPlaylistDescription] = useState<string>('')

  // State for organization process
  const [isOrganizing, setIsOrganizing] = useState<boolean>(false)
  const [organizationResults, setOrganizationResults] = useState<string[]>([])
  const [showResults, setShowResults] = useState<boolean>(false)
  const [currentPlaylist, setCurrentPlaylist] = useState<MultimediaPlaylist | null>(null)

  // State for playlist management
  const [savedPlaylists, setSavedPlaylists] = useState<MultimediaPlaylist[]>([])
  const [showPlaylistManager, setShowPlaylistManager] = useState<boolean>(false)

  // Playback modal state
  const [showPlaybackModal, setShowPlaybackModal] = useState<boolean>(false)
  const [playbackPlaylist, setPlaybackPlaylist] = useState<MultimediaPlaylist | null>(null)
  const [playbackInitialIndex, setPlaybackInitialIndex] = useState<number>(0)

  // Bookmark selection state
  const [selectionMode, setSelectionMode] = useState<'preview' | 'custom'>('preview')
  const [customSelectedBookmarksIds, setCustomSelectedBookmarksIds] = useState<string[]>([])
  const [showBookmarkSelector, setShowBookmarkSelector] = useState<boolean>(false)

  // State for multimedia content
  const [videoCount, setVideoCount] = useState<number>(0)
  const [audioCount, setAudioCount] = useState<number>(0)
  const [documentCount, setDocumentCount] = useState<number>(0)
  const [totalDuration, setTotalDuration] = useState<number>(0)



  // Initialize with default values (fixed infinite loop)
  useEffect(() => {
    if (!isOpen) return;

    // Set default playlist name based on selection
    if (collectionId) {
      setPlaylistName(`${collectionId} Playlist`)
      setPlaylistDescription(`Multimedia content from ${collectionId} collection`)
    } else if (selectedBookmarks && selectedBookmarks.length > 0) {
      setPlaylistName(`Selected Bookmarks Playlist`)
      setPlaylistDescription(`Playlist from ${selectedBookmarks.length} selected bookmarks`)
    } else {
      setPlaylistName(`All Bookmarks Playlist`)
      setPlaylistDescription(`Multimedia content from all bookmarks`)
    }
  }, [isOpen, collectionId, selectedBookmarks])

  // Get target bookmarks based on selection mode (memoized for performance)
  const getTargetBookmarks = React.useMemo((): Bookmark[] => {
    if (selectionMode === 'custom') {
      return bookmarks.filter(b => customSelectedBookmarksIds.includes(b.id))
    } else if (selectedBookmarks && selectedBookmarks.length > 0) {
      return selectedBookmarks
    } else if (collectionId) {
      return bookmarks.filter(b => b.collection === collectionId)
    } else if (mindMapSelection && mindMapSelection.length > 0) {
      return bookmarks.filter(b => mindMapSelection.includes(b.id))
    } else {
      return bookmarks
    }
  }, [selectionMode, customSelectedBookmarksIds, selectedBookmarks, collectionId, mindMapSelection, bookmarks])

  // Separate effect for analysis to prevent infinite loops
  useEffect(() => {
    if (!isOpen) return;

    const timeoutId = setTimeout(() => {
      analyzeMultimediaContent()
    }, 100)

    return () => clearTimeout(timeoutId)
  }, [isOpen, getTargetBookmarks])

  // Load saved playlists only once when component opens
  useEffect(() => {
    if (isOpen) {
      loadSavedPlaylists()
    }
  }, [isOpen, loadSavedPlaylists])

  // Analyze multimedia content in bookmarks (memoized to prevent infinite loops)
  const analyzeMultimediaContent = React.useCallback(() => {
    const targetBookmarks = getTargetBookmarks

    // Count by content type
    let videos = 0
    let audios = 0
    let docs = 0
    let duration = 0

    targetBookmarks.forEach(bookmark => {
      const url = bookmark.url.toLowerCase()
      const title = bookmark.title.toLowerCase()

      // Video content detection (more comprehensive)
      if (url.includes('youtube.com') || url.includes('youtu.be') ||
        url.includes('vimeo.com') || url.includes('dailymotion.com') ||
        url.includes('twitch.tv') || url.includes('netflix.com') ||
        url.includes('.mp4') || url.includes('.webm') || url.includes('.mov') ||
        url.includes('.avi') || url.includes('.mkv') ||
        title.includes('video') || title.includes('tutorial') ||
        title.includes('course') || title.includes('watch') ||
        title.includes('stream') || title.includes('movie')) {
        videos++
        duration += 300 // Estimate 5 minutes per video
      }
      // Audio content detection (more comprehensive)
      else if (url.includes('spotify.com') || url.includes('soundcloud.com') ||
        url.includes('apple.com/music') || url.includes('music.youtube.com') ||
        url.includes('.mp3') || url.includes('.wav') || url.includes('.ogg') ||
        url.includes('.flac') || url.includes('.aac') ||
        title.includes('podcast') || title.includes('music') ||
        title.includes('audio') || title.includes('song') ||
        title.includes('album') || title.includes('track')) {
        audios++
        duration += 180 // Estimate 3 minutes per audio
      }
      // Document content detection (more comprehensive)
      else {
        // Default to document for most content
        docs++
        duration += 600 // Estimate 10 minutes per document
      }
    })

    setVideoCount(videos)
    setAudioCount(audios)
    setDocumentCount(docs)
    setTotalDuration(duration)
  }, [getTargetBookmarks])

  // Toggle bookmark selection in custom mode
  const toggleBookmarkSelection = (bookmarkId: string) => {
    setCustomSelectedBookmarksIds(prev =>
      prev.includes(bookmarkId)
        ? prev.filter(id => id !== bookmarkId)
        : [...prev, bookmarkId]
    )
  }

  // Clear custom selection
  const clearCustomSelection = () => {
    setCustomSelectedBookmarksIds([])
  }

  // Group bookmarks by collection (memoized to prevent unnecessary recalculations)
  const getBookmarksByCollection = React.useMemo(() => {
    const grouped: { [key: string]: Bookmark[] } = {}

    bookmarks.slice(0, 50).forEach(bookmark => {
      const collection = bookmark.collection || 'Uncategorized'
      if (!grouped[collection]) {
        grouped[collection] = []
      }
      grouped[collection].push(bookmark)
    })

    return grouped
  }, [bookmarks])

  // Select all bookmarks from a collection
  const selectAllFromCollection = (collection: string) => {
    const collectionBookmarks = bookmarks.filter(b => (b.collection || 'Uncategorized') === collection)
    const collectionIds = collectionBookmarks.map(b => b.id)
    setCustomSelectedBookmarksIds(prev => [...new Set([...prev, ...collectionIds])])
  }

  // Deselect all bookmarks from a collection
  const deselectAllFromCollection = (collection: string) => {
    const collectionBookmarks = bookmarks.filter(b => (b.collection || 'Uncategorized') === collection)
    const collectionIds = collectionBookmarks.map(b => b.id)
    setCustomSelectedBookmarksIds(prev => prev.filter(id => !collectionIds.includes(id)))
  }

  // Generate preview of organization
  const generatePreview = async () => {
    try {
      setIsOrganizing(true)
      setShowResults(true)

      const targetBookmarks = getTargetBookmarks
      const results: string[] = []

      results.push(`🔍 Analyzing ${targetBookmarks.length} bookmarks for multimedia content...`)
      setOrganizationResults([...results])

      // Simulate analysis delay
      await new Promise(resolve => setTimeout(resolve, 800))

      results.push(`📊 Analysis complete: Found ${videoCount} videos, ${audioCount} audio files, and ${documentCount} documents`)
      setOrganizationResults([...results])

      // Simulate organization delay
      await new Promise(resolve => setTimeout(resolve, 600))

      results.push(`🎬 Creating multimedia playlist: "${playlistName}"`)
      results.push(`⚙️ Settings: ${enableTTS ? 'TTS Enabled' : 'TTS Disabled'}, ${enableAIEnhancement ? 'AI Enhancement Enabled' : 'AI Enhancement Disabled'}`)
      setOrganizationResults([...results])

      // Simulate playlist creation delay
      await new Promise(resolve => setTimeout(resolve, 1000))

      results.push(`✅ Playlist created successfully!`)
      results.push(`📋 Total items: ${videoCount + audioCount + documentCount}`)
      results.push(`⏱️ Estimated duration: ${Math.floor(totalDuration / 60)} minutes`)
      results.push(`🎯 Click "Create Multimedia Playlist" to apply changes`)

      setOrganizationResults(results)
    } catch (error) {
      console.error('Preview generation failed:', error)
      setOrganizationResults(['❌ Preview generation failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  // Create multimedia playlist
  const createMultimediaPlaylist = async () => {
    try {
      setIsOrganizing(true)

      const targetBookmarks = getTargetBookmarks
      const results = [...organizationResults]

      results.push(`🎬 Creating multimedia playlist...`)
      setOrganizationResults([...results])

      // Create playlist items from bookmarks
      const playlistItems = targetBookmarks.map((bookmark, index) => {
        const url = bookmark.url.toLowerCase()
        const title = bookmark.title.toLowerCase()

        let type: 'video' | 'audio' | 'document' = 'document'
        let duration = 600 // Default 10 minutes for documents

        // Determine content type (same logic as analysis)
        if (url.includes('youtube.com') || url.includes('youtu.be') ||
          url.includes('vimeo.com') || url.includes('dailymotion.com') ||
          url.includes('twitch.tv') || url.includes('netflix.com') ||
          url.includes('.mp4') || url.includes('.webm') || url.includes('.mov') ||
          url.includes('.avi') || url.includes('.mkv') ||
          title.includes('video') || title.includes('tutorial') ||
          title.includes('course') || title.includes('watch') ||
          title.includes('stream') || title.includes('movie')) {
          type = 'video'
          duration = 300 // 5 minutes for videos
        } else if (url.includes('spotify.com') || url.includes('soundcloud.com') ||
          url.includes('apple.com/music') || url.includes('music.youtube.com') ||
          url.includes('.mp3') || url.includes('.wav') || url.includes('.ogg') ||
          url.includes('.flac') || url.includes('.aac') ||
          title.includes('podcast') || title.includes('music') ||
          title.includes('audio') || title.includes('song') ||
          title.includes('album') || title.includes('track')) {
          type = 'audio'
          duration = 180 // 3 minutes for audio
        }

        return {
          id: `item-${bookmark.id}-${Date.now()}-${index}`,
          bookmarkId: bookmark.id,
          title: bookmark.title,
          url: bookmark.url,
          type,
          duration,
          position: index,
          status: 'pending' as const
        }
      })

      // Create the playlist object
      const playlist: MultimediaPlaylist = {
        id: Date.now().toString(),
        name: playlistName,
        description: playlistDescription,
        color: '#3b82f6',
        bookmarkIds: targetBookmarks.map(b => b.id),
        dateCreated: new Date().toISOString(),
        items: playlistItems,
        totalDuration: playlistItems.reduce((sum, item) => sum + (item.duration || 0), 0),
        playbackSettings: {
          autoPlay: false,
          shuffle: false,
          repeat: 'none',
          playbackSpeed: 1.0,
          volume: 80,
          textToSpeechEnabled: enableTTS,
          textToSpeechSpeed: 1.0
        },
        completedItems: []
      }

      setCurrentPlaylist(playlist)

      results.push(`✅ Playlist "${playlistName}" created with ${playlist.items.length} items`)
      results.push(`📊 Content breakdown: ${videoCount} videos, ${audioCount} audio files, ${documentCount} documents`)
      results.push(`⏱️ Total duration: ${Math.floor((playlist.totalDuration || 0) / 60)} minutes`)

      setOrganizationResults(results)
    } catch (error) {
      console.error('Playlist creation failed:', error)
      setOrganizationResults([...organizationResults, '❌ Playlist creation failed. Please try again.'])
    } finally {
      setIsOrganizing(false)
    }
  }

  // Format time in minutes and seconds
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  // Save playlist to local storage
  const savePlaylist = (playlist: MultimediaPlaylist) => {
    try {
      // Check if playlist already exists (update vs create)
      const existingIndex = savedPlaylists.findIndex(p => p.id === playlist.id)
      let updatedPlaylists: MultimediaPlaylist[]

      if (existingIndex >= 0) {
        // Update existing playlist
        updatedPlaylists = [...savedPlaylists]
        updatedPlaylists[existingIndex] = playlist
        console.log(`📝 Updated playlist: ${playlist.name}`)
      } else {
        // Add new playlist
        updatedPlaylists = [...savedPlaylists, playlist]
        console.log(`💾 Saved new playlist: ${playlist.name}`)
      }

      setSavedPlaylists(updatedPlaylists)
      localStorage.setItem('multimediaPlaylists', JSON.stringify(updatedPlaylists))

      // Force refresh of the playlist manager if it's open
      if (showPlaylistManager) {
        // Trigger a re-render by toggling and immediately setting back
        setShowPlaylistManager(false)
        setTimeout(() => setShowPlaylistManager(true), 10)
      }
    } catch (error) {
      console.error('Failed to save playlist:', error)
    }
  }



  // Delete playlist
  const deletePlaylist = (playlistId: string) => {
    try {
      const playlistToDelete = savedPlaylists.find(p => p.id === playlistId)
      const updatedPlaylists = savedPlaylists.filter(p => p.id !== playlistId)
      setSavedPlaylists(updatedPlaylists)
      localStorage.setItem('multimediaPlaylists', JSON.stringify(updatedPlaylists))
      console.log(`🗑️ Deleted playlist: ${playlistToDelete?.name || playlistId}`)
    } catch (error) {
      console.error('Failed to delete playlist:', error)
    }
  }

  // Refresh saved playlists (force reload from localStorage)
  const refreshSavedPlaylists = () => {
    loadSavedPlaylists()
  }



  // Load saved playlists on component mount
  useEffect(() => {
    loadSavedPlaylists()
  }, [])

  if (!isOpen) return null

  return (
    <div className={`import-panel ${isModernTheme ? 'modern-enhanced' : ''}`}>
      <div className="import-header">
        <h2 className="import-title">🎬 Multimedia Organization</h2>
        <button onClick={onClose} className="close-btn" aria-label="Close multimedia organization panel">
          <X size={20} />
        </button>
      </div>

      <div className="import-content" style={{
        maxHeight: 'calc(85vh - 120px)',
        overflowY: 'auto',
        overflowX: 'hidden',
        scrollBehavior: 'smooth'
      }}>
        {/* Strategy Description */}
        <div className="import-section">
          <h3 className="section-title">
            <Video size={16} />
            🎬 Multimedia Organization
          </h3>
          <p className="section-description">
            Organize your bookmarks into multimedia playlists based on content type.
            Automatically detects videos, audio, and documents to create structured playlists.
          </p>

          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <Video size={16} className="text-white" />
              <span>Video content</span>
              <small>YouTube, Vimeo, and other video platforms</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <Headphones size={16} className="text-white" />
              <span>Audio content</span>
              <small>Spotify, SoundCloud, and podcast links</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <FileText size={16} className="text-white" />
              <span>Document content</span>
              <small>Articles, PDFs, and documentation</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--tertiary-bg)' }}>
              <Zap size={16} className="text-orange-500" />
              <span>AI-enhanced playlists</span>
              <small>Smart organization with content analysis</small>
            </div>
          </div>
        </div>

        {/* Bookmark Selection */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            📋 Bookmark Selection
          </h3>
          <p className="section-description">
            Choose which bookmarks to include in your multimedia playlist.
          </p>

          <div className="selection-mode-options">
            <label className={`selection-mode-option ${selectionMode === 'preview' ? 'active' : ''}`}>
              <input
                type="radio"
                name="selectionMode"
                checked={selectionMode === 'preview'}
                onChange={() => setSelectionMode('preview')}
                className="selection-radio"
              />
              <div className="selection-content">
                <div className="selection-header">
                  <div className="selection-icon">🔍</div>
                  <h4 className="selection-title">Preview Mode</h4>
                </div>
                <p className="selection-description">
                  Use current selection or all bookmarks for preview
                </p>
              </div>
            </label>

            <label className={`selection-mode-option ${selectionMode === 'custom' ? 'active' : ''}`}>
              <input
                type="radio"
                name="selectionMode"
                checked={selectionMode === 'custom'}
                onChange={() => setSelectionMode('custom')}
                className="selection-radio"
              />
              <div className="selection-content">
                <div className="selection-header">
                  <div className="selection-icon">✏️</div>
                  <h4 className="selection-title">Custom Selection</h4>
                </div>
                <p className="selection-description">
                  Manually choose specific bookmarks for the playlist
                </p>
              </div>
            </label>
          </div>

          {selectionMode === 'custom' && (
            <div className="mt-3">
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">
                  Selected: {customSelectedBookmarksIds.length} bookmarks
                </span>
                <div className="space-x-2">
                  <button
                    onClick={() => setShowBookmarkSelector(!showBookmarkSelector)}
                    className="btn-compact secondary"
                  >
                    {showBookmarkSelector ? 'Hide' : 'Show'} Bookmarks
                  </button>
                  {customSelectedBookmarksIds.length > 0 && (
                    <button
                      onClick={clearCustomSelection}
                      className="btn-compact secondary"
                    >
                      Clear Selection
                    </button>
                  )}
                </div>
              </div>

              {showBookmarkSelector && (
                <div className="enhanced-bookmark-selector">
                  <div className="bookmark-list">
                    {Object.entries(getBookmarksByCollection).map(([collection, collectionBookmarks]) => {
                      const selectedInCollection = collectionBookmarks.filter(b => customSelectedBookmarksIds.includes(b.id)).length
                      const totalInCollection = collectionBookmarks.length
                      const allSelected = selectedInCollection === totalInCollection
                      const someSelected = selectedInCollection > 0 && selectedInCollection < totalInCollection

                      return (
                        <div key={collection} className="collection-group">
                          <div className="collection-header">
                            <div className="collection-info">
                              <span className="collection-icon">📁</span>
                              <span className="collection-name">{collection}</span>
                              <span className="collection-count">({totalInCollection} bookmarks)</span>
                            </div>
                            <div className="collection-actions">
                              <button
                                onClick={() => allSelected ? deselectAllFromCollection(collection) : selectAllFromCollection(collection)}
                                className={`collection-select-btn ${allSelected ? 'selected' : someSelected ? 'partial' : ''}`}
                                title={allSelected ? 'Deselect all' : 'Select all'}
                              >
                                {allSelected ? '✓ All' : someSelected ? '◐ Some' : '○ None'}
                              </button>
                            </div>
                          </div>

                          <div className="collection-bookmarks">
                            {collectionBookmarks.map(bookmark => (
                              <label
                                key={bookmark.id}
                                className={`bookmark-item ${customSelectedBookmarksIds.includes(bookmark.id) ? 'selected' : ''}`}
                              >
                                <input
                                  type="checkbox"
                                  checked={customSelectedBookmarksIds.includes(bookmark.id)}
                                  onChange={() => toggleBookmarkSelection(bookmark.id)}
                                  className="bookmark-checkbox"
                                />
                                <div className="bookmark-content">
                                  <div className="bookmark-title">{bookmark.title}</div>
                                  <div className="bookmark-url">{bookmark.url}</div>
                                </div>
                                <div className="bookmark-indicator">
                                  {customSelectedBookmarksIds.includes(bookmark.id) && (
                                    <span className="selected-indicator">✓</span>
                                  )}
                                </div>
                              </label>
                            ))}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                  {bookmarks.length > 50 && (
                    <div className="multimedia-bookmark-limit-notice">
                      Showing first 50 bookmarks. Use search to find specific bookmarks.
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>

        {/* Content Analysis */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            📊 Content Analysis
          </h3>
          <p className="section-description">
            Analysis of multimedia content in your {selectionMode === 'custom' ? 'selected' : 'target'} bookmarks.
          </p>

          <div className="format-options">
            <div className="format-option" style={{ cursor: 'default', background: 'var(--primary-bg)' }}>
              <Video size={16} className="text-blue-500" />
              <span>{videoCount} Videos</span>
              <small>Estimated {Math.floor(videoCount * 5)} minutes</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--primary-bg)' }}>
              <Headphones size={16} className="text-green-500" />
              <span>{audioCount} Audio Files</span>
              <small>Estimated {Math.floor(audioCount * 3)} minutes</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--primary-bg)' }}>
              <FileText size={16} className="text-purple-500" />
              <span>{documentCount} Documents</span>
              <small>Estimated {Math.floor(documentCount * 10)} minutes</small>
            </div>
            <div className="format-option" style={{ cursor: 'default', background: 'var(--primary-bg)' }}>
              <Play size={16} className="text-orange-500" />
              <span>Total Duration</span>
              <small>{Math.floor(totalDuration / 60)} minutes</small>
            </div>
          </div>
        </div>

        {/* Playlist Configuration */}
        <div className="import-section">
          <h3 className="section-title">
            <Settings size={16} />
            🎛️ Playlist Configuration
          </h3>
          <p className="section-description">
            Configure your multimedia playlist settings and options.
          </p>

          <div className="format-options">
            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={enableTTS}
                onChange={(e) => setEnableTTS(e.target.checked)}
                className="mr-3"
              />
              <Mic size={16} className={enableTTS ? "text-green-500" : "text-gray-400"} />
              <span>Text-to-Speech</span>
              <small>Enable TTS for documents and articles</small>
            </label>
            <label className="format-option" style={{ cursor: 'pointer' }}>
              <input
                type="checkbox"
                checked={enableAIEnhancement}
                onChange={(e) => setEnableAIEnhancement(e.target.checked)}
                className="mr-3"
              />
              <Zap size={16} className={enableAIEnhancement ? "text-blue-500" : "text-gray-400"} />
              <span>AI Enhancement</span>
              <small>Smart content analysis and summaries</small>
            </label>
          </div>

          <div className="mt-4 space-y-3">
            <div>
              <label className="block text-sm font-medium mb-1">Playlist Name</label>
              <input
                type="text"
                value={playlistName}
                onChange={(e) => setPlaylistName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter playlist name"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Description</label>
              <textarea
                value={playlistDescription}
                onChange={(e) => setPlaylistDescription(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={3}
                placeholder="Enter playlist description"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="import-section">
          <h3 className="section-title">
            <Zap size={16} />
            🚀 Actions
          </h3>
          <p className="multimedia-section__description">
            {selectionMode === 'custom' && customSelectedBookmarksIds.length === 0
              ? 'Select bookmarks above to enable playlist creation.'
              : 'Generate a preview or create your multimedia playlist.'
            }
          </p>

          <div className="multimedia-grid">
            <button
              onClick={generatePreview}
              disabled={isOrganizing || (selectionMode === 'custom' && customSelectedBookmarksIds.length === 0)}
              className={`multimedia-btn ${isOrganizing || (selectionMode === 'custom' && customSelectedBookmarksIds.length === 0) ? 'multimedia-btn--disabled' : 'multimedia-btn--secondary'}`}
            >
              {isOrganizing ? (
                <>
                  <div className="inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
                  Analyzing...
                </>
              ) : (
                <>
                  <Settings size={16} className="inline mr-2" />
                  Preview
                </>
              )}
            </button>

            {showResults && !isOrganizing && (
              <button
                onClick={createMultimediaPlaylist}
                disabled={!playlistName.trim()}
                className={`multimedia-btn ${!playlistName.trim() ? 'multimedia-btn--disabled' : 'multimedia-btn--primary'}`}
              >
                <Play size={16} className="inline mr-2" />
                Create Playlist
              </button>
            )}
          </div>

          {selectionMode === 'custom' && (
            <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="text-sm">
                <strong>Custom Selection Mode:</strong> You have selected {customSelectedBookmarksIds.length} bookmarks.
                {customSelectedBookmarksIds.length === 0 && (
                  <span className="text-blue-600"> Please select bookmarks above to continue.</span>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Results Display */}
        {showResults && (
          <div className="multimedia-section">
            <div className="multimedia-section__header">
              <CheckCircle className="multimedia-section__icon" size={16} />
              <h3 className="multimedia-section__title">📋 Organization Results</h3>
            </div>
            <div className="multimedia-results-container">
              {organizationResults.map((result, index) => (
                <div key={index} className="multimedia-result-item">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Created Playlist Display */}
        {currentPlaylist && (
          <div className="multimedia-section">
            <div className="multimedia-section__header">
              <Play className="multimedia-section__icon" size={16} />
              <h3 className="multimedia-section__title">🎬 Created Playlist</h3>
            </div>
            <div className="multimedia-playlist-info">
              <h4 className="font-semibold text-lg">{currentPlaylist.name}</h4>
              <p className="text-gray-600 mb-3">{currentPlaylist.description}</p>

              <div className="grid grid-cols-2 gap-4 text-sm mb-4">
                <div>
                  <span className="font-medium">Items:</span> {currentPlaylist.items.length}
                </div>
                <div>
                  <span className="font-medium">Duration:</span> {formatTime(currentPlaylist.totalDuration || 0)}
                </div>
                <div>
                  <span className="font-medium">Created:</span> {new Date(currentPlaylist.dateCreated).toLocaleDateString()}
                </div>
                <div>
                  <span className="font-medium">Type:</span> Multimedia
                </div>
              </div>

              <div className="multimedia-playlist-items">
                <h5 className="font-medium mb-2">Playlist Items</h5>
                <div className="max-h-60 overflow-y-auto">
                  {currentPlaylist.items.map((item, index) => (
                    <div key={item.id} className="p-2 border-b border-gray-200">
                      <div className="flex items-center">
                        <span className="mr-2">{index + 1}.</span>
                        <span className="mr-2">
                          {item.type === 'video' ? '🎬' :
                            item.type === 'audio' ? '🎵' :
                              item.type === 'document' ? '📄' : '🔗'}
                        </span>
                        <span className="flex-1 truncate">{item.title}</span>
                        <span className="text-sm text-gray-500">{formatTime(item.duration || 0)}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Playlist Actions */}
        {currentPlaylist && (
          <div className="multimedia-section">
            <div className="multimedia-section__header">
              <Play className="multimedia-section__icon" size={16} />
              <h3 className="multimedia-section__title">🎬 Playlist Actions</h3>
            </div>
            <div className="space-y-3">
              <button
                onClick={() => {
                  setPlaybackPlaylist(currentPlaylist);
                  setPlaybackInitialIndex(0);
                  setShowPlaybackModal(true);
                }}
                className="multimedia-btn multimedia-btn--primary w-full flex items-center justify-center"
              >
                <Play size={16} className="mr-2" />
                Play Now
              </button>
              <button
                onClick={() => savePlaylist(currentPlaylist)}
                className="multimedia-btn multimedia-btn--secondary w-full"
              >
                💾 Save Playlist
              </button>
            </div>
          </div>
        )}

        {/* Saved Playlists */}
        <div className="multimedia-section">
          <div className="multimedia-section__header">
            <Settings className="multimedia-section__icon" size={16} />
            <h3 className="multimedia-section__title">📋 Saved Playlists</h3>
          </div>
          <p className="multimedia-section__description">
            Manage your saved multimedia playlists.
          </p>

          <div className="multimedia-grid mb-4">
            <button
              onClick={() => {
                refreshSavedPlaylists();
                setShowPlaylistManager(!showPlaylistManager);
              }}
              className="multimedia-btn multimedia-btn--secondary"
            >
              {showPlaylistManager ? 'Hide Playlists' : 'Show Playlists'}
            </button>
            <button
              onClick={refreshSavedPlaylists}
              className="multimedia-btn multimedia-btn--secondary"
              title="Refresh saved playlists"
            >
              🔄 Refresh
            </button>
          </div>

          {showPlaylistManager && (
            <div className="multimedia-playlist-manager">
              {savedPlaylists.length === 0 ? (
                <div className="text-center py-4 text-gray-500">
                  No saved playlists. Create and save a playlist to see it here.
                </div>
              ) : (
                <div className="space-y-3">
                  {savedPlaylists.map(playlist => (
                    <div key={playlist.id} className="multimedia-saved-playlist-item">
                      <div className="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                        <div>
                          <h4 className="font-medium">{playlist.name}</h4>
                          <p className="text-sm text-gray-500">{playlist.items.length} items • {formatTime(playlist.totalDuration || 0)}</p>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => {
                              setPlaybackPlaylist(playlist);
                              setPlaybackInitialIndex(0);
                              setShowPlaybackModal(true);
                            }}
                            className="multimedia-btn multimedia-btn--primary multimedia-btn--sm"
                            title="Play playlist"
                          >
                            <Play size={16} />
                          </button>
                          <button
                            onClick={() => deletePlaylist(playlist.id)}
                            className="multimedia-btn multimedia-btn--danger multimedia-btn--sm"
                            title="Delete playlist"
                          >
                            <X size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Playback Modal */}
      {showPlaybackModal && playbackPlaylist && (
        <MultimediaPlaybackModal
          playlist={playbackPlaylist}
          initialItemIndex={playbackInitialIndex}
          isOpen={showPlaybackModal}
          onClose={() => setShowPlaybackModal(false)}
        />
      )}
    </div>
  )
}
