import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'

export type ThemeMode = 'classic' | 'modern'
export type ColorScheme = 'light' | 'dark' | 'auto'

interface ModernThemeContextType {
  themeMode: ThemeMode
  colorScheme: ColorScheme
  isDarkMode: boolean
  setThemeMode: (mode: ThemeMode) => void
  setColorScheme: (scheme: ColorScheme) => void
  toggleTheme: () => void
  toggleColorScheme: () => void
}

const ModernThemeContext = createContext<ModernThemeContextType | undefined>(undefined)

interface ModernThemeProviderProps {
  children: ReactNode
}

export const ModernThemeProvider: React.FC<ModernThemeProviderProps> = ({ children }) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(() => {
    const saved = localStorage.getItem('bookmark-studio-theme-mode')
    return (saved as ThemeMode) || 'classic'
  })

  const [colorScheme, setColorScheme] = useState<ColorScheme>(() => {
    const saved = localStorage.getItem('bookmark-studio-color-scheme')
    return (saved as ColorScheme) || 'light'
  })

  const [isDarkMode, setIsDarkMode] = useState(false)

  // Determine if dark mode should be active
  useEffect(() => {
    const updateDarkMode = () => {
      if (colorScheme === 'auto') {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
        setIsDarkMode(mediaQuery.matches)
      } else {
        setIsDarkMode(colorScheme === 'dark')
      }
    }

    updateDarkMode()

    if (colorScheme === 'auto') {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
      const handler = (e: MediaQueryListEvent) => setIsDarkMode(e.matches)
      mediaQuery.addEventListener('change', handler)
      return () => mediaQuery.removeEventListener('change', handler)
    }
  }, [colorScheme])

  // Apply theme classes to document
  useEffect(() => {
    const root = document.documentElement
    
    // Remove existing theme classes
    root.classList.remove('theme-classic', 'theme-modern', 'dark', 'light')
    
    // Apply theme mode class
    root.classList.add(`theme-${themeMode}`)
    
    // Apply color scheme class
    if (isDarkMode) {
      root.classList.add('dark')
    } else {
      root.classList.add('light')
    }

    // Save to localStorage
    localStorage.setItem('bookmark-studio-theme-mode', themeMode)
    localStorage.setItem('bookmark-studio-color-scheme', colorScheme)

    // Log theme change for debugging
    console.log(`🎨 Theme applied: ${themeMode} (${isDarkMode ? 'dark' : 'light'})`)
  }, [themeMode, isDarkMode, colorScheme])

  const toggleTheme = () => {
    setThemeMode(prev => prev === 'classic' ? 'modern' : 'classic')
  }

  const toggleColorScheme = () => {
    setColorScheme(prev => {
      switch (prev) {
        case 'light': return 'dark'
        case 'dark': return 'auto'
        case 'auto': return 'light'
        default: return 'light'
      }
    })
  }

  const value: ModernThemeContextType = {
    themeMode,
    colorScheme,
    isDarkMode,
    setThemeMode,
    setColorScheme,
    toggleTheme,
    toggleColorScheme
  }

  return (
    <ModernThemeContext.Provider value={value}>
      {children}
    </ModernThemeContext.Provider>
  )
}

export const useModernTheme = (): ModernThemeContextType => {
  const context = useContext(ModernThemeContext)
  if (context === undefined) {
    throw new Error('useModernTheme must be used within a ModernThemeProvider')
  }
  return context
}

// Theme configuration objects
export const THEME_CONFIGS = {
  classic: {
    name: 'Classic',
    description: 'Original Bookmark Studio design',
    preview: '/theme-previews/classic.png'
  },
  modern: {
    name: 'Modern',
    description: 'Sleek, contemporary design with enhanced panels',
    preview: '/theme-previews/modern.png'
  }
} as const

export const COLOR_SCHEME_CONFIGS = {
  light: {
    name: 'Light',
    description: 'Light color scheme',
    icon: '☀️'
  },
  dark: {
    name: 'Dark', 
    description: 'Dark color scheme',
    icon: '🌙'
  },
  auto: {
    name: 'Auto',
    description: 'Follow system preference',
    icon: '🔄'
  }
} as const

// Utility functions for theme management
export const getThemeClasses = (themeMode: ThemeMode, isDarkMode: boolean): string => {
  const classes = [`theme-${themeMode}`]
  if (isDarkMode) classes.push('dark')
  else classes.push('light')
  return classes.join(' ')
}

export const isModernTheme = (themeMode: ThemeMode): boolean => {
  return themeMode === 'modern'
}

export const getThemeTransition = (): string => {
  return 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
}
