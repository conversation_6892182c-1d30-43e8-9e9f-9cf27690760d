/**
 * SILENT CLEANUP INDICATOR
 * Shows a subtle notification when automatic cleanup is running
 */

import { CheckCircle, Shield } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { intervalManager } from '../utils/intervalManager'

interface CleanupStatus {
  isRunning: boolean
  lastCleanup: number
  currentMemoryUsage: number | null
}

export const SilentCleanupIndicator: React.FC = () => {
  const [cleanupStatus, setCleanupStatus] = useState<CleanupStatus>({
    isRunning: false,
    lastCleanup: 0,
    currentMemoryUsage: null
  })
  const [showNotification, setShowNotification] = useState(false)
  const [lastNotificationTime, setLastNotificationTime] = useState(0)
  // Use imported intervalManager instance

  useEffect(() => {
    // Check cleanup status every 5 seconds
    const intervalId = intervalManager.createInterval(() => {
      if ((window as any).silentEmergencyCleanup) {
        const status = (window as any).silentEmergencyCleanup.getStatus()

        // Show notification when cleanup starts
        if (status.isRunning && !cleanupStatus.isRunning) {
          const now = Date.now()
          // Only show notification if it's been more than 2 minutes since last one
          if (now - lastNotificationTime > 120000) {
            setShowNotification(true)
            setLastNotificationTime(now)

            // Hide notification after 3 seconds
            intervalManager.createTimeout(() => {
              setShowNotification(false)
            }, 3000, 'cleanup-notification-hide')
          }
        }

        setCleanupStatus(status)
      }
    }, 5000, 'silent-cleanup-monitor')

    return () => intervalManager.clearInterval(intervalId)
  }, [cleanupStatus.isRunning, lastNotificationTime, intervalManager])

  if (!showNotification) return null

  return (
    <div className="silent-cleanup-indicator">
      <div className="cleanup-notification">
        <div className="notification-icon">
          <Shield size={20} className="shield-icon" />
        </div>
        <div className="notification-content">
          <div className="notification-title">Memory Optimization</div>
          <div className="notification-message">
            Automatic cleanup running silently...
          </div>
        </div>
        <div className="notification-progress">
          <div className="progress-bar">
            <div className="progress-fill"></div>
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .silent-cleanup-indicator {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 9999;
          pointer-events: none;
        }
        
        .cleanup-notification {
          background: linear-gradient(135deg, #059669, #10b981);
          color: white;
          padding: 12px 16px;
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
          display: flex;
          align-items: center;
          gap: 12px;
          min-width: 280px;
          animation: slideIn 0.3s ease-out;
        }
        
        .notification-icon {
          flex-shrink: 0;
        }
        
        .shield-icon {
          animation: pulse 2s infinite;
        }
        
        .notification-content {
          flex: 1;
        }
        
        .notification-title {
          font-weight: 600;
          font-size: 14px;
          margin-bottom: 2px;
        }
        
        .notification-message {
          font-size: 12px;
          opacity: 0.9;
        }
        
        .notification-progress {
          flex-shrink: 0;
          width: 40px;
        }
        
        .progress-bar {
          width: 100%;
          height: 3px;
          background: rgba(255, 255, 255, 0.3);
          border-radius: 2px;
          overflow: hidden;
        }
        
        .progress-fill {
          height: 100%;
          background: white;
          border-radius: 2px;
          animation: progress 3s ease-in-out;
        }
        
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes pulse {
          0%, 100% {
            opacity: 1;
          }
          50% {
            opacity: 0.7;
          }
        }
        
        @keyframes progress {
          from {
            width: 0%;
          }
          to {
            width: 100%;
          }
        }
      `}</style>
    </div>
  )
}

// Success notification component
export const CleanupSuccessIndicator: React.FC<{ 
  show: boolean
  memoryBefore: number
  memoryAfter: number
  onHide: () => void
}> = ({ show, memoryBefore, memoryAfter, onHide }) => {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(onHide, 4000)
      return () => clearTimeout(timer)
    }
  }, [show, onHide])

  if (!show) return null

  const memoryFreed = memoryBefore - memoryAfter

  return (
    <div className="cleanup-success-indicator">
      <div className="success-notification">
        <div className="success-icon">
          <CheckCircle size={20} />
        </div>
        <div className="success-content">
          <div className="success-title">Memory Optimized</div>
          <div className="success-message">
            {memoryBefore.toFixed(1)}% → {memoryAfter.toFixed(1)}% 
            ({memoryFreed.toFixed(1)}% freed)
          </div>
        </div>
      </div>
      
      <style jsx>{`
        .cleanup-success-indicator {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 9999;
          pointer-events: none;
        }
        
        .success-notification {
          background: linear-gradient(135deg, #059669, #10b981);
          color: white;
          padding: 12px 16px;
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(5, 150, 105, 0.3);
          display: flex;
          align-items: center;
          gap: 12px;
          min-width: 280px;
          animation: slideIn 0.3s ease-out, fadeOut 0.3s ease-out 3.7s forwards;
        }
        
        .success-icon {
          flex-shrink: 0;
          animation: checkmark 0.5s ease-out;
        }
        
        .success-content {
          flex: 1;
        }
        
        .success-title {
          font-weight: 600;
          font-size: 14px;
          margin-bottom: 2px;
        }
        
        .success-message {
          font-size: 12px;
          opacity: 0.9;
          font-family: monospace;
        }
        
        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }
        
        @keyframes fadeOut {
          from {
            opacity: 1;
          }
          to {
            opacity: 0;
            transform: translateX(100%);
          }
        }
        
        @keyframes checkmark {
          0% {
            transform: scale(0);
          }
          50% {
            transform: scale(1.2);
          }
          100% {
            transform: scale(1);
          }
        }
      `}</style>
    </div>
  )
}
