// Performance Monitor for Vibe Testing
// Monitors performance metrics that affect emotional responses

export class PerformanceMonitor {
  
  static async initialize() {
    this.metrics = {
      responseTimeThresholds: {
        star: 50,        // Star/unstar operations
        filter: 100,     // Favorites filtering
        search: 100,     // Search operations
        bulk: 1000,      // Bulk operations
        load: 500        // Page/view loading
      },
      memoryThresholds: {
        baseline: 50,    // MB - baseline memory usage
        warning: 100,    // MB - warning threshold
        critical: 200    // MB - critical threshold
      },
      performanceLog: []
    };
  }
  
  static async startMonitoring(page, testName) {
    // Start performance monitoring for a test
    const session = {
      testName,
      startTime: Date.now(),
      metrics: {
        responseTime: [],
        memoryUsage: [],
        networkRequests: [],
        domMutations: [],
        userInteractions: []
      }
    };
    
    // Set up performance observers
    await page.addInitScript(() => {
      // Monitor response times
      window.vibePerformance = {
        startTime: Date.now(),
        interactions: [],
        
        recordInteraction: (type, duration, element) => {
          window.vibePerformance.interactions.push({
            type,
            duration,
            element: element?.tagName || 'unknown',
            timestamp: Date.now() - window.vibePerformance.startTime
          });
        }
      };
      
      // Override click handlers to measure response time
      const originalAddEventListener = EventTarget.prototype.addEventListener;
      EventTarget.prototype.addEventListener = function(type, listener, options) {
        if (type === 'click') {
          const wrappedListener = function(event) {
            const startTime = performance.now();
            const result = listener.call(this, event);
            const duration = performance.now() - startTime;
            
            window.vibePerformance.recordInteraction('click', duration, event.target);
            return result;
          };
          return originalAddEventListener.call(this, type, wrappedListener, options);
        }
        return originalAddEventListener.call(this, type, listener, options);
      };
    });
    
    return session;
  }
  
  static async measureStarOperationPerformance(page) {
    const measurements = [];
    
    // Test single star operation
    const singleStarTime = await this.measureOperation(page, async () => {
      await page.locator('[data-testid="bookmark-star"]').first().click();
    });
    
    measurements.push({
      operation: 'single-star',
      duration: singleStarTime,
      threshold: this.metrics.responseTimeThresholds.star,
      passed: singleStarTime <= this.metrics.responseTimeThresholds.star
    });
    
    // Test rapid starring (flow state)
    const rapidStarTime = await this.measureOperation(page, async () => {
      const stars = page.locator('[data-testid="bookmark-star"]');
      const count = Math.min(5, await stars.count());
      
      for (let i = 0; i < count; i++) {
        await stars.nth(i).click();
      }
    });
    
    measurements.push({
      operation: 'rapid-starring',
      duration: rapidStarTime,
      threshold: this.metrics.responseTimeThresholds.star * 5, // 5 operations
      passed: rapidStarTime <= this.metrics.responseTimeThresholds.star * 5
    });
    
    return measurements;
  }
  
  static async measureBulkOperationPerformance(page, itemCount = 10) {
    const measurements = [];
    
    // Measure selection performance
    const selectionTime = await this.measureOperation(page, async () => {
      // Select multiple items
      for (let i = 0; i < itemCount; i++) {
        await page.locator('[data-testid="bookmark-checkbox"]').nth(i).check();
      }
    });
    
    measurements.push({
      operation: 'bulk-selection',
      itemCount,
      duration: selectionTime,
      threshold: 50 * itemCount, // 50ms per item
      passed: selectionTime <= 50 * itemCount
    });
    
    // Measure bulk star operation
    const bulkStarTime = await this.measureOperation(page, async () => {
      await page.locator('[data-testid="bulk-star-button"]').click();
      await page.waitForSelector('[data-testid="bulk-operation-complete"]', { timeout: 5000 });
    });
    
    measurements.push({
      operation: 'bulk-starring',
      itemCount,
      duration: bulkStarTime,
      threshold: this.metrics.responseTimeThresholds.bulk,
      passed: bulkStarTime <= this.metrics.responseTimeThresholds.bulk
    });
    
    return measurements;
  }
  
  static async measureFavoritesViewPerformance(page, favoritesCount) {
    const measurements = [];
    
    // Measure favorites view loading
    const loadTime = await this.measureOperation(page, async () => {
      await page.goto('/favorites');
      await page.waitForSelector('[data-testid="favorites-container"]');
    });
    
    measurements.push({
      operation: 'favorites-view-load',
      itemCount: favoritesCount,
      duration: loadTime,
      threshold: this.metrics.responseTimeThresholds.load,
      passed: loadTime <= this.metrics.responseTimeThresholds.load
    });
    
    // Measure favorites filtering
    const filterTime = await this.measureOperation(page, async () => {
      await page.locator('[data-testid="favorites-filter"]').selectOption('recent');
      await page.waitForSelector('[data-testid="filtered-favorites"]');
    });
    
    measurements.push({
      operation: 'favorites-filtering',
      itemCount: favoritesCount,
      duration: filterTime,
      threshold: this.metrics.responseTimeThresholds.filter,
      passed: filterTime <= this.metrics.responseTimeThresholds.filter
    });
    
    // Measure favorites search
    const searchTime = await this.measureOperation(page, async () => {
      await page.locator('[data-testid="favorites-search"]').fill('javascript');
      await page.waitForSelector('[data-testid="search-results"]');
    });
    
    measurements.push({
      operation: 'favorites-search',
      itemCount: favoritesCount,
      duration: searchTime,
      threshold: this.metrics.responseTimeThresholds.search,
      passed: searchTime <= this.metrics.responseTimeThresholds.search
    });
    
    return measurements;
  }
  
  static async measureMemoryUsage(page) {
    const memoryMetrics = await page.evaluate(() => {
      if (performance.memory) {
        return {
          usedJSHeapSize: performance.memory.usedJSHeapSize,
          totalJSHeapSize: performance.memory.totalJSHeapSize,
          jsHeapSizeLimit: performance.memory.jsHeapSizeLimit,
          timestamp: Date.now()
        };
      }
      return null;
    });
    
    if (memoryMetrics) {
      const usedMB = memoryMetrics.usedJSHeapSize / (1024 * 1024);
      const totalMB = memoryMetrics.totalJSHeapSize / (1024 * 1024);
      
      return {
        ...memoryMetrics,
        usedMB: Math.round(usedMB * 100) / 100,
        totalMB: Math.round(totalMB * 100) / 100,
        memoryPressure: this.getMemoryPressureLevel(usedMB),
        withinThreshold: usedMB <= this.metrics.memoryThresholds.warning
      };
    }
    
    return null;
  }
  
  static getMemoryPressureLevel(usedMB) {
    if (usedMB >= this.metrics.memoryThresholds.critical) return 'critical';
    if (usedMB >= this.metrics.memoryThresholds.warning) return 'warning';
    if (usedMB >= this.metrics.memoryThresholds.baseline) return 'normal';
    return 'low';
  }
  
  static async measureOperation(page, operation) {
    const startTime = performance.now();
    await operation();
    return performance.now() - startTime;
  }
  
  static async measureNetworkPerformance(page) {
    // Monitor network requests during favorites operations
    const networkMetrics = {
      requests: [],
      totalRequests: 0,
      failedRequests: 0,
      averageResponseTime: 0
    };
    
    page.on('request', request => {
      networkMetrics.requests.push({
        url: request.url(),
        method: request.method(),
        startTime: Date.now()
      });
      networkMetrics.totalRequests++;
    });
    
    page.on('response', response => {
      const request = networkMetrics.requests.find(req => req.url === response.url());
      if (request) {
        request.endTime = Date.now();
        request.duration = request.endTime - request.startTime;
        request.status = response.status();
        
        if (response.status() >= 400) {
          networkMetrics.failedRequests++;
        }
      }
    });
    
    return networkMetrics;
  }
  
  static async measureScrollPerformance(page) {
    // Measure scroll performance for large favorites collections
    const scrollMetrics = await page.evaluate(() => {
      return new Promise((resolve) => {
        const startTime = performance.now();
        let frameCount = 0;
        let lastFrameTime = startTime;
        
        const measureFrame = () => {
          const currentTime = performance.now();
          frameCount++;
          
          if (currentTime - startTime > 1000) { // Measure for 1 second
            const avgFrameTime = (currentTime - startTime) / frameCount;
            const fps = 1000 / avgFrameTime;
            
            resolve({
              duration: currentTime - startTime,
              frameCount,
              averageFrameTime: avgFrameTime,
              fps: Math.round(fps),
              smooth: fps >= 55 // Consider 55+ FPS as smooth
            });
          } else {
            requestAnimationFrame(measureFrame);
          }
        };
        
        // Start scrolling
        window.scrollBy(0, 100);
        requestAnimationFrame(measureFrame);
      });
    });
    
    return scrollMetrics;
  }
  
  static async generatePerformanceReport(measurements) {
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalTests: measurements.length,
        passedTests: measurements.filter(m => m.passed).length,
        failedTests: measurements.filter(m => !m.passed).length
      },
      measurements,
      recommendations: []
    };
    
    // Generate recommendations based on failures
    measurements.forEach(measurement => {
      if (!measurement.passed) {
        report.recommendations.push({
          operation: measurement.operation,
          issue: `Operation took ${Math.round(measurement.duration)}ms, exceeding threshold of ${measurement.threshold}ms`,
          suggestion: this.getPerformanceRecommendation(measurement.operation, measurement.duration, measurement.threshold)
        });
      }
    });
    
    return report;
  }
  
  static getPerformanceRecommendation(operation, duration, threshold) {
    const ratio = duration / threshold;
    
    if (operation.includes('star')) {
      if (ratio > 2) return 'Consider optimistic UI updates and background processing for star operations';
      return 'Optimize star operation response time with immediate visual feedback';
    }
    
    if (operation.includes('bulk')) {
      if (ratio > 2) return 'Implement progressive bulk operations with progress indicators';
      return 'Add loading states and progress feedback for bulk operations';
    }
    
    if (operation.includes('search') || operation.includes('filter')) {
      if (ratio > 2) return 'Implement debounced search with virtual scrolling for large datasets';
      return 'Optimize search/filter performance with better indexing';
    }
    
    if (operation.includes('load')) {
      if (ratio > 2) return 'Implement lazy loading and code splitting for faster initial load';
      return 'Optimize initial loading with skeleton screens and progressive enhancement';
    }
    
    return 'Optimize operation performance to meet user experience expectations';
  }
  
  static async endMonitoring(session) {
    session.endTime = Date.now();
    session.totalDuration = session.endTime - session.startTime;
    
    this.metrics.performanceLog.push(session);
    
    return session;
  }
}