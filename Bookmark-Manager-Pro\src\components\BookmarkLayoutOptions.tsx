import React, { useState } from 'react'
import { Bookmark } from '../../types'
import { BookmarkCardFlip } from './BookmarkCardFlip'

// Sample bookmark for demonstration
const sampleBookmark: Bookmark = {
  id: '1',
  title: 'React Documentation - Advanced Patterns and Best Practices',
  url: 'https://react.dev/learn',
  description: 'Comprehensive guide to React patterns, hooks, and modern development practices for building scalable applications.',
  favicon: 'https://react.dev/favicon.ico',
  dateAdded: '2024-01-15T10:00:00Z',
  collection: 'Development',
  tags: ['react', 'javascript', 'frontend', 'documentation'],
  visits: 42,
  isFavorite: true,
  selected: false
}

type LayoutOption = 'default' | 'compact' | 'horizontal' | 'card-style'

export const BookmarkLayoutOptions: React.FC = () => {
  const [selectedLayout, setSelectedLayout] = useState<LayoutOption>('default')

  const layouts: { key: LayoutOption; name: string; description: string }[] = [
    {
      key: 'default',
      name: 'Current Layout',
      description: 'Current spacious layout with large padding'
    },
    {
      key: 'compact',
      name: 'Option 1: Compact Vertical',
      description: 'Reduced padding and tighter spacing for more content density'
    },
    {
      key: 'horizontal',
      name: 'Option 2: Horizontal Split',
      description: 'Grid-based layout with better space utilization'
    },
    {
      key: 'card-style',
      name: 'Option 3: Card-Style with Tags',
      description: 'Compact card design with visible tags and optimized spacing'
    }
  ]

  return (
    <div style={{ padding: '20px', background: 'var(--primary-bg)' }}>
      <h2 style={{ marginBottom: '20px', color: 'var(--text-primary)' }}>
        Bookmark Card Layout Options
      </h2>
      
      <div style={{ marginBottom: '30px' }}>
        <h3 style={{ marginBottom: '15px', color: 'var(--text-primary)' }}>
          Choose a layout option:
        </h3>
        <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
          {layouts.map(layout => (
            <button
              key={layout.key}
              onClick={() => setSelectedLayout(layout.key)}
              style={{
                padding: '10px 15px',
                border: selectedLayout === layout.key ? '2px solid var(--accent-primary)' : '1px solid var(--border-color)',
                borderRadius: 'var(--radius-md)',
                background: selectedLayout === layout.key ? 'var(--accent-primary)' : 'var(--secondary-bg)',
                color: selectedLayout === layout.key ? 'white' : 'var(--text-primary)',
                cursor: 'pointer',
                transition: 'all 0.2s ease'
              }}
            >
              {layout.name}
            </button>
          ))}
        </div>
        
        <p style={{ 
          marginTop: '10px', 
          color: 'var(--text-secondary)',
          fontStyle: 'italic'
        }}>
          {layouts.find(l => l.key === selectedLayout)?.description}
        </p>
      </div>

      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '20px',
        maxWidth: '1200px'
      }}>
        <div style={{ height: '280px', position: 'relative' }}>
          <BookmarkCardFlip
            bookmark={sampleBookmark}
            onToggleFavorite={() => {}}
            layoutClass={selectedLayout}
          />
        </div>
      </div>

      <div style={{ marginTop: '40px' }}>
        <h3 style={{ marginBottom: '15px', color: 'var(--text-primary)' }}>
          Layout Comparison Details:
        </h3>
        
        <div style={{ display: 'grid', gap: '15px' }}>
          <div style={{ 
            padding: '15px', 
            background: 'var(--secondary-bg)', 
            borderRadius: 'var(--radius-md)',
            border: '1px solid var(--border-color)'
          }}>
            <h4 style={{ color: 'var(--accent-primary)', marginBottom: '8px' }}>
              Option 1: Compact Vertical Layout
            </h4>
            <ul style={{ color: 'var(--text-secondary)', margin: 0, paddingLeft: '20px' }}>
              <li>Reduces padding from large to medium (16px → 12px)</li>
              <li>Tighter spacing between elements (gap: 4px)</li>
              <li>Fits more content in same space</li>
              <li>Maintains current vertical flow</li>
            </ul>
          </div>

          <div style={{ 
            padding: '15px', 
            background: 'var(--secondary-bg)', 
            borderRadius: 'var(--radius-md)',
            border: '1px solid var(--border-color)'
          }}>
            <h4 style={{ color: 'var(--accent-primary)', marginBottom: '8px' }}>
              Option 2: Horizontal Split Layout
            </h4>
            <ul style={{ color: 'var(--text-secondary)', margin: 0, paddingLeft: '20px' }}>
              <li>Uses CSS Grid for better space distribution</li>
              <li>More efficient use of horizontal space</li>
              <li>Better alignment of elements</li>
              <li>Responsive grid-based approach</li>
            </ul>
          </div>

          <div style={{ 
            padding: '15px', 
            background: 'var(--secondary-bg)', 
            borderRadius: 'var(--radius-md)',
            border: '1px solid var(--border-color)'
          }}>
            <h4 style={{ color: 'var(--accent-primary)', marginBottom: '8px' }}>
              Option 3: Card-Style with Tags
            </h4>
            <ul style={{ color: 'var(--text-secondary)', margin: 0, paddingLeft: '20px' }}>
              <li>Minimal padding for maximum content density</li>
              <li>Shows actual bookmark tags as chips</li>
              <li>Optimized spacing between all elements</li>
              <li>More information visible at a glance</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
