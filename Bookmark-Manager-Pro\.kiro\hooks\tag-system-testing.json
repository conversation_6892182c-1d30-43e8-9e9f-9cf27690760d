{"name": "Tag System Testing Hook", "description": "Test each feature in the Tag System using the Favorites System testing methodology", "trigger": {"type": "file_change", "patterns": ["docs/features/17-tag-system/feature-intent.md"]}, "action": {"type": "agent_execution", "prompt": "Test each feature described in the Tag System feature intent document (docs/features/17-tag-system/feature-intent.md) using the comprehensive testing methodology outlined in the Favorites System testing guide (docs/features/18-favorites-system/testing-guide.md).\n\nFor each tag system feature, create detailed test cases that follow the same structure as the favorites testing guide:\n\n1. **Core Functionality Tests** for basic tag operations:\n   - Smart tag generation and suggestions\n   - Hierarchical tag structure creation and management\n   - Visual tag management interface\n   - Tag-based discovery and navigation\n\n2. **Advanced Feature Tests** for intelligent tag capabilities:\n   - Tag relationship mapping and clustering\n   - Automated tag management and cleanup\n   - Tag analytics and insights\n   - Collaborative tagging features\n\n3. **Integration Tests** with other bookmark features:\n   - Search and discovery integration\n   - Organization feature enhancement\n   - Analytics and reporting integration\n\n4. **Performance Tests** for tag system scalability:\n   - Tag operation response times\n   - Large tag vocabulary performance\n   - Concurrent tag operations\n\n5. **User Experience Tests** for tag interface usability:\n   - Tag interface intuitive design\n   - Tag suggestion accuracy and relevance\n   - Error handling and recovery\n\n6. **Edge Case Tests** for complex tag scenarios:\n   - Maximum tag limits and complex hierarchies\n   - Cross-platform tag consistency\n   - Special characters and Unicode handling\n\nFor each test category, include:\n- Test objectives and expected outcomes\n- Detailed test steps and procedures\n- Validation criteria and performance benchmarks\n- Integration points with existing bookmark features\n- Quality assurance measures\n\nEnsure the testing approach validates all the advanced tag features mentioned in the feature intent, including machine learning integration, knowledge graph capabilities, and enterprise-grade governance features.", "context": ["docs/features/17-tag-system/feature-intent.md", "docs/features/18-favorites-system/testing-guide.md"]}, "settings": {"autoExecute": true, "timeout": 300000, "retryCount": 2}}