/**
 * HYBRID RECOMMENDATION ENGINE
 * Combines content-based, collaborative, and temporal approaches
 */

import type { 
  RecommendationEngine,
  CandidateGenerator,
  RankingService,
  RecommendationContext,
  Candidate,
  RankedRecommendation,
  UserProfile,
  StreamingOptions,
  RecommendationStream,
  FeedbackType,
  FeatureVector,
  Explanation
} from './interfaces'

import { ModernContentAnalyzer } from './ContentAnalyzer'
import { ModernCollaborativeFilteringService } from './CollaborativeFilteringService'

export class HybridRecommendationEngine implements RecommendationEngine {
  constructor(
    private contentAnalyzer: ModernContentAnalyzer,
    private collaborativeEngine: ModernCollaborativeFilteringService,
    private candidateGenerator: CandidateGenerator,
    private rankingService: RankingService
  ) {}

  async generateCandidates(userId: string, context: RecommendationContext): Promise<Candidate[]> {
    // Generate candidates from multiple sources in parallel
    const [contentCandidates, collaborativeCandidates, temporalCandidates] = await Promise.all([
      this.candidateGenerator.generateContentBased(context.bookmarks),
      this.candidateGenerator.generateCollaborative(context.userProfile),
      this.candidateGenerator.generateTemporal({
        timeRange: context.timeRange || { start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), end: new Date() },
        patterns: [],
        seasonality: { trends: [], cycles: [], anomalies: [] }
      })
    ])

    // Merge and deduplicate candidates
    const allCandidates = this.mergeCandidates([
      contentCandidates,
      collaborativeCandidates,
      temporalCandidates
    ])

    // Apply diversity and novelty filters
    return this.applyDiversityFilters(allCandidates, context.userProfile)
  }

  async rankCandidates(candidates: Candidate[], userProfile: UserProfile): Promise<RankedRecommendation[]> {
    // Extract features for ranking
    const features = await this.extractFeatures(candidates, userProfile)
    
    // Use ensemble ranking combining multiple signals
    const rankedCandidates = await this.ensembleRanking(candidates, features, userProfile)
    
    // Generate explanations for top recommendations
    const rankedWithExplanations = await Promise.all(
      rankedCandidates.map(async (candidate, index) => ({
        ...candidate,
        rank: index + 1,
        explanation: await this.generateExplanation(candidate, userProfile)
      }))
    )

    return rankedWithExplanations
  }

  streamRecommendations(options: StreamingOptions): RecommendationStream {
    let isActive = true
    let isPaused = false
    
    const processRecommendations = async () => {
      try {
        while (isActive && !isPaused) {
          // Generate candidates
          const candidates = await this.generateCandidates(options.userId, options.context)
          
          // Rank candidates
          const ranked = await this.rankCandidates(candidates, options.context.userProfile)
          
          // Stream recommendations one by one
          for (const recommendation of ranked.slice(0, options.maxRecommendations || 10)) {
            if (!isActive || isPaused) break
            
            options.onRecommendation(recommendation)
            options.onExplanation(recommendation.explanation)
            
            // Small delay between recommendations for better UX
            await new Promise(resolve => setTimeout(resolve, 100))
          }
          
          // Wait before next batch
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      } catch (error) {
        options.onError(error as Error)
      }
    }

    // Start streaming
    processRecommendations()

    return {
      close: () => { isActive = false },
      pause: () => { isPaused = true },
      resume: () => { isPaused = false },
      updateContext: (newContext) => {
        Object.assign(options.context, newContext)
      }
    }
  }

  async recordFeedback(recommendationId: string, feedback: FeedbackType): Promise<void> {
    // Store feedback for model improvement
    const feedbackData = {
      recommendationId,
      feedback,
      timestamp: new Date()
    }
    
    // Update user profile based on feedback
    await this.updateUserProfileFromFeedback(feedbackData)
    
    // Update ranking model with feedback
    await this.updateRankingModel(feedbackData)
  }

  private mergeCandidates(candidateGroups: Candidate[][]): Candidate[] {
    const candidateMap = new Map<string, Candidate>()
    
    candidateGroups.forEach(group => {
      group.forEach(candidate => {
        const key = this.generateCandidateKey(candidate)
        
        if (candidateMap.has(key)) {
          // Merge similar candidates
          const existing = candidateMap.get(key)!
          candidateMap.set(key, this.mergeSimilarCandidates(existing, candidate))
        } else {
          candidateMap.set(key, candidate)
        }
      })
    })
    
    return Array.from(candidateMap.values())
  }

  private generateCandidateKey(candidate: Candidate): string {
    // Generate a key based on bookmark IDs to identify similar candidates
    const sortedIds = [...candidate.bookmarkIds].sort()
    return sortedIds.join('|')
  }

  private mergeSimilarCandidates(candidate1: Candidate, candidate2: Candidate): Candidate {
    // Merge two similar candidates, keeping the best properties
    return {
      ...candidate1,
      confidence: Math.max(candidate1.confidence, candidate2.confidence),
      source: 'hybrid',
      bookmarkIds: Array.from(new Set([...candidate1.bookmarkIds, ...candidate2.bookmarkIds])),
      features: this.mergeFeatureVectors(candidate1.features, candidate2.features)
    }
  }

  private mergeFeatureVectors(features1: FeatureVector, features2: FeatureVector): FeatureVector {
    return {
      contentFeatures: this.averageVectors(features1.contentFeatures, features2.contentFeatures),
      collaborativeFeatures: this.averageVectors(features1.collaborativeFeatures, features2.collaborativeFeatures),
      temporalFeatures: this.averageVectors(features1.temporalFeatures, features2.temporalFeatures),
      contextualFeatures: this.averageVectors(features1.contextualFeatures, features2.contextualFeatures)
    }
  }

  private averageVectors(vector1: number[], vector2: number[]): number[] {
    const maxLength = Math.max(vector1.length, vector2.length)
    const result = new Array(maxLength).fill(0)
    
    for (let i = 0; i < maxLength; i++) {
      const val1 = i < vector1.length ? vector1[i] : 0
      const val2 = i < vector2.length ? vector2[i] : 0
      result[i] = (val1 + val2) / 2
    }
    
    return result
  }

  private applyDiversityFilters(candidates: Candidate[], userProfile: UserProfile): Candidate[] {
    // Apply diversity and novelty filters based on user preferences
    const diversityThreshold = userProfile.preferences.diversityPreference
    const noveltyThreshold = userProfile.preferences.noveltyPreference
    
    // Sort by confidence first
    const sortedCandidates = candidates.sort((a, b) => b.confidence - a.confidence)
    
    const filteredCandidates: Candidate[] = []
    const usedTopics = new Set<string>()
    
    for (const candidate of sortedCandidates) {
      // Check diversity
      const candidateTopics = this.extractTopicsFromCandidate(candidate)
      const topicOverlap = candidateTopics.filter(topic => usedTopics.has(topic)).length
      const diversityScore = 1 - (topicOverlap / candidateTopics.length)
      
      if (diversityScore >= diversityThreshold) {
        filteredCandidates.push(candidate)
        candidateTopics.forEach(topic => usedTopics.add(topic))
      }
      
      if (filteredCandidates.length >= 20) break // Limit candidates
    }
    
    return filteredCandidates
  }

  private extractTopicsFromCandidate(candidate: Candidate): string[] {
    // Extract topics from candidate for diversity calculation
    // This would typically analyze the bookmark content
    return [candidate.type, candidate.source] // Simplified
  }

  private async extractFeatures(candidates: Candidate[], userProfile: UserProfile): Promise<FeatureVector[]> {
    return Promise.all(candidates.map(async candidate => {
      const contentFeatures = await this.extractContentFeatures(candidate)
      const collaborativeFeatures = this.extractCollaborativeFeatures(candidate, userProfile)
      const temporalFeatures = this.extractTemporalFeatures(candidate)
      const contextualFeatures = this.extractContextualFeatures(candidate, userProfile)
      
      return {
        contentFeatures,
        collaborativeFeatures,
        temporalFeatures,
        contextualFeatures
      }
    }))
  }

  private async extractContentFeatures(candidate: Candidate): Promise<number[]> {
    // Extract content-based features
    return [
      candidate.confidence,
      candidate.bookmarkIds.length,
      candidate.type === 'playlist' ? 1 : 0,
      candidate.source === 'content' ? 1 : 0
    ]
  }

  private extractCollaborativeFeatures(candidate: Candidate, userProfile: UserProfile): number[] {
    // Extract collaborative filtering features
    return [
      candidate.source === 'collaborative' ? 1 : 0,
      userProfile.interactions.length / 100, // Normalized interaction count
      userProfile.preferences.diversityPreference,
      userProfile.preferences.noveltyPreference
    ]
  }

  private extractTemporalFeatures(candidate: Candidate): number[] {
    // Extract temporal features
    const now = new Date()
    const hourOfDay = now.getHours() / 24
    const dayOfWeek = now.getDay() / 7
    
    return [
      candidate.source === 'temporal' ? 1 : 0,
      hourOfDay,
      dayOfWeek
    ]
  }

  private extractContextualFeatures(candidate: Candidate, userProfile: UserProfile): number[] {
    // Extract contextual features
    return [
      userProfile.clusters.length / 10, // Normalized cluster count
      candidate.bookmarkIds.length / 20, // Normalized bookmark count
      Math.random() // Exploration factor
    ]
  }

  private async ensembleRanking(
    candidates: Candidate[], 
    features: FeatureVector[], 
    userProfile: UserProfile
  ): Promise<RankedRecommendation[]> {
    // Combine multiple ranking signals
    const rankedCandidates = candidates.map((candidate, index) => {
      const feature = features[index]
      
      // Weighted combination of different ranking signals
      const contentScore = this.calculateContentScore(feature.contentFeatures)
      const collaborativeScore = this.calculateCollaborativeScore(feature.collaborativeFeatures)
      const temporalScore = this.calculateTemporalScore(feature.temporalFeatures)
      const contextualScore = this.calculateContextualScore(feature.contextualFeatures)
      
      // Ensemble weights based on user preferences
      const weights = this.getEnsembleWeights(userProfile)
      
      const finalScore = 
        contentScore * weights.content +
        collaborativeScore * weights.collaborative +
        temporalScore * weights.temporal +
        contextualScore * weights.contextual
      
      return {
        ...candidate,
        rank: 0, // Will be set after sorting
        score: finalScore,
        explanation: { primary: '', secondary: [], confidence: 0, factors: [] }, // Will be generated later
        diversity: this.calculateDiversityScore(candidate, userProfile),
        novelty: this.calculateNoveltyScore(candidate, userProfile)
      }
    })
    
    // Sort by final score
    return rankedCandidates.sort((a, b) => b.score - a.score)
  }

  private calculateContentScore(features: number[]): number {
    return features.reduce((sum, feature) => sum + feature, 0) / features.length
  }

  private calculateCollaborativeScore(features: number[]): number {
    return features.reduce((sum, feature) => sum + feature, 0) / features.length
  }

  private calculateTemporalScore(features: number[]): number {
    return features.reduce((sum, feature) => sum + feature, 0) / features.length
  }

  private calculateContextualScore(features: number[]): number {
    return features.reduce((sum, feature) => sum + feature, 0) / features.length
  }

  private getEnsembleWeights(userProfile: UserProfile): {
    content: number
    collaborative: number
    temporal: number
    contextual: number
  } {
    // Adaptive weights based on user profile
    const interactionCount = userProfile.interactions.length
    
    if (interactionCount < 10) {
      // New users: rely more on content-based
      return { content: 0.6, collaborative: 0.1, temporal: 0.2, contextual: 0.1 }
    } else if (interactionCount < 50) {
      // Medium users: balanced approach
      return { content: 0.4, collaborative: 0.3, temporal: 0.2, contextual: 0.1 }
    } else {
      // Experienced users: rely more on collaborative
      return { content: 0.2, collaborative: 0.5, temporal: 0.2, contextual: 0.1 }
    }
  }

  private calculateDiversityScore(candidate: Candidate, userProfile: UserProfile): number {
    // Calculate how diverse this candidate is compared to user's history
    return Math.random() // Simplified for now
  }

  private calculateNoveltyScore(candidate: Candidate, userProfile: UserProfile): number {
    // Calculate how novel this candidate is for the user
    return Math.random() // Simplified for now
  }

  private async generateExplanation(candidate: RankedRecommendation, userProfile: UserProfile): Promise<Explanation> {
    const factors = []
    
    if (candidate.source === 'content' || candidate.source === 'hybrid') {
      factors.push({
        type: 'content_similarity' as const,
        weight: 0.4,
        description: 'Based on content similarity to your bookmarks',
        evidence: ['Similar topics', 'Related keywords']
      })
    }
    
    if (candidate.source === 'collaborative' || candidate.source === 'hybrid') {
      factors.push({
        type: 'user_behavior' as const,
        weight: 0.3,
        description: 'Users with similar interests also saved these',
        evidence: ['Similar user patterns', 'Community preferences']
      })
    }
    
    if (candidate.source === 'temporal' || candidate.source === 'hybrid') {
      factors.push({
        type: 'temporal_pattern' as const,
        weight: 0.2,
        description: 'Trending in your usage patterns',
        evidence: ['Recent activity', 'Time-based preferences']
      })
    }
    
    return {
      primary: `Recommended because ${factors[0]?.description || 'of multiple factors'}`,
      secondary: factors.slice(1).map(f => f.description),
      confidence: candidate.confidence,
      factors
    }
  }

  private async updateUserProfileFromFeedback(feedbackData: any): Promise<void> {
    // Update user profile based on feedback
    // This would involve updating preferences and interaction history
  }

  private async updateRankingModel(feedbackData: any): Promise<void> {
    // Update the ranking model with new feedback data
    // This would involve retraining or updating model weights
  }
}
