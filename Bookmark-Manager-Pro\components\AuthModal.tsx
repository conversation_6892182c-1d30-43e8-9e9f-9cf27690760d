import React, { useState } from 'react';
import { userTierService } from '../services/userTierService';
import { LockClosedIcon, SparklesIcon, UserIcon, XMarkIcon } from './icons/HeroIcons';
import SpinnerIcon from './icons/SpinnerIcon';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onAuthSuccess: () => void;
}

const AuthModal: React.FC<AuthModalProps> = ({ isOpen, onClose, onAuthSuccess }) => {
  const [mode, setMode] = useState<'login' | 'register'>('login');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      const result = mode === 'login' 
        ? await userTierService.login(email, password)
        : await userTierService.register(email, password);

      if (result.success) {
        onAuthSuccess();
        onClose();
        setEmail('');
        setPassword('');
      } else {
        setError(result.error || 'Authentication failed');
      }
    } catch (_err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  const resetForm = () => {
    setEmail('');
    setPassword('');
    setError('');
  };

  const switchMode = () => {
    setMode(mode === 'login' ? 'register' : 'login');
    resetForm();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4 border border-slate-700">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-2">
            <SparklesIcon className="w-6 h-6 text-sky-400" />
            <h2 className="text-xl font-semibold text-white">
              {mode === 'login' ? 'Sign In' : 'Create Account'}
            </h2>
          </div>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white transition-colors"
          >
            <XMarkIcon className="w-6 h-6" />
          </button>
        </div>

        {/* Premium Features Info */}
        <div className="bg-slate-700 rounded-lg p-4 mb-6">
          <h3 className="text-sm font-medium text-sky-300 mb-2">Premium Features</h3>
          <ul className="text-sm text-slate-300 space-y-1">
            <li>• AI-powered summarization</li>
            <li>• Smart auto-tagging</li>
            <li>• Advanced search capabilities</li>
            <li>• Cloud sync (coming soon)</li>
            <li>• Longer summaries & more tags</li>
          </ul>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Email Field */}
          <div>
            <label htmlFor="email" className="block text-sm font-medium text-slate-300 mb-1">
              Email
            </label>
            <div className="relative">
              <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                placeholder="Enter your email"
                required
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label htmlFor="password" className="block text-sm font-medium text-slate-300 mb-1">
              Password
            </label>
            <div className="relative">
              <LockClosedIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-slate-400" />
              <input
                type="password"
                id="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full pl-10 pr-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-sky-500 focus:border-transparent"
                placeholder={mode === 'register' ? 'Create a password (min 6 chars)' : 'Enter your password'}
                required
                minLength={6}
                disabled={isLoading}
              />
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="text-red-400 text-sm bg-red-900/20 border border-red-800 rounded-md p-2">
              {error}
            </div>
          )}

          {/* Demo Info */}
          <div className="text-xs text-muted-foreground bg-card rounded-md p-2">
            <strong>Demo Mode:</strong> Use any email and password (min 6 chars) to access premium features.
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isLoading}
            className="w-full bg-sky-600 hover:bg-sky-700 disabled:bg-slate-600 text-white font-medium py-2 px-4 rounded-md transition-colors flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <SpinnerIcon className="w-4 h-4" />
                <span>{mode === 'login' ? 'Signing In...' : 'Creating Account...'}</span>
              </>
            ) : (
              <span>{mode === 'login' ? 'Sign In' : 'Create Account'}</span>
            )}
          </button>
        </form>

        {/* Mode Switch */}
        <div className="mt-4 text-center">
          <button
            onClick={switchMode}
            className="text-sky-400 hover:text-sky-300 text-sm transition-colors"
            disabled={isLoading}
          >
            {mode === 'login' 
              ? "Don't have an account? Sign up" 
              : "Already have an account? Sign in"
            }
          </button>
        </div>

        {/* Basic Features Info */}
        <div className="mt-4 pt-4 border-t border-slate-700">
          <p className="text-xs text-slate-400 text-center">
            Basic features (client-side processing) are always available without an account.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;