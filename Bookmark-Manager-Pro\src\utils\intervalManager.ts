/**
 * INTERVAL MANAGEMENT SYSTEM
 * Prevents runaway intervals and manages all timers centrally
 */

interface ManagedInterval {
  id: number
  name: string
  callback: () => void
  interval: number
  created: number
  lastRun: number
  runCount: number
  maxRuns?: number
}

export class IntervalManager {
  private static instance: IntervalManager | null = null

  private intervals = new Map<number, ManagedInterval>()
  private cleanupInterval: number | null = null

  // Store original functions to avoid triggering our own warnings
  private originalSetInterval: typeof setInterval
  private originalClearInterval: typeof clearInterval
  private originalSetTimeout: typeof setTimeout
  private originalClearTimeout: typeof clearTimeout

  // Configuration
  private readonly MAX_INTERVALS = 20
  private readonly MAX_RUN_COUNT = 1000
  private readonly CLEANUP_INTERVAL = 60000 // 1 minute
  private readonly STALE_THRESHOLD = 300000 // 5 minutes

  // Singleton pattern
  static getInstance(): IntervalManager {
    if (!IntervalManager.instance) {
      IntervalManager.instance = new IntervalManager()
    }
    return IntervalManager.instance
  }

  constructor() {
    // Store original functions before overriding
    this.originalSetInterval = window.setInterval.bind(window)
    this.originalClearInterval = window.clearInterval.bind(window)
    this.originalSetTimeout = window.setTimeout.bind(window)
    this.originalClearTimeout = window.clearTimeout.bind(window)

    this.startCleanup()

    // Override global setInterval to track all intervals
    this.overrideGlobalInterval()
  }

  createInterval(
    callback: () => void, 
    interval: number, 
    name: string = 'unnamed',
    maxRuns?: number
  ): number {
    // Check limits
    if (this.intervals.size >= this.MAX_INTERVALS) {
      console.warn(`⚠️ Too many intervals (${this.intervals.size}). Cleaning up old ones.`)
      this.cleanupStaleIntervals()
    }
    
    const now = Date.now()
    
    // Create the actual interval with wrapper
    const wrappedCallback = () => {
      const interval = this.intervals.get(actualId)
      if (!interval) return
      
      interval.lastRun = Date.now()
      interval.runCount++
      
      try {
        interval.callback()
      } catch (error) {
        console.error(`❌ Error in interval "${name}":`, error)
        this.clearInterval(actualId)
        return
      }
      
      // Check if max runs reached
      if (interval.maxRuns && interval.runCount >= interval.maxRuns) {
        console.log(`✅ Interval "${name}" completed ${interval.runCount} runs`)
        this.clearInterval(actualId)
      }
      
      // Check for runaway intervals
      if (interval.runCount > this.MAX_RUN_COUNT) {
        console.warn(`🚨 Runaway interval detected: "${name}" (${interval.runCount} runs)`)
        this.clearInterval(actualId)
      }
    }
    
    const actualId = this.originalSetInterval(wrappedCallback, interval) as unknown as number
    
    const managedInterval: ManagedInterval = {
      id: actualId,
      name,
      callback,
      interval,
      created: now,
      lastRun: now,
      runCount: 0,
      maxRuns
    }

    this.intervals.set(actualId, managedInterval)
    
    console.log(`⏰ Created interval "${name}" (${interval}ms, ID: ${actualId})`)
    return actualId
  }

  clearInterval(id: number): void {
    const interval = this.intervals.get(id)
    if (interval) {
      this.originalClearInterval(id)
      this.intervals.delete(id)
      console.log(`🗑️ Cleared interval "${interval.name}" (ID: ${id})`)
    }
  }

  clearAllIntervals(): void {
    console.log(`🧹 Clearing all ${this.intervals.size} managed intervals`)

    for (const [id] of Array.from(this.intervals)) {
      this.originalClearInterval(id)
    }

    this.intervals.clear()
  }

  // Timeout management methods
  createTimeout(callback: () => void, delay: number, name: string = 'unnamed'): number {
    const timeoutId = this.originalSetTimeout(() => {
      try {
        callback()
      } catch (error) {
        console.error(`❌ Error in timeout "${name}":`, error)
      }
    }, delay) as unknown as number

    console.log(`⏱️ Created timeout "${name}" (${delay}ms, ID: ${timeoutId})`)
    return timeoutId
  }

  clearTimeout(id: number): void {
    this.originalClearTimeout(id)
    console.log(`🗑️ Cleared timeout (ID: ${id})`)
  }

  clearIntervalsByName(name: string): void {
    const toDelete: number[] = []
    
    for (const [id, interval] of Array.from(this.intervals)) {
      if (interval.name === name) {
        toDelete.push(id)
      }
    }
    
    toDelete.forEach(id => this.clearInterval(id))
    console.log(`🗑️ Cleared ${toDelete.length} intervals named "${name}"`)
  }

  private startCleanup(): void {
    this.cleanupInterval = this.originalSetInterval(() => {
      this.cleanupStaleIntervals()
    }, this.CLEANUP_INTERVAL) as unknown as number
  }

  private cleanupStaleIntervals(): void {
    const now = Date.now()
    const toDelete: number[] = []
    
    for (const [id, interval] of Array.from(this.intervals)) {
      // Remove stale intervals (not run in 5 minutes)
      if (now - interval.lastRun > this.STALE_THRESHOLD) {
        toDelete.push(id)
      }
    }
    
    if (toDelete.length > 0) {
      console.log(`🧹 Cleaning up ${toDelete.length} stale intervals`)
      toDelete.forEach(id => this.clearInterval(id))
    }
  }

  private overrideGlobalInterval(): void {
    const originalSetInterval = window.setInterval
    const originalClearInterval = window.clearInterval
    
    // Override setInterval to track unmanaged intervals
    window.setInterval = (callback: any, interval: number, ...args: any[]) => {
      // If it's a function, try to get its name
      const name = typeof callback === 'function' ?
        callback.name || 'anonymous' : 'unknown'

      // Get stack trace to help identify source
      const stack = new Error().stack
      const caller = stack?.split('\n')[2]?.trim() || 'unknown'

      console.warn(`⚠️ Unmanaged interval created: "${name}" - consider using IntervalManager`)
      console.warn(`📍 Called from: ${caller}`)

      return originalSetInterval(callback, interval, ...args) as any
    }
    
    // Keep original clearInterval
    window.clearInterval = originalClearInterval
  }

  // Emergency methods
  emergencyStop(): void {
    console.error('🚨 EMERGENCY: Stopping all intervals')
    
    // Clear all managed intervals
    this.clearAllIntervals()
    
    // Clear all possible unmanaged intervals (brute force)
    for (let i = 1; i < 99999; i++) {
      try {
        window.clearInterval(i as any)
        window.clearTimeout(i)
      } catch (e) {
        // Ignore errors
      }
    }
    
    console.error('🚨 All intervals and timeouts cleared')
  }

  // Monitoring methods
  getStats(): {
    totalIntervals: number
    intervalDetails: Array<{
      id: number | NodeJS.Timeout
      name: string
      interval: number
      runCount: number
      age: number
      timeSinceLastRun: number
    }>
    oldestInterval: number
    mostActiveInterval: {
      id: number | NodeJS.Timeout
      name: string
      runCount: number
    }
  } {
    const now = Date.now()
    const details = Array.from(this.intervals.values()).map(interval => ({
      id: interval.id,
      name: interval.name,
      interval: interval.interval,
      runCount: interval.runCount,
      age: now - interval.created,
      timeSinceLastRun: now - interval.lastRun
    }))
    
    const oldestInterval = Math.max(...details.map(d => d.age))
    const mostActive = details.reduce((max, current) => 
      current.runCount > max.runCount ? current : max,
      { id: 0 as any, name: 'none', runCount: 0, age: 0 }
    )
    
    return {
      totalIntervals: this.intervals.size,
      intervalDetails: details,
      oldestInterval,
      mostActiveInterval: {
        id: mostActive.id as any,
        name: mostActive.name,
        runCount: mostActive.runCount
      }
    }
  }

  // Utility methods
  pauseAllIntervals(): void {
    console.log('⏸️ Pausing all intervals')
    for (const [id] of Array.from(this.intervals)) {
      window.clearInterval(id as any)
    }
  }

  resumeAllIntervals(): void {
    console.log('▶️ Resuming all intervals')
    for (const [id, interval] of Array.from(this.intervals)) {
      const wrappedCallback = () => {
        interval.lastRun = Date.now()
        interval.runCount++
        try {
          interval.callback()
        } catch (error) {
          console.error(`❌ Error in interval "${interval.name}":`, error)
          this.clearInterval(id)
        }
      }
      
      const newId = this.originalSetInterval(wrappedCallback, interval.interval)
      this.intervals.delete(id)
      interval.id = newId as any
      this.intervals.set(newId as any, interval)
    }
  }
}

// Global instance
export const intervalManager = IntervalManager.getInstance()

// Auto-setup
if (typeof window !== 'undefined') {
  // Make available globally for debugging
  ;(window as any).intervalManager = intervalManager
  
  // Emergency cleanup on page unload
  window.addEventListener('beforeunload', () => {
    intervalManager.clearAllIntervals()
  })
  
  // Monitor for too many intervals
  intervalManager.createInterval(() => {
    const stats = intervalManager.getStats()
    if (stats.totalIntervals > 15) {
      console.warn(`⚠️ High interval count: ${stats.totalIntervals}`)
    }
  }, 30000, 'interval-monitor', undefined)
}
