{"test_datasets": {"basic_development": {"description": "Basic development tools and resources for initial testing", "bookmarks": [{"id": "dev-001", "title": "React Documentation", "url": "https://react.dev/learn", "tags": ["development", "react", "frontend"], "description": "Official React learning resources and documentation", "dateAdded": "2024-01-15T10:30:00Z", "folder": "Development"}, {"id": "dev-002", "title": "TypeScript Handbook", "url": "https://www.typescriptlang.org/docs/", "tags": ["development", "typescript", "programming"], "description": "Complete TypeScript documentation and guides", "dateAdded": "2024-01-16T14:20:00Z", "folder": "Development"}, {"id": "dev-003", "title": "MDN Web Docs", "url": "https://developer.mozilla.org/", "tags": ["development", "reference", "web"], "description": "Comprehensive web development reference", "dateAdded": "2024-01-17T09:15:00Z", "folder": "Reference"}, {"id": "dev-004", "title": "GitHub", "url": "https://github.com", "tags": ["development", "tools", "git"], "description": "Code repository and collaboration platform", "dateAdded": "2024-01-18T16:45:00Z", "folder": "Tools"}, {"id": "dev-005", "title": "Stack Overflow", "url": "https://stackoverflow.com", "tags": ["development", "community", "help"], "description": "Programming questions and answers community", "dateAdded": "2024-01-19T11:30:00Z", "folder": "Community"}]}, "mixed_categories": {"description": "Diverse bookmark categories for comprehensive analysis testing", "bookmarks": [{"id": "mix-001", "title": "Netflix", "url": "https://netflix.com", "tags": ["entertainment", "streaming", "movies"], "description": "Video streaming service", "dateAdded": "2024-01-20T20:00:00Z", "folder": "Entertainment"}, {"id": "mix-002", "title": "Amazon", "url": "https://amazon.com", "tags": ["shopping", "ecommerce", "retail"], "description": "Online shopping platform", "dateAdded": "2024-01-21T15:30:00Z", "folder": "Shopping"}, {"id": "mix-003", "title": "Gmail", "url": "https://gmail.com", "tags": ["email", "communication", "google"], "description": "Email service by Google", "dateAdded": "2024-01-22T08:45:00Z", "folder": "Productivity"}, {"id": "mix-004", "title": "Wikipedia", "url": "https://wikipedia.org", "tags": ["reference", "knowledge", "encyclopedia"], "description": "Free online encyclopedia", "dateAdded": "2024-01-23T12:15:00Z", "folder": "Reference"}, {"id": "mix-005", "title": "YouTube", "url": "https://youtube.com", "tags": ["entertainment", "video", "learning"], "description": "Video sharing platform", "dateAdded": "2024-01-24T19:20:00Z", "folder": "Entertainment"}, {"id": "mix-006", "title": "LinkedIn", "url": "https://linkedin.com", "tags": ["professional", "networking", "career"], "description": "Professional networking platform", "dateAdded": "2024-01-25T10:10:00Z", "folder": "Professional"}, {"id": "mix-007", "title": "Spotify", "url": "https://spotify.com", "tags": ["music", "streaming", "entertainment"], "description": "Music streaming service", "dateAdded": "2024-01-26T17:30:00Z", "folder": "Entertainment"}, {"id": "mix-008", "title": "Notion", "url": "https://notion.so", "tags": ["productivity", "notes", "organization"], "description": "All-in-one workspace for notes and collaboration", "dateAdded": "2024-01-27T13:45:00Z", "folder": "Productivity"}]}, "edge_cases": {"description": "Edge cases and problematic data for robustness testing", "bookmarks": [{"id": "edge-001", "title": "", "url": "https://example.com/empty-title", "tags": [], "description": "Bookmark with empty title", "dateAdded": "2024-01-28T09:00:00Z", "folder": "Test"}, {"id": "edge-002", "title": "Invalid URL Test", "url": "not-a-valid-url", "tags": ["test", "invalid"], "description": "Bookmark with malformed URL", "dateAdded": "2024-01-29T10:30:00Z", "folder": "Test"}, {"id": "edge-003", "title": "Duplicate URL Test 1", "url": "https://duplicate-test.com", "tags": ["test", "duplicate"], "description": "First instance of duplicate URL", "dateAdded": "2024-01-30T11:00:00Z", "folder": "Test"}, {"id": "edge-004", "title": "Duplicate URL Test 2", "url": "https://duplicate-test.com", "tags": ["test", "duplicate", "second"], "description": "Second instance of duplicate URL with different title", "dateAdded": "2024-01-31T12:15:00Z", "folder": "Test"}, {"id": "edge-005", "title": "Very Long Title That Exceeds Normal Length Expectations And Might Cause Layout Issues In The User Interface When Displayed", "url": "https://long-title-test.com", "tags": ["test", "long", "title", "layout"], "description": "Testing very long titles for UI robustness", "dateAdded": "2024-02-01T13:30:00Z", "folder": "Test"}, {"id": "edge-006", "title": "Special Characters Test: !@#$%^&*()_+-=[]{}|;':,.<>?", "url": "https://special-chars.com/test?param=value&other=123", "tags": ["test", "special", "characters"], "description": "Testing special characters in title and URL", "dateAdded": "2024-02-02T14:45:00Z", "folder": "Test"}, {"id": "edge-007", "title": "Unicode Test: 🚀 测试 العربية Русский", "url": "https://unicode-test.com/测试", "tags": ["test", "unicode", "international"], "description": "Testing Unicode characters and international text", "dateAdded": "2024-02-03T15:20:00Z", "folder": "Test"}]}, "large_dataset_sample": {"description": "Sample of larger dataset for performance testing (represents 1000+ bookmarks)", "bookmarks": [{"id": "large-001", "title": "Performance Test Bookmark 1", "url": "https://performance-test-1.com", "tags": ["performance", "test", "category-a"], "description": "First bookmark in large dataset performance test", "dateAdded": "2024-02-04T16:00:00Z", "folder": "Performance"}, {"id": "large-002", "title": "Performance Test Bookmark 2", "url": "https://performance-test-2.com", "tags": ["performance", "test", "category-b"], "description": "Second bookmark in large dataset performance test", "dateAdded": "2024-02-04T16:01:00Z", "folder": "Performance"}], "note": "This is a sample. For actual performance testing, generate 1000+ similar bookmarks programmatically."}}, "expected_analysis_results": {"basic_development": {"domain_groups": [{"domain": "react.dev", "bookmarks": ["dev-001"], "category": "Documentation"}, {"domain": "typescriptlang.org", "bookmarks": ["dev-002"], "category": "Documentation"}, {"domain": "developer.mozilla.org", "bookmarks": ["dev-003"], "category": "Reference"}, {"domain": "github.com", "bookmarks": ["dev-004"], "category": "Tools"}, {"domain": "stackoverflow.com", "bookmarks": ["dev-005"], "category": "Community"}], "content_categories": [{"category": "Development Tools", "bookmarks": ["dev-001", "dev-002", "dev-003", "dev-004"], "confidence": 0.9}, {"category": "Community Resources", "bookmarks": ["dev-005"], "confidence": 0.8}], "suggested_collections": [{"name": "Frontend Development", "bookmarks": ["dev-001", "dev-002", "dev-003"], "confidence": 0.85}, {"name": "Development Tools", "bookmarks": ["dev-004"], "confidence": 0.9}, {"name": "Developer Community", "bookmarks": ["dev-005"], "confidence": 0.8}]}, "mixed_categories": {"domain_groups": [{"domain": "google.com", "bookmarks": ["mix-003"], "category": "Google Services"}, {"domain": "streaming", "bookmarks": ["mix-001", "mix-005", "mix-007"], "category": "Entertainment"}], "content_categories": [{"category": "Entertainment", "bookmarks": ["mix-001", "mix-005", "mix-007"], "confidence": 0.9}, {"category": "Productivity", "bookmarks": ["mix-003", "mix-008"], "confidence": 0.85}, {"category": "Professional", "bookmarks": ["mix-006"], "confidence": 0.8}]}}, "test_instructions": {"import_process": {"step_1": "Copy bookmark data from desired test dataset", "step_2": "Use Bookmark Manager Pro import functionality", "step_3": "Verify bookmarks appear in the application", "step_4": "Run Smart AI analysis on imported data"}, "validation_checklist": ["Verify all bookmarks imported correctly", "Check that Smart AI analysis completes without errors", "Validate domain grouping accuracy", "Confirm content categorization makes sense", "Test confidence scores are reasonable (0.1-1.0)", "Ensure suggested collections are logical", "Verify edge cases are handled gracefully"], "performance_testing": {"small_dataset": "Use basic_development (5 bookmarks) - should complete in <1 second", "medium_dataset": "Use mixed_categories (8 bookmarks) - should complete in <2 seconds", "large_dataset": "Generate 1000+ bookmarks based on large_dataset_sample pattern", "edge_cases": "Use edge_cases dataset to test error handling and robustness"}}}