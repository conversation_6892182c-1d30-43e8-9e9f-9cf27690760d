import React, { useState } from 'react';
import { Bookmark } from '../types';
import { Button, Card, Dropdown } from './base';
import { ArrowTopRightOnSquareIcon, CalendarIcon, DocumentDuplicateIcon, EllipsisHorizontalIcon, EyeIcon, PencilSquareIcon, ShareIcon, StarIcon, TagIcon, TrashIcon } from './icons/HeroIcons';
import './ModernBookmarkCard.css';

interface ModernBookmarkCardProps {
  bookmark: Bookmark;
  viewMode: 'grid' | 'list';
  isSelected?: boolean;
  onSelect?: (id: string, selected: boolean) => void;
  onEdit: (bookmark: Bookmark) => void;
  onDelete: (id: string) => void;
  onToggleFavorite: (id: string, isFavorite: boolean) => void;
  onVisit?: (id: string, url: string) => void;
}

const ModernBookmarkCard: React.FC<ModernBookmarkCardProps> = ({
  bookmark,
  viewMode,
  onToggleFavorite,
  onEdit: _onEdit,
  onDelete,
  onVisit,
  isSelected = false,
  onSelect
}) => {
  const [_showMenu, _setShowMenu] = useState(false);
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  // Helper functions to adapt the bookmark data
  const getDomain = (url: string) => {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  };

  const formatDate = (dateValue?: Date | string) => {
    if (!dateValue) return 'Unknown date';

    let date: Date;
    
    // Handle Date objects
    if (dateValue instanceof Date) {
      date = dateValue;
    }
    // Handle string timestamps
    else if (typeof dateValue === 'string') {
      const timestamp = parseInt(dateValue);
      date = isNaN(timestamp) ? new Date(dateValue) : new Date(timestamp * 1000);
    }
    else {
      return 'Invalid date';
    }

    if (isNaN(date.getTime())) return 'Invalid date';

    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    if (diffDays < 365) return `${Math.ceil(diffDays / 30)} months ago`;
    return `${Math.ceil(diffDays / 365)} years ago`;
  };

  const getDomainColor = (domain: string) => {
    const colors = [
      '#ef4444', '#f97316', '#f59e0b', '#eab308',
      '#84cc16', '#22c55e', '#10b981', '#14b8a6',
      '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1',
      '#8b5cf6', '#a855f7', '#d946ef', '#ec4899'
    ];
    let hash = 0;
    for (let i = 0; i < domain.length; i++) {
      hash = domain.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  // Adapted properties
  const domain = getDomain(bookmark.url);
  const tags = bookmark.tags || [];
  const isFavorite = bookmark.isFavorite || false;
  const description = bookmark.summary;
  const favicon = bookmark.icon;
  const thumbnail = bookmark.thumbnail;
  const dateAdded = bookmark.addDate;

  const menuItems = [
    { id: 'mark-read', icon: <EyeIcon className="w-4 h-4" />, label: 'Mark as read', action: () => {} },
    { id: 'edit', icon: <PencilSquareIcon className="w-4 h-4" />, label: 'Edit', action: () => _onEdit(bookmark) },
    { id: 'copy-url', icon: <DocumentDuplicateIcon className="w-4 h-4" />, label: 'Copy URL', action: () => navigator.clipboard.writeText(bookmark.url) },
    { id: 'share', icon: <ShareIcon className="w-4 h-4" />, label: 'Share', action: () => {} },
    { id: 'delete', icon: <TrashIcon className="w-4 h-4" />, label: 'Delete', action: () => onDelete(bookmark.id), danger: true },
  ];

  if (viewMode === 'list') {
    return (
      <Card 
        className={`bookmark-card list-view ${isSelected ? 'selected' : ''}`}
        padding="md"
        hover
      >
        <div className="flex items-center gap-4">
          {onSelect && (
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect?.(bookmark.id, e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          )}
          
          <div className="flex items-center gap-3 flex-1">
            <div className="bookmark-favicon">
              {favicon && !imageError ? (
                <img
                  src={favicon}
                  alt=""
                  onError={handleImageError}
                  className="w-6 h-6 rounded"
                />
              ) : (
                <div
                  className="w-6 h-6 rounded flex items-center justify-center text-white text-sm font-medium"
                  style={{ backgroundColor: getDomainColor(domain) }}
                >
                  {domain.charAt(0).toUpperCase()}
                </div>
              )}
            </div>

            <div className="flex-1 min-w-0">
              <h3 className="text-lg font-semibold text-gray-900 truncate">
                <a
                  href={bookmark.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  onClick={() => onVisit?.(bookmark.id, bookmark.url)}
                  className="hover:text-blue-600 transition-colors"
                >
                  {bookmark.title}
                </a>
              </h3>
              <p className="text-sm text-gray-500">{domain}</p>
              {description && (
                <p className="text-sm text-gray-700 mt-1 line-clamp-2">{description}</p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-4">
            <div className="flex flex-wrap gap-1">
              {tags.slice(0, 3).map((tag, index) => (
                <span key={index} className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                  <TagIcon className="w-3 h-3" />
                  {tag}
                </span>
              ))}
              {tags.length > 3 && (
                <span className="text-xs text-gray-500">+{tags.length - 3}</span>
              )}
            </div>

            <div className="flex items-center gap-1 text-sm text-gray-500">
              <CalendarIcon className="w-4 h-4" />
              {formatDate(dateAdded)}
            </div>

            <div className="flex items-center gap-2">
              <Button
                variant={isFavorite ? "primary" : "ghost"}
                size="sm"
                onClick={() => onToggleFavorite(bookmark.id, !isFavorite)}
                className="p-2"
              >
                <StarIcon className={`w-4 h-4 ${isFavorite ? 'fill-current' : ''}`} />
              </Button>

              <Dropdown
                trigger={
                  <Button variant="ghost" size="sm" className="p-2">
                    <EllipsisHorizontalIcon className="w-4 h-4" />
                  </Button>
                }
                items={menuItems}
                placement="bottom-right"
              />
            </div>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card 
      className={`bookmark-card grid-view ${isSelected ? 'selected' : ''}`}
      padding="none"
      hover
    >
      {onSelect && (
        <div className="absolute top-2 left-2 z-10">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={(e) => onSelect?.(bookmark.id, e.target.checked)}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
          />
        </div>
      )}
      
      <div className="relative aspect-video bg-gray-100 overflow-hidden">
        {thumbnail && !imageError ? (
          <img
            src={thumbnail}
            alt={bookmark.title}
            onError={handleImageError}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div
              className="w-16 h-16 rounded-full flex items-center justify-center text-white text-2xl font-bold"
              style={{ backgroundColor: getDomainColor(domain) }}
            >
              {domain.charAt(0).toUpperCase()}
            </div>
          </div>
        )}

        <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 hover:opacity-100">
          <Button
            variant="primary"
            size="sm"
            onClick={() => onVisit?.(bookmark.id, bookmark.url)}
            className="bg-white text-gray-900 hover:bg-gray-100"
          >
            <ArrowTopRightOnSquareIcon className="w-4 h-4" />
          </Button>
        </div>
      </div>

      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <div className="bookmark-favicon">
              {favicon && !imageError ? (
                <img
                  src={favicon}
                  alt=""
                  onError={handleImageError}
                  className="w-4 h-4 rounded"
                />
              ) : (
                <div
                  className="w-4 h-4 rounded flex items-center justify-center text-white text-xs font-medium"
                  style={{ backgroundColor: getDomainColor(domain) }}
                >
                  {domain.charAt(0).toUpperCase()}
                </div>
              )}
            </div>
            <span className="text-sm text-gray-500 truncate">{domain}</span>
          </div>

          <div className="flex items-center gap-1">
            <Button
              variant={isFavorite ? "primary" : "ghost"}
              size="sm"
              onClick={() => onToggleFavorite(bookmark.id, !isFavorite)}
              className="p-1"
            >
              <StarIcon className={`w-3 h-3 ${isFavorite ? 'fill-current' : ''}`} />
            </Button>

            <Dropdown
              trigger={
                <Button variant="ghost" size="sm" className="p-1">
                  <EllipsisHorizontalIcon className="w-3 h-3" />
                </Button>
              }
              items={menuItems}
              placement="bottom-right"
            />
          </div>
        </div>

        <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2">
          <a
            href={bookmark.url}
            target="_blank"
            rel="noopener noreferrer"
            onClick={() => onVisit?.(bookmark.id, bookmark.url)}
            className="hover:text-blue-600 transition-colors"
          >
            {bookmark.title}
          </a>
        </h3>

        {description && (
          <p className="text-sm text-gray-600 mb-3 line-clamp-3">{description}</p>
        )}

        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-1">
            {tags.slice(0, 2).map((tag, index) => (
              <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                {tag}
              </span>
            ))}
            {tags.length > 2 && (
              <span className="text-xs text-gray-500">+{tags.length - 2}</span>
            )}
          </div>

          <div className="text-xs text-gray-500">
            {formatDate(dateAdded)}
          </div>
        </div>
      </div>
    </Card>
  );
};

export default ModernBookmarkCard;