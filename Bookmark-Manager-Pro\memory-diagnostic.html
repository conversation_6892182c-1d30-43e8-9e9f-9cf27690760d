<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emergency Memory Diagnostic</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .critical { background: #ffebee; color: #c62828; border-left: 4px solid #c62828; }
        .warning { background: #fff3e0; color: #ef6c00; border-left: 4px solid #ef6c00; }
        .success { background: #e8f5e8; color: #2e7d32; border-left: 4px solid #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; border-left: 4px solid #1565c0; }
        button {
            background: #1976d2;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover { background: #1565c0; }
        .emergency { background: #d32f2f; }
        .emergency:hover { background: #c62828; }
        #output {
            background: #263238;
            color: #00e676;
            padding: 20px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 Emergency Memory Diagnostic</h1>
        <p>Addressing <strong>63.2% memory growth</strong> issue</p>
        
        <div id="currentStatus" class="status info">
            📊 Checking current memory status...
        </div>
        
        <div class="stats" id="memoryStats">
            <!-- Memory stats will be populated here -->
        </div>
        
        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runQuickDiagnostic()">🔍 Run Quick Diagnostic</button>
            <button onclick="runEmergencyCleanup()" class="emergency">🧹 Emergency Cleanup</button>
            <button onclick="refreshStats()">🔄 Refresh Stats</button>
            <button onclick="clearOutput()">🗑️ Clear Output</button>
        </div>
        
        <div id="output"></div>
        
        <div class="status info">
            💡 <strong>Quick Actions:</strong><br>
            • Open browser DevTools (F12) → Console for detailed logs<br>
            • Use "Emergency Cleanup" if memory usage > 60%<br>
            • Monitor stats after cleanup to verify improvement
        </div>
    </div>

    <script src="emergency-memory-diagnostic.js"></script>
    <script>
        let outputElement = document.getElementById('output');
        let statusElement = document.getElementById('currentStatus');
        let statsElement = document.getElementById('memoryStats');
        
        // Override console.log to show in our output
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            outputElement.textContent += args.join(' ') + '\n';
            outputElement.scrollTop = outputElement.scrollHeight;
        };
        
        function updateMemoryStats() {
            if (!('memory' in performance)) {
                statusElement.className = 'status warning';
                statusElement.innerHTML = '⚠️ Memory API not available in this browser';
                return;
            }
            
            const memory = performance.memory;
            const used = Math.round(memory.usedJSHeapSize / 1024 / 1024);
            const total = Math.round(memory.totalJSHeapSize / 1024 / 1024);
            const limit = Math.round(memory.jsHeapSizeLimit / 1024 / 1024);
            const usagePercentage = Math.round((memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100);
            
            // Update status
            if (usagePercentage >= 80) {
                statusElement.className = 'status critical';
                statusElement.innerHTML = `🚨 CRITICAL: ${usagePercentage}% memory usage - Immediate cleanup required!`;
            } else if (usagePercentage >= 60) {
                statusElement.className = 'status warning';
                statusElement.innerHTML = `⚠️ HIGH: ${usagePercentage}% memory usage - Cleanup recommended`;
            } else {
                statusElement.className = 'status success';
                statusElement.innerHTML = `✅ NORMAL: ${usagePercentage}% memory usage`;
            }
            
            // Update stats cards
            statsElement.innerHTML = `
                <div class="stat-card">
                    <div class="stat-value">${used} MB</div>
                    <div class="stat-label">Used Memory</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${total} MB</div>
                    <div class="stat-label">Total Allocated</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">${limit} MB</div>
                    <div class="stat-label">Memory Limit</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" style="color: ${usagePercentage >= 60 ? '#d32f2f' : '#1976d2'}">${usagePercentage}%</div>
                    <div class="stat-label">Usage Percentage</div>
                </div>
            `;
        }
        
        function runQuickDiagnostic() {
            outputElement.textContent = '';
            console.log('🔍 Starting quick diagnostic...');
            EmergencyMemoryDiagnostic.quickCleanup().then(() => {
                updateMemoryStats();
                console.log('✅ Diagnostic completed!');
            });
        }
        
        function runEmergencyCleanup() {
            outputElement.textContent = '';
            console.log('🚨 Starting emergency cleanup...');
            const diagnostic = new EmergencyMemoryDiagnostic();
            diagnostic.performEmergencyCleanup().then(() => {
                updateMemoryStats();
                console.log('🎉 Emergency cleanup completed!');
            });
        }
        
        function refreshStats() {
            updateMemoryStats();
            console.log('📊 Memory stats refreshed at ' + new Date().toLocaleTimeString());
        }
        
        function clearOutput() {
            outputElement.textContent = '';
        }
        
        // Auto-refresh stats every 5 seconds
        setInterval(updateMemoryStats, 5000);
        
        // Initial load
        updateMemoryStats();
        
        // Auto-run diagnostic if memory is high
        setTimeout(() => {
            if ('memory' in performance) {
                const currentUsage = (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit) * 100;
                if (currentUsage >= 63) {
                    console.log('🚨 Detected high memory usage (' + currentUsage.toFixed(1) + '%) - Auto-running diagnostic...');
                    runQuickDiagnostic();
                }
            }
        }, 1000);
    </script>
</body>
</html>