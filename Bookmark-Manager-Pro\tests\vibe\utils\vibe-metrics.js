// Vibe Metrics Collection System
// Measures emotional responses and micro-UX quality

export class VibeMetrics {
  
  static async measureResponseTime(page, action, expectedThreshold = 50) {
    const start = performance.now();
    await action();
    const duration = performance.now() - start;
    
    return {
      duration,
      threshold: expectedThreshold,
      passed: duration <= expectedThreshold,
      quality: this.getResponseQuality(duration, expectedThreshold)
    };
  }
  
  static getResponseQuality(duration, threshold) {
    if (duration <= threshold * 0.5) return 'excellent';
    if (duration <= threshold * 0.75) return 'good';
    if (duration <= threshold) return 'acceptable';
    if (duration <= threshold * 1.5) return 'poor';
    return 'unacceptable';
  }
  
  static async detectCognitiveLoad(page) {
    const metrics = await page.evaluate(() => {
      // Count interactive elements visible simultaneously
      const interactiveElements = document.querySelectorAll(
        'button:not([disabled]), input:not([disabled]), [role="button"]:not([aria-disabled="true"]), a[href]'
      );
      
      // Count visual distractions
      const animations = document.querySelectorAll('[style*="animation"], .animate-');
      const coloredElements = document.querySelectorAll('[style*="color"], [class*="text-"]');
      
      // Measure visual complexity
      const visibleElements = document.querySelectorAll('*:not([hidden]):not([style*="display: none"])');
      
      return {
        interactiveCount: interactiveElements.length,
        animationCount: animations.length,
        colorVariations: coloredElements.length,
        totalVisible: visibleElements.length,
        viewportArea: window.innerWidth * window.innerHeight
      };
    });
    
    // Calculate cognitive load score
    const density = metrics.totalVisible / (metrics.viewportArea / 10000);
    const interactionComplexity = metrics.interactiveCount > 15 ? 'high' : 'normal';
    const visualNoise = metrics.animationCount > 5 ? 'high' : 'low';
    
    return {
      ...metrics,
      density,
      interactionComplexity,
      visualNoise,
      overallLoad: this.calculateOverallCognitiveLoad(metrics, density)
    };
  }
  
  static calculateOverallCognitiveLoad(metrics, density) {
    let score = 0;
    
    // Interactive element density
    if (metrics.interactiveCount > 20) score += 3;
    else if (metrics.interactiveCount > 15) score += 2;
    else if (metrics.interactiveCount > 10) score += 1;
    
    // Visual density
    if (density > 50) score += 3;
    else if (density > 30) score += 2;
    else if (density > 20) score += 1;
    
    // Animation noise
    if (metrics.animationCount > 5) score += 2;
    else if (metrics.animationCount > 2) score += 1;
    
    if (score >= 6) return 'overwhelming';
    if (score >= 4) return 'high';
    if (score >= 2) return 'moderate';
    return 'low';
  }
  
  static async validateEmotionalFeedback(page, selector) {
    const element = page.locator(selector);
    
    const feedback = await element.evaluate((el) => {
      const styles = getComputedStyle(el);
      
      return {
        // Visual feedback indicators
        hasAnimation: styles.animationName !== 'none',
        hasTransition: styles.transitionDuration !== '0s',
        hasColorChange: styles.color !== 'rgb(0, 0, 0)', // Not default black
        hasBackgroundChange: styles.backgroundColor !== 'rgba(0, 0, 0, 0)', // Not transparent
        hasBorderChange: styles.borderWidth !== '0px',
        hasShadow: styles.boxShadow !== 'none',
        
        // Accessibility feedback
        hasAriaLabel: el.hasAttribute('aria-label'),
        hasAriaDescribedBy: el.hasAttribute('aria-describedby'),
        hasRole: el.hasAttribute('role'),
        
        // State indicators
        isDisabled: el.disabled || el.getAttribute('aria-disabled') === 'true',
        isPressed: el.getAttribute('aria-pressed') === 'true',
        isSelected: el.getAttribute('aria-selected') === 'true'
      };
    });
    
    // Calculate emotional feedback score
    const visualScore = [
      feedback.hasAnimation,
      feedback.hasTransition,
      feedback.hasColorChange,
      feedback.hasBackgroundChange,
      feedback.hasBorderChange,
      feedback.hasShadow
    ].filter(Boolean).length;
    
    const accessibilityScore = [
      feedback.hasAriaLabel,
      feedback.hasAriaDescribedBy,
      feedback.hasRole
    ].filter(Boolean).length;
    
    return {
      ...feedback,
      visualScore,
      accessibilityScore,
      overallQuality: this.getEmotionalFeedbackQuality(visualScore, accessibilityScore)
    };
  }
  
  static getEmotionalFeedbackQuality(visualScore, accessibilityScore) {
    const totalScore = visualScore + accessibilityScore;
    
    if (totalScore >= 6 && accessibilityScore >= 2) return 'excellent';
    if (totalScore >= 4 && accessibilityScore >= 1) return 'good';
    if (totalScore >= 2) return 'acceptable';
    return 'poor';
  }
  
  static async measureFlowDisruption(page, actions) {
    const disruptions = [];
    let totalTime = 0;
    
    for (let i = 0; i < actions.length; i++) {
      const action = actions[i];
      const startTime = performance.now();
      
      try {
        // Execute action
        await action.execute(page);
        
        // Check for flow disruptions
        const disruption = await this.detectFlowDisruption(page, action);
        if (disruption.detected) {
          disruptions.push({
            actionIndex: i,
            actionName: action.name,
            disruption
          });
        }
        
        totalTime += performance.now() - startTime;
        
      } catch (error) {
        disruptions.push({
          actionIndex: i,
          actionName: action.name,
          disruption: {
            detected: true,
            type: 'error',
            severity: 'high',
            description: error.message
          }
        });
      }
    }
    
    return {
      totalActions: actions.length,
      totalTime,
      averageActionTime: totalTime / actions.length,
      disruptionCount: disruptions.length,
      disruptionRate: disruptions.length / actions.length,
      disruptions,
      flowQuality: this.calculateFlowQuality(disruptions.length, actions.length, totalTime)
    };
  }
  
  static async detectFlowDisruption(page, action) {
    // Check for common flow disruptors
    const disruptors = await page.evaluate(() => {
      return {
        // Modal dialogs that interrupt
        hasModal: document.querySelector('[role="dialog"], .modal, .popup') !== null,
        
        // Loading states that create anxiety
        hasLoadingSpinner: document.querySelector('.loading, .spinner, [aria-busy="true"]') !== null,
        
        // Error messages that break confidence
        hasErrorMessage: document.querySelector('.error, [role="alert"], .alert-error') !== null,
        
        // Confirmation dialogs that break flow
        hasConfirmation: document.querySelector('.confirm, [role="alertdialog"]') !== null,
        
        // Network loading indicators
        hasNetworkIndicator: document.querySelector('.offline, .network-error, .sync-pending') !== null
      };
    });
    
    // Determine disruption severity
    let severity = 'none';
    let type = null;
    let description = '';
    
    if (disruptors.hasModal) {
      severity = 'high';
      type = 'modal-interruption';
      description = 'Modal dialog interrupted user flow';
    } else if (disruptors.hasConfirmation) {
      severity = 'medium';
      type = 'confirmation-interruption';
      description = 'Confirmation dialog required user decision';
    } else if (disruptors.hasErrorMessage) {
      severity = 'high';
      type = 'error-interruption';
      description = 'Error message broke user confidence';
    } else if (disruptors.hasLoadingSpinner) {
      severity = 'low';
      type = 'loading-interruption';
      description = 'Loading state created waiting anxiety';
    } else if (disruptors.hasNetworkIndicator) {
      severity = 'medium';
      type = 'network-interruption';
      description = 'Network issue indicator appeared';
    }
    
    return {
      detected: severity !== 'none',
      type,
      severity,
      description,
      disruptors
    };
  }
  
  static calculateFlowQuality(disruptionCount, totalActions, totalTime) {
    const disruptionRate = disruptionCount / totalActions;
    const averageTime = totalTime / totalActions;
    
    // Flow quality based on disruption rate and timing
    if (disruptionRate === 0 && averageTime < 100) return 'excellent';
    if (disruptionRate < 0.1 && averageTime < 200) return 'good';
    if (disruptionRate < 0.2 && averageTime < 500) return 'acceptable';
    if (disruptionRate < 0.3) return 'poor';
    return 'unacceptable';
  }
  
  static async measureSuggestionQuality(page, suggestions) {
    const quality = await page.evaluate((suggestions) => {
      const metrics = {
        totalSuggestions: suggestions.length,
        relevantSuggestions: 0,
        timingAppropriate: 0,
        userFriendly: 0,
        actionable: 0
      };
      
      suggestions.forEach(suggestion => {
        // Check relevance (would need AI/ML validation in real implementation)
        if (suggestion.relevanceScore > 0.7) {
          metrics.relevantSuggestions++;
        }
        
        // Check timing appropriateness
        if (suggestion.timing === 'natural-pause' || suggestion.timing === 'workflow-break') {
          metrics.timingAppropriate++;
        }
        
        // Check user-friendliness
        if (suggestion.dismissible && !suggestion.intrusive) {
          metrics.userFriendly++;
        }
        
        // Check actionability
        if (suggestion.hasAction && suggestion.actionClear) {
          metrics.actionable++;
        }
      });
      
      return metrics;
    }, suggestions);
    
    // Calculate quality scores
    const relevanceRate = quality.relevantSuggestions / quality.totalSuggestions;
    const timingRate = quality.timingAppropriate / quality.totalSuggestions;
    const friendlinessRate = quality.userFriendly / quality.totalSuggestions;
    const actionabilityRate = quality.actionable / quality.totalSuggestions;
    
    const overallScore = (relevanceRate + timingRate + friendlinessRate + actionabilityRate) / 4;
    
    return {
      ...quality,
      rates: {
        relevance: relevanceRate,
        timing: timingRate,
        friendliness: friendlinessRate,
        actionability: actionabilityRate
      },
      overallScore,
      quality: this.getSuggestionQualityRating(overallScore)
    };
  }
  
  static getSuggestionQualityRating(score) {
    if (score >= 0.9) return 'excellent';
    if (score >= 0.7) return 'good';
    if (score >= 0.5) return 'acceptable';
    if (score >= 0.3) return 'poor';
    return 'unacceptable';
  }
  
  static async measureBulkOperationAnxiety(page, operation) {
    const anxietyIndicators = await page.evaluate(() => {
      return {
        // Confidence indicators
        hasPreview: document.querySelector('.bulk-preview, .operation-preview') !== null,
        hasUndo: document.querySelector('.undo, [data-action="undo"]') !== null,
        hasConfirmation: document.querySelector('.confirm-bulk, .bulk-confirm') !== null,
        hasProgress: document.querySelector('.progress, .bulk-progress') !== null,
        
        // Anxiety reducers
        showsCount: document.querySelector('.selection-count, .bulk-count') !== null,
        showsAffected: document.querySelector('.affected-items, .bulk-affected') !== null,
        allowsCancel: document.querySelector('.cancel, [data-action="cancel"]') !== null,
        
        // Feedback quality
        hasImmediateFeedback: document.querySelector('.bulk-feedback, .operation-feedback') !== null,
        hasSuccessMessage: document.querySelector('.success, .bulk-success') !== null
      };
    });
    
    // Calculate anxiety reduction score
    const confidenceScore = [
      anxietyIndicators.hasPreview,
      anxietyIndicators.hasUndo,
      anxietyIndicators.hasProgress
    ].filter(Boolean).length;
    
    const clarityScore = [
      anxietyIndicators.showsCount,
      anxietyIndicators.showsAffected,
      anxietyIndicators.allowsCancel
    ].filter(Boolean).length;
    
    const feedbackScore = [
      anxietyIndicators.hasImmediateFeedback,
      anxietyIndicators.hasSuccessMessage
    ].filter(Boolean).length;
    
    const totalScore = confidenceScore + clarityScore + feedbackScore;
    
    return {
      ...anxietyIndicators,
      scores: {
        confidence: confidenceScore,
        clarity: clarityScore,
        feedback: feedbackScore,
        total: totalScore
      },
      anxietyLevel: this.getBulkOperationAnxietyLevel(totalScore),
      recommendations: this.getBulkOperationRecommendations(anxietyIndicators)
    };
  }
  
  static getBulkOperationAnxietyLevel(score) {
    if (score >= 7) return 'very-low'; // Excellent UX
    if (score >= 5) return 'low';      // Good UX
    if (score >= 3) return 'medium';   // Acceptable UX
    if (score >= 1) return 'high';     // Poor UX
    return 'very-high';                // Unacceptable UX
  }
  
  static getBulkOperationRecommendations(indicators) {
    const recommendations = [];
    
    if (!indicators.hasPreview) {
      recommendations.push('Add operation preview to build user confidence');
    }
    if (!indicators.hasUndo) {
      recommendations.push('Implement undo functionality to reduce operation anxiety');
    }
    if (!indicators.showsCount) {
      recommendations.push('Display selection count to clarify operation scope');
    }
    if (!indicators.hasProgress) {
      recommendations.push('Add progress indicator for long operations');
    }
    if (!indicators.hasImmediateFeedback) {
      recommendations.push('Provide immediate feedback for operation completion');
    }
    
    return recommendations;
  }
}