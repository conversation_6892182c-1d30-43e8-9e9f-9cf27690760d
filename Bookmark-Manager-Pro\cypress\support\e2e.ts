// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';
import './layout-commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Import Cypress Axe for accessibility testing
import 'cypress-axe';

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Returning false here prevents <PERSON><PERSON> from failing the test
  // on uncaught exceptions. You can customize this based on your needs.
  console.log('Uncaught exception:', err.message);
  
  // Don't fail tests on these specific errors
  if (
    err.message.includes('ResizeObserver loop limit exceeded') ||
    err.message.includes('Non-Error promise rejection captured')
  ) {
    return false;
  }
  
  // Let other errors fail the test
  return true;
});

// Custom commands for common test operations
Cypress.Commands.add('login', () => {
  // Add login logic if authentication is implemented
  cy.log('Login command - implement when auth is added');
});

Cypress.Commands.add('logout', () => {
  // Add logout logic if authentication is implemented
  cy.log('Logout command - implement when auth is added');
});

// Command to clear all bookmarks
Cypress.Commands.add('clearBookmarks', () => {
  cy.window().then((win) => {
    win.localStorage.clear();
    win.sessionStorage.clear();
  });
});

// Command to seed test data
Cypress.Commands.add('seedBookmarks', (bookmarks) => {
  cy.window().then((win) => {
    win.localStorage.setItem('bookmarks', JSON.stringify(bookmarks));
  });
});

// Command to wait for the app to be ready
Cypress.Commands.add('waitForApp', () => {
  cy.get('[data-testid="app-container"]', { timeout: 10000 }).should('be.visible');
});

// Command to add a bookmark via UI
Cypress.Commands.add('addBookmark', (url: string, title?: string) => {
  cy.get('[data-testid="add-bookmark-button"]').click();
  cy.get('[data-testid="bookmark-url-input"]').type(url);
  
  if (title) {
    cy.get('[data-testid="bookmark-title-input"]').clear().type(title);
  }
  
  cy.get('[data-testid="save-bookmark-button"]').click();
});

// Command to search bookmarks
Cypress.Commands.add('searchBookmarks', (query: string) => {
  cy.get('[data-testid="search-input"]').clear().type(query);
  cy.get('[data-testid="search-button"]').click();
});

// Command to check accessibility
Cypress.Commands.add('checkA11y', (context?: string, options?: any) => {
  cy.injectAxe();
  cy.checkA11y(context, options);
});

// Command to take a screenshot with timestamp
Cypress.Commands.add('screenshotWithTimestamp', (name: string) => {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  cy.screenshot(`${name}-${timestamp}`);
});

// Command to wait for network requests to complete
Cypress.Commands.add('waitForNetworkIdle', (timeout = 1000) => {
  cy.window().then((win) => {
    return new Cypress.Promise((resolve) => {
      let requestCount = 0;
      let timer: NodeJS.Timeout;
      
      const originalFetch = win.fetch;
      win.fetch = (...args) => {
        requestCount++;
        clearTimeout(timer);
        
        return originalFetch(...args).finally(() => {
          requestCount--;
          if (requestCount === 0) {
            timer = setTimeout(resolve, timeout);
          }
        });
      };
      
      // Start the timer immediately if no requests are pending
      if (requestCount === 0) {
        timer = setTimeout(resolve, timeout);
      }
    });
  });
});

// Declare custom commands for TypeScript
declare global {
  namespace Cypress {
    interface Chainable {
      login(): Chainable<void>;
      logout(): Chainable<void>;
      clearBookmarks(): Chainable<void>;
      seedBookmarks(bookmarks: any[]): Chainable<void>;
      waitForApp(): Chainable<void>;
      addBookmark(url: string, title?: string): Chainable<void>;
      searchBookmarks(query: string): Chainable<void>;
      checkA11y(context?: string, options?: any): Chainable<void>;
      screenshotWithTimestamp(name: string): Chainable<void>;
      waitForNetworkIdle(timeout?: number): Chainable<void>;
    }
  }
}