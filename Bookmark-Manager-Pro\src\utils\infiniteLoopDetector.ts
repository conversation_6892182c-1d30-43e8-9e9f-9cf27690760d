/**
 * Infinite Loop Detector
 * Utility to detect and prevent infinite re-render loops in React components
 */
import React from 'react'

export class InfiniteLoopDetector {
  private static renderCounts = new Map<string, number>()
  private static lastRenderTime = new Map<string, number>()
  private static readonly MAX_RENDERS_PER_SECOND = 100
  private static readonly RESET_INTERVAL = 1000 // 1 second

  /**
   * Track component renders and detect infinite loops
   */
  static trackRender(componentName: string): boolean {
    const now = Date.now()
    const lastTime = this.lastRenderTime.get(componentName) || 0
    const count = this.renderCounts.get(componentName) || 0

    // Reset counter if more than 1 second has passed
    if (now - lastTime > this.RESET_INTERVAL) {
      this.renderCounts.set(componentName, 1)
      this.lastRenderTime.set(componentName, now)
      return false
    }

    // Increment render count
    const newCount = count + 1
    this.renderCounts.set(componentName, newCount)
    this.lastRenderTime.set(componentName, now)

    // Check for infinite loop
    if (newCount > this.MAX_RENDERS_PER_SECOND) {
      console.error(`🚨 INFINITE LOOP DETECTED in ${componentName}!`)
      console.error(`Component rendered ${newCount} times in ${now - lastTime}ms`)
      console.error('This indicates a dependency loop in useEffect or useState')
      
      // Reset to prevent further spam
      this.renderCounts.set(componentName, 0)
      return true
    }

    return false
  }

  /**
   * Get current render statistics
   */
  static getStats(): { [componentName: string]: { count: number; lastRender: number } } {
    const stats: { [componentName: string]: { count: number; lastRender: number } } = {}
    
    this.renderCounts.forEach((count, componentName) => {
      stats[componentName] = {
        count,
        lastRender: this.lastRenderTime.get(componentName) || 0
      }
    })

    return stats
  }

  /**
   * Clear all tracking data
   */
  static reset(): void {
    this.renderCounts.clear()
    this.lastRenderTime.clear()
  }

  /**
   * React hook to automatically track component renders
   */
  static useRenderTracking(componentName: string): void {
    React.useEffect(() => {
      const hasInfiniteLoop = InfiniteLoopDetector.trackRender(componentName)
      
      if (hasInfiniteLoop) {
        // Force component to stop re-rendering by throwing an error
        throw new Error(`Infinite loop detected in ${componentName}. Check useEffect dependencies.`)
      }
    })
  }
}

/**
 * React hook for automatic infinite loop detection
 */
export const useInfiniteLoopDetection = (componentName: string) => {
  React.useEffect(() => {
    const hasInfiniteLoop = InfiniteLoopDetector.trackRender(componentName)
    
    if (hasInfiniteLoop) {
      console.warn(`⚠️ Potential infinite loop in ${componentName}. Monitoring...`)
    }
  })

  return {
    getStats: () => InfiniteLoopDetector.getStats(),
    reset: () => InfiniteLoopDetector.reset()
  }
}

/**
 * Debug utility to log useEffect dependency changes
 */
export const useEffectDebugger = (effectHook: React.EffectCallback, dependencies: React.DependencyList, depNames: string[]) => {
  const previousDeps = React.useRef<React.DependencyList>()
  const changedDeps = React.useRef<string[]>([])

  React.useEffect(() => {
    if (previousDeps.current) {
      changedDeps.current = []
      
      dependencies.forEach((dep, index) => {
        if (dep !== previousDeps.current![index]) {
          changedDeps.current.push(depNames[index] || `dep${index}`)
        }
      })

      if (changedDeps.current.length > 0) {
        console.log('🔄 useEffect triggered by:', changedDeps.current.join(', '))
      }
    }

    previousDeps.current = dependencies
    return effectHook()
  }, dependencies)
}

// Global monitoring
if (typeof window !== 'undefined') {
  // Log stats every 5 seconds in development
  if (process.env.NODE_ENV === 'development') {
    setInterval(() => {
      const stats = InfiniteLoopDetector.getStats()
      const activeComponents = Object.keys(stats).filter(name => stats[name].count > 0)
      
      if (activeComponents.length > 0) {
        console.log('📊 Render Stats:', stats)
      }
    }, 5000)
  }
}
