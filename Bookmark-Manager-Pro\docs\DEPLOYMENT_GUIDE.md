# Deployment Guide - Bookmark Manager Pro

## Overview

This guide provides comprehensive instructions for deploying the Bookmark Manager Pro application to various platforms and environments.

⚠️ **DEPLOYMENT UPDATE** - Updated for new architecture:
- **State Management**: Context providers require proper SSR considerations
- **Bundle Optimization**: New code splitting for context and hooks
- **Performance Monitoring**: Updated metrics for new architecture
- **Build Process**: Enhanced build steps for refactored components

## Prerequisites

- Node.js 18+ and npm
- Git repository access
- Gemini API key for AI features
- Build artifacts ready

### Node.js Installation (If Required)

**For Linux/Unix Systems (Ubuntu/Debian)**

If you encounter PATH issues or need to install Node.js on your deployment server:

1. **Fix PATH Issues**
   ```bash
   export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"
   ```

2. **Verify sudo works**
   ```bash
   sudo --version
   ```

3. **Install Node.js via snap (recommended)**
   ```bash
   sudo snap install node --channel=20/stable --classic
   ```

4. **Verify installation**
   ```bash
   node --version
   npm --version
   ```

5. **Make PATH permanent**
   ```bash
   # Add to bashrc for permanent fix
   echo 'export PATH="/usr/bin:/bin:/usr/sbin:/sbin:$PATH"' >> ~/.bashrc
   source ~/.bashrc
   ```

**For Windows/macOS**
- Download from [nodejs.org](https://nodejs.org/) (LTS version recommended)
- Follow the installer instructions

**For Production Servers**
- Consider using Node Version Manager (nvm) for better version control
- Ensure Node.js is installed system-wide for service deployments

## Environment Variables

Required environment variables:
```bash
VITE_GEMINI_API_KEY=your_gemini_api_key_here
```

**Security Note**: Never commit API keys to version control. Use environment-specific configuration for production deployments.

### Production Environment Variables

For production deployments, ensure these environment variables are set:

```env
# Required
VITE_GEMINI_API_KEY=your_production_gemini_api_key

# Optional - for analytics and monitoring
VITE_ANALYTICS_ID=your_analytics_id
VITE_SENTRY_DSN=your_sentry_dsn
```

## Build Process

### Development Build

```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

### Production Build (Updated for New Architecture)

```bash
# Install dependencies
npm install

# Type check (critical for new architecture)
npm run type-check

# Run tests (ensure state management works)
npm run test

# Build for production
npm run build

# Preview production build locally
npm run preview
```

### Build Optimization for New Architecture

**Code Splitting Configuration:**
- Context providers are automatically code-split
- Custom hooks are bundled efficiently
- Base components are shared across chunks

**Bundle Analysis:**
```bash
# Analyze bundle size
npm run build:analyze

# Check for duplicate dependencies
npm run build:check-deps
```

**Performance Considerations:**
- Context providers are lazy-loaded where possible
- Custom hooks use React.useMemo for expensive computations
- Base components implement React.memo for re-render optimization

## Deployment Platforms

## Platform-Specific Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository**
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Login and deploy
   vercel login
   vercel --prod
   ```

2. **Environment Variables**
   - Add `VITE_GEMINI_API_KEY` in Vercel dashboard
   - Go to Project Settings → Environment Variables
   - Add variable for Production, Preview, and Development

3. **Build Configuration**
   - Vercel auto-detects Vite projects
   - Build command: `npm run build`
   - Output directory: `dist`
   - Framework preset: Vite

### Netlify Deployment

1. **Connect Repository**
   - Link GitHub repository in Netlify dashboard
   - Or use Netlify CLI: `netlify deploy --prod`

2. **Build Settings**
   - Build command: `npm run build`
   - Publish directory: `dist`
   - Node version: 18+

3. **Environment Variables**
   - Add `VITE_GEMINI_API_KEY` in site settings
   - Go to Site Settings → Environment Variables

4. **Redirects Configuration**
   Create `public/_redirects` file:
   ```
   /*    /index.html   200
   ```

### GitHub Pages Deployment

1. **Enable GitHub Pages**
   - Go to repository Settings → Pages
   - Select source: GitHub Actions

2. **Create Workflow**
   Create `.github/workflows/deploy.yml`:
   ```yaml
   name: Deploy to GitHub Pages
   
   on:
     push:
       branches: [ main ]
   
   jobs:
     deploy:
       runs-on: ubuntu-latest
       permissions:
         contents: read
         pages: write
         id-token: write
       steps:
         - uses: actions/checkout@v4
         - uses: actions/setup-node@v4
           with:
             node-version: '18'
             cache: 'npm'
         - run: npm ci
         - run: npm run build
           env:
             VITE_GEMINI_API_KEY: ${{ secrets.VITE_GEMINI_API_KEY }}
         - uses: actions/upload-pages-artifact@v2
           with:
             path: dist
         - uses: actions/deploy-pages@v2
   ```

3. **Add Repository Secrets**
   - Go to Settings → Secrets and variables → Actions
   - Add `VITE_GEMINI_API_KEY` secret

### Docker Deployment

1. **Create Dockerfile**
   ```dockerfile
   FROM node:18-alpine AS builder
   WORKDIR /app
   COPY package*.json ./
   RUN npm ci
   COPY . .
   ARG VITE_GEMINI_API_KEY
   ENV VITE_GEMINI_API_KEY=$VITE_GEMINI_API_KEY
   RUN npm run build
   
   FROM nginx:alpine
   COPY --from=builder /app/dist /usr/share/nginx/html
   COPY nginx.conf /etc/nginx/nginx.conf
   EXPOSE 80
   CMD ["nginx", "-g", "daemon off;"]
   ```

2. **Create nginx.conf**
   ```nginx
   events {
     worker_connections 1024;
   }
   
   http {
     include /etc/nginx/mime.types;
     default_type application/octet-stream;
     
     server {
       listen 80;
       server_name localhost;
       root /usr/share/nginx/html;
       index index.html;
       
       location / {
         try_files $uri $uri/ /index.html;
       }
     }
   }
   ```

3. **Build and Run**
   ```bash
   docker build --build-arg VITE_GEMINI_API_KEY=your_key -t bookmark-manager .
   docker run -p 80:80 bookmark-manager
   ```

### AWS S3 + CloudFront

#### Prerequisites
- AWS CLI configured
- S3 bucket created
- CloudFront distribution set up

#### Deployment Script

```bash
#!/bin/bash

# Build the project
npm run build

# Sync to S3
aws s3 sync dist/ s3://your-bucket-name --delete

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id YOUR_DISTRIBUTION_ID --paths "/*"
```

### 5. Docker Deployment

#### Dockerfile

```dockerfile
# Build stage
FROM node:18-alpine as build

WORKDIR /app

# Copy package files
COPY package*.json ./
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built assets
COPY --from=build /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

EXPOSE 80

CMD ["nginx", "-g", "daemon off;"]
```

#### nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    server {
        listen 80;
        server_name localhost;
        
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }
        
        # Security headers
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
    }
}
```

#### Docker Commands

```bash
# Build image
docker build -t bookmark-manager-pro .

# Run container
docker run -p 80:80 bookmark-manager-pro
```

## Performance Optimization

### 1. Build Optimization

- **Code Splitting**: Vite automatically handles code splitting
- **Tree Shaking**: Remove unused code during build
- **Asset Optimization**: Images and assets are optimized

### 2. CDN Configuration

```javascript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          ui: ['@headlessui/react']
        }
      }
    }
  }
})
```

### 3. Caching Strategy

- **Static Assets**: Cache for 1 year with versioning
- **HTML**: Cache for 1 hour
- **API Responses**: Cache based on content type

## Security Considerations

### 1. Environment Variables

- Never expose API keys in client-side code
- Use environment-specific configurations
- Rotate API keys regularly

### 2. Content Security Policy

Add to your HTML head:

```html
<meta http-equiv="Content-Security-Policy" content="
  default-src 'self';
  script-src 'self' 'unsafe-inline';
  style-src 'self' 'unsafe-inline';
  connect-src 'self' https://generativelanguage.googleapis.com;
  img-src 'self' data: https:;
">
```

### 3. HTTPS Configuration

- Always use HTTPS in production
- Configure SSL certificates
- Enable HSTS headers

## Monitoring and Analytics

### 1. Error Tracking

Integrate Sentry for error monitoring:

```bash
npm install @sentry/react @sentry/tracing
```

### 2. Performance Monitoring

- Use Web Vitals for performance metrics
- Monitor bundle size with webpack-bundle-analyzer
- Set up uptime monitoring

### 3. Analytics

Integrate Google Analytics or similar:

```javascript
// Add to index.html
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
```

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Node.js version compatibility
   - Clear node_modules and reinstall
   - Verify environment variables

2. **API Key Issues**:
   - Ensure VITE_ prefix for client-side variables
   - Check API key validity
   - Verify environment variable configuration

3. **Routing Issues**:
   - Configure server for SPA routing
   - Check base URL configuration
   - Verify asset paths

### Debug Commands

```bash
# Check build output
npm run build -- --debug

# Analyze bundle size
npx vite-bundle-analyzer

# Check for security vulnerabilities
npm audit
```

## Rollback Strategy

### 1. Version Control

- Tag releases for easy rollback
- Maintain deployment logs
- Use feature flags for gradual rollouts

### 2. Database Backups

- Regular automated backups
- Test restore procedures
- Document recovery processes

## Maintenance

### 1. Regular Updates

- Update dependencies monthly
- Monitor security advisories
- Test updates in staging environment

### 2. Performance Reviews

- Monthly performance audits
- Monitor Core Web Vitals
- Optimize based on user feedback

## Support

For deployment issues:

1. Check the troubleshooting section
2. Review deployment logs
3. Consult platform-specific documentation
4. Create an issue in the project repository

---

**Last Updated**: December 2024
**Version**: 1.0.0