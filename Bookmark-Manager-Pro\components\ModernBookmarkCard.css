/* Modern Bookmark Card Styles */
.bookmark-card {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.bookmark-card:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.bookmark-card.selected {
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.bookmark-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

.bookmark-checkbox input {
  width: 16px;
  height: 16px;
  accent-color: #2563eb;
}

/* Grid View Styles */
.bookmark-card.grid-view {
  display: flex;
  flex-direction: column;
  height: 320px;
}

.bookmark-thumbnail {
  height: 160px;
  position: relative;
  overflow: hidden;
  background: #f9fafb;
}

.bookmark-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.thumbnail-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
}

.domain-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
  font-weight: 600;
}

.bookmark-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.bookmark-card:hover .bookmark-overlay {
  opacity: 1;
}

.overlay-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  color: #374151;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.overlay-btn:hover {
  background: #ffffff;
  transform: scale(1.1);
}

.bookmark-content {
  flex: 1;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.bookmark-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.bookmark-favicon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.bookmark-favicon img {
  width: 100%;
  height: 100%;
  border-radius: 4px;
}

.favicon-placeholder {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.favicon-placeholder.small {
  width: 16px;
  height: 16px;
  font-size: 8px;
}

.bookmark-domain {
  font-size: 12px;
  color: #6b7280;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.4;
  color: #1f2937;
}

.bookmark-title a {
  color: inherit;
  text-decoration: none;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-title a:hover {
  color: #2563eb;
}

.bookmark-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.bookmark-footer {
  margin-top: auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.bookmark-tags {
  display: flex;
  align-items: center;
  gap: 6px;
  flex: 1;
  overflow: hidden;
}

.tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 8px;
  background: #f3f4f6;
  color: #6b7280;
  font-size: 11px;
  font-weight: 500;
  border-radius: 12px;
  white-space: nowrap;
}

.tag-more {
  font-size: 11px;
  color: #9ca3af;
  font-weight: 500;
}

.bookmark-date {
  font-size: 11px;
  color: #9ca3af;
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
}

.bookmark-actions {
  display: flex;
  align-items: center;
  gap: 4px;
}

.action-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: none;
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #f3f4f6;
  color: #374151;
}

.action-btn.favorite.active {
  color: #f59e0b;
}

.action-btn.favorite.active:hover {
  color: #d97706;
}

/* List View Styles */
.bookmark-card.list-view {
  display: flex;
  align-items: center;
  padding: 16px;
  height: auto;
  min-height: 80px;
}

.bookmark-card.list-view .bookmark-content {
  flex: 1;
  padding: 0;
}

.bookmark-card.list-view .bookmark-header {
  margin-bottom: 8px;
}

.bookmark-card.list-view .bookmark-info {
  flex: 1;
  min-width: 0;
}

.bookmark-card.list-view .bookmark-title {
  font-size: 15px;
  margin: 0 0 4px 0;
}

.bookmark-card.list-view .bookmark-title a {
  -webkit-line-clamp: 1;
}

.bookmark-card.list-view .bookmark-url {
  font-size: 13px;
  color: #6b7280;
  margin: 0 0 4px 0;
}

.bookmark-card.list-view .bookmark-description {
  font-size: 13px;
  margin: 0;
  -webkit-line-clamp: 1;
}

.bookmark-card.list-view .bookmark-meta {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 8px;
}

.bookmark-card.list-view .bookmark-actions {
  margin-left: 16px;
}

/* Menu Styles */
.menu-container {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 50;
  min-width: 160px;
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 4px;
  margin-top: 4px;
}

.menu-item {
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #374151;
  font-size: 14px;
  text-align: left;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background: #f3f4f6;
}

.menu-item.danger {
  color: #dc2626;
}

.menu-item.danger:hover {
  background: #fef2f2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bookmark-card.grid-view {
    height: 280px;
  }

  .bookmark-thumbnail {
    height: 140px;
  }

  .bookmark-content {
    padding: 12px;
  }

  .bookmark-title {
    font-size: 15px;
  }

  .bookmark-description {
    font-size: 13px;
  }

  .bookmark-card.list-view {
    padding: 12px;
    min-height: 70px;
  }

  .bookmark-card.list-view .bookmark-title {
    font-size: 14px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bookmark-card {
    background: #1f2937;
    border-color: #374151;
  }

  .bookmark-card:hover {
    border-color: #4b5563;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .thumbnail-placeholder {
    background: linear-gradient(135deg, #374151, #4b5563);
  }

  .bookmark-title {
    color: #f9fafb;
  }

  .bookmark-title a:hover {
    color: #60a5fa;
  }

  .bookmark-description,
  .bookmark-domain,
  .bookmark-url {
    color: #d1d5db;
  }

  .bookmark-date {
    color: #9ca3af;
  }

  .tag {
    background: #374151;
    color: #d1d5db;
  }

  .action-btn {
    color: #9ca3af;
  }

  .action-btn:hover {
    background: #374151;
    color: #f9fafb;
  }

  .dropdown-menu {
    background: #1f2937;
    border-color: #374151;
  }

  .menu-item {
    color: #f9fafb;
  }

  .menu-item:hover {
    background: #374151;
  }

  .menu-item.danger {
    color: #f87171;
  }

  .menu-item.danger:hover {
    background: #7f1d1d;
  }
}
