// UI Showcase Page
// Bookmark Manager Pro - Modern UI Framework Demo Page

import * as React from "react"
import { ComponentShowcase } from "@/components/showcase/ComponentShowcase"
import { But<PERSON> } from "@/components/ui/Button"
import { ArrowLeft, Github, ExternalLink } from "lucide-react"
import { useNavigate } from "react-router-dom"

/**
 * UI Showcase Page Component
 * 
 * This page demonstrates the complete modern UI framework implementation
 * including all enhanced components with their variants, states, and features.
 * 
 * Features:
 * - Complete component showcase
 * - Interactive examples
 * - Dark mode toggle
 * - Real-world usage patterns
 * - Navigation integration
 */
export function UIShowcasePage() {
  const navigate = useNavigate()

  const handleGoBack = () => {
    if (window.history.length > 1) {
      navigate(-1)
    } else {
      navigate('/')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Navigation Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleGoBack}
            leftIcon={<ArrowLeft className="w-4 h-4" />}
          >
            Back to App
          </Button>
          
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              leftIcon={<Github className="w-4 h-4" />}
              onClick={() => window.open('https://github.com', '_blank')}
            >
              View Source
            </Button>
            <Button
              variant="outline"
              size="sm"
              leftIcon={<ExternalLink className="w-4 h-4" />}
              onClick={() => window.open('/docs', '_blank')}
            >
              Documentation
            </Button>
          </div>
        </div>
      </div>

      {/* Component Showcase */}
      <ComponentShowcase />
    </div>
  )
}

export default UIShowcasePage