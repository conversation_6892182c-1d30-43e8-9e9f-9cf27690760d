# Project Structure & Organization

## Root Directory Structure

```
Bookmark-Manager-Pro/
├── src/                    # Main source code (new architecture)
├── components/             # Legacy components (being migrated)
├── services/              # Business logic services
├── docs/                  # Comprehensive documentation
├── tests/                 # E2E and integration tests
├── cypress/               # Component testing
├── public/                # Static assets
├── styles/                # Global CSS and design system
└── utils/                 # Utility functions
```

## Source Code Organization (`src/`)

```
src/
├── components/            # React components
│   ├── base/             # Base/shared components
│   └── icons/            # Icon components
├── contexts/             # React Context providers
├── hooks/                # Custom React hooks
├── services/             # Service layer
├── types/                # TypeScript type definitions
├── utils/                # Utility functions
├── styles/               # Component-specific styles
├── test/                 # Test utilities and setup
└── workers/              # Web Workers
```

## Component Architecture

### Current State (Legacy - Being Refactored)
- **App.tsx**: Monolithic component (963 lines) - needs refactoring
- **Mixed styling**: Global CSS + Tailwind + inline styles
- **Prop drilling**: State passed through multiple layers

### Target Architecture (Post-Refactoring)
- **Context Providers**: Centralized state management
- **Custom Hooks**: Domain-specific logic
- **Base Components**: Shared component logic
- **Design System**: Consistent Tailwind-based styling

## File Naming Conventions

- **Components**: `PascalCase.tsx`
- **Hooks**: `use[Name].ts`
- **Services**: `camelCase.ts`
- **Types**: `camelCase.types.ts`
- **Constants**: `UPPER_SNAKE_CASE.ts`
- **Tests**: `ComponentName.test.tsx`
- **Utilities**: `camelCase.ts`

## Import Organization

```typescript
// 1. External libraries
import React from 'react'
import { useState } from 'react'

// 2. Internal services/utilities
import { BookmarkService } from '../services'
import { formatDate } from '../utils'

// 3. Components
import { Button } from './Button'

// 4. Types
import type { Bookmark } from '../types'

// 5. Styles (last)
import './Component.css'
```

## Service Layer Structure

```
services/
├── bookmarkService.ts     # Core bookmark operations
├── geminiService.ts       # AI integration
├── youtubeService.ts      # YouTube processing
├── searchService.ts       # Search functionality
├── exportService.ts       # Export operations
├── importService.ts       # Import operations
└── errorService.ts        # Error handling
```

## State Management Patterns

### Context Structure
```
contexts/
├── BookmarkContext.tsx    # Global bookmark state
├── ThemeContext.tsx       # Theme management
├── UIContext.tsx          # UI preferences
└── AuthContext.tsx        # User authentication
```

### Custom Hooks Pattern
```
hooks/
├── useBookmarks.ts        # Bookmark operations
├── useSearch.ts           # Search functionality
├── useTheme.ts            # Theme management
└── useLocalStorage.ts     # Persistence
```

## Documentation Organization

```
docs/
├── ARCHITECTURE.md        # System architecture
├── API_DOCUMENTATION.md   # API reference
├── COMPONENT_GUIDE.md     # Component documentation
├── IMPLEMENTATION_PLAN.md # Development roadmap
└── features/              # Feature-specific docs
```

## Testing Structure

```
tests/                     # E2E tests (Playwright)
├── accessibility.spec.js
├── performance.spec.js
└── cross-browser.spec.js

src/test/                  # Unit test utilities
├── setup.ts              # Test configuration
└── mocks/                # Mock data

cypress/                   # Component tests
├── component/
├── e2e/
└── support/
```

## Asset Organization

```
public/
├── data/                 # Sample data files
├── favicon.ico           # App icon
├── manifest.json         # PWA manifest
└── sw.js                # Service worker

styles/
├── components.css        # Component styles
├── design-system.css     # Design tokens
└── layout-system.css     # Layout utilities
```

## Configuration Files

- **package.json**: Dependencies and scripts
- **vite.config.ts**: Build configuration
- **tsconfig.json**: TypeScript configuration
- **tailwind.config.js**: Styling configuration
- **eslint.config.js**: Code quality rules
- **playwright.config.js**: E2E test configuration
- **vitest.config.ts**: Unit test configuration

## Memory Optimization Structure

```
utils/
├── memoryOptimization.ts     # Memory management
├── emergencyMemoryCleanup.ts # Emergency cleanup
├── consoleProtection.ts      # Console spam prevention
└── intervalManager.ts        # Interval cleanup
```

## Development Workflow

1. **Feature Development**: Start in `src/components/`
2. **Business Logic**: Add to `services/`
3. **State Management**: Use contexts and hooks
4. **Testing**: Add unit tests alongside components
5. **Documentation**: Update relevant docs in `docs/`

## Migration Guidelines

- **Legacy Components**: Located in root `components/` - being migrated to `src/components/`
- **State Refactoring**: Moving from useState to Context API
- **Styling Consolidation**: Standardizing on Tailwind CSS
- **Performance Optimization**: Implementing virtual scrolling and lazy loading