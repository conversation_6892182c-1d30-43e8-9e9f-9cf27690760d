import json
import os
import sys
import subprocess
import psutil
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import argparse
import logging

class SessionRecoveryManager:
    """Manages recovery of Windows Terminal sessions after crashes or restarts."""
    
    def __init__(self, config_path: str = "terminal_rules_enhanced.json"):
        self.config = self.load_config(config_path)
        self.session_storage = Path(self.config["session_management"]["session_storage_path"])
        self.recovery_storage = self.session_storage / "recovery"
        self.recovery_storage.mkdir(parents=True, exist_ok=True)
        self.setup_logging()
        
    def load_config(self, config_path: str) -> Dict:
        """Load configuration from JSON file."""
        try:
            with open(config_path, 'r') as f:
                return json.load(f)
        except FileNotFoundError:
            return self.get_default_config()
    
    def get_default_config(self) -> Dict:
        """Return default configuration."""
        return {
            "session_management": {
                "session_storage_path": "./.terminal_sessions",
                "auto_cleanup_hours": 24,
                "session_recovery_enabled": True
            },
            "windows_terminal": {
                "executable_path": "wt.exe",
                "default_profile": "PowerShell",
                "session_persistence": True
            },
            "logging": {
                "level": "INFO",
                "file_path": "./logs/session_recovery.log"
            }
        }
    
    def setup_logging(self):
        """Setup logging configuration."""
        log_config = self.config.get("logging", {})
        log_level = getattr(logging, log_config.get("level", "INFO"))
        log_file = log_config.get("file_path", "./logs/session_recovery.log")
        
        # Ensure log directory exists
        Path(log_file).parent.mkdir(parents=True, exist_ok=True)
        
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def create_recovery_snapshot(self) -> bool:
        """Create a snapshot of current active sessions for recovery."""
        try:
            active_sessions = self.get_active_sessions()
            
            if not active_sessions:
                self.logger.info("No active sessions to snapshot")
                return True
            
            snapshot = {
                "timestamp": datetime.now().isoformat(),
                "sessions": active_sessions,
                "system_info": {
                    "hostname": os.environ.get("COMPUTERNAME", "unknown"),
                    "username": os.environ.get("USERNAME", "unknown"),
                    "working_directory": os.getcwd()
                }
            }
            
            snapshot_file = self.recovery_storage / f"snapshot_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(snapshot_file, 'w') as f:
                json.dump(snapshot, f, indent=2)
            
            self.logger.info(f"Recovery snapshot created: {snapshot_file}")
            
            # Clean up old snapshots (keep last 10)
            self.cleanup_old_snapshots()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create recovery snapshot: {e}")
            return False
    
    def get_active_sessions(self) -> List[Dict]:
        """Get list of currently active sessions."""
        active_sessions = []
        
        for session_file in self.session_storage.glob("*.json"):
            if session_file.parent.name == "recovery":
                continue  # Skip recovery files
                
            try:
                with open(session_file, 'r') as f:
                    session_info = json.load(f)
                
                # Check if process is still running
                if psutil.pid_exists(session_info.get("pid", 0)):
                    active_sessions.append({
                        "name": session_file.stem,
                        "info": session_info
                    })
                    
            except Exception as e:
                self.logger.warning(f"Error reading session file {session_file}: {e}")
        
        return active_sessions
    
    def cleanup_old_snapshots(self, keep_count: int = 10):
        """Clean up old recovery snapshots."""
        try:
            snapshot_files = list(self.recovery_storage.glob("snapshot_*.json"))
            snapshot_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Remove old snapshots
            for old_snapshot in snapshot_files[keep_count:]:
                old_snapshot.unlink()
                self.logger.info(f"Removed old snapshot: {old_snapshot.name}")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up old snapshots: {e}")
    
    def get_recovery_snapshots(self) -> List[Dict]:
        """Get list of available recovery snapshots."""
        snapshots = []
        
        for snapshot_file in self.recovery_storage.glob("snapshot_*.json"):
            try:
                with open(snapshot_file, 'r') as f:
                    snapshot_data = json.load(f)
                
                snapshots.append({
                    "file": snapshot_file,
                    "timestamp": snapshot_data.get("timestamp"),
                    "session_count": len(snapshot_data.get("sessions", [])),
                    "system_info": snapshot_data.get("system_info", {})
                })
                
            except Exception as e:
                self.logger.warning(f"Error reading snapshot {snapshot_file}: {e}")
        
        # Sort by timestamp (newest first)
        snapshots.sort(key=lambda x: x["timestamp"], reverse=True)
        return snapshots
    
    def recover_from_snapshot(self, snapshot_file: Path = None, interactive: bool = True) -> bool:
        """Recover sessions from a snapshot."""
        try:
            # If no specific snapshot provided, use the latest
            if snapshot_file is None:
                snapshots = self.get_recovery_snapshots()
                if not snapshots:
                    self.logger.error("No recovery snapshots found")
                    return False
                snapshot_file = snapshots[0]["file"]
            
            # Load snapshot data
            with open(snapshot_file, 'r') as f:
                snapshot_data = json.load(f)
            
            sessions = snapshot_data.get("sessions", [])
            if not sessions:
                self.logger.info("No sessions to recover from snapshot")
                return True
            
            self.logger.info(f"Recovering {len(sessions)} sessions from {snapshot_file.name}")
            
            recovered_count = 0
            failed_count = 0
            
            for session in sessions:
                session_name = session["name"]
                session_info = session["info"]
                
                if interactive:
                    response = input(f"Recover session '{session_name}' ({session_info.get('test_type', 'unknown')})? [y/N]: ")
                    if response.lower() not in ['y', 'yes']:
                        continue
                
                if self.recover_single_session(session_name, session_info):
                    recovered_count += 1
                    self.logger.info(f"✅ Recovered session: {session_name}")
                else:
                    failed_count += 1
                    self.logger.error(f"❌ Failed to recover session: {session_name}")
            
            self.logger.info(f"Recovery completed: {recovered_count} successful, {failed_count} failed")
            return failed_count == 0
            
        except Exception as e:
            self.logger.error(f"Error during recovery: {e}")
            return False
    
    def recover_single_session(self, session_name: str, session_info: Dict) -> bool:
        """Recover a single session."""
        try:
            # Generate new session name to avoid conflicts
            timestamp = datetime.now().strftime('%H%M%S')
            new_session_name = f"{session_name}-recovered-{timestamp}"
            
            # Extract session details
            test_type = session_info.get("test_type", "unit")
            command = session_info.get("command", "")
            
            # Use the session manager to create new session
            wt_executable = self.config["windows_terminal"]["executable_path"]
            profile = self.config["windows_terminal"]["default_profile"]
            tab_name = f"Recovered-{test_type}-{timestamp}"
            
            # Build Windows Terminal command
            wt_cmd = [
                wt_executable,
                "new-tab",
                "--profile", profile,
                "--title", tab_name,
                "powershell.exe", "-NoExit", "-Command",
                command or f"cd '{os.getcwd()}'; Write-Host 'Recovered session {new_session_name} - Ready for testing'"
            ]
            
            # Execute command
            process = subprocess.Popen(wt_cmd)
            
            # Save new session info
            new_session_info = {
                "pid": process.pid,
                "tab_name": tab_name,
                "test_type": test_type,
                "created": datetime.now().isoformat(),
                "persistent": session_info.get("persistent", False),
                "command": command,
                "recovered_from": session_name,
                "original_created": session_info.get("created")
            }
            
            session_file = self.session_storage / f"{new_session_name}.json"
            with open(session_file, 'w') as f:
                json.dump(new_session_info, f, indent=2)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error recovering session {session_name}: {e}")
            return False
    
    def auto_recovery_check(self) -> bool:
        """Check if auto-recovery should be performed."""
        if not self.config["session_management"].get("session_recovery_enabled", True):
            return False
        
        # Check if there are any current active sessions
        active_sessions = self.get_active_sessions()
        if active_sessions:
            self.logger.info("Active sessions found, skipping auto-recovery")
            return False
        
        # Check for recent snapshots
        snapshots = self.get_recovery_snapshots()
        if not snapshots:
            self.logger.info("No recovery snapshots available")
            return False
        
        # Check if the latest snapshot is recent (within last hour)
        latest_snapshot = snapshots[0]
        snapshot_time = datetime.fromisoformat(latest_snapshot["timestamp"])
        time_diff = datetime.now() - snapshot_time
        
        if time_diff > timedelta(hours=1):
            self.logger.info("Latest snapshot is too old for auto-recovery")
            return False
        
        self.logger.info("Auto-recovery conditions met")
        return True
    
    def schedule_periodic_snapshots(self, interval_minutes: int = 15):
        """Schedule periodic snapshot creation (for use with task scheduler)."""
        import time
        
        self.logger.info(f"Starting periodic snapshots every {interval_minutes} minutes")
        
        try:
            while True:
                self.create_recovery_snapshot()
                time.sleep(interval_minutes * 60)
        except KeyboardInterrupt:
            self.logger.info("Periodic snapshot scheduling stopped")
        except Exception as e:
            self.logger.error(f"Error in periodic snapshot scheduling: {e}")

def main():
    parser = argparse.ArgumentParser(description="Windows Terminal Session Recovery Manager")
    parser.add_argument("--create-snapshot", action="store_true", 
                       help="Create a recovery snapshot of current sessions")
    parser.add_argument("--list-snapshots", action="store_true", 
                       help="List available recovery snapshots")
    parser.add_argument("--recover", help="Recover from specific snapshot file")
    parser.add_argument("--auto-recover", action="store_true", 
                       help="Perform automatic recovery if conditions are met")
    parser.add_argument("--non-interactive", action="store_true", 
                       help="Run recovery without user prompts")
    parser.add_argument("--schedule-snapshots", type=int, metavar="MINUTES",
                       help="Schedule periodic snapshots (interval in minutes)")
    parser.add_argument("--config", default="terminal_rules_enhanced.json", 
                       help="Configuration file path")
    
    args = parser.parse_args()
    
    recovery_manager = SessionRecoveryManager(args.config)
    
    if args.create_snapshot:
        success = recovery_manager.create_recovery_snapshot()
        sys.exit(0 if success else 1)
    
    elif args.list_snapshots:
        snapshots = recovery_manager.get_recovery_snapshots()
        if snapshots:
            print("Available recovery snapshots:")
            for i, snapshot in enumerate(snapshots, 1):
                print(f"  {i}. {snapshot['file'].name}")
                print(f"     Timestamp: {snapshot['timestamp']}")
                print(f"     Sessions: {snapshot['session_count']}")
                print(f"     System: {snapshot['system_info'].get('hostname', 'unknown')}")
                print()
        else:
            print("No recovery snapshots found")
    
    elif args.recover:
        snapshot_file = Path(args.recover)
        if not snapshot_file.exists():
            # Try to find it in recovery directory
            snapshot_file = recovery_manager.recovery_storage / args.recover
        
        if not snapshot_file.exists():
            print(f"Snapshot file not found: {args.recover}")
            sys.exit(1)
        
        success = recovery_manager.recover_from_snapshot(
            snapshot_file, 
            interactive=not args.non_interactive
        )
        sys.exit(0 if success else 1)
    
    elif args.auto_recover:
        if recovery_manager.auto_recovery_check():
            success = recovery_manager.recover_from_snapshot(
                interactive=not args.non_interactive
            )
            sys.exit(0 if success else 1)
        else:
            print("Auto-recovery conditions not met")
            sys.exit(0)
    
    elif args.schedule_snapshots:
        recovery_manager.schedule_periodic_snapshots(args.schedule_snapshots)
    
    else:
        parser.print_help()

if __name__ == "__main__":
    main()