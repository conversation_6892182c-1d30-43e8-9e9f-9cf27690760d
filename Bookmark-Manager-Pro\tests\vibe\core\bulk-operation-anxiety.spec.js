// Bulk Operation Anxiety Prevention Tests
// Tests that bulk starring operations feel safe and confidence-building

import { expect, test } from '@playwright/test';
import { PerformanceMonitor } from '../utils/performance-monitor.js';
import { VibeMetrics } from '../utils/vibe-metrics.js';
import { VibeTestData } from '../utils/vibe-test-data.js';

test.describe('Bulk Operation Anxiety Prevention - Vibe Testing', () => {
  
  test.beforeEach(async ({ page }) => {
    // Setup bulk operation test data
    await VibeTestData.createBulkOperationTestData(page);
    await page.goto('/bookmarks');
  });
  
  test('Bulk starring feels safe and reversible', async ({ page }) => {
    const session = await PerformanceMonitor.startMonitoring(page, 'bulk-safety');
    
    // Select multiple bookmarks - should feel controlled
    await page.locator('[data-testid="select-all-checkbox"]').check();
    
    // Verify selection feedback reduces anxiety
    const selectedCount = await page.locator('[data-testid="selected-count"]').textContent();
    expect(selectedCount).toContain('selected');
    
    // Measure bulk operation anxiety levels
    const bulkAnxietyMetrics = await VibeMetrics.measureBulkOperationAnxiety(page, {
      operation: 'bulk-star',
      itemCount: 10
    });
    
    // Bulk star operation - should provide confidence indicators
    await page.locator('[data-testid="bulk-star-button"]').click();
    
    // Should provide clear feedback about what happened
    await expect(page.locator('[data-testid="bulk-operation-feedback"]')).toBeVisible();
    
    // Undo should be immediately available (anxiety reducer)
    await expect(page.locator('[data-testid="undo-bulk-operation"]')).toBeVisible();
    
    // Test undo functionality - should feel safe to experiment
    await page.locator('[data-testid="undo-bulk-operation"]').click();
    
    // Verify undo worked (builds confidence in system)
    const starredCount = await page.locator('[data-testid="bookmark-star"].starred').count();
    expect(starredCount).toBe(0);
    
    // Anxiety should be low with proper safety measures
    expect(bulkAnxietyMetrics.anxietyLevel).toMatch(/very-low|low/);
    
    await test.info().attach('vibe-metrics', {
      body: JSON.stringify({
        bulkOperation: bulkAnxietyMetrics,
        testType: 'bulk-safety'
      }),
      contentType: 'application/json'
    });
    
    // Attach emotional journey showing confidence building
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        session: {
          type: 'bulk-operation-confidence',
          duration: 8000,
          interactions: 4
        },
        emotionalStates: [
          { emotion: 'cautious', timestamp: 0, trigger: 'bulk-selection' },
          { emotion: 'confident', timestamp: 2000, trigger: 'clear-feedback' },
          { emotion: 'secure', timestamp: 4000, trigger: 'undo-available' },
          { emotion: 'empowered', timestamp: 6000, trigger: 'successful-undo' }
        ],
        satisfactionPoints: [
          { category: 'control', trigger: 'undo-functionality', intensity: 9 },
          { category: 'feedback', trigger: 'operation-confirmation', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
    
    await PerformanceMonitor.endMonitoring(session);
  });
  
  test('Bulk operations maintain UI responsiveness', async ({ page }) => {
    // Setup larger collection for performance testing
    await VibeTestData.createLargeFavoritesCollection(page, 100);
    await page.goto('/bookmarks');
    
    // Select all bookmarks
    await page.locator('[data-testid="select-all-checkbox"]').check();
    
    const performanceMetrics = await PerformanceMonitor.measureBulkOperationPerformance(page, 100);
    
    // UI should remain responsive during operation
    await expect(page.locator('[data-testid="progress-indicator"]')).toBeVisible({ timeout: 100 });
    
    // Operation should complete without anxiety-inducing delays
    await expect(page.locator('[data-testid="bulk-operation-complete"]')).toBeVisible({ timeout: 5000 });
    
    // Verify performance meets expectations
    const bulkStarMetrics = performanceMetrics.find(m => m.operation === 'bulk-starring');
    expect(bulkStarMetrics.passed).toBe(true);
    
    await test.info().attach('vibe-metrics', {
      body: JSON.stringify({
        performanceMetrics,
        testType: 'bulk-performance'
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk selection provides clear visual feedback', async ({ page }) => {
    const bookmarkItems = page.locator('[data-testid="bookmark-item"]');
    const count = Math.min(5, await bookmarkItems.count());
    
    // Test individual selection feedback
    for (let i = 0; i < count; i++) {
      await bookmarkItems.nth(i).locator('[data-testid="bookmark-checkbox"]').check();
      
      // Each selection should provide immediate visual feedback
      await expect(bookmarkItems.nth(i)).toHaveClass(/selected/);
    }
    
    // Test selection count updates
    const selectionCount = await page.locator('[data-testid="selected-count"]').textContent();
    expect(selectionCount).toContain(count.toString());
    
    // Test bulk actions become available
    await expect(page.locator('[data-testid="bulk-actions-panel"]')).toBeVisible();
    
    // Test clear selection functionality
    await page.locator('[data-testid="clear-selection"]').click();
    
    // All items should be deselected
    const selectedItems = await page.locator('[data-testid="bookmark-item"].selected').count();
    expect(selectedItems).toBe(0);
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        satisfactionPoints: [
          { category: 'clarity', trigger: 'selection-feedback', intensity: 8 },
          { category: 'control', trigger: 'clear-selection', intensity: 7 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk operation preview reduces decision anxiety', async ({ page }) => {
    // Select multiple bookmarks
    const bookmarkItems = page.locator('[data-testid="bookmark-item"]');
    const count = Math.min(3, await bookmarkItems.count());
    
    for (let i = 0; i < count; i++) {
      await bookmarkItems.nth(i).locator('[data-testid="bookmark-checkbox"]').check();
    }
    
    // Click bulk star button
    await page.locator('[data-testid="bulk-star-button"]').click();
    
    // Should show preview of what will happen
    if (await page.locator('[data-testid="bulk-operation-preview"]').isVisible()) {
      // Preview should show affected items
      const previewItems = await page.locator('[data-testid="preview-item"]').count();
      expect(previewItems).toBe(count);
      
      // Should show clear action description
      await expect(page.locator('[data-testid="operation-description"]')).toContainText('star');
      
      // Should provide confirm/cancel options
      await expect(page.locator('[data-testid="confirm-operation"]')).toBeVisible();
      await expect(page.locator('[data-testid="cancel-operation"]')).toBeVisible();
      
      // Test cancel functionality (anxiety reducer)
      await page.locator('[data-testid="cancel-operation"]').click();
      
      // Should return to normal state without changes
      const starredCount = await page.locator('[data-testid="bookmark-star"].starred').count();
      expect(starredCount).toBe(0);
    }
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        emotionalStates: [
          { emotion: 'cautious', timestamp: 0, trigger: 'bulk-operation-start' },
          { emotion: 'informed', timestamp: 1000, trigger: 'preview-shown' },
          { emotion: 'confident', timestamp: 2000, trigger: 'cancel-available' }
        ],
        satisfactionPoints: [
          { category: 'transparency', trigger: 'operation-preview', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk operation progress prevents abandonment anxiety', async ({ page }) => {
    // Setup larger collection to trigger progress indicators
    await VibeTestData.createLargeFavoritesCollection(page, 50);
    await page.goto('/bookmarks');
    
    // Select all bookmarks
    await page.locator('[data-testid="select-all-checkbox"]').check();
    
    // Start bulk operation
    await page.locator('[data-testid="bulk-star-button"]').click();
    
    // Should show progress indicator immediately
    await expect(page.locator('[data-testid="progress-indicator"]')).toBeVisible({ timeout: 100 });
    
    // Progress should be informative, not just a spinner
    const progressMetrics = await page.evaluate(() => {
      const progressElement = document.querySelector('[data-testid="progress-indicator"]');
      
      return {
        hasPercentage: progressElement?.textContent?.includes('%'),
        hasItemCount: progressElement?.textContent?.match(/\d+.*\d+/),
        hasTimeEstimate: progressElement?.textContent?.includes('remaining'),
        allowsCancel: document.querySelector('[data-testid="cancel-bulk-operation"]') !== null
      };
    });
    
    // Wait for completion
    await expect(page.locator('[data-testid="bulk-operation-complete"]')).toBeVisible({ timeout: 10000 });
    
    // Should show completion summary
    await expect(page.locator('[data-testid="operation-summary"]')).toBeVisible();
    
    await test.info().attach('vibe-metrics', {
      body: JSON.stringify({
        progressMetrics,
        testType: 'bulk-progress'
      }),
      contentType: 'application/json'
    });
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        emotionalStates: [
          { emotion: 'committed', timestamp: 0, trigger: 'operation-start' },
          { emotion: 'informed', timestamp: 1000, trigger: 'progress-shown' },
          { emotion: 'patient', timestamp: 5000, trigger: 'progress-updates' },
          { emotion: 'accomplished', timestamp: 8000, trigger: 'operation-complete' }
        ],
        satisfactionPoints: [
          { category: 'transparency', trigger: 'progress-feedback', intensity: 8 },
          { category: 'completion', trigger: 'operation-summary', intensity: 9 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk operation errors provide recovery guidance', async ({ page }) => {
    // Simulate network issues during bulk operation
    await page.route('**/api/favorites/bulk', route => route.abort());
    
    // Select bookmarks
    await page.locator('[data-testid="select-all-checkbox"]').check();
    
    // Attempt bulk operation
    await page.locator('[data-testid="bulk-star-button"]').click();
    
    // Should show helpful error message, not just failure
    await expect(page.locator('[data-testid="bulk-operation-error"]')).toBeVisible();
    
    // Error should provide recovery options
    const errorRecoveryOptions = await page.evaluate(() => {
      const errorElement = document.querySelector('[data-testid="bulk-operation-error"]');
      
      return {
        hasRetryOption: document.querySelector('[data-testid="retry-bulk-operation"]') !== null,
        hasPartialSuccess: errorElement?.textContent?.includes('partially'),
        hasRecoveryGuidance: errorElement?.textContent?.includes('try again') || 
                            errorElement?.textContent?.includes('check connection'),
        preservesSelection: document.querySelectorAll('[data-testid="bookmark-item"].selected').length > 0
      };
    });
    
    expect(errorRecoveryOptions.hasRetryOption).toBe(true);
    expect(errorRecoveryOptions.preservesSelection).toBe(true);
    
    // Test retry functionality
    await page.unroute('**/api/favorites/bulk');
    await page.locator('[data-testid="retry-bulk-operation"]').click();
    
    // Should complete successfully on retry
    await expect(page.locator('[data-testid="bulk-operation-complete"]')).toBeVisible({ timeout: 5000 });
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        emotionalStates: [
          { emotion: 'confident', timestamp: 0, trigger: 'operation-start' },
          { emotion: 'frustrated', timestamp: 2000, trigger: 'operation-failed' },
          { emotion: 'hopeful', timestamp: 3000, trigger: 'recovery-options' },
          { emotion: 'relieved', timestamp: 5000, trigger: 'retry-success' }
        ],
        frustrationPoints: [
          { category: 'technical', cause: 'network-error', intensity: 6, resolved: true }
        ],
        satisfactionPoints: [
          { category: 'recovery', trigger: 'retry-success', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk operation confirmation prevents accidental actions', async ({ page }) => {
    // Select many bookmarks
    await page.locator('[data-testid="select-all-checkbox"]').check();
    
    // Click bulk delete (more dangerous operation)
    if (await page.locator('[data-testid="bulk-delete-button"]').isVisible()) {
      await page.locator('[data-testid="bulk-delete-button"]').click();
      
      // Should require confirmation for destructive actions
      await expect(page.locator('[data-testid="confirm-bulk-delete"]')).toBeVisible();
      
      // Confirmation should be clear about consequences
      const confirmationText = await page.locator('[data-testid="confirmation-message"]').textContent();
      expect(confirmationText).toMatch(/delete|remove|permanently/i);
      
      // Should show count of affected items
      expect(confirmationText).toMatch(/\d+/);
      
      // Test cancellation (most common user action)
      await page.locator('[data-testid="cancel-confirmation"]').click();
      
      // Should return to normal state
      await expect(page.locator('[data-testid="confirm-bulk-delete"]')).not.toBeVisible();
      
      // No items should be deleted
      const bookmarkCount = await page.locator('[data-testid="bookmark-item"]').count();
      expect(bookmarkCount).toBeGreaterThan(0);
    }
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        emotionalStates: [
          { emotion: 'decisive', timestamp: 0, trigger: 'bulk-delete-click' },
          { emotion: 'cautious', timestamp: 500, trigger: 'confirmation-shown' },
          { emotion: 'relieved', timestamp: 1500, trigger: 'cancelled-safely' }
        ],
        satisfactionPoints: [
          { category: 'safety', trigger: 'confirmation-dialog', intensity: 9 },
          { category: 'control', trigger: 'easy-cancellation', intensity: 8 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
  test('Bulk operation keyboard shortcuts feel efficient', async ({ page }) => {
    // Test keyboard-driven bulk operations for power users
    
    // Select items with keyboard
    await page.keyboard.press('Control+a'); // Select all
    
    // Should show selection feedback
    const selectedCount = await page.locator('[data-testid="bookmark-item"].selected').count();
    expect(selectedCount).toBeGreaterThan(0);
    
    // Test bulk star with keyboard shortcut
    if (await page.locator('[data-testid="bulk-star-button"]').isVisible()) {
      await page.keyboard.press('Control+s'); // Bulk star shortcut
      
      // Should complete operation
      await expect(page.locator('[data-testid="bulk-operation-complete"]')).toBeVisible({ timeout: 3000 });
    }
    
    // Test undo with keyboard
    await page.keyboard.press('Control+z');
    
    // Should undo the bulk operation
    const starredCount = await page.locator('[data-testid="bookmark-star"].starred').count();
    expect(starredCount).toBe(0);
    
    await test.info().attach('emotional-journey', {
      body: JSON.stringify({
        session: {
          type: 'keyboard-efficiency',
          duration: 3000,
          interactions: 3
        },
        emotionalStates: [
          { emotion: 'efficient', timestamp: 0, trigger: 'keyboard-selection' },
          { emotion: 'powerful', timestamp: 1000, trigger: 'keyboard-shortcuts' },
          { emotion: 'accomplished', timestamp: 2000, trigger: 'quick-completion' }
        ],
        satisfactionPoints: [
          { category: 'efficiency', trigger: 'keyboard-shortcuts', intensity: 9 }
        ]
      }),
      contentType: 'application/json'
    });
  });
  
});