/**
 * BOOKMARK SELECTOR COMPONENT
 * Easy bookmark selection interface for playlist creation
 * Follows ImportPanel design patterns
 */

import React, { useState, useEffect } from 'react'
import { Check, Search, Filter, Video, Headphones, FileText, Globe, X } from 'lucide-react'
import { useBookmarks } from '../contexts/BookmarkContext'
import type { Bookmark } from '../types'

interface BookmarkSelectorProps {
  onSelectionChange: (bookmarks: Bookmark[]) => void
  initialSelection?: Bookmark[]
  maxSelection?: number
  filterType?: 'all' | 'video' | 'audio' | 'reading'
}

export const BookmarkSelector: React.FC<BookmarkSelectorProps> = ({
  onSelectionChange,
  initialSelection = [],
  maxSelection = 50,
  filterType = 'all'
}) => {
  const { bookmarks } = useBookmarks()
  const [selectedBookmarks, setSelectedBookmarks] = useState<Bookmark[]>(initialSelection)
  const [searchQuery, setSearchQuery] = useState('')
  const [currentFilter, setCurrentFilter] = useState(filterType)

  // Filter bookmarks based on search and type
  const filteredBookmarks = bookmarks.filter(bookmark => {
    // Search filter
    const matchesSearch = searchQuery === '' || 
      bookmark.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.url.toLowerCase().includes(searchQuery.toLowerCase()) ||
      bookmark.description?.toLowerCase().includes(searchQuery.toLowerCase())

    if (!matchesSearch) return false

    // Type filter
    if (currentFilter === 'all') return true
    
    const url = bookmark.url.toLowerCase()
    switch (currentFilter) {
      case 'video':
        return url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')
      case 'audio':
        return url.includes('spotify.com') || url.includes('soundcloud.com') || url.includes('audio') || url.includes('music')
      case 'reading':
        return url.includes('pdf') || bookmark.title.toLowerCase().includes('article') || 
               bookmark.title.toLowerCase().includes('blog') || bookmark.title.toLowerCase().includes('doc')
      default:
        return true
    }
  })

  // Update parent when selection changes
  useEffect(() => {
    onSelectionChange(selectedBookmarks)
  }, [selectedBookmarks, onSelectionChange])

  const toggleBookmark = (bookmark: Bookmark) => {
    setSelectedBookmarks(prev => {
      const isSelected = prev.some(b => b.id === bookmark.id)
      if (isSelected) {
        return prev.filter(b => b.id !== bookmark.id)
      } else if (prev.length < maxSelection) {
        return [...prev, bookmark]
      }
      return prev
    })
  }

  const selectAll = () => {
    const availableBookmarks = filteredBookmarks.slice(0, maxSelection)
    setSelectedBookmarks(availableBookmarks)
  }

  const clearSelection = () => {
    setSelectedBookmarks([])
  }

  const getBookmarkIcon = (bookmark: Bookmark) => {
    const url = bookmark.url.toLowerCase()
    if (url.includes('youtube.com') || url.includes('vimeo.com')) return <Video size={16} className="text-red-500" />
    if (url.includes('spotify.com') || url.includes('soundcloud.com')) return <Headphones size={16} className="text-white" />
    if (url.includes('pdf') || bookmark.title.toLowerCase().includes('article')) return <FileText size={16} className="text-white" />
    return <Globe size={16} className="text-gray-500" />
  }

  const getFilterCount = (filter: string) => {
    return bookmarks.filter(bookmark => {
      const url = bookmark.url.toLowerCase()
      switch (filter) {
        case 'video':
          return url.includes('youtube.com') || url.includes('vimeo.com') || url.includes('video')
        case 'audio':
          return url.includes('spotify.com') || url.includes('soundcloud.com') || url.includes('audio')
        case 'reading':
          return url.includes('pdf') || bookmark.title.toLowerCase().includes('article')
        default:
          return true
      }
    }).length
  }

  return (
    <div className="bookmark-selector">
      {/* Search and Filter Controls */}
      <div className="selector-controls">
        <div className="search-container">
          <Search size={16} className="search-icon" />
          <input
            type="text"
            placeholder="Search bookmarks..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
          {searchQuery && (
            <button
              onClick={() => setSearchQuery('')}
              className="clear-search"
            >
              <X size={14} />
            </button>
          )}
        </div>

        <div className="filter-tabs">
          <button
            onClick={() => setCurrentFilter('all')}
            className={`filter-tab ${currentFilter === 'all' ? 'active' : ''}`}
          >
            All ({bookmarks.length})
          </button>
          <button
            onClick={() => setCurrentFilter('video')}
            className={`filter-tab ${currentFilter === 'video' ? 'active' : ''}`}
          >
            <Video size={14} />
            Videos ({getFilterCount('video')})
          </button>
          <button
            onClick={() => setCurrentFilter('audio')}
            className={`filter-tab ${currentFilter === 'audio' ? 'active' : ''}`}
          >
            <Headphones size={14} />
            Audio ({getFilterCount('audio')})
          </button>
          <button
            onClick={() => setCurrentFilter('reading')}
            className={`filter-tab ${currentFilter === 'reading' ? 'active' : ''}`}
          >
            <FileText size={14} />
            Reading ({getFilterCount('reading')})
          </button>
        </div>
      </div>

      {/* Selection Summary */}
      <div className="selection-summary">
        <span className="selection-count">
          {selectedBookmarks.length} of {maxSelection} selected
        </span>
        <div className="selection-actions">
          <button
            onClick={selectAll}
            className="selection-btn"
            disabled={filteredBookmarks.length === 0}
          >
            Select All ({Math.min(filteredBookmarks.length, maxSelection)})
          </button>
          <button
            onClick={clearSelection}
            className="selection-btn"
            disabled={selectedBookmarks.length === 0}
          >
            Clear Selection
          </button>
        </div>
      </div>

      {/* Bookmark List */}
      <div className="bookmark-list">
        {filteredBookmarks.length === 0 ? (
          <div className="empty-state">
            <Filter size={32} className="empty-icon" />
            <p>No bookmarks match your search</p>
            <small>Try adjusting your search terms or filters</small>
          </div>
        ) : (
          filteredBookmarks.map(bookmark => {
            const isSelected = selectedBookmarks.some(b => b.id === bookmark.id)
            const canSelect = !isSelected && selectedBookmarks.length < maxSelection

            return (
              <div
                key={bookmark.id}
                className={`bookmark-item ${isSelected ? 'selected' : ''} ${!canSelect && !isSelected ? 'disabled' : ''}`}
                onClick={() => (canSelect || isSelected) && toggleBookmark(bookmark)}
              >
                <div className="bookmark-checkbox">
                  {isSelected && <Check size={14} />}
                </div>
                
                <div className="bookmark-icon">
                  {getBookmarkIcon(bookmark)}
                </div>
                
                <div className="bookmark-content">
                  <div className="bookmark-title">{bookmark.title}</div>
                  <div className="bookmark-url">{new URL(bookmark.url).hostname}</div>
                  {bookmark.description && (
                    <div className="bookmark-description">{bookmark.description}</div>
                  )}
                </div>

                {bookmark.collection && (
                  <div className="bookmark-collection">
                    {bookmark.collection}
                  </div>
                )}
              </div>
            )
          })
        )}
      </div>

      {/* Selection Limit Warning */}
      {selectedBookmarks.length >= maxSelection && (
        <div className="selection-warning">
          <span>Maximum selection limit reached ({maxSelection} bookmarks)</span>
        </div>
      )}
    </div>
  )
}

export default BookmarkSelector
