/**
 * Collection Color System
 * Provides consistent, unique colors for bookmark collections
 */

export interface CollectionColorInfo {
  color: string
  name: string
  category: string
}

// Comprehensive color palette with 200+ distinct, vibrant colors for large bookmark imports
export const COLLECTION_COLORS = [
  // Primary Colors (20)
  { color: '#3B82F6', name: 'Blue', category: 'Development' },
  { color: '#10B981', name: 'Emerald', category: 'Learning' },
  { color: '#F59E0B', name: 'Amber', category: 'News' },
  { color: '#EF4444', name: 'Red', category: 'Shopping' },
  { color: '#8B5CF6', name: 'Purple', category: 'Entertainment' },
  { color: '#F97316', name: 'Orange', category: 'Lifestyle' },
  { color: '#06B6D4', name: 'Cyan', category: 'Social' },
  { color: '#84CC16', name: 'Lime', category: 'Health' },
  { color: '#EC4899', name: 'Pink', category: 'Design' },
  { color: '#6366F1', name: 'Indigo', category: 'Reference' },
  { color: '#14B8A6', name: 'Tea<PERSON>', category: 'Finance' },
  { color: '#F43F5E', name: '<PERSON>', category: 'Travel' },
  { color: '#22C55E', name: 'Green', category: 'Productivity' },
  { color: '#A855F7', name: 'Violet', category: 'Creative' },
  { color: '#0EA5E9', name: 'Sky', category: 'Technology' },
  { color: '#DC2626', name: 'Red-600', category: 'Important' },
  { color: '#7C3AED', name: 'Violet-600', category: 'Personal' },
  { color: '#059669', name: 'Emerald-600', category: 'Business' },
  { color: '#D97706', name: 'Amber-600', category: 'Education' },
  { color: '#BE185D', name: 'Pink-700', category: 'Inspiration' },

  // Extended Blue Family (20)
  { color: '#1E40AF', name: 'Blue-800', category: 'Deep Blue' },
  { color: '#1D4ED8', name: 'Blue-700', category: 'Royal Blue' },
  { color: '#2563EB', name: 'Blue-600', category: 'Bright Blue' },
  { color: '#60A5FA', name: 'Blue-400', category: 'Light Blue' },
  { color: '#93C5FD', name: 'Blue-300', category: 'Pale Blue' },
  { color: '#0F172A', name: 'Slate-900', category: 'Dark Slate' },
  { color: '#1E293B', name: 'Slate-800', category: 'Slate' },
  { color: '#334155', name: 'Slate-700', category: 'Gray Slate' },
  { color: '#475569', name: 'Slate-600', category: 'Light Slate' },
  { color: '#64748B', name: 'Slate-500', category: 'Medium Slate' },
  { color: '#0C4A6E', name: 'Sky-900', category: 'Deep Sky' },
  { color: '#075985', name: 'Sky-800', category: 'Dark Sky' },
  { color: '#0369A1', name: 'Sky-700', category: 'Sky Blue' },
  { color: '#0284C7', name: 'Sky-600', category: 'Bright Sky' },
  { color: '#38BDF8', name: 'Sky-400', category: 'Light Sky' },
  { color: '#7DD3FC', name: 'Sky-300', category: 'Pale Sky' },
  { color: '#164E63', name: 'Cyan-900', category: 'Deep Cyan' },
  { color: '#155E75', name: 'Cyan-800', category: 'Dark Cyan' },
  { color: '#0891B2', name: 'Cyan-600', category: 'Bright Cyan' },
  { color: '#22D3EE', name: 'Cyan-400', category: 'Light Cyan' },

  // Extended Green Family (20)
  { color: '#14532D', name: 'Green-900', category: 'Forest Green' },
  { color: '#166534', name: 'Green-800', category: 'Dark Green' },
  { color: '#15803D', name: 'Green-700', category: 'Deep Green' },
  { color: '#16A34A', name: 'Green-600', category: 'Bright Green' },
  { color: '#4ADE80', name: 'Green-400', category: 'Light Green' },
  { color: '#86EFAC', name: 'Green-300', category: 'Pale Green' },
  { color: '#064E3B', name: 'Emerald-900', category: 'Deep Emerald' },
  { color: '#065F46', name: 'Emerald-800', category: 'Dark Emerald' },
  { color: '#047857', name: 'Emerald-700', category: 'Emerald' },
  { color: '#34D399', name: 'Emerald-400', category: 'Light Emerald' },
  { color: '#6EE7B7', name: 'Emerald-300', category: 'Pale Emerald' },
  { color: '#134E4A', name: 'Teal-900', category: 'Deep Teal' },
  { color: '#115E59', name: 'Teal-800', category: 'Dark Teal' },
  { color: '#0F766E', name: 'Teal-700', category: 'Teal' },
  { color: '#0D9488', name: 'Teal-600', category: 'Bright Teal' },
  { color: '#2DD4BF', name: 'Teal-400', category: 'Light Teal' },
  { color: '#7DD3FC', name: 'Teal-300', category: 'Pale Teal' },
  { color: '#365314', name: 'Lime-900', category: 'Deep Lime' },
  { color: '#3F6212', name: 'Lime-800', category: 'Dark Lime' },
  { color: '#4D7C0F', name: 'Lime-700', category: 'Lime' },

  // Extended Red Family (20)
  { color: '#7F1D1D', name: 'Red-900', category: 'Deep Red' },
  { color: '#991B1B', name: 'Red-800', category: 'Dark Red' },
  { color: '#B91C1C', name: 'Red-700', category: 'Red' },
  { color: '#F87171', name: 'Red-400', category: 'Light Red' },
  { color: '#FCA5A5', name: 'Red-300', category: 'Pale Red' },
  { color: '#881337', name: 'Rose-900', category: 'Deep Rose' },
  { color: '#9F1239', name: 'Rose-800', category: 'Dark Rose' },
  { color: '#BE123C', name: 'Rose-700', category: 'Rose' },
  { color: '#E11D48', name: 'Rose-600', category: 'Bright Rose' },
  { color: '#FB7185', name: 'Rose-400', category: 'Light Rose' },
  { color: '#FDA4AF', name: 'Rose-300', category: 'Pale Rose' },
  { color: '#831843', name: 'Pink-900', category: 'Deep Pink' },
  { color: '#9D174D', name: 'Pink-800', category: 'Dark Pink' },
  { color: '#C2185B', name: 'Pink-700', category: 'Pink' },
  { color: '#DB2777', name: 'Pink-600', category: 'Bright Pink' },
  { color: '#F472B6', name: 'Pink-400', category: 'Light Pink' },
  { color: '#F9A8D4', name: 'Pink-300', category: 'Pale Pink' },
  { color: '#701A75', name: 'Fuchsia-900', category: 'Deep Fuchsia' },
  { color: '#86198F', name: 'Fuchsia-800', category: 'Dark Fuchsia' },
  { color: '#A21CAF', name: 'Fuchsia-700', category: 'Fuchsia' },

  // Extended Purple Family (20)
  { color: '#581C87', name: 'Purple-900', category: 'Deep Purple' },
  { color: '#6B21A8', name: 'Purple-800', category: 'Dark Purple' },
  { color: '#7E22CE', name: 'Purple-700', category: 'Purple' },
  { color: '#9333EA', name: 'Purple-600', category: 'Bright Purple' },
  { color: '#C084FC', name: 'Purple-400', category: 'Light Purple' },
  { color: '#DDD6FE', name: 'Purple-300', category: 'Pale Purple' },
  { color: '#4C1D95', name: 'Violet-900', category: 'Deep Violet' },
  { color: '#5B21B6', name: 'Violet-800', category: 'Dark Violet' },
  { color: '#6D28D9', name: 'Violet-700', category: 'Violet' },
  { color: '#8B5CF6', name: 'Violet-500', category: 'Bright Violet' },
  { color: '#A78BFA', name: 'Violet-400', category: 'Light Violet' },
  { color: '#C4B5FD', name: 'Violet-300', category: 'Pale Violet' },
  { color: '#312E81', name: 'Indigo-900', category: 'Deep Indigo' },
  { color: '#3730A3', name: 'Indigo-800', category: 'Dark Indigo' },
  { color: '#4338CA', name: 'Indigo-700', category: 'Indigo' },
  { color: '#5145CD', name: 'Indigo-600', category: 'Bright Indigo' },
  { color: '#818CF8', name: 'Indigo-400', category: 'Light Indigo' },
  { color: '#A5B4FC', name: 'Indigo-300', category: 'Pale Indigo' },
  { color: '#1E1B4B', name: 'Indigo-950', category: 'Midnight Indigo' },
  { color: '#2E1065', name: 'Violet-950', category: 'Midnight Violet' },

  // Extended Orange/Yellow Family (20)
  { color: '#7C2D12', name: 'Orange-900', category: 'Deep Orange' },
  { color: '#9A3412', name: 'Orange-800', category: 'Dark Orange' },
  { color: '#C2410C', name: 'Orange-700', category: 'Orange' },
  { color: '#EA580C', name: 'Orange-600', category: 'Bright Orange' },
  { color: '#FB923C', name: 'Orange-400', category: 'Light Orange' },
  { color: '#FED7AA', name: 'Orange-300', category: 'Pale Orange' },
  { color: '#78350F', name: 'Amber-900', category: 'Deep Amber' },
  { color: '#92400E', name: 'Amber-800', category: 'Dark Amber' },
  { color: '#B45309', name: 'Amber-700', category: 'Amber' },
  { color: '#F59E0B', name: 'Amber-500', category: 'Bright Amber' },
  { color: '#FBB042', name: 'Amber-400', category: 'Light Amber' },
  { color: '#FCD34D', name: 'Amber-300', category: 'Pale Amber' },
  { color: '#713F12', name: 'Yellow-900', category: 'Deep Yellow' },
  { color: '#854D0E', name: 'Yellow-800', category: 'Dark Yellow' },
  { color: '#A16207', name: 'Yellow-700', category: 'Yellow' },
  { color: '#CA8A04', name: 'Yellow-600', category: 'Bright Yellow' },
  { color: '#FACC15', name: 'Yellow-400', category: 'Light Yellow' },
  { color: '#FDE047', name: 'Yellow-300', category: 'Pale Yellow' },
  { color: '#422006', name: 'Yellow-950', category: 'Midnight Yellow' },
  { color: '#451A03', name: 'Amber-950', category: 'Midnight Amber' },

  // Extended Neutral Family (20)
  { color: '#0F0F23', name: 'Neutral-950', category: 'Midnight' },
  { color: '#171717', name: 'Neutral-900', category: 'Charcoal' },
  { color: '#262626', name: 'Neutral-800', category: 'Dark Gray' },
  { color: '#404040', name: 'Neutral-700', category: 'Gray' },
  { color: '#525252', name: 'Neutral-600', category: 'Medium Gray' },
  { color: '#737373', name: 'Neutral-500', category: 'Light Gray' },
  { color: '#A3A3A3', name: 'Neutral-400', category: 'Silver' },
  { color: '#D4D4D4', name: 'Neutral-300', category: 'Light Silver' },
  { color: '#E5E5E5', name: 'Neutral-200', category: 'Pale Gray' },
  { color: '#F5F5F5', name: 'Neutral-100', category: 'Off White' },
  { color: '#0C0A09', name: 'Stone-950', category: 'Deep Stone' },
  { color: '#1C1917', name: 'Stone-900', category: 'Dark Stone' },
  { color: '#292524', name: 'Stone-800', category: 'Stone' },
  { color: '#44403C', name: 'Stone-700', category: 'Medium Stone' },
  { color: '#57534E', name: 'Stone-600', category: 'Light Stone' },
  { color: '#78716C', name: 'Stone-500', category: 'Warm Gray' },
  { color: '#A8A29E', name: 'Stone-400', category: 'Warm Silver' },
  { color: '#D6D3D1', name: 'Stone-300', category: 'Warm Light' },
  { color: '#E7E5E4', name: 'Stone-200', category: 'Warm Pale' },
  { color: '#F5F5F4', name: 'Stone-100', category: 'Warm White' },

  // Unique Accent Colors (40)
  { color: '#FF6B6B', name: 'Coral', category: 'Warm' },
  { color: '#4ECDC4', name: 'Turquoise', category: 'Cool' },
  { color: '#45B7D1', name: 'Ocean Blue', category: 'Ocean' },
  { color: '#96CEB4', name: 'Mint', category: 'Fresh' },
  { color: '#FFEAA7', name: 'Cream', category: 'Soft' },
  { color: '#DDA0DD', name: 'Plum', category: 'Rich' },
  { color: '#98D8C8', name: 'Seafoam', category: 'Aqua' },
  { color: '#F7DC6F', name: 'Gold', category: 'Luxury' },
  { color: '#BB8FCE', name: 'Lavender', category: 'Floral' },
  { color: '#85C1E9', name: 'Powder Blue', category: 'Soft Blue' },
  { color: '#F8C471', name: 'Peach', category: 'Warm Orange' },
  { color: '#82E0AA', name: 'Spring Green', category: 'Fresh Green' },
  { color: '#F1948A', name: 'Salmon', category: 'Pink Orange' },
  { color: '#85929E', name: 'Steel Blue', category: 'Industrial' },
  { color: '#D7BDE2', name: 'Lilac', category: 'Light Purple' },
  { color: '#A9DFBF', name: 'Sage', category: 'Herb Green' },
  { color: '#F9E79F', name: 'Butter', category: 'Soft Yellow' },
  { color: '#AED6F1', name: 'Baby Blue', category: 'Pastel Blue' },
  { color: '#FADBD8', name: 'Blush', category: 'Soft Pink' },
  { color: '#D5DBDB', name: 'Mist', category: 'Light Gray' },
  { color: '#FF8A80', name: 'Light Coral', category: 'Bright Coral' },
  { color: '#80CBC4', name: 'Teal Green', category: 'Blue Green' },
  { color: '#81C784', name: 'Grass Green', category: 'Natural Green' },
  { color: '#FFB74D', name: 'Tangerine', category: 'Bright Orange' },
  { color: '#9575CD', name: 'Amethyst', category: 'Purple Gem' },
  { color: '#4FC3F7', name: 'Sky Light', category: 'Light Sky' },
  { color: '#AED581', name: 'Lime Light', category: 'Bright Lime' },
  { color: '#FFD54F', name: 'Sunshine', category: 'Bright Yellow' },
  { color: '#F06292', name: 'Hot Pink', category: 'Vibrant Pink' },
  { color: '#64B5F6', name: 'Cerulean', category: 'Deep Sky' },
  { color: '#A1C181', name: 'Olive', category: 'Earth Green' },
  { color: '#D4A574', name: 'Caramel', category: 'Brown Orange' },
  { color: '#B39DDB', name: 'Periwinkle', category: 'Blue Purple' },
  { color: '#80DEEA', name: 'Aqua', category: 'Water Blue' },
  { color: '#C5E1A5', name: 'Celery', category: 'Yellow Green' },
  { color: '#FFCC02', name: 'Canary', category: 'Pure Yellow' },
  { color: '#FF7043', name: 'Burnt Orange', category: 'Deep Orange' },
  { color: '#7986CB', name: 'Periwinkle Blue', category: 'Soft Blue' },
  { color: '#26A69A', name: 'Teal Blue', category: 'Blue Teal' },
  { color: '#66BB6A', name: 'Forest', category: 'Deep Green' }
] as const

// Common collection name mappings to ensure consistent colors
const COLLECTION_NAME_MAPPINGS: Record<string, string> = {
  // Development related
  'development': '#3B82F6', // Blue
  'dev': '#3B82F6',
  'coding': '#3B82F6',
  'programming': '#3B82F6',
  'code': '#3B82F6',
  'tech': '#0EA5E9', // Sky
  'technology': '#0EA5E9',
  'software': '#6366F1', // Indigo

  // Learning related
  'learning': '#10B981', // Emerald
  'education': '#D97706', // Amber-600
  'tutorial': '#10B981',
  'course': '#10B981',
  'study': '#10B981',

  // Work related
  'work': '#059669', // Emerald-600 (Business)
  'business': '#059669',
  'office': '#059669',
  'productivity': '#22C55E', // Green

  // Personal
  'personal': '#7C3AED', // Violet-600
  'private': '#7C3AED',
  'me': '#7C3AED',

  // Entertainment
  'entertainment': '#8B5CF6', // Purple
  'fun': '#8B5CF6',
  'games': '#8B5CF6',
  'gaming': '#8B5CF6',
  'movies': '#8B5CF6',
  'music': '#8B5CF6',

  // News & Information
  'news': '#F59E0B', // Amber
  'articles': '#F59E0B',
  'blog': '#F59E0B',
  'blogs': '#F59E0B',

  // Shopping
  'shopping': '#EF4444', // Red
  'shop': '#EF4444',
  'buy': '#EF4444',
  'purchase': '#EF4444',

  // Design & Creative
  'design': '#EC4899', // Pink
  'creative': '#A855F7', // Violet
  'art': '#EC4899',
  'graphics': '#EC4899',

  // Reference
  'reference': '#6366F1', // Indigo
  'docs': '#6366F1',
  'documentation': '#6366F1',
  'manual': '#6366F1',

  // Social
  'social': '#06B6D4', // Cyan
  'friends': '#06B6D4',
  'community': '#06B6D4',

  // Health
  'health': '#84CC16', // Lime
  'fitness': '#84CC16',
  'medical': '#84CC16',

  // Finance
  'finance': '#14B8A6', // Teal
  'money': '#14B8A6',
  'banking': '#14B8A6',
  'investment': '#14B8A6',

  // Travel
  'travel': '#F43F5E', // Rose
  'vacation': '#F43F5E',
  'trip': '#F43F5E',

  // Important
  'important': '#DC2626', // Red-600
  'urgent': '#DC2626',
  'priority': '#DC2626',

  // Inspiration
  'inspiration': '#BE185D', // Pink-700
  'ideas': '#BE185D',
  'quotes': '#BE185D',

  // Quick Add and Bookmarks Bar
  'quick add': '#84CC16', // Lime (Health/Quick actions)
  'bookmarks bar': '#84CC16',
  'bookmark bar': '#84CC16',
  'quick': '#84CC16',
  'bar': '#84CC16'
}

/**
 * Smart collection color mapping - prioritizes exact matches, then category matches, then hash
 * Ensures "Development" gets blue, "Learning" gets green, etc.
 */
export function getCollectionColor(collection: string): string {
  if (!collection || collection.trim() === '') {
    return COLLECTION_COLORS[0].color // Default to blue
  }

  const normalizedCollection = collection.toLowerCase().trim()

  // First priority: Check direct name mappings
  if (COLLECTION_NAME_MAPPINGS[normalizedCollection]) {
    const mappedColor = COLLECTION_NAME_MAPPINGS[normalizedCollection]
    console.log(`🎯 Direct mapping for "${collection}": ${mappedColor}`)
    return mappedColor
  }

  // Second priority: Check for exact category matches
  const categoryMatch = COLLECTION_COLORS.find(colorItem =>
    colorItem.category.toLowerCase() === normalizedCollection ||
    colorItem.name.toLowerCase() === normalizedCollection
  )

  if (categoryMatch) {
    console.log(`🎯 Category match for "${collection}": ${categoryMatch.color} (${categoryMatch.name})`)
    return categoryMatch.color
  }

  // Third priority: Check for partial matches (e.g., "dev" matches "development")
  const partialMatch = COLLECTION_COLORS.find(colorItem => {
    const category = colorItem.category.toLowerCase()
    const name = colorItem.name.toLowerCase()
    return category.includes(normalizedCollection) ||
           normalizedCollection.includes(category) ||
           name.includes(normalizedCollection) ||
           normalizedCollection.includes(name)
  })

  if (partialMatch) {
    console.log(`🎯 Partial match for "${collection}": ${partialMatch.color} (${partialMatch.name})`)
    return partialMatch.color
  }

  // Last resort: Use consistent hash for unique assignment
  let hash = 0
  for (let i = 0; i < normalizedCollection.length; i++) {
    hash = normalizedCollection.charCodeAt(i) + ((hash << 5) - hash)
  }

  // Use the hash to select a color (ensures same collection always gets same color)
  const colorIndex = Math.abs(hash) % COLLECTION_COLORS.length
  const selectedColor = COLLECTION_COLORS[colorIndex].color

  console.log(`🎲 Hash-based assignment for "${collection}": ${selectedColor} (index: ${colorIndex})`)

  // For collections beyond our 200 colors, generate procedural colors
  if (colorIndex >= COLLECTION_COLORS.length - 10) {
    return generateProceduralColor(normalizedCollection)
  }

  return selectedColor
}

/**
 * Generate procedural colors for collections beyond the predefined palette
 * Ensures consistent colors for the same collection name
 */
function generateProceduralColor(collection: string): string {
  let hash = 0
  for (let i = 0; i < collection.length; i++) {
    hash = collection.charCodeAt(i) + ((hash << 5) - hash)
  }

  // Generate HSL values for better color distribution
  const hue = Math.abs(hash) % 360
  const saturation = 65 + (Math.abs(hash >> 8) % 25) // 65-90%
  const lightness = 45 + (Math.abs(hash >> 16) % 20) // 45-65%

  return hslToHex(hue, saturation, lightness)
}

/**
 * Convert HSL to HEX color
 */
function hslToHex(h: number, s: number, l: number): string {
  l /= 100
  const a = s * Math.min(l, 1 - l) / 100
  const f = (n: number) => {
    const k = (n + h / 30) % 12
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1)
    return Math.round(255 * color).toString(16).padStart(2, '0')
  }
  return `#${f(0)}${f(8)}${f(4)}`
}

/**
 * Get collection color info including name and category
 */
export function getCollectionColorInfo(collection: string): CollectionColorInfo {
  const color = getCollectionColor(collection)
  const colorInfo = COLLECTION_COLORS.find(c => c.color === color)
  
  return {
    color,
    name: colorInfo?.name || 'Blue',
    category: colorInfo?.category || 'General'
  }
}

/**
 * Get a lighter version of the collection color for backgrounds
 */
export function getCollectionColorLight(collection: string, opacity: number = 0.1): string {
  const color = getCollectionColor(collection)
  
  // Convert hex to RGB and add opacity
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  return `rgba(${r}, ${g}, ${b}, ${opacity})`
}

/**
 * Get a darker version of the collection color for borders/accents
 */
export function getCollectionColorDark(collection: string): string {
  const color = getCollectionColor(collection)
  
  // Convert hex to RGB and darken
  const hex = color.replace('#', '')
  const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - 30)
  const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - 30)
  const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - 30)
  
  return `rgb(${r}, ${g}, ${b})`
}

/**
 * Get all unique colors currently in use
 */
export function getUsedCollectionColors(collections: string[]): CollectionColorInfo[] {
  const uniqueCollections = [...new Set(collections)]
  return uniqueCollections.map(collection => getCollectionColorInfo(collection))
}

/**
 * Generate CSS custom properties for collection colors
 */
export function generateCollectionColorCSS(collections: string[]): string {
  const uniqueCollections = [...new Set(collections)]
  
  return uniqueCollections.map(collection => {
    const sanitizedName = collection.toLowerCase().replace(/[^a-z0-9]/g, '-')
    const color = getCollectionColor(collection)
    const lightColor = getCollectionColorLight(collection, 0.1)
    const darkColor = getCollectionColorDark(collection)
    
    return `
      --collection-${sanitizedName}: ${color};
      --collection-${sanitizedName}-light: ${lightColor};
      --collection-${sanitizedName}-dark: ${darkColor};
    `
  }).join('')
}

/**
 * Validate if a color is in the collection color palette
 */
export function isValidCollectionColor(color: string): boolean {
  return COLLECTION_COLORS.some(c => c.color.toLowerCase() === color.toLowerCase())
}

/**
 * Get the next available color for a new collection
 */
export function getNextAvailableColor(usedCollections: string[]): string {
  const usedColors = usedCollections.map(collection => getCollectionColor(collection))
  const availableColors = COLLECTION_COLORS.filter(c => !usedColors.includes(c.color))

  if (availableColors.length > 0) {
    return availableColors[0].color
  }

  // If all predefined colors are used, generate a new procedural color
  const newCollectionName = `generated-${usedCollections.length}`
  return generateProceduralColor(newCollectionName)
}

/**
 * Verify color consistency between borders and indicators
 * Returns the exact same color for both uses
 */
export function getConsistentCollectionColor(collection: string): {
  borderColor: string
  indicatorColor: string
  textColor: string
  lightColor: string
  darkColor: string
} {
  const baseColor = getCollectionColor(collection)

  return {
    borderColor: baseColor,
    indicatorColor: baseColor, // Exactly the same
    textColor: getCollectionTextColor(collection),
    lightColor: getCollectionColorLight(collection, 0.1),
    darkColor: getCollectionColorDark(collection)
  }
}

/**
 * Get all collection colors currently in use with consistency verification
 */
export function getCollectionColorMap(collections: string[]): Map<string, string> {
  const colorMap = new Map<string, string>()

  collections.forEach(collection => {
    const color = getCollectionColor(collection)
    colorMap.set(collection, color)
  })

  return colorMap
}

/**
 * Debug function to verify color consistency
 */
export function verifyColorConsistency(collections: string[]): {
  isConsistent: boolean
  duplicates: string[]
  totalColors: number
  proceduralColors: number
} {
  const colorMap = new Map<string, string[]>()
  let proceduralCount = 0

  collections.forEach(collection => {
    const color = getCollectionColor(collection)

    // Check if this is a procedural color (not in predefined palette)
    const isPredefined = COLLECTION_COLORS.some(c => c.color === color)
    if (!isPredefined) {
      proceduralCount++
    }

    if (!colorMap.has(color)) {
      colorMap.set(color, [])
    }
    colorMap.get(color)!.push(collection)
  })

  const duplicates: string[] = []
  colorMap.forEach((collections, color) => {
    if (collections.length > 1) {
      duplicates.push(`${color}: ${collections.join(', ')}`)
    }
  })

  return {
    isConsistent: duplicates.length === 0,
    duplicates,
    totalColors: colorMap.size,
    proceduralColors: proceduralCount
  }
}

/**
 * Get color contrast ratio for accessibility
 */
export function getColorContrast(color: string): 'light' | 'dark' {
  const hex = color.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  
  // Calculate relative luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255
  
  return luminance > 0.5 ? 'light' : 'dark'
}

/**
 * Get appropriate text color for a collection background
 */
export function getCollectionTextColor(collection: string): string {
  const bgColor = getCollectionColor(collection)
  const contrast = getColorContrast(bgColor)
  
  return contrast === 'light' ? '#000000' : '#FFFFFF'
}
