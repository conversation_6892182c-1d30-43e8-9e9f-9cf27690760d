/**
 * MODERN CONTENT ANALYSIS SERVICE
 * Implements proper TF-IDF, embeddings, and semantic similarity
 */

import type {
    Bookmark,
    ContentAnalyzer,
    EmbeddingVector,
    SimilarityMatrix,
    Topic
} from './interfaces'

export class ModernContentAnalyzer implements ContentAnalyzer {
  private embeddingCache = new Map<string, EmbeddingVector>()
  private tfidfVectorizer: TFIDFVectorizer
  private topicModel: TopicModel

  // Memory limits
  private readonly MAX_CACHE_SIZE = 1000
  private readonly MAX_EMBEDDING_DIMENSION = 50 // Reduced from 100

  constructor() {
    this.tfidfVectorizer = new TFIDFVectorizer()
    this.topicModel = new TopicModel()

    // Auto-cleanup cache when it gets large
    setInterval(() => this.cleanupCache(), 60000) // Every minute
  }

  async analyzeContentSimilarity(bookmarks: Bookmark[]): Promise<SimilarityMatrix> {
    // Extract text content for analysis
    const documents = bookmarks.map(bookmark => 
      `${bookmark.title} ${bookmark.description || ''} ${bookmark.tags?.join(' ') || ''}`
    )
    
    // Generate embeddings using modern techniques
    const embeddings = await this.generateEmbeddings(documents)
    
    // Calculate cosine similarity matrix
    const similarityMatrix = this.calculateCosineSimilarity(embeddings)
    
    return {
      items: bookmarks.map(b => b.id),
      matrix: similarityMatrix,
      algorithm: 'cosine'
    }
  }

  async generateEmbeddings(texts: string[]): Promise<EmbeddingVector[]> {
    const embeddings: EmbeddingVector[] = []
    
    for (let i = 0; i < texts.length; i++) {
      const text = texts[i]
      const cacheKey = this.hashText(text)
      
      // Check cache first
      if (this.embeddingCache.has(cacheKey)) {
        embeddings.push(this.embeddingCache.get(cacheKey)!)
        continue
      }
      
      // Generate embedding using TF-IDF + semantic enhancement
      const tfidfVector = await this.tfidfVectorizer.transform([text])
      const semanticVector = await this.generateSemanticEmbedding(text)
      
      // Combine TF-IDF and semantic embeddings
      const combinedVector = this.combineVectors(tfidfVector[0], semanticVector)
      
      const embedding: EmbeddingVector = {
        id: `embedding_${i}`,
        vector: combinedVector,
        dimension: combinedVector.length,
        model: 'tfidf_semantic_hybrid'
      }
      
      this.embeddingCache.set(cacheKey, embedding)
      embeddings.push(embedding)
    }
    
    return embeddings
  }

  async extractTopics(content: string): Promise<Topic[]> {
    // Use Latent Dirichlet Allocation (LDA) for topic modeling
    const preprocessedText = this.preprocessText(content)
    const topics = await this.topicModel.extractTopics(preprocessedText)
    
    return topics.map((topic, index) => ({
      id: `topic_${index}`,
      name: topic.name,
      keywords: topic.keywords,
      weight: topic.weight,
      coherence: topic.coherence
    }))
  }

  async calculateSemanticSimilarity(text1: string, text2: string): Promise<number> {
    const [embedding1, embedding2] = await this.generateEmbeddings([text1, text2])
    return this.cosineSimilarity(embedding1.vector, embedding2.vector)
  }

  private calculateCosineSimilarity(embeddings: EmbeddingVector[]): number[][] {
    const n = embeddings.length
    const matrix: number[][] = Array(n).fill(null).map(() => Array(n).fill(0))
    
    for (let i = 0; i < n; i++) {
      for (let j = i; j < n; j++) {
        if (i === j) {
          matrix[i][j] = 1.0
        } else {
          const similarity = this.cosineSimilarity(
            embeddings[i].vector, 
            embeddings[j].vector
          )
          matrix[i][j] = similarity
          matrix[j][i] = similarity
        }
      }
    }
    
    return matrix
  }

  private cosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      throw new Error('Vectors must have the same dimension')
    }
    
    let dotProduct = 0
    let normA = 0
    let normB = 0
    
    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i]
      normA += vectorA[i] * vectorA[i]
      normB += vectorB[i] * vectorB[i]
    }
    
    const magnitude = Math.sqrt(normA) * Math.sqrt(normB)
    return magnitude === 0 ? 0 : dotProduct / magnitude
  }

  private cleanupCache(): void {
    if (this.embeddingCache.size > this.MAX_CACHE_SIZE) {
      // Remove oldest 50% of cache entries
      const entries = Array.from(this.embeddingCache.entries())
      const toRemove = Math.floor(entries.length * 0.5)

      for (let i = 0; i < toRemove; i++) {
        this.embeddingCache.delete(entries[i][0])
      }

      console.log(`🧹 ContentAnalyzer cache cleaned: ${toRemove} entries removed`)
    }
  }

  private async generateSemanticEmbedding(text: string): Promise<number[]> {
    // Simplified semantic embedding using word frequency and semantic weights
    const words = this.tokenize(text)
    const semanticWeights = await this.getSemanticWeights(words)

    // Create a fixed-dimension semantic vector (reduced size)
    const dimension = this.MAX_EMBEDDING_DIMENSION
    const vector = new Array(dimension).fill(0)
    
    words.forEach((word, index) => {
      const weight = semanticWeights.get(word) || 0.1
      const position = this.hashWord(word) % dimension
      vector[position] += weight
    })
    
    // Normalize the vector
    const norm = Math.sqrt(vector.reduce((sum, val) => sum + val * val, 0))
    return norm > 0 ? vector.map(val => val / norm) : vector
  }

  private combineVectors(tfidfVector: number[], semanticVector: number[]): number[] {
    // Weighted combination of TF-IDF and semantic vectors
    const tfidfWeight = 0.7
    const semanticWeight = 0.3
    
    const maxLength = Math.max(tfidfVector.length, semanticVector.length)
    const combined = new Array(maxLength).fill(0)
    
    for (let i = 0; i < maxLength; i++) {
      const tfidf = i < tfidfVector.length ? tfidfVector[i] : 0
      const semantic = i < semanticVector.length ? semanticVector[i] : 0
      combined[i] = tfidf * tfidfWeight + semantic * semanticWeight
    }
    
    return combined
  }

  private preprocessText(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .replace(/\s+/g, ' ')
      .trim()
  }

  private tokenize(text: string): string[] {
    const preprocessed = this.preprocessText(text)
    const words = preprocessed.split(' ').filter(word => word.length > 2)
    
    // Remove common stop words
    const stopWords = new Set([
      'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
      'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
      'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should'
    ])
    
    return words.filter(word => !stopWords.has(word))
  }

  private async getSemanticWeights(words: string[]): Promise<Map<string, number>> {
    const weights = new Map<string, number>()
    
    // Simplified semantic weighting based on word categories
    const techWords = new Set(['javascript', 'react', 'typescript', 'css', 'html', 'node', 'api'])
    const designWords = new Set(['design', 'ui', 'ux', 'figma', 'sketch', 'prototype'])
    const learningWords = new Set(['tutorial', 'guide', 'learn', 'course', 'documentation'])
    
    words.forEach(word => {
      let weight = 0.1 // base weight
      
      if (techWords.has(word)) weight = 0.8
      else if (designWords.has(word)) weight = 0.7
      else if (learningWords.has(word)) weight = 0.6
      else if (word.length > 6) weight = 0.4 // longer words tend to be more specific
      
      weights.set(word, weight)
    })
    
    return weights
  }

  private hashText(text: string): string {
    // Simple hash function for caching
    let hash = 0
    for (let i = 0; i < text.length; i++) {
      const char = text.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }
    return hash.toString()
  }

  private hashWord(word: string): number {
    let hash = 0
    for (let i = 0; i < word.length; i++) {
      const char = word.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }
    return Math.abs(hash)
  }
}

// Supporting classes
class TFIDFVectorizer {
  private vocabulary = new Map<string, number>()
  private idfScores = new Map<string, number>()
  
  async transform(documents: string[]): Promise<number[][]> {
    // Build vocabulary and calculate IDF scores
    this.buildVocabulary(documents)
    this.calculateIDF(documents)
    
    // Transform documents to TF-IDF vectors
    return documents.map(doc => this.documentToVector(doc))
  }
  
  private buildVocabulary(documents: string[]): void {
    const allWords = new Set<string>()
    
    documents.forEach(doc => {
      const words = this.tokenize(doc)
      words.forEach(word => allWords.add(word))
    })
    
    Array.from(allWords).forEach((word, index) => {
      this.vocabulary.set(word, index)
    })
  }
  
  private calculateIDF(documents: string[]): void {
    const docCount = documents.length
    
    this.vocabulary.forEach((index, word) => {
      const docsWithWord = documents.filter(doc => 
        this.tokenize(doc).includes(word)
      ).length
      
      const idf = Math.log(docCount / (1 + docsWithWord))
      this.idfScores.set(word, idf)
    })
  }
  
  private documentToVector(document: string): number[] {
    const words = this.tokenize(document)
    const vector = new Array(this.vocabulary.size).fill(0)
    
    // Calculate term frequencies
    const termFreq = new Map<string, number>()
    words.forEach(word => {
      termFreq.set(word, (termFreq.get(word) || 0) + 1)
    })
    
    // Calculate TF-IDF scores
    termFreq.forEach((tf, word) => {
      const index = this.vocabulary.get(word)
      const idf = this.idfScores.get(word) || 0
      
      if (index !== undefined) {
        vector[index] = tf * idf
      }
    })
    
    return vector
  }
  
  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2)
  }
}

class TopicModel {
  async extractTopics(text: string): Promise<Array<{
    name: string
    keywords: string[]
    weight: number
    coherence: number
  }>> {
    // Simplified topic extraction using keyword clustering
    const words = this.tokenize(text)
    const clusters = this.clusterKeywords(words)
    
    return clusters.map((cluster, index) => ({
      name: this.generateTopicName(cluster),
      keywords: cluster,
      weight: cluster.length / words.length,
      coherence: this.calculateCoherence(cluster)
    }))
  }
  
  private tokenize(text: string): string[] {
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
  }
  
  private clusterKeywords(words: string[]): string[][] {
    // Simple clustering based on semantic similarity
    const clusters: string[][] = []
    const used = new Set<string>()
    
    words.forEach(word => {
      if (used.has(word)) return
      
      const cluster = [word]
      used.add(word)
      
      // Find similar words
      words.forEach(otherWord => {
        if (!used.has(otherWord) && this.areWordsSimilar(word, otherWord)) {
          cluster.push(otherWord)
          used.add(otherWord)
        }
      })
      
      if (cluster.length > 1) {
        clusters.push(cluster)
      }
    })
    
    return clusters
  }
  
  private areWordsSimilar(word1: string, word2: string): boolean {
    // Simple similarity based on common prefixes/suffixes
    if (word1.length < 4 || word2.length < 4) return false
    
    const prefix1 = word1.substring(0, 3)
    const prefix2 = word2.substring(0, 3)
    const suffix1 = word1.substring(word1.length - 3)
    const suffix2 = word2.substring(word2.length - 3)
    
    return prefix1 === prefix2 || suffix1 === suffix2
  }
  
  private generateTopicName(keywords: string[]): string {
    // Use the most frequent or longest keyword as topic name
    return keywords.reduce((longest, current) => 
      current.length > longest.length ? current : longest
    )
  }
  
  private calculateCoherence(keywords: string[]): number {
    // Simplified coherence calculation
    return Math.min(1.0, keywords.length / 10)
  }
}
