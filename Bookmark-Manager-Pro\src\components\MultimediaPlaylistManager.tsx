/**
 * MULTIMEDIA PLAYLIST MANAGER
 * Complete integration manager for multimedia playlist functionality
 * Handles all playlist operations, integrations, and UI coordination
 */

import { Settings } from 'lucide-react'
import React, { useEffect, useState } from 'react'
import { useBookmarks } from '../contexts/BookmarkContext'
import { useModernTheme } from '../contexts/ModernThemeContext'
import { MultimediaPlaylist, multimediaPlaylistService } from '../services/multimedia/MultimediaPlaylistService'
import type { Bookmark } from '../types'
import { MultimediaPlaylistIntegration } from './MultimediaPlaylistIntegration'
import { MultimediaPlaylistPanel } from './MultimediaPlaylistPanel'

interface MultimediaPlaylistManagerProps {
  // Context for playlist creation
  selectedBookmarks?: Bookmark[]
  collectionId?: string
  mindMapSelection?: string[]
  
  // UI configuration
  mode?: 'panel' | 'integration' | 'floating' | 'sidebar'
  size?: 'small' | 'medium' | 'large'
  autoShow?: boolean
  
  // Callbacks
  onPlaylistCreated?: (playlist: MultimediaPlaylist) => void
  onPlaybackStarted?: (sessionId: string) => void
  onExportCompleted?: (result: any) => void
}

export const MultimediaPlaylistManager: React.FC<MultimediaPlaylistManagerProps> = ({
  selectedBookmarks,
  collectionId,
  mindMapSelection,
  mode = 'integration',
  size = 'medium',
  autoShow = false,
  onPlaylistCreated,
  onPlaybackStarted,
  onExportCompleted
}) => {
  const { bookmarks, playlists } = useBookmarks()
  const { themeMode } = useModernTheme()
  const isModernTheme = themeMode === 'modern'

  // State management
  const [showPanel, setShowPanel] = useState(autoShow)
  const [currentPlaylist, setCurrentPlaylist] = useState<MultimediaPlaylist | null>(null)
  const [smartSuggestions, setSmartSuggestions] = useState<MultimediaPlaylist[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Auto-generate smart suggestions when bookmarks change
  useEffect(() => {
    if (bookmarks.length > 0 && playlists.length >= 0) {
      generateSmartSuggestions()
    }
  }, [bookmarks, playlists])

  const generateSmartSuggestions = async () => {
    try {
      setIsLoading(true)
      const suggestions = await multimediaPlaylistService.getSmartPlaylistSuggestions(
        bookmarks,
        playlists
      )
      setSmartSuggestions(suggestions)
    } catch (err) {
      console.error('Failed to generate smart suggestions:', err)
      setError('Failed to generate playlist suggestions')
    } finally {
      setIsLoading(false)
    }
  }

  const handlePlaylistCreated = (playlist: MultimediaPlaylist) => {
    setCurrentPlaylist(playlist)
    onPlaylistCreated?.(playlist)
    console.log('🎬 Multimedia playlist created:', playlist.name)
  }

  const handlePlaybackStarted = (sessionId: string) => {
    onPlaybackStarted?.(sessionId)
    console.log('▶️ Playback session started:', sessionId)
  }

  const handleExportCompleted = (result: any) => {
    onExportCompleted?.(result)
    console.log('📤 Export completed:', result)
  }

  // Quick action handlers
  const createQuickVideoPlaylist = async () => {
    const videoBookmarks = bookmarks.filter(b => 
      b.url.toLowerCase().includes('youtube.com') || 
      b.url.toLowerCase().includes('vimeo.com')
    )
    
    if (videoBookmarks.length === 0) {
      setError('No video bookmarks found')
      return
    }

    try {
      const playlist = await multimediaPlaylistService.createMultimediaPlaylist(
        'Quick Video Playlist',
        'Auto-generated video playlist',
        videoBookmarks.slice(0, 10),
        { autoDetectTypes: true, enableTTS: false }
      )
      handlePlaylistCreated(playlist)
      setShowPanel(true)
    } catch (err) {
      setError('Failed to create video playlist')
    }
  }

  const createGymModePlaylist = async () => {
    const targetBookmarks = selectedBookmarks || bookmarks.slice(0, 15)
    
    try {
      const playlist = await multimediaPlaylistService.createMultimediaPlaylist(
        '🏃‍♂️ Gym Mode Playlist',
        'Optimized for hands-free workout sessions',
        targetBookmarks,
        {
          autoDetectTypes: true,
          enableTTS: true,
          defaultPlaybackSettings: {
            autoPlay: true,
            textToSpeechEnabled: true,
            volume: 80
          }
        }
      )
      handlePlaylistCreated(playlist)
      setShowPanel(true)
    } catch (err) {
      setError('Failed to create gym mode playlist')
    }
  }

  // Render different modes
  const renderContent = () => {
    switch (mode) {
      case 'panel':
        return (
          <MultimediaPlaylistPanel
            isOpen={showPanel}
            onClose={() => setShowPanel(false)}
            selectedBookmarks={selectedBookmarks}
            collectionId={collectionId}
            mindMapSelection={mindMapSelection}
          />
        )

      case 'floating':
        return (
          <div className="fixed bottom-6 right-6 z-40">
            <div className={`
              ${isModernTheme ? 'bg-black/80 backdrop-blur-xl border-white/20' : 'bg-white shadow-lg border-gray-200'}
              rounded-xl p-4 border max-w-sm
            `}>
              <h3 className={`font-semibold mb-3 ${
                isModernTheme ? 'text-white' : 'text-gray-900'
              }`}>
                🎬 Multimedia Playlists
              </h3>
              
              <div className="space-y-2">
                <button
                  onClick={createQuickVideoPlaylist}
                  className={`w-full p-2 rounded text-sm transition-colors ${
                    isModernTheme
                      ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30'
                      : 'bg-red-100 text-red-700 hover:bg-red-200'
                  }`}
                >
                  🎥 Quick Video Queue
                </button>
                
                <button
                  onClick={createGymModePlaylist}
                  className={`w-full p-2 rounded text-sm transition-colors ${
                    isModernTheme
                      ? 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                      : 'bg-green-100 text-green-700 hover:bg-green-200'
                  }`}
                >
                  🏃‍♂️ Gym Mode
                </button>
                
                <button
                  onClick={() => setShowPanel(true)}
                  className={`w-full p-2 rounded text-sm transition-colors ${
                    isModernTheme
                      ? 'bg-blue-500/20 text-blue-300 hover:bg-blue-500/30'
                      : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                  }`}
                >
                  ⚙️ Advanced Options
                </button>
              </div>

              {smartSuggestions.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200/20">
                  <p className={`text-xs mb-2 ${
                    isModernTheme ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Smart Suggestions ({smartSuggestions.length})
                  </p>
                  {smartSuggestions.slice(0, 2).map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setCurrentPlaylist(suggestion)
                        setShowPanel(true)
                      }}
                      className={`w-full p-2 mb-1 rounded text-xs text-left transition-colors ${
                        isModernTheme
                          ? 'text-white hover:opacity-80'
                          : 'hover:opacity-80'
                      }`}
                    >
                      ✨ {suggestion.name}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>
        )

      case 'sidebar':
        return (
          <div className={`
            ${isModernTheme ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'}
            border rounded-lg p-4 mb-4
          `}>
            <div className="flex items-center justify-between mb-3">
              <h3 className={`font-semibold ${
                isModernTheme ? 'text-white' : 'text-gray-900'
              }`}>
                🎬 Multimedia Playlists
              </h3>
              <button
                onClick={() => setShowPanel(true)}
                className={`p-1 rounded transition-colors ${
                  isModernTheme
                    ? 'text-white hover:opacity-80'
                    : 'hover:opacity-80'
                }`}
              >
                <Settings className="w-4 h-4" />
              </button>
            </div>

            <div className="grid grid-cols-2 gap-2 mb-3">
              <button
                onClick={createQuickVideoPlaylist}
                className={`p-2 rounded text-xs transition-colors ${
                  isModernTheme
                    ? 'bg-red-500/20 text-red-300 hover:bg-red-500/30'
                    : 'bg-red-100 text-red-700 hover:bg-red-200'
                }`}
              >
                🎥 Videos
              </button>
              <button
                onClick={createGymModePlaylist}
                className={`p-2 rounded text-xs transition-colors ${
                  isModernTheme
                    ? 'bg-green-500/20 text-green-300 hover:bg-green-500/30'
                    : 'bg-green-100 text-green-700 hover:bg-green-200'
                }`}
              >
                🏃‍♂️ Gym
              </button>
            </div>

            {isLoading && (
              <div className="text-center py-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mx-auto"></div>
              </div>
            )}

            {error && (
              <div className="text-red-500 text-xs p-2 bg-red-100 rounded mb-2">
                {error}
              </div>
            )}
          </div>
        )

      default: // integration
        return (
          <MultimediaPlaylistIntegration
            selectedBookmarks={selectedBookmarks}
            collectionId={collectionId}
            mindMapSelection={mindMapSelection}
            size={size}
            position="inline"
          />
        )
    }
  }

  return (
    <>
      {renderContent()}
      
      {/* Always render the panel for advanced functionality */}
      <MultimediaPlaylistPanel
        isOpen={showPanel}
        onClose={() => setShowPanel(false)}
        selectedBookmarks={selectedBookmarks}
        collectionId={collectionId}
        mindMapSelection={mindMapSelection}
      />
    </>
  )
}

export default MultimediaPlaylistManager
