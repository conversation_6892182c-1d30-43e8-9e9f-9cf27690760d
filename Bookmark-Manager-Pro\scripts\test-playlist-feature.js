#!/usr/bin/env node

/**
 * PLAYLIST FEATURE TEST RUNNER
 * Comprehensive testing script for playlist components and services
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🧪 PLAYLIST FEATURE TESTING SUITE');
console.log('=================================');

// Test configuration
const testConfig = {
  testFiles: [
    'src/tests/playlist.test.ts',
    'src/tests/playlist-integration.test.ts'
  ],
  coverage: true,
  verbose: true,
  timeout: 30000
};

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function colorLog(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// Check if test files exist
function checkTestFiles() {
  colorLog('blue', '\n📋 Checking test files...');
  
  let allFilesExist = true;
  
  testConfig.testFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      colorLog('green', `✅ ${file}`);
    } else {
      colorLog('red', `❌ ${file} - NOT FOUND`);
      allFilesExist = false;
    }
  });
  
  return allFilesExist;
}

// Run individual test file
function runTestFile(testFile) {
  colorLog('cyan', `\n🧪 Running ${testFile}...`);
  
  try {
    const command = `npx vitest run ${testFile} --reporter=verbose`;
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    colorLog('green', '✅ Test passed');
    console.log(output);
    return true;
  } catch (error) {
    colorLog('red', '❌ Test failed');
    console.error(error.stdout || error.message);
    return false;
  }
}

// Run all tests
function runAllTests() {
  colorLog('magenta', '\n🚀 Running all playlist tests...');
  
  try {
    const command = testConfig.coverage 
      ? 'npx vitest run src/tests/playlist*.test.ts --coverage --reporter=verbose'
      : 'npx vitest run src/tests/playlist*.test.ts --reporter=verbose';
    
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    colorLog('green', '✅ All tests passed');
    console.log(output);
    return true;
  } catch (error) {
    colorLog('red', '❌ Some tests failed');
    console.error(error.stdout || error.message);
    return false;
  }
}

// Generate test report
function generateTestReport() {
  colorLog('blue', '\n📊 Generating test report...');
  
  const reportData = {
    timestamp: new Date().toISOString(),
    testFiles: testConfig.testFiles,
    configuration: testConfig,
    results: {
      // This would be populated with actual test results
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      coverage: 0
    }
  };
  
  const reportPath = path.join(process.cwd(), 'test-reports', 'playlist-feature-report.json');
  
  // Ensure reports directory exists
  const reportsDir = path.dirname(reportPath);
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(reportData, null, 2));
  colorLog('green', `✅ Report generated: ${reportPath}`);
}

// Manual test scenarios
function runManualTestScenarios() {
  colorLog('yellow', '\n🎯 MANUAL TEST SCENARIOS');
  colorLog('yellow', '========================');
  
  const scenarios = [
    {
      name: 'Playlist Creation',
      steps: [
        '1. Open PlaylistPanel',
        '2. Enter playlist name: "Test Playlist"',
        '3. Enter description: "Test Description"',
        '4. Select color: Blue (#3b82f6)',
        '5. Click "Create Playlist"',
        '6. Verify playlist appears in list'
      ],
      expected: 'New playlist created and displayed'
    },
    {
      name: 'AI Suggestion Generation',
      steps: [
        '1. Click "AI Suggest" button',
        '2. Wait for suggestions to load',
        '3. Verify suggestions appear',
        '4. Check suggestion quality and relevance',
        '5. Click "Create" on a suggestion'
      ],
      expected: 'Relevant playlist suggestions generated and can be created'
    },
    {
      name: 'Playlist Analytics',
      steps: [
        '1. Click analytics button on existing playlist',
        '2. Verify analytics modal opens',
        '3. Check all analytics sections load',
        '4. Verify recommendations are actionable',
        '5. Close modal'
      ],
      expected: 'Comprehensive analytics displayed with actionable insights'
    },
    {
      name: 'Auto-Create Feature',
      steps: [
        '1. Click "Auto-Create" button',
        '2. Wait for processing',
        '3. Verify high-confidence playlists are created',
        '4. Check playlist quality and naming',
        '5. Verify no duplicates created'
      ],
      expected: 'High-quality playlists automatically created'
    },
    {
      name: 'Playlist Editing',
      steps: [
        '1. Click edit button on existing playlist',
        '2. Modify name and description',
        '3. Change color',
        '4. Click "Save"',
        '5. Verify changes are applied'
      ],
      expected: 'Playlist properties updated successfully'
    }
  ];
  
  scenarios.forEach((scenario, index) => {
    colorLog('cyan', `\n${index + 1}. ${scenario.name}`);
    console.log('Steps:');
    scenario.steps.forEach(step => console.log(`   ${step}`));
    colorLog('green', `Expected: ${scenario.expected}`);
  });
}

// Performance test scenarios
function runPerformanceTests() {
  colorLog('yellow', '\n⚡ PERFORMANCE TEST SCENARIOS');
  colorLog('yellow', '=============================');
  
  const performanceTests = [
    {
      name: 'Large Dataset Processing',
      description: 'Test with 1000+ bookmarks',
      target: '< 5 seconds processing time',
      memory: '< 50MB memory increase'
    },
    {
      name: 'Real-time Suggestion Generation',
      description: 'Generate suggestions while user types',
      target: '< 500ms response time',
      memory: '< 10MB memory increase'
    },
    {
      name: 'Analytics Calculation',
      description: 'Calculate comprehensive analytics',
      target: '< 2 seconds for medium playlist',
      memory: '< 20MB memory increase'
    },
    {
      name: 'Memory Cleanup',
      description: 'Verify memory is released after operations',
      target: 'Memory returns to baseline',
      memory: 'No memory leaks detected'
    }
  ];
  
  performanceTests.forEach((test, index) => {
    colorLog('cyan', `\n${index + 1}. ${test.name}`);
    console.log(`   Description: ${test.description}`);
    colorLog('green', `   Target: ${test.target}`);
    colorLog('blue', `   Memory: ${test.memory}`);
  });
}

// Main execution
function main() {
  try {
    // Check prerequisites
    if (!checkTestFiles()) {
      colorLog('red', '\n❌ Test files missing. Please ensure test files exist.');
      process.exit(1);
    }
    
    // Run automated tests
    const testsPass = runAllTests();
    
    // Generate report
    generateTestReport();
    
    // Display manual test scenarios
    runManualTestScenarios();
    
    // Display performance test scenarios
    runPerformanceTests();
    
    // Summary
    colorLog('bright', '\n📋 TESTING SUMMARY');
    colorLog('bright', '==================');
    
    if (testsPass) {
      colorLog('green', '✅ Automated tests: PASSED');
    } else {
      colorLog('red', '❌ Automated tests: FAILED');
    }
    
    colorLog('blue', '📋 Manual tests: See scenarios above');
    colorLog('yellow', '⚡ Performance tests: See scenarios above');
    
    colorLog('bright', '\n🎯 NEXT STEPS:');
    console.log('1. Run manual test scenarios in the browser');
    console.log('2. Execute performance tests with large datasets');
    console.log('3. Monitor memory usage during testing');
    console.log('4. Verify all features work as expected');
    
    if (!testsPass) {
      process.exit(1);
    }
    
  } catch (error) {
    colorLog('red', `\n❌ Testing failed: ${error.message}`);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runAllTests,
  runTestFile,
  generateTestReport,
  checkTestFiles
};
