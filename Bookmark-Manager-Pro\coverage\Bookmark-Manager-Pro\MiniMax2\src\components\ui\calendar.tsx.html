
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for Bookmark-Manager-Pro/MiniMax2/src/components/ui/calendar.tsx</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../../../prettify.css" />
    <link rel="stylesheet" href="../../../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../../../index.html">All files</a> / <a href="index.html">Bookmark-Manager-Pro/MiniMax2/src/components/ui</a> calendar.tsx</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>0/64</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">100% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>1/1</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">0% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>0/64</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <pre><table class="coverage">
<tr><td class="line-count quiet"><a name='L1'></a><a href='#L1'>1</a>
<a name='L2'></a><a href='#L2'>2</a>
<a name='L3'></a><a href='#L3'>3</a>
<a name='L4'></a><a href='#L4'>4</a>
<a name='L5'></a><a href='#L5'>5</a>
<a name='L6'></a><a href='#L6'>6</a>
<a name='L7'></a><a href='#L7'>7</a>
<a name='L8'></a><a href='#L8'>8</a>
<a name='L9'></a><a href='#L9'>9</a>
<a name='L10'></a><a href='#L10'>10</a>
<a name='L11'></a><a href='#L11'>11</a>
<a name='L12'></a><a href='#L12'>12</a>
<a name='L13'></a><a href='#L13'>13</a>
<a name='L14'></a><a href='#L14'>14</a>
<a name='L15'></a><a href='#L15'>15</a>
<a name='L16'></a><a href='#L16'>16</a>
<a name='L17'></a><a href='#L17'>17</a>
<a name='L18'></a><a href='#L18'>18</a>
<a name='L19'></a><a href='#L19'>19</a>
<a name='L20'></a><a href='#L20'>20</a>
<a name='L21'></a><a href='#L21'>21</a>
<a name='L22'></a><a href='#L22'>22</a>
<a name='L23'></a><a href='#L23'>23</a>
<a name='L24'></a><a href='#L24'>24</a>
<a name='L25'></a><a href='#L25'>25</a>
<a name='L26'></a><a href='#L26'>26</a>
<a name='L27'></a><a href='#L27'>27</a>
<a name='L28'></a><a href='#L28'>28</a>
<a name='L29'></a><a href='#L29'>29</a>
<a name='L30'></a><a href='#L30'>30</a>
<a name='L31'></a><a href='#L31'>31</a>
<a name='L32'></a><a href='#L32'>32</a>
<a name='L33'></a><a href='#L33'>33</a>
<a name='L34'></a><a href='#L34'>34</a>
<a name='L35'></a><a href='#L35'>35</a>
<a name='L36'></a><a href='#L36'>36</a>
<a name='L37'></a><a href='#L37'>37</a>
<a name='L38'></a><a href='#L38'>38</a>
<a name='L39'></a><a href='#L39'>39</a>
<a name='L40'></a><a href='#L40'>40</a>
<a name='L41'></a><a href='#L41'>41</a>
<a name='L42'></a><a href='#L42'>42</a>
<a name='L43'></a><a href='#L43'>43</a>
<a name='L44'></a><a href='#L44'>44</a>
<a name='L45'></a><a href='#L45'>45</a>
<a name='L46'></a><a href='#L46'>46</a>
<a name='L47'></a><a href='#L47'>47</a>
<a name='L48'></a><a href='#L48'>48</a>
<a name='L49'></a><a href='#L49'>49</a>
<a name='L50'></a><a href='#L50'>50</a>
<a name='L51'></a><a href='#L51'>51</a>
<a name='L52'></a><a href='#L52'>52</a>
<a name='L53'></a><a href='#L53'>53</a>
<a name='L54'></a><a href='#L54'>54</a>
<a name='L55'></a><a href='#L55'>55</a>
<a name='L56'></a><a href='#L56'>56</a>
<a name='L57'></a><a href='#L57'>57</a>
<a name='L58'></a><a href='#L58'>58</a>
<a name='L59'></a><a href='#L59'>59</a>
<a name='L60'></a><a href='#L60'>60</a>
<a name='L61'></a><a href='#L61'>61</a>
<a name='L62'></a><a href='#L62'>62</a>
<a name='L63'></a><a href='#L63'>63</a>
<a name='L64'></a><a href='#L64'>64</a>
<a name='L65'></a><a href='#L65'>65</a>
<a name='L66'></a><a href='#L66'>66</a>
<a name='L67'></a><a href='#L67'>67</a>
<a name='L68'></a><a href='#L68'>68</a>
<a name='L69'></a><a href='#L69'>69</a>
<a name='L70'></a><a href='#L70'>70</a>
<a name='L71'></a><a href='#L71'>71</a>
<a name='L72'></a><a href='#L72'>72</a>
<a name='L73'></a><a href='#L73'>73</a>
<a name='L74'></a><a href='#L74'>74</a>
<a name='L75'></a><a href='#L75'>75</a></td><td class="line-coverage quiet"><span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-no">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span>
<span class="cline-any cline-neutral">&nbsp;</span></td><td class="text"><pre class="prettyprint lang-js">import * as React from "react"
<span class="cstat-no" title="statement not covered" >import { ChevronLeft, ChevronRight } from "lucide-react"</span>
<span class="cstat-no" title="statement not covered" >import { DayPicker } from "react-day-picker"</span>
&nbsp;
<span class="cstat-no" title="statement not covered" >import { cn } from "@/lib/utils"</span>
<span class="cstat-no" title="statement not covered" >import { buttonVariants } from "@/components/ui/button"</span>
&nbsp;
export type CalendarProps = React.ComponentProps&lt;typeof DayPicker&gt;
&nbsp;
<span class="cstat-no" title="statement not covered" >function Calendar({</span>
<span class="cstat-no" title="statement not covered" >  className,</span>
<span class="cstat-no" title="statement not covered" >  classNames,</span>
<span class="cstat-no" title="statement not covered" >  showOutsideDays = true,</span>
<span class="cstat-no" title="statement not covered" >  ...props</span>
<span class="cstat-no" title="statement not covered" >}: CalendarProps) {</span>
<span class="cstat-no" title="statement not covered" >  return (</span>
<span class="cstat-no" title="statement not covered" >    &lt;DayPicker</span>
<span class="cstat-no" title="statement not covered" >      showOutsideDays={showOutsideDays}</span>
<span class="cstat-no" title="statement not covered" >      className={cn("p-3", className)}</span>
<span class="cstat-no" title="statement not covered" >      classNames={{</span>
<span class="cstat-no" title="statement not covered" >        months: "flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0",</span>
<span class="cstat-no" title="statement not covered" >        month: "space-y-4",</span>
<span class="cstat-no" title="statement not covered" >        caption: "flex justify-center pt-1 relative items-center",</span>
<span class="cstat-no" title="statement not covered" >        caption_label: "text-sm font-medium",</span>
<span class="cstat-no" title="statement not covered" >        nav: "space-x-1 flex items-center",</span>
<span class="cstat-no" title="statement not covered" >        nav_button: cn(</span>
<span class="cstat-no" title="statement not covered" >          buttonVariants({ variant: "outline" }),</span>
<span class="cstat-no" title="statement not covered" >          "h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100"</span>
<span class="cstat-no" title="statement not covered" >        ),</span>
<span class="cstat-no" title="statement not covered" >        nav_button_previous: "absolute left-1",</span>
<span class="cstat-no" title="statement not covered" >        nav_button_next: "absolute right-1",</span>
<span class="cstat-no" title="statement not covered" >        table: "w-full border-collapse space-y-1",</span>
<span class="cstat-no" title="statement not covered" >        head_row: "flex",</span>
<span class="cstat-no" title="statement not covered" >        head_cell:</span>
<span class="cstat-no" title="statement not covered" >          "text-zinc-500 rounded-md w-8 font-normal text-[0.8rem] dark:text-zinc-400",</span>
<span class="cstat-no" title="statement not covered" >        row: "flex w-full mt-2",</span>
<span class="cstat-no" title="statement not covered" >        cell: cn(</span>
<span class="cstat-no" title="statement not covered" >          "relative p-0 text-center text-sm focus-within:relative focus-within:z-20 [&amp;:has([aria-selected])]:bg-zinc-100 [&amp;:has([aria-selected].day-outside)]:bg-zinc-100/50 [&amp;:has([aria-selected].day-range-end)]:rounded-r-md dark:[&amp;:has([aria-selected])]:bg-zinc-800 dark:[&amp;:has([aria-selected].day-outside)]:bg-zinc-800/50",</span>
<span class="cstat-no" title="statement not covered" >          props.mode === "range"</span>
<span class="cstat-no" title="statement not covered" >            ? "[&amp;:has(&gt;.day-range-end)]:rounded-r-md [&amp;:has(&gt;.day-range-start)]:rounded-l-md first:[&amp;:has([aria-selected])]:rounded-l-md last:[&amp;:has([aria-selected])]:rounded-r-md"</span>
<span class="cstat-no" title="statement not covered" >            : "[&amp;:has([aria-selected])]:rounded-md"</span>
<span class="cstat-no" title="statement not covered" >        ),</span>
<span class="cstat-no" title="statement not covered" >        day: cn(</span>
<span class="cstat-no" title="statement not covered" >          buttonVariants({ variant: "ghost" }),</span>
<span class="cstat-no" title="statement not covered" >          "h-8 w-8 p-0 font-normal aria-selected:opacity-100"</span>
<span class="cstat-no" title="statement not covered" >        ),</span>
<span class="cstat-no" title="statement not covered" >        day_range_start: "day-range-start",</span>
<span class="cstat-no" title="statement not covered" >        day_range_end: "day-range-end",</span>
<span class="cstat-no" title="statement not covered" >        day_selected:</span>
<span class="cstat-no" title="statement not covered" >          "bg-zinc-900 text-zinc-50 hover:bg-zinc-900 hover:text-zinc-50 focus:bg-zinc-900 focus:text-zinc-50 dark:bg-zinc-50 dark:text-zinc-900 dark:hover:bg-zinc-50 dark:hover:text-zinc-900 dark:focus:bg-zinc-50 dark:focus:text-zinc-900",</span>
<span class="cstat-no" title="statement not covered" >        day_today: "bg-zinc-100 text-zinc-900 dark:bg-zinc-800 dark:text-zinc-50",</span>
<span class="cstat-no" title="statement not covered" >        day_outside:</span>
<span class="cstat-no" title="statement not covered" >          "day-outside text-zinc-500 aria-selected:bg-zinc-100/50 aria-selected:text-zinc-500 dark:text-zinc-400 dark:aria-selected:bg-zinc-800/50 dark:aria-selected:text-zinc-400",</span>
<span class="cstat-no" title="statement not covered" >        day_disabled: "text-zinc-500 opacity-50 dark:text-zinc-400",</span>
<span class="cstat-no" title="statement not covered" >        day_range_middle:</span>
<span class="cstat-no" title="statement not covered" >          "aria-selected:bg-zinc-100 aria-selected:text-zinc-900 dark:aria-selected:bg-zinc-800 dark:aria-selected:text-zinc-50",</span>
<span class="cstat-no" title="statement not covered" >        day_hidden: "invisible",</span>
<span class="cstat-no" title="statement not covered" >        ...classNames,</span>
<span class="cstat-no" title="statement not covered" >      }}</span>
<span class="cstat-no" title="statement not covered" >      components={{</span>
<span class="cstat-no" title="statement not covered" >        IconLeft: ({ className, ...props }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;ChevronLeft className={cn("h-4 w-4", className)} {...props} /&gt;</span>
        ),
<span class="cstat-no" title="statement not covered" >        IconRight: ({ className, ...props }) =&gt; (</span>
<span class="cstat-no" title="statement not covered" >          &lt;ChevronRight className={cn("h-4 w-4", className)} {...props} /&gt;</span>
        ),
<span class="cstat-no" title="statement not covered" >      }}</span>
<span class="cstat-no" title="statement not covered" >      {...props}</span>
<span class="cstat-no" title="statement not covered" >    /&gt;</span>
  )
<span class="cstat-no" title="statement not covered" >}</span>
<span class="cstat-no" title="statement not covered" >Calendar.displayName = "Calendar"</span>
&nbsp;
export { Calendar }
&nbsp;</pre></td></tr></table></pre>

                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-27T06:55:52.394Z
            </div>
        <script src="../../../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../../../sorter.js"></script>
        <script src="../../../../../block-navigation.js"></script>
    </body>
</html>
    