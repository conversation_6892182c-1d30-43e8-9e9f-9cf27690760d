/**
 * MULTIMEDIA PLAYLIST INTEGRATION STYLES
 * Styles for the integrated multimedia playlist functionality
 */

/* Floating Multimedia Button */
.floating-multimedia-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #8B5CF6, #A855F7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(139, 92, 246, 0.4);
  z-index: 1000;
  font-size: 24px;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.floating-multimedia-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(139, 92, 246, 0.6);
  background: linear-gradient(135deg, #7C3AED, #8B5CF6);
}

.floating-multimedia-button:active {
  transform: scale(0.95);
}

/* Floating Play Button */
.floating-play-button {
  position: fixed;
  bottom: 90px;
  right: 20px;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #10B981, #34D399);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(16, 185, 129, 0.4);
  z-index: 1000;
  font-size: 20px;
  transition: all 0.3s ease;
  border: none;
  outline: none;
}

.floating-play-button:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(16, 185, 129, 0.6);
  background: linear-gradient(135deg, #059669, #10B981);
}

.floating-play-button:active {
  transform: scale(0.95);
}

.floating-play-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Floating buttons animation */
@keyframes float-in {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.floating-multimedia-button,
.floating-play-button {
  animation: float-in 0.5s ease-out;
}

/* Multimedia Quick Actions Bar */
.multimedia-quick-actions {
  display: flex;
  gap: 8px;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.multimedia-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-height: 32px;
}

.multimedia-action-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.multimedia-action-btn:active {
  transform: translateY(0);
}

/* Specific button styles */
.multimedia-action-btn[style*="background-color: #8B5CF6"] {
  background: linear-gradient(135deg, #8B5CF6, #A855F7) !important;
}

.multimedia-action-btn[style*="background-color: #EF4444"] {
  background: linear-gradient(135deg, #EF4444, #F87171) !important;
}

.multimedia-action-btn[style*="background-color: #10B981"] {
  background: linear-gradient(135deg, #10B981, #34D399) !important;
}

/* Multimedia sidebar styling removed - now uses standard bookmark tool styling */

/* Responsive design */
@media (max-width: 768px) {
  .multimedia-quick-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .multimedia-action-btn {
    width: 100%;
    justify-content: center;
  }
  
  .floating-multimedia-button {
    width: 50px;
    height: 50px;
    font-size: 20px;
    bottom: 15px;
    right: 15px;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .multimedia-quick-actions {
    border-bottom-color: #374151;
  }
}

/* Modern theme integration */
.modern-theme .multimedia-quick-actions {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 16px;
  margin: 16px 0;
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.modern-theme .multimedia-action-btn {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.modern-theme .multimedia-action-btn:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  border-color: rgba(255, 255, 255, 0.3);
}

.modern-theme .floating-multimedia-button {
  background: rgba(139, 92, 246, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Animation for multimedia features */
@keyframes multimedia-pulse {
  0% { box-shadow: 0 0 0 0 rgba(139, 92, 246, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(139, 92, 246, 0); }
  100% { box-shadow: 0 0 0 0 rgba(139, 92, 246, 0); }
}

.floating-multimedia-button.has-suggestions {
  animation: multimedia-pulse 2s infinite;
}

/* Multimedia panel tab styling */
.tabbed-panel .tab-button[data-tab="multimedia"] {
  background: linear-gradient(135deg, #8B5CF6, #A855F7);
  color: white;
}

.tabbed-panel .tab-button[data-tab="multimedia"]:hover {
  background: linear-gradient(135deg, #7C3AED, #8B5CF6);
}

/* Loading states */
.multimedia-action-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
  position: relative;
}

.multimedia-action-btn.loading::after {
  content: '';
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Success states */
.multimedia-action-btn.success {
  background: linear-gradient(135deg, #10B981, #34D399) !important;
}

.multimedia-action-btn.success::before {
  content: '✓ ';
  font-weight: bold;
}

/* Integration with existing bookmark grid */
.bookmark-grid-container .multimedia-quick-actions {
  margin: 0 0 20px 0;
  padding: 16px;
  background: rgba(139, 92, 246, 0.05);
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 12px;
}

/* Accessibility improvements */
.multimedia-action-btn:focus {
  outline: 2px solid #8B5CF6;
  outline-offset: 2px;
}

.floating-multimedia-button:focus {
  outline: 3px solid #8B5CF6;
  outline-offset: 3px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .multimedia-action-btn {
    border: 2px solid currentColor;
  }
  
  .floating-multimedia-button {
    border: 3px solid white;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .multimedia-action-btn,
  .floating-multimedia-button {
    transition: none;
  }

  .floating-multimedia-button.has-suggestions {
    animation: none;
  }

  .multimedia-action-btn.loading::after {
    animation: none;
  }
}

/* NEW MULTIMEDIA PANEL STYLES - Following ImportPanel patterns */

/* Playlist Configuration */
.playlist-config {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.config-field label {
  font-weight: 500;
  font-size: 14px;
  color: var(--text-primary, #374151);
}

.config-input,
.config-textarea {
  padding: 12px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.config-input:focus,
.config-textarea:focus {
  outline: none;
  border-color: #8B5CF6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.config-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

/* Enhancement Options */
.enhancement-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.enhancement-option {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: white;
}

.enhancement-option:hover {
  border-color: #8B5CF6;
  background: rgba(139, 92, 246, 0.02);
}

.enhancement-option input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: #8B5CF6;
}

.option-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.option-content span {
  font-weight: 500;
  font-size: 14px;
  color: #374151;
  display: flex;
  align-items: center;
  gap: 8px;
}

.option-content small {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

/* Bookmarks Preview */
.bookmarks-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.bookmark-preview-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  transition: border-color 0.2s ease;
}

.bookmark-preview-item:hover {
  border-color: #8B5CF6;
}

.bookmark-title {
  font-weight: 500;
  font-size: 13px;
  color: #374151;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-url {
  font-size: 11px;
  color: #6b7280;
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.bookmark-preview-more {
  text-align: center;
  padding: 8px;
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

/* Dark theme support for new styles */
@media (prefers-color-scheme: dark) {
  .config-input,
  .config-textarea {
    background: #1f2937;
    border-color: #374151;
    color: white;
  }

  .config-input:focus,
  .config-textarea:focus {
    border-color: #8B5CF6;
  }

  .enhancement-option {
    background: #1f2937;
    border-color: #374151;
  }

  .enhancement-option:hover {
    background: rgba(139, 92, 246, 0.1);
  }

  .bookmarks-preview {
    background: #1f2937;
    border-color: #374151;
  }

  .bookmark-preview-item {
    background: #111827;
    border-color: #374151;
  }

  .bookmark-url {
    background: #374151;
    color: #9ca3af;
  }
}

/* Modern theme integration for new styles */
.modern-theme .config-input,
.modern-theme .config-textarea {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.modern-theme .config-input:focus,
.modern-theme .config-textarea:focus {
  border-color: rgba(139, 92, 246, 0.8);
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2);
}

.modern-theme .enhancement-option {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.modern-theme .enhancement-option:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(139, 92, 246, 0.5);
}

.modern-theme .bookmarks-preview {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.modern-theme .bookmark-preview-item {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(255, 255, 255, 0.15);
}

.modern-theme .bookmark-url {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* Responsive design for new components */
@media (max-width: 768px) {
  .playlist-config {
    gap: 12px;
  }

  .enhancement-options {
    gap: 8px;
  }

  .enhancement-option {
    padding: 12px;
  }

  .bookmarks-preview {
    max-height: 150px;
  }

  .bookmark-preview-item {
    padding: 6px 8px;
  }

  .bookmark-title {
    font-size: 12px;
  }

  .bookmark-url {
    font-size: 10px;
  }
}

/* BOOKMARK SELECTOR STYLES */

.bookmark-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
  max-height: 500px;
}

/* Search and Filter Controls */
.selector-controls {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 40px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
  background: white;
}

.search-input:focus {
  outline: none;
  border-color: #8B5CF6;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.clear-search {
  position: absolute;
  right: 8px;
  padding: 4px;
  border: none;
  background: none;
  color: #6b7280;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.clear-search:hover {
  background: #f3f4f6;
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  gap: 4px;
  overflow-x: auto;
  padding: 2px;
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  background: white;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.filter-tab:hover {
  border-color: #8B5CF6;
  background: rgba(139, 92, 246, 0.05);
}

.filter-tab.active {
  background: #8B5CF6;
  color: white;
  border-color: #8B5CF6;
}

/* Selection Summary */
.selection-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.selection-count {
  font-weight: 500;
  font-size: 14px;
  color: #374151;
}

.selection-actions {
  display: flex;
  gap: 8px;
}

.selection-btn {
  padding: 6px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.selection-btn:hover:not(:disabled) {
  border-color: #8B5CF6;
  background: rgba(139, 92, 246, 0.05);
}

.selection-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Bookmark List */
.bookmark-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  background: white;
}

.bookmark-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bookmark-item:last-child {
  border-bottom: none;
}

.bookmark-item:hover:not(.disabled) {
  background: #f9fafb;
}

.bookmark-item.selected {
  background: rgba(139, 92, 246, 0.1);
  border-color: rgba(139, 92, 246, 0.2);
}

.bookmark-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.bookmark-checkbox {
  width: 18px;
  height: 18px;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.bookmark-item.selected .bookmark-checkbox {
  background: #8B5CF6;
  border-color: #8B5CF6;
  color: white;
}

.bookmark-icon {
  flex-shrink: 0;
}

.bookmark-content {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-weight: 500;
  font-size: 14px;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 2px;
}

.bookmark-url {
  font-size: 12px;
  color: #6b7280;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-description {
  font-size: 11px;
  color: #9ca3af;
  margin-top: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-collection {
  font-size: 10px;
  color: #8B5CF6;
  background: rgba(139, 92, 246, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  margin-bottom: 12px;
  opacity: 0.5;
}

.empty-state p {
  font-weight: 500;
  margin-bottom: 4px;
}

.empty-state small {
  font-size: 12px;
  opacity: 0.8;
}

/* Selection Warning */
.selection-warning {
  padding: 8px 12px;
  background: #fef3cd;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  font-size: 12px;
  color: #92400e;
  text-align: center;
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .search-input {
    background: #1f2937;
    border-color: #374151;
    color: white;
  }

  .filter-tab {
    background: #1f2937;
    border-color: #374151;
    color: white;
  }

  .selection-summary {
    background: #1f2937;
    border-color: #374151;
  }

  .selection-btn {
    background: #1f2937;
    border-color: #374151;
    color: white;
  }

  .bookmark-list {
    background: #1f2937;
    border-color: #374151;
  }

  .bookmark-item {
    border-bottom-color: #374151;
  }

  .bookmark-item:hover:not(.disabled) {
    background: #111827;
  }

  .bookmark-checkbox {
    border-color: #6b7280;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .selector-controls {
    gap: 8px;
  }

  .filter-tabs {
    gap: 2px;
  }

  .filter-tab {
    padding: 6px 8px;
    font-size: 11px;
  }

  .selection-summary {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .selection-actions {
    justify-content: center;
  }

  .bookmark-item {
    padding: 8px;
    gap: 8px;
  }

  .bookmark-title {
    font-size: 13px;
  }

  .bookmark-url {
    font-size: 11px;
  }
}

/* Simple Bookmark Selector (for multimedia panel) */
.simple-bookmark-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.selector-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.selector-summary span {
  font-weight: 500;
  color: #374151;
}

/* Enhanced bookmark preview items with remove buttons */
.bookmark-preview-item {
  position: relative;
}

.bookmark-preview-item:hover button {
  opacity: 1;
}

.bookmark-preview-item button {
  opacity: 0;
  transition: opacity 0.2s ease;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

/* Dark theme support for simple selector */
@media (prefers-color-scheme: dark) {
  .selector-summary {
    border-bottom-color: #374151;
  }

  .selector-summary span {
    color: white;
  }
}

/* Modern theme support for simple selector */
.modern-theme .selector-summary {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.modern-theme .selector-summary span {
  color: white;
}
